#!/bin/bash

echo "=== 手动环境设置（Apple M4优化） ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "\n${BLUE}🍎 Apple M4手动环境设置${NC}"

# 1. 创建简单的conda环境
echo -e "\n${YELLOW}1. 创建简单的conda环境${NC}"
conda create -n Assessment python=3.11 -y
eval "$(conda shell.bash hook)"
conda activate Assessment

# 2. 安装基础包
echo -e "\n${YELLOW}2. 安装基础包${NC}"
conda install -c conda-forge nodejs=18 openjdk=17 git curl wget -y

# 3. 使用pip安装Python包
echo -e "\n${YELLOW}3. 安装Python包${NC}"
pip install --upgrade pip
pip install psycopg2-binary redis requests pyyaml python-dotenv
pip install black ruff isort pytest pytest-cov
pip install mkdocs mkdocs-material
pip install typer rich

# 4. 检查Java版本
echo -e "\n${YELLOW}4. 检查Java版本${NC}"
java -version

# 5. 检查Node.js版本
echo -e "\n${YELLOW}5. 检查Node.js版本${NC}"
node --version
npm --version

# 6. 安装前端依赖
echo -e "\n${YELLOW}6. 安装前端依赖${NC}"
cd frontend/uni-app
npm install
cd ../admin
npm install
cd ../..

# 7. 检查Maven
echo -e "\n${YELLOW}7. 检查Maven${NC}"
cd backend
chmod +x ./mvnw

# 尝试下载Maven wrapper
if [ ! -f .mvn/wrapper/maven-wrapper.jar ]; then
    echo "下载Maven Wrapper..."
    mkdir -p .mvn/wrapper
    curl -s -L "https://repo.maven.apache.org/maven2/org/apache/maven/wrapper/maven-wrapper/3.2.0/maven-wrapper-3.2.0.jar" -o .mvn/wrapper/maven-wrapper.jar
fi

./mvnw --version
cd ..

# 8. 设置环境变量
echo -e "\n${YELLOW}8. 设置环境变量${NC}"
if [ ! -f .env ]; then
    cp .env.example .env
    echo "已创建 .env 文件"
fi

# 9. 创建目录
echo -e "\n${YELLOW}9. 创建必要目录${NC}"
mkdir -p {data,logs,backup,ssl}

echo -e "\n${GREEN}=== 手动环境设置完成 ===${NC}"
echo
echo -e "${BLUE}下一步：${NC}"
echo "1. 激活环境: conda activate Assessment"
echo "2. 启动服务: ./scripts/dev-start-m4.sh"
echo "3. 监控性能: ./scripts/monitor-m4.sh"
echo
echo -e "${YELLOW}如果遇到问题，请查看日志文件${NC}"