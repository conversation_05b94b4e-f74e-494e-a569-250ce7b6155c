<template>
  <view class="page-container" :class="containerClass">
    <!-- 自定义导航栏 -->
    <view v-if="showNavbar" class="custom-navbar" :style="navbarStyle">
      <view class="navbar-content">
        <!-- 左侧内容 -->
        <view class="navbar-left">
          <view v-if="showBack" class="back-button" @click="handleBack">
            <text class="iconfont icon-arrow-left" />
          </view>
          <slot name="navbar-left"></slot>
        </view>

        <!-- 中间标题 -->
        <view class="navbar-center">
          <text v-if="title" class="navbar-title">{{ title }}</text>
          <slot name="navbar-center"></slot>
        </view>

        <!-- 右侧内容 -->
        <view class="navbar-right">
          <slot name="navbar-right"></slot>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content" :style="contentStyle">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <view class="error-icon">
          <text class="iconfont icon-error" />
        </view>
        <text class="error-message">{{ errorMessage }}</text>
        <button v-if="showRetry" class="retry-button" @click="handleRetry">重试</button>
      </view>

      <!-- 空状态 -->
      <view v-else-if="empty" class="empty-container">
        <view class="empty-icon">
          <text class="iconfont icon-empty" />
        </view>
        <text class="empty-message">{{ emptyMessage }}</text>
        <slot name="empty-action"></slot>
      </view>

      <!-- 正常内容 -->
      <template v-else>
        <slot></slot>
      </template>
    </view>

    <!-- 版权信息 -->
    <Copyright v-if="showCopyright" />

    <!-- 底部安全区域 -->
    <view v-if="safeAreaBottom" class="safe-area-bottom" />
  </view>
</template>

<script>
import Copyright from '@/components/Common/Copyright.vue'

export default {
  name: 'PageContainer',
  components: {
    Copyright
  },
  props: {
    // 页面标题
    title: {
      type: String,
      default: ''
    },

    // 是否显示导航栏
    showNavbar: {
      type: Boolean,
      default: true
    },

    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    },

    // 导航栏背景色
    navbarBg: {
      type: String,
      default: '#ffffff'
    },

    // 导航栏文字颜色
    navbarColor: {
      type: String,
      default: '#333333'
    },

    // 页面背景色
    backgroundColor: {
      type: String,
      default: '#f5f5f5'
    },

    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },

    // 加载文本
    loadingText: {
      type: String,
      default: '加载中...'
    },

    // 错误状态
    error: {
      type: Boolean,
      default: false
    },

    // 错误信息
    errorMessage: {
      type: String,
      default: '加载失败，请重试'
    },

    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      default: true
    },

    // 空状态
    empty: {
      type: Boolean,
      default: false
    },

    // 空状态信息
    emptyMessage: {
      type: String,
      default: '暂无数据'
    },

    // 是否需要底部安全区域
    safeAreaBottom: {
      type: Boolean,
      default: false
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },

    // 是否显示版权信息
    showCopyright: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      statusBarHeight: 0,
      navbarHeight: 44
    }
  },

  computed: {
    containerClass() {
      return {
        'has-navbar': this.showNavbar,
        'safe-area-bottom': this.safeAreaBottom,
        [this.customClass]: !!this.customClass
      }
    },

    navbarStyle() {
      return {
        backgroundColor: this.navbarBg,
        color: this.navbarColor,
        paddingTop: `${this.statusBarHeight}px`,
        height: `${this.statusBarHeight + this.navbarHeight}px`
      }
    },

    contentStyle() {
      const paddingTop = this.showNavbar ? this.statusBarHeight + this.navbarHeight : 0
      return {
        backgroundColor: this.backgroundColor,
        paddingTop: `${paddingTop}px`
      }
    }
  },

  mounted() {
    this.getSystemInfo()
  },

  methods: {
    // 获取系统信息
    getSystemInfo() {
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 0

      // 根据平台调整导航栏高度
      if (systemInfo.platform === 'ios') {
        this.navbarHeight = 44
      } else {
        this.navbarHeight = 48
      }
    },

    // 返回按钮点击
    handleBack() {
      this.$emit('back')

      // 默认行为：返回上一页
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    },

    // 重试按钮点击
    handleRetry() {
      this.$emit('retry')
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  &.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 1px solid $border-color-light;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 $spacing-md;
  }

  .navbar-left,
  .navbar-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    min-width: 60px;
  }

  .navbar-right {
    justify-content: flex-end;
  }

  .navbar-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 $spacing-sm;
  }

  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: $border-radius-sm;
    transition: background-color 0.2s;

    &:active {
      background-color: rgba(0, 0, 0, 0.1);
    }

    .iconfont {
      font-size: 18px;
    }
  }

  .navbar-title {
    font-size: $font-size-lg;
    font-weight: 500;
    @include text-ellipsis;
  }
}

.page-content {
  flex: 1;
  position: relative;
}

.loading-container {
  @include flex-center;
  flex-direction: column;
  padding: $spacing-xl 0;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid $border-color-light;
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: $spacing-md;
  }

  .loading-text {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }
}

.error-container {
  @include flex-center;
  flex-direction: column;
  padding: $spacing-xl 0;

  .error-icon {
    width: 64px;
    height: 64px;
    @include flex-center;
    background-color: $error-color-light;
    border-radius: 50%;
    margin-bottom: $spacing-md;

    .iconfont {
      font-size: 32px;
      color: $error-color;
    }
  }

  .error-message {
    font-size: $font-size-base;
    color: $text-color-secondary;
    margin-bottom: $spacing-lg;
    text-align: center;
  }

  .retry-button {
    padding: $spacing-sm $spacing-lg;
    background-color: $primary-color;
    color: #ffffff;
    border: none;
    border-radius: $border-radius-base;
    font-size: $font-size-sm;

    &:active {
      background-color: $primary-color-dark;
    }
  }
}

.empty-container {
  @include flex-center;
  flex-direction: column;
  padding: $spacing-xl 0;

  .empty-icon {
    width: 64px;
    height: 64px;
    @include flex-center;
    background-color: $bg-color-light;
    border-radius: 50%;
    margin-bottom: $spacing-md;

    .iconfont {
      font-size: 32px;
      color: $text-color-placeholder;
    }
  }

  .empty-message {
    font-size: $font-size-base;
    color: $text-color-secondary;
    margin-bottom: $spacing-lg;
    text-align: center;
  }
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
