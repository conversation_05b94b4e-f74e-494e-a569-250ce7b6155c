<template>
  <view class="list-item" :class="itemClass" @click="handleClick">
    <!-- 左侧内容 -->
    <view v-if="$slots.prefix || avatar || icon" class="list-item-prefix">
      <!-- 头像 -->
      <image v-if="avatar" class="list-item-avatar" :src="avatar" mode="aspectFill" />

      <!-- 图标 -->
      <view v-else-if="icon" class="list-item-icon">
        <text class="icon-text" :style="{ fontSize: iconSize + 'px', color: iconColor }">●</text>
      </view>

      <!-- 自定义前置内容 -->
      <slot name="prefix"></slot>
    </view>

    <!-- 主要内容 -->
    <view class="list-item-content">
      <!-- 标题 -->
      <view v-if="title || $slots.title" class="list-item-title">
        <text v-if="title">{{ title }}</text>
        <slot name="title"></slot>
      </view>

      <!-- 描述 -->
      <view v-if="description || $slots.description" class="list-item-description">
        <text v-if="description">{{ description }}</text>
        <slot name="description"></slot>
      </view>

      <!-- 自定义内容 -->
      <slot></slot>
    </view>

    <!-- 右侧内容 -->
    <view v-if="$slots.suffix || value || showArrow" class="list-item-suffix">
      <!-- 值 -->
      <view v-if="value" class="list-item-value">
        <text>{{ value }}</text>
      </view>

      <!-- 自定义后置内容 -->
      <slot name="suffix"></slot>

      <!-- 箭头 -->
      <view v-if="showArrow" class="list-item-arrow">
        <text class="arrow-text" style="font-size: 16px; color: #c0c4cc">→</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ListItem',

  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },

    // 描述
    description: {
      type: String,
      default: ''
    },

    // 值
    value: {
      type: [String, Number],
      default: ''
    },

    // 头像
    avatar: {
      type: String,
      default: ''
    },

    // 图标
    icon: {
      type: String,
      default: ''
    },

    // 图标大小
    iconSize: {
      type: Number,
      default: 20
    },

    // 图标颜色
    iconColor: {
      type: String,
      default: '#909399'
    },

    // 是否显示箭头
    showArrow: {
      type: Boolean,
      default: false
    },

    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 是否显示分割线
    border: {
      type: Boolean,
      default: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  emits: ['click'],

  computed: {
    itemClass() {
      return {
        'list-item--clickable': this.clickable || this.showArrow,
        'list-item--disabled': this.disabled,
        'list-item--no-border': !this.border,
        [this.customClass]: !!this.customClass
      }
    }
  },

  methods: {
    handleClick(e) {
      if (this.disabled) return

      if (this.clickable || this.showArrow) {
        this.$emit('click', e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.list-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: $spacing-md;
  background-color: $bg-color-white;
  transition: background-color 0.2s;

  &:not(.list-item--no-border):not(:last-child) {
    border-bottom: 1px solid $border-color-lighter;
  }

  &.list-item--clickable {
    cursor: pointer;

    &:hover {
      background-color: $bg-color-hover;
    }

    &:active {
      background-color: $bg-color-active;
    }
  }

  &.list-item--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
    }
  }
}

.list-item-prefix {
  display: flex;
  align-items: center;
  margin-right: $spacing-md;
  flex-shrink: 0;
}

.list-item-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $bg-color-page;
}

.list-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $bg-color-page;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: $font-size-base;
  font-weight: 500;
  color: $text-color-primary;
  line-height: 1.4;
  margin-bottom: $spacing-xs;

  @include text-ellipsis;

  &:last-child {
    margin-bottom: 0;
  }
}

.list-item-description {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  line-height: 1.4;

  @include text-ellipsis(2);
}

.list-item-suffix {
  display: flex;
  align-items: center;
  margin-left: $spacing-md;
  flex-shrink: 0;
}

.list-item-value {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: $spacing-xs;

  @include text-ellipsis;
}

.list-item-arrow {
  display: flex;
  align-items: center;
  margin-left: $spacing-xs;
}

// 特殊布局样式
.list-item--large {
  padding: $spacing-lg $spacing-md;

  .list-item-avatar,
  .list-item-icon {
    width: 48px;
    height: 48px;
  }

  .list-item-title {
    font-size: $font-size-lg;
  }
}

.list-item--small {
  padding: $spacing-sm $spacing-md;

  .list-item-avatar,
  .list-item-icon {
    width: 32px;
    height: 32px;
  }

  .list-item-title {
    font-size: $font-size-sm;
  }

  .list-item-description {
    font-size: $font-size-xs;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .list-item {
    padding: $spacing-sm;
  }

  .list-item-prefix {
    margin-right: $spacing-sm;
  }

  .list-item-suffix {
    margin-left: $spacing-sm;
  }
}
</style>
