<template>
  <view v-if="visible" class="modal-overlay" @tap="handleOverlayTap">
    <view class="modal-container" @tap.stop>
      <view v-if="title || $slots.header" class="modal-header">
        <slot name="header">
          <view class="modal-title">{{ title }}</view>
          <view v-if="closable" class="modal-close" @tap="handleClose">
            <text class="close-icon">×</text>
          </view>
        </slot>
      </view>

      <view class="modal-content">
        <slot></slot>
      </view>

      <view v-if="$slots.footer" class="modal-footer">
        <slot name="footer"></slot>
      </view>
    </view>
  </view>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'Modal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    closable: {
      type: <PERSON>olean,
      default: true
    },
    maskClosable: {
      type: <PERSON>olean,
      default: true
    },
    width: {
      type: [String, Number],
      default: '80%'
    },
    zIndex: {
      type: Number,
      default: 1000
    }
  },
  emits: ['update:visible', 'close', 'confirm', 'cancel'],
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    handleOverlayTap() {
      if (this.maskClosable) {
        this.handleClose()
      }
    },

    handleConfirm() {
      this.$emit('confirm')
    },

    handleCancel() {
      this.$emit('cancel')
      this.handleClose()
    }
  }
})
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: $bg-color-white;
  border-radius: $border-radius-lg;
  width: 80%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: $box-shadow-lg;
}

.modal-header {
  position: relative;
  padding: $spacing-lg;
  border-bottom: 1px solid $border-color-light;

  .modal-title {
    font-size: $font-size-lg;
    font-weight: 500;
    color: $text-color-primary;
    margin: 0;
  }

  .modal-close {
    position: absolute;
    top: $spacing-md;
    right: $spacing-md;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: $border-radius-sm;
    transition: background-color 0.2s;

    &:hover {
      background-color: $bg-color-light;
    }

    .close-icon {
      font-size: 24px;
      color: $text-color-secondary;
      line-height: 1;
    }
  }
}

.modal-content {
  padding: $spacing-lg;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: $spacing-md $spacing-lg;
  border-top: 1px solid $border-color-light;
  display: flex;
  justify-content: flex-end;
  gap: $spacing-sm;
}

// 响应式设计
@media screen and (max-width: 768px) {
  .modal-container {
    width: 90%;
    margin: $spacing-md;
  }

  .modal-header {
    padding: $spacing-md;
  }

  .modal-content {
    padding: $spacing-md;
    max-height: 50vh;
  }

  .modal-footer {
    padding: $spacing-sm $spacing-md;
  }
}
</style>
