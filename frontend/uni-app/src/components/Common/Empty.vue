<template>
  <view class="empty" :class="emptyClass">
    <!-- 图标或图片 -->
    <view class="empty-image">
      <image v-if="image" :src="image" mode="aspectFit" class="empty-img" />
      <view v-else-if="icon" class="empty-icon">
        <text class="icon-text" :style="{ fontSize: iconSize + 'px', color: iconColor }">📄</text>
      </view>
      <view v-else class="empty-default-icon">
        <!-- 默认空状态图标 -->
        <svg viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
          <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
            <ellipse fill="#f5f5f5" cx="32" cy="33" rx="32" ry="7" />
            <g fill-rule="nonzero" stroke="#d9d9d9">
              <path
                d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
              />
              <path
                d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                fill="#fafafa"
              />
            </g>
          </g>
        </svg>
      </view>
    </view>

    <!-- 描述文本 -->
    <view class="empty-description">
      <text v-if="description">{{ description }}</text>
      <slot v-else name="description">
        <text>{{ defaultDescription }}</text>
      </slot>
    </view>

    <!-- 操作按钮 -->
    <view v-if="$slots.default || buttonText" class="empty-actions">
      <slot>
        <button v-if="buttonText" class="empty-button" @click="handleButtonClick">
          {{ buttonText }}
        </button>
      </slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Empty',

  props: {
    // 空状态图片
    image: {
      type: String,
      default: ''
    },

    // 空状态图标
    icon: {
      type: String,
      default: ''
    },

    // 图标大小
    iconSize: {
      type: Number,
      default: 60
    },

    // 图标颜色
    iconColor: {
      type: String,
      default: '#dcdfe6'
    },

    // 描述文本
    description: {
      type: String,
      default: ''
    },

    // 按钮文本
    buttonText: {
      type: String,
      default: ''
    },

    // 空状态类型
    type: {
      type: String,
      default: 'default'
    },

    // 尺寸
    size: {
      type: String,
      default: 'medium'
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  emits: ['button-click'],

  computed: {
    emptyClass() {
      return {
        [`empty--${this.type}`]: true,
        [`empty--${this.size}`]: true,
        [this.customClass]: !!this.customClass
      }
    },

    defaultDescription() {
      const descriptions = {
        default: '暂无数据',
        search: '搜索无结果',
        network: '网络异常',
        error: '加载失败',
        nodata: '暂无内容'
      }
      return descriptions[this.type] || descriptions.default
    }
  },

  methods: {
    handleButtonClick() {
      this.$emit('button-click')
    }
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  text-align: center;

  &.empty--small {
    padding: $spacing-md;

    .empty-image {
      width: 80px;
      height: 80px;
    }

    .empty-description {
      font-size: $font-size-sm;
    }
  }

  &.empty--medium {
    padding: $spacing-xl;

    .empty-image {
      width: 120px;
      height: 120px;
    }

    .empty-description {
      font-size: $font-size-base;
    }
  }

  &.empty--large {
    padding: $spacing-xxl;

    .empty-image {
      width: 160px;
      height: 160px;
    }

    .empty-description {
      font-size: $font-size-lg;
    }
  }
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-img {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.empty-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-default-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;

  svg {
    width: 80%;
    height: 80%;
  }
}

.empty-description {
  margin-bottom: $spacing-lg;
  color: $text-color-secondary;
  line-height: 1.6;

  text {
    display: block;
  }
}

.empty-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-sm;
}

.empty-button {
  padding: $spacing-sm $spacing-lg;
  background-color: $primary-color;
  color: $color-white;
  border: none;
  border-radius: $border-radius-base;
  font-size: $font-size-sm;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: color.adjust($primary-color, $lightness: 10%);
  }

  &:active {
    background-color: color.adjust($primary-color, $lightness: -5%);
  }
}

// 不同类型的样式
.empty--search {
  .empty-description {
    color: $text-color-regular;
  }
}

.empty--network {
  .empty-description {
    color: $warning-color;
  }

  .empty-button {
    background-color: $warning-color;

    &:hover {
      background-color: color.adjust($warning-color, $lightness: 10%);
    }
  }
}

.empty--error {
  .empty-description {
    color: $error-color;
  }

  .empty-button {
    background-color: $error-color;

    &:hover {
      background-color: color.adjust($error-color, $lightness: 10%);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .empty {
    padding: $spacing-lg $spacing-md;

    &.empty--small {
      padding: $spacing-md $spacing-sm;
    }

    &.empty--large {
      padding: $spacing-xl $spacing-md;
    }
  }

  .empty-image {
    width: 100px;
    height: 100px;
    margin-bottom: $spacing-md;
  }

  .empty-description {
    margin-bottom: $spacing-md;
    font-size: $font-size-sm;
  }
}
</style>
