<template>
  <view class="question-preview">
    <view class="question-header">
      <text class="question-content">{{ question.content }}</text>
      <text v-if="question.required" class="required-mark">*</text>
    </view>

    <view v-if="question.description" class="question-description">
      <text>{{ question.description }}</text>
    </view>

    <!-- 单选题 -->
    <view v-if="question.type === 'single_choice'" class="question-options">
      <radio-group>
        <label v-for="(option, index) in question.options" :key="index" class="option-item">
          <radio :value="option.value" color="#1890ff" />
          <text class="option-text">{{ option.label }}</text>
        </label>
      </radio-group>
    </view>

    <!-- 多选题 -->
    <view v-else-if="question.type === 'multiple_choice'" class="question-options">
      <checkbox-group>
        <label v-for="(option, index) in question.options" :key="index" class="option-item">
          <checkbox :value="option.value" color="#1890ff" />
          <text class="option-text">{{ option.label }}</text>
        </label>
      </checkbox-group>
    </view>

    <!-- 评分题 -->
    <view v-else-if="question.type === 'rating'" class="rating-question">
      <view class="rating-range">
        <text class="range-text">{{ question.minScore || 0 }}</text>
        <slider
          :min="question.minScore || 0"
          :max="question.maxScore || 10"
          :step="question.step || 1"
          show-value
          active-color="#1890ff"
        />
        <text class="range-text">{{ question.maxScore || 10 }}</text>
      </view>
    </view>

    <!-- 单行文本 -->
    <view v-else-if="question.type === 'text'" class="text-question">
      <input
        type="text"
        :placeholder="question.placeholder || '请输入答案'"
        class="text-input"
        disabled
      />
    </view>

    <!-- 多行文本 -->
    <view v-else-if="question.type === 'textarea'" class="textarea-question">
      <textarea
        :placeholder="question.placeholder || '请输入答案'"
        :maxlength="question.maxLength || 200"
        class="textarea-input"
        disabled
      ></textarea>
    </view>

    <!-- 解析说明 -->
    <view v-if="question.analysis" class="question-analysis">
      <text class="analysis-label">解析：</text>
      <text class="analysis-text">{{ question.analysis }}</text>
    </view>
  </view>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'QuestionPreview',

  props: {
    question: {
      type: Object,
      required: true
    }
  }
})
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;

.question-preview {
  padding: $spacing-md;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-md;
  background-color: $bg-color-white;
}

.question-header {
  display: flex;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-bottom: $spacing-sm;
}

.question-content {
  flex: 1;
  font-size: $font-size-base;
  color: $text-color-primary;
  line-height: 1.5;
}

.required-mark {
  color: $error-color;
  font-weight: bold;
}

.question-description {
  margin-bottom: $spacing-md;
  padding: $spacing-sm;
  background-color: $bg-color-light;
  border-radius: $border-radius-sm;

  text {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    line-height: 1.4;
  }
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.option-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  border-radius: $border-radius-sm;

  &:hover {
    background-color: $bg-color-light;
  }
}

.option-text {
  font-size: $font-size-sm;
  color: $text-color-primary;
}

.rating-question {
  padding: $spacing-md 0;
}

.rating-range {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.range-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  min-width: 24px;
  text-align: center;
}

.text-input,
.textarea-input {
  width: 100%;
  padding: $spacing-sm;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  background-color: $bg-color-light;
}

.textarea-input {
  min-height: 80px;
  resize: none;
}

.question-analysis {
  margin-top: $spacing-md;
  padding: $spacing-sm;
  background-color: #f0f9ff;
  border-radius: $border-radius-sm;
  border-left: 3px solid $primary-color;
}

.analysis-label {
  font-size: $font-size-xs;
  color: $text-color-secondary;
  font-weight: 600;
}

.analysis-text {
  font-size: $font-size-xs;
  color: $text-color-primary;
  line-height: 1.4;
  margin-left: $spacing-sm;
}
</style>
