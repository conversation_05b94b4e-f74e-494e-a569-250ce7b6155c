<template>
  <view class="question-editor">
    <Card class="question-card">
      <template #header>
        <view class="question-header">
          <text class="question-title">{{ title || `题目 ${questionIndex + 1}` }}</text>
          <view class="question-actions">
            <Button v-if="showPreview" type="default" size="mini" @click="$emit('preview')">
              预览
            </Button>
            <Button v-if="showDelete" type="default" size="mini" @click="$emit('delete')">
              删除
            </Button>
          </view>
        </view>
      </template>

      <view class="question-content">
        <!-- 基本信息 -->
        <view class="editor-section">
          <view class="section-title">基本信息</view>

          <!-- 题目类型 -->
          <view class="form-item">
            <text class="form-label">题目类型 <text class="required">*</text></text>
            <view class="question-types">
              <view
                v-for="type in questionTypes"
                :key="type.value"
                class="type-option"
                :class="{ active: questionData.type === type.value }"
                @tap="updateQuestionData('type', type.value)"
              >
                <text class="type-icon">{{ type.icon }}</text>
                <text class="type-text">{{ type.label }}</text>
              </view>
            </view>
          </view>

          <!-- 题目内容 -->
          <view class="form-item">
            <text class="form-label">题目内容 <text class="required">*</text></text>
            <textarea
              v-model="questionData.content"
              class="question-textarea"
              placeholder="请输入题目内容"
              :maxlength="500"
              @input="handleContentChange"
            ></textarea>
            <text class="char-count">{{ questionData.content?.length || 0 }}/500</text>
          </view>

          <!-- 题目描述 -->
          <view class="form-item">
            <text class="form-label">题目描述</text>
            <textarea
              v-model="questionData.description"
              class="question-textarea small"
              placeholder="请输入题目描述或说明（可选）"
              :maxlength="200"
              @input="handleDescriptionChange"
            ></textarea>
          </view>

          <!-- 是否必填 -->
          <view class="form-item">
            <view class="form-checkbox">
              <checkbox
                :checked="questionData.required"
                color="#1890ff"
                @change="handleRequiredChange"
              />
              <text class="checkbox-label">必填题目</text>
            </view>
          </view>
        </view>

        <!-- 选择题选项 -->
        <view
          v-if="['single_choice', 'multiple_choice'].includes(questionData.type)"
          class="editor-section"
        >
          <view class="section-title">
            <text>选项设置</text>
            <Button type="primary" size="mini" @click="addOption"> 添加选项 </Button>
          </view>

          <view class="options-list">
            <view v-for="(option, index) in questionData.options" :key="index" class="option-item">
              <view class="option-prefix">
                <text>{{ getOptionPrefix(index) }}</text>
              </view>

              <Input
                v-model="option.label"
                placeholder="请输入选项内容"
                @input="updateOption(index, 'label', $event)"
              />

              <Input
                v-model="option.score"
                type="number"
                placeholder="分值"
                class="score-input"
                @input="updateOption(index, 'score', $event)"
              />

              <Button
                type="default"
                size="mini"
                :disabled="questionData.options.length <= 2"
                @click="removeOption(index)"
              >
                删除
              </Button>
            </view>
          </view>
        </view>

        <!-- 评分题设置 -->
        <view v-else-if="questionData.type === 'rating'" class="editor-section">
          <view class="section-title">评分设置</view>

          <view class="rating-config">
            <view class="form-item-inline">
              <text class="form-label">最小值：</text>
              <Input
                v-model="questionData.minScore"
                type="number"
                placeholder="0"
                @input="handleMinScoreChange"
              />
            </view>

            <view class="form-item-inline">
              <text class="form-label">最大值：</text>
              <Input
                v-model="questionData.maxScore"
                type="number"
                placeholder="10"
                @input="handleMaxScoreChange"
              />
            </view>

            <view class="form-item-inline">
              <text class="form-label">步长：</text>
              <Input
                v-model="questionData.step"
                type="number"
                placeholder="1"
                @input="handleStepChange"
              />
            </view>
          </view>
        </view>

        <!-- 文本题设置 -->
        <view v-else-if="['text', 'textarea'].includes(questionData.type)" class="editor-section">
          <view class="section-title">文本设置</view>

          <view class="text-config">
            <view class="form-item">
              <text class="form-label">占位符文本</text>
              <Input
                v-model="questionData.placeholder"
                placeholder="请输入占位符文本"
                @input="handlePlaceholderChange"
              />
            </view>

            <view v-if="questionData.type === 'textarea'" class="form-item">
              <text class="form-label">最大字符数</text>
              <Input
                v-model="questionData.maxLength"
                type="number"
                placeholder="200"
                @input="handleMaxLengthChange"
              />
            </view>
          </view>
        </view>

        <!-- 高级设置 -->
        <view class="editor-section">
          <view class="section-title">高级设置</view>

          <!-- 权重 -->
          <view class="form-item">
            <text class="form-label">权重</text>
            <Input
              v-model="questionData.weight"
              type="number"
              placeholder="1.0"
              @input="handleWeightChange"
            />
            <text class="form-hint">用于计算总分时的权重系数</text>
          </view>

          <!-- 显示条件 -->
          <view class="form-item">
            <text class="form-label">显示条件</text>
            <Input
              v-model="questionData.condition"
              placeholder="例如：Q1 === 'A' && Q2 > 5"
              @input="handleConditionChange"
            />
            <text class="form-hint">当满足条件时才显示此题目</text>
          </view>

          <!-- 解析说明 -->
          <view class="form-item">
            <text class="form-label">解析说明</text>
            <textarea
              v-model="questionData.analysis"
              class="question-textarea small"
              placeholder="请输入题目解析或说明"
              :maxlength="300"
              @input="handleAnalysisChange"
            ></textarea>
          </view>
        </view>
      </view>
    </Card>

    <!-- 预览弹窗 -->
    <Modal v-model:visible="showPreviewModal" title="题目预览" :show-cancel="false">
      <view class="preview-content">
        <QuestionPreview :question="questionData" />
      </view>
    </Modal>
  </view>
</template>

<script>
import { defineComponent } from 'vue'
import Card from '@/components/Common/Card.vue'
import Button from '@/components/Common/Button.vue'
import Input from '@/components/Form/Input.vue'
import Modal from '@/components/Common/Modal.vue'

export default defineComponent({
  name: 'QuestionEditor',

  components: {
    Card,
    Button,
    Input,
    Modal
  },

  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    },

    questionIndex: {
      type: Number,
      default: 0
    },

    title: {
      type: String,
      default: ''
    },

    showPreview: {
      type: Boolean,
      default: true
    },

    showDelete: {
      type: Boolean,
      default: true
    }
  },

  emits: ['update:modelValue', 'delete', 'preview', 'change'],

  data() {
    return {
      showPreviewModal: false,

      questionTypes: [
        { value: 'single_choice', label: '单选题', icon: '◉' },
        { value: 'multiple_choice', label: '多选题', icon: '☑' },
        { value: 'rating', label: '评分题', icon: '★' },
        { value: 'text', label: '单行文本', icon: '✎' },
        { value: 'textarea', label: '多行文本', icon: '☰' }
      ]
    }
  },

  computed: {
    questionData: {
      get() {
        return this.getDefaultQuestionData()
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },

  methods: {
    getDefaultQuestionData() {
      const defaultData = {
        type: 'single_choice',
        content: '',
        description: '',
        required: true,
        weight: 1,
        condition: '',
        analysis: '',
        options: [
          { label: '', score: 0, value: 'A' },
          { label: '', score: 0, value: 'B' }
        ],
        minScore: 0,
        maxScore: 10,
        step: 1,
        placeholder: '',
        maxLength: 200
      }

      return { ...defaultData, ...this.modelValue }
    },

    updateQuestionData(key, value) {
      const newData = { ...this.questionData, [key]: value }

      // 切换题目类型时重置相关配置
      if (key === 'type') {
        if (['single_choice', 'multiple_choice'].includes(value)) {
          newData.options = [
            { label: '', score: 0, value: 'A' },
            { label: '', score: 0, value: 'B' }
          ]
        } else if (value === 'rating') {
          newData.minScore = 0
          newData.maxScore = 10
          newData.step = 1
        }
      }

      this.$emit('update:modelValue', newData)
      this.$emit('change', newData)
    },

    handleContentChange(e) {
      this.updateQuestionData('content', e.detail.value)
    },

    handleDescriptionChange(e) {
      this.updateQuestionData('description', e.detail.value)
    },

    handleRequiredChange(e) {
      this.updateQuestionData('required', e.detail.value)
    },

    handleMinScoreChange(value) {
      this.updateQuestionData('minScore', parseInt(value) || 0)
    },

    handleMaxScoreChange(value) {
      this.updateQuestionData('maxScore', parseInt(value) || 10)
    },

    handleStepChange(value) {
      this.updateQuestionData('step', parseInt(value) || 1)
    },

    handlePlaceholderChange(value) {
      this.updateQuestionData('placeholder', value)
    },

    handleMaxLengthChange(value) {
      this.updateQuestionData('maxLength', parseInt(value) || 200)
    },

    handleWeightChange(value) {
      this.updateQuestionData('weight', parseFloat(value) || 1)
    },

    handleConditionChange(value) {
      this.updateQuestionData('condition', value)
    },

    handleAnalysisChange(e) {
      this.updateQuestionData('analysis', e.detail.value)
    },

    addOption() {
      const options = [...this.questionData.options]
      const nextValue = String.fromCharCode(65 + options.length) // A, B, C, D...
      options.push({
        label: '',
        score: 0,
        value: nextValue
      })
      this.updateQuestionData('options', options)
    },

    removeOption(index) {
      if (this.questionData.options.length <= 2) return

      const options = [...this.questionData.options]
      options.splice(index, 1)

      // 重新分配value (A, B, C, D...)
      options.forEach((option, i) => {
        option.value = String.fromCharCode(65 + i)
      })

      this.updateQuestionData('options', options)
    },

    updateOption(index, key, value) {
      const options = [...this.questionData.options]
      if (key === 'score') {
        options[index][key] = parseInt(value) || 0
      } else {
        options[index][key] = value
      }
      this.updateQuestionData('options', options)
    },

    getOptionPrefix(index) {
      return `${String.fromCharCode(65 + index)}.`
    }
  }
})
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.question-editor {
  margin-bottom: $spacing-lg;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
}

.question-actions {
  display: flex;
  gap: $spacing-sm;
}

.question-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.editor-section {
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: $font-size-md;
    font-weight: 600;
    color: $text-color-primary;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $border-color-light;
  }
}

.form-item {
  margin-bottom: $spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-item-inline {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;

  .form-label {
    min-width: 60px;
    flex-shrink: 0;
  }

  .input {
    flex: 1;
    max-width: 120px;
  }
}

.form-label {
  display: block;
  font-size: $font-size-sm;
  color: $text-color-primary;
  margin-bottom: $spacing-xs;

  .required {
    color: $error-color;
  }
}

.form-hint {
  display: block;
  font-size: $font-size-xs;
  color: $text-color-secondary;
  margin-top: $spacing-xs;
  line-height: 1.4;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  .checkbox-label {
    font-size: $font-size-sm;
    color: $text-color-primary;
  }
}

.question-types {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.type-option {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-md;
  background-color: $bg-color-white;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: $primary-color;
  }

  &.active {
    border-color: $primary-color;
    background-color: $primary-color-light;
    color: $primary-color;
  }
}

.type-icon {
  font-size: $font-size-md;
}

.type-text {
  font-size: $font-size-sm;
}

.question-textarea {
  width: 100%;
  padding: $spacing-sm;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;

  &.small {
    min-height: 60px;
  }

  &:focus {
    border-color: $primary-color;
    outline: none;
  }
}

.char-count {
  display: block;
  text-align: right;
  font-size: $font-size-xs;
  color: $text-color-secondary;
  margin-top: $spacing-xs;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.option-item {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .option-prefix {
    width: 24px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;
    font-size: $font-size-sm;
    font-weight: 600;
    color: $text-color-secondary;
    flex-shrink: 0;
  }

  .input {
    flex: 1;
  }

  .score-input {
    width: 80px;
    flex-shrink: 0;
  }
}

.rating-config {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.text-config {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.preview-content {
  padding: $spacing-md;
}

// 响应式设计
@media screen and (max-width: 768px) {
  .question-header {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  .question-actions {
    justify-content: center;
  }

  .type-option {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }

  .option-item {
    flex-wrap: wrap;
    gap: $spacing-xs;

    .option-prefix {
      order: 1;
    }

    .input {
      order: 2;
      flex: 1 1 100%;
    }

    .score-input {
      order: 3;
      flex: 1;
    }

    .button {
      order: 4;
      flex: 1;
    }
  }

  .form-item-inline {
    flex-direction: column;
    align-items: stretch;

    .input {
      max-width: none;
    }
  }
}
</style>
