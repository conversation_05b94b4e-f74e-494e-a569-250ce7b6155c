<template>
  <view class="input-wrapper" :class="wrapperClass">
    <!-- 前置图标或内容 -->
    <view v-if="prefixIcon || $slots.prefix" class="input-prefix">
      <text
        v-if="prefixIcon"
        class="icon-text"
        :style="{ fontSize: iconSize + 'px', color: iconColor }"
        >🔍</text
      >
      <slot name="prefix"></slot>
    </view>

    <!-- 输入框 -->
    <input
      v-if="!isTextarea"
      class="input-control"
      :type="inputType"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :maxlength="maxlength"
      :focus="focus"
      :confirm-type="confirmType"
      :cursor-spacing="cursorSpacing"
      :selection-start="selectionStart"
      :selection-end="selectionEnd"
      :adjust-position="adjustPosition"
      :hold-keyboard="holdKeyboard"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @keyboardheightchange="handleKeyboardHeightChange"
    />

    <!-- 多行文本框 -->
    <textarea
      v-else
      class="textarea-control"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :maxlength="maxlength"
      :focus="focus"
      :auto-height="autoHeight"
      :cursor-spacing="cursorSpacing"
      :selection-start="selectionStart"
      :selection-end="selectionEnd"
      :adjust-position="adjustPosition"
      :hold-keyboard="holdKeyboard"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
      @linechange="handleLineChange"
      @keyboardheightchange="handleKeyboardHeightChange"
    ></textarea>

    <!-- 后置图标或内容 -->
    <view v-if="suffixIcon || $slots.suffix || showClear || showPassword" class="input-suffix">
      <!-- 清除按钮 -->
      <view v-if="showClear && modelValue" class="clear-btn" @click="handleClear">
        <text class="icon-text" :style="{ fontSize: iconSize + 'px', color: iconColor }">✕</text>
      </view>

      <!-- 密码显示切换 -->
      <view v-if="showPassword" class="password-btn" @click="togglePassword">
        <text class="icon-text" :style="{ fontSize: iconSize + 'px', color: iconColor }">
          {{ passwordVisible ? '🙈' : '👁️' }}
        </text>
      </view>

      <!-- 自定义后置图标 -->
      <text
        v-if="suffixIcon"
        class="icon-text"
        :style="{ fontSize: iconSize + 'px', color: iconColor }"
        >⚙️</text
      >
      <slot name="suffix"></slot>
    </view>

    <!-- 字数统计 -->
    <view v-if="showWordLimit && maxlength" class="word-limit">
      <text class="word-count">{{ wordCount }}/{{ maxlength }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Input',

  props: {
    // v-model 值
    modelValue: {
      type: [String, Number],
      default: ''
    },

    // 输入框类型
    type: {
      type: String,
      default: 'text'
    },

    // 是否为多行文本
    textarea: {
      type: Boolean,
      default: false
    },

    // 占位符
    placeholder: {
      type: String,
      default: '请输入'
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },

    // 最大长度
    maxlength: {
      type: [String, Number],
      default: -1
    },

    // 是否显示字数限制
    showWordLimit: {
      type: Boolean,
      default: false
    },

    // 是否显示清除按钮
    clearable: {
      type: Boolean,
      default: false
    },

    // 是否显示密码切换
    showPassword: {
      type: Boolean,
      default: false
    },

    // 前置图标
    prefixIcon: {
      type: String,
      default: ''
    },

    // 后置图标
    suffixIcon: {
      type: String,
      default: ''
    },

    // 尺寸
    size: {
      type: String,
      default: 'medium'
    },

    // 是否自动获取焦点
    focus: {
      type: Boolean,
      default: false
    },

    // 确认按钮文字
    confirmType: {
      type: String,
      default: 'done'
    },

    // 光标与键盘的距离
    cursorSpacing: {
      type: Number,
      default: 0
    },

    // 光标起始位置
    selectionStart: {
      type: Number,
      default: -1
    },

    // 光标结束位置
    selectionEnd: {
      type: Number,
      default: -1
    },

    // 键盘弹起时是否自动上推页面
    adjustPosition: {
      type: Boolean,
      default: true
    },

    // focus时是否保持键盘不收起
    holdKeyboard: {
      type: Boolean,
      default: false
    },

    // 是否自动增高（textarea）
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'update:modelValue',
    'input',
    'focus',
    'blur',
    'confirm',
    'clear',
    'keyboardheightchange',
    'linechange'
  ],

  data() {
    return {
      passwordVisible: false
    }
  },

  computed: {
    isTextarea() {
      return this.textarea || this.type === 'textarea'
    },

    inputType() {
      if (this.type === 'password' && this.showPassword) {
        return this.passwordVisible ? 'text' : 'password'
      }
      return this.type
    },

    wrapperClass() {
      return {
        'input-wrapper--disabled': this.disabled,
        'input-wrapper--readonly': this.readonly,
        'input-wrapper--textarea': this.isTextarea,
        [`input-wrapper--${this.size}`]: true
      }
    },

    showClear() {
      return this.clearable && !this.disabled && !this.readonly
    },

    iconSize() {
      const sizeMap = {
        small: 16,
        medium: 18,
        large: 20
      }
      return sizeMap[this.size] || 18
    },

    iconColor() {
      return this.disabled ? '#c0c4cc' : '#909399'
    },

    wordCount() {
      return String(this.modelValue || '').length
    }
  },

  methods: {
    handleInput(e) {
      const value = e.detail.value
      this.$emit('update:modelValue', value)
      this.$emit('input', e)
    },

    handleFocus(e) {
      this.$emit('focus', e)
    },

    handleBlur(e) {
      this.$emit('blur', e)
    },

    handleConfirm(e) {
      this.$emit('confirm', e)
    },

    handleClear() {
      this.$emit('update:modelValue', '')
      this.$emit('clear')
    },

    togglePassword() {
      this.passwordVisible = !this.passwordVisible
    },

    handleKeyboardHeightChange(e) {
      this.$emit('keyboardheightchange', e)
    },

    handleLineChange(e) {
      this.$emit('linechange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: $bg-color-white;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  transition: border-color 0.2s;

  &:focus-within {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
  }

  &.input-wrapper--disabled {
    background-color: $bg-color-disabled;
    border-color: $border-color-light;
    cursor: not-allowed;

    .input-control,
    .textarea-control {
      color: $text-color-disabled;
      cursor: not-allowed;
    }
  }

  &.input-wrapper--readonly {
    background-color: $bg-color-page;

    .input-control,
    .textarea-control {
      cursor: default;
    }
  }

  &.input-wrapper--textarea {
    align-items: flex-start;

    .word-limit {
      position: absolute;
      bottom: $spacing-xs;
      right: $spacing-sm;
    }
  }

  // 尺寸
  &.input-wrapper--small {
    min-height: 32px;
    padding: $spacing-xs $spacing-sm;

    .input-control,
    .textarea-control {
      font-size: $font-size-sm;
    }
  }

  &.input-wrapper--medium {
    min-height: 40px;
    padding: $spacing-sm;

    .input-control,
    .textarea-control {
      font-size: $font-size-base;
    }
  }

  &.input-wrapper--large {
    min-height: 48px;
    padding: $spacing-sm $spacing-md;

    .input-control,
    .textarea-control {
      font-size: $font-size-lg;
    }
  }
}

.input-prefix,
.input-suffix {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  color: $text-color-secondary;
}

.input-prefix {
  margin-right: $spacing-xs;
}

.input-suffix {
  margin-left: $spacing-xs;

  .clear-btn,
  .password-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-left: $spacing-xs;
    cursor: pointer;

    &:hover {
      color: $primary-color;
    }
  }
}

.input-control,
.textarea-control {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: $text-color-primary;
  line-height: 1.4;

  &::placeholder {
    color: $text-color-placeholder;
  }
}

.textarea-control {
  min-height: 60px;
  resize: none;
  padding: $spacing-xs 0;
}

.word-limit {
  .word-count {
    font-size: $font-size-sm;
    color: $text-color-secondary;

    &.over-limit {
      color: $error-color;
    }
  }
}
</style>
