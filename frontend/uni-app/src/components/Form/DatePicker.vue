<template>
  <view class="date-picker">
    <view
      class="date-picker-input"
      :class="{
        'is-disabled': disabled,
        'is-placeholder': !displayValue,
        'is-focused': focused
      }"
      @tap="handleTap"
    >
      <text class="date-picker-text">{{ displayValue || placeholder }}</text>
      <text class="date-picker-icon">📅</text>
    </view>

    <picker
      v-if="!disabled"
      mode="date"
      :value="value"
      :start="start"
      :end="end"
      :disabled="disabled"
      class="date-picker-hidden"
      @change="handleChange"
      @cancel="handleCancel"
    >
      <view />
    </picker>
  </view>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'DatePicker',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择日期'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    start: {
      type: String,
      default: '1900-01-01'
    },
    end: {
      type: String,
      default: '2100-12-31'
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD'
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change', 'cancel', 'focus', 'blur'],
  data() {
    return {
      focused: false
    }
  },
  computed: {
    value() {
      return this.modelValue || ''
    },
    displayValue() {
      if (!this.value) return ''
      return this.formatDate(this.value)
    }
  },
  methods: {
    handleTap() {
      if (this.disabled) return
      this.focused = true
      this.$emit('focus')

      // 触发picker显示 - uni-app中需要通过点击picker元素
      this.$nextTick(() => {
        const picker = this.$el.querySelector('.date-picker-hidden picker')
        if (picker) {
          picker.click && picker.click()
        }
      })
    },

    handleChange(e) {
      const { value } = e.detail
      this.$emit('update:modelValue', value)
      this.$emit('change', {
        value,
        formattedValue: this.formatDate(value)
      })
      this.focused = false
      this.$emit('blur')
    },

    handleCancel() {
      this.focused = false
      this.$emit('cancel')
      this.$emit('blur')
    },

    formatDate(dateString) {
      if (!dateString) return ''

      const date = new Date(dateString)
      if (isNaN(date.getTime())) return dateString

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')

      switch (this.format) {
        case 'YYYY-MM-DD':
          return `${year}-${month}-${day}`
        case 'YYYY/MM/DD':
          return `${year}/${month}/${day}`
        case 'MM-DD-YYYY':
          return `${month}-${day}-${year}`
        case 'DD/MM/YYYY':
          return `${day}/${month}/${year}`
        case 'Chinese':
          return `${year}年${month}月${day}日`
        default:
          return `${year}-${month}-${day}`
      }
    },

    clear() {
      if (this.disabled) return
      this.$emit('update:modelValue', '')
      this.$emit('change', { value: '', formattedValue: '' })
    },

    // 验证日期是否在范围内
    isDateInRange(dateString) {
      if (!dateString) return true

      const date = new Date(dateString)
      const startDate = new Date(this.start)
      const endDate = new Date(this.end)

      return date >= startDate && date <= endDate
    }
  }
})
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.date-picker {
  position: relative;
  width: 100%;
}

.date-picker-input {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border: 1px solid $border-color-light;
  border-radius: $border-radius-md;
  min-height: 44px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: $primary-color;
  }

  &.is-focused {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px color.adjust($primary-color, $alpha: -0.8);
  }

  &.is-disabled {
    background-color: $bg-color-light;
    color: $text-color-disabled;
    cursor: not-allowed;

    &:hover {
      border-color: $border-color-light;
    }
  }

  &.is-placeholder {
    .date-picker-text {
      color: $text-color-placeholder;
    }
  }
}

.date-picker-text {
  flex: 1;
  font-size: $font-size-base;
  color: $text-color-primary;

  .is-disabled & {
    color: $text-color-disabled;
  }
}

.date-picker-icon {
  font-size: $font-size-lg;
  color: $text-color-secondary;
  margin-left: $spacing-sm;

  .is-disabled & {
    color: $text-color-disabled;
  }
}

.date-picker-hidden {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  pointer-events: none;

  picker {
    width: 100%;
    height: 100%;
  }
}

// 清除按钮样式
.date-picker-clear {
  position: absolute;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $text-color-secondary;
  color: $bg-color-white;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s;

  .date-picker-input:hover & {
    opacity: 1;
  }

  &:hover {
    background-color: $text-color-primary;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .date-picker-input {
    padding: $spacing-sm $spacing-md;
    min-height: 40px;
  }

  .date-picker-text {
    font-size: $font-size-sm;
  }

  .date-picker-icon {
    font-size: $font-size-base;
  }
}

// 表单验证状态
.date-picker {
  &.is-error {
    .date-picker-input {
      border-color: $error-color;

      &:hover,
      &.is-focused {
        border-color: $error-color;
        box-shadow: 0 0 0 2px color.adjust($error-color, $alpha: -0.8);
      }
    }
  }

  &.is-success {
    .date-picker-input {
      border-color: $success-color;

      &:hover,
      &.is-focused {
        border-color: $success-color;
        box-shadow: 0 0 0 2px color.adjust($success-color, $alpha: -0.8);
      }
    }
  }

  &.is-warning {
    .date-picker-input {
      border-color: $warning-color;

      &:hover,
      &.is-focused {
        border-color: $warning-color;
        box-shadow: 0 0 0 2px color.adjust($warning-color, $alpha: -0.8);
      }
    }
  }
}

// 大小变体
.date-picker {
  &.size-small {
    .date-picker-input {
      padding: $spacing-xs $spacing-sm;
      min-height: 32px;
    }

    .date-picker-text {
      font-size: $font-size-xs;
    }

    .date-picker-icon {
      font-size: $font-size-sm;
    }
  }

  &.size-large {
    .date-picker-input {
      padding: $spacing-lg;
      min-height: 52px;
    }

    .date-picker-text {
      font-size: $font-size-lg;
    }

    .date-picker-icon {
      font-size: $font-size-xl;
    }
  }
}
</style>
