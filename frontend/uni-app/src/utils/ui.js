/**
 * UI 工具函数
 * 用于统一处理各种UI相关的操作
 */

/**
 * 显示消息提示
 * @param {string} title - 提示文本
 * @param {string} icon - 图标类型 success/error/loading/none
 * @param {number} duration - 显示时长(毫秒)
 */
export function showToast(title, icon = 'none', duration = 2000) {
  uni.showToast({
    title,
    icon,
    duration
  })
}

/**
 * 显示成功提示
 * @param {string} title - 提示文本
 */
export function showSuccess(title) {
  showToast(title, 'success')
}

/**
 * 显示错误提示
 * @param {string} title - 提示文本
 */
export function showError(title) {
  showToast(title, 'error')
}

/**
 * 显示加载中
 * @param {string} title - 加载文本
 */
export function showLoading(title = '加载中...') {
  uni.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载中
 */
export function hideLoading() {
  uni.hideLoading()
}

/**
 * 显示确认对话框
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @param {object} options - 其他选项
 * @returns {Promise<boolean>} 是否确认
 */
export function showModal(title, content, options = {}) {
  return new Promise(resolve => {
    uni.showModal({
      title,
      content,
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消',
      ...options,
      success: res => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 显示操作菜单
 * @param {Array<string>} itemList - 菜单项列表
 * @returns {Promise<number>} 选中的索引，-1表示取消
 */
export function showActionSheet(itemList) {
  return new Promise(resolve => {
    uni.showActionSheet({
      itemList,
      success: res => {
        resolve(res.tapIndex)
      },
      fail: () => {
        resolve(-1)
      }
    })
  })
}

/**
 * 页面导航
 * @param {string} url - 页面路径
 * @param {object} options - 导航选项
 */
export function navigateTo(url, options = {}) {
  uni.navigateTo({
    url,
    ...options
  })
}

/**
 * 页面重定向
 * @param {string} url - 页面路径
 * @param {object} options - 导航选项
 */
export function redirectTo(url, options = {}) {
  uni.redirectTo({
    url,
    ...options
  })
}

/**
 * 切换到 tabBar 页面
 * @param {string} url - 页面路径
 * @param {object} options - 导航选项
 */
export function switchTab(url, options = {}) {
  uni.switchTab({
    url,
    ...options
  })
}

/**
 * 返回上一页
 * @param {number} delta - 返回页面数
 */
export function navigateBack(delta = 1) {
  uni.navigateBack({
    delta
  })
}

/**
 * 设置导航栏标题
 * @param {string} title - 标题文本
 */
export function setNavigationBarTitle(title) {
  uni.setNavigationBarTitle({
    title
  })
}

/**
 * 设置导航栏颜色
 * @param {string} frontColor - 前景颜色
 * @param {string} backgroundColor - 背景颜色
 */
export function setNavigationBarColor(frontColor = '#000000', backgroundColor = '#ffffff') {
  uni.setNavigationBarColor({
    frontColor,
    backgroundColor
  })
}

/**
 * 显示导航栏加载动画
 */
export function showNavigationBarLoading() {
  uni.showNavigationBarLoading()
}

/**
 * 隐藏导航栏加载动画
 */
export function hideNavigationBarLoading() {
  uni.hideNavigationBarLoading()
}

/**
 * 开始下拉刷新
 */
export function startPullDownRefresh() {
  uni.startPullDownRefresh()
}

/**
 * 停止下拉刷新
 */
export function stopPullDownRefresh() {
  uni.stopPullDownRefresh()
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间(毫秒)
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 1000) {
  let timer = null
  return function (...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间(毫秒)
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 500) {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * 选择图片
 * @param {object} options - 选择选项
 * @returns {Promise<Array>} 图片文件路径数组
 */
export function chooseImage(options = {}) {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
      ...options,
      success: res => {
        resolve(res.tempFilePaths)
      },
      fail: reject
    })
  })
}

/**
 * 预览图片
 * @param {Array<string>} urls - 图片链接数组
 * @param {number} current - 当前显示图片的索引
 */
export function previewImage(urls, current = 0) {
  uni.previewImage({
    urls,
    current: typeof current === 'number' ? current : urls.indexOf(current)
  })
}

/**
 * 选择文件
 * @param {object} options - 选择选项
 * @returns {Promise<Array>} 文件数组
 */
export function chooseFile(options = {}) {
  return new Promise((resolve, reject) => {
    // #ifdef H5
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = options.extension?.map(ext => `.${ext}`).join(',') || '*'
    input.multiple = options.count > 1

    input.onchange = e => {
      const files = Array.from(e.target.files).map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        path: URL.createObjectURL(file),
        file
      }))
      resolve(files)
    }

    input.click()
    // #endif

    // #ifdef MP-WEIXIN || APP-PLUS
    uni.chooseMessageFile({
      count: 1,
      type: 'file',
      ...options,
      success: res => {
        resolve(res.tempFiles)
      },
      fail: reject
    })
    // #endif
  })
}

/**
 * 复制到剪贴板
 * @param {string} data - 要复制的内容
 * @returns {Promise<void>}
 */
export function setClipboardData(data) {
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data,
      success: () => {
        showSuccess('已复制到剪贴板')
        resolve()
      },
      fail: reject
    })
  })
}

/**
 * 获取剪贴板内容
 * @returns {Promise<string>}
 */
export function getClipboardData() {
  return new Promise((resolve, reject) => {
    uni.getClipboardData({
      success: res => {
        resolve(res.data)
      },
      fail: reject
    })
  })
}

/**
 * 震动反馈
 * @param {string} type - 震动类型 short/medium/long
 */
export function vibrateShort(type = 'short') {
  if (type === 'short') {
    uni.vibrateShort()
  } else {
    uni.vibrateLong()
  }
}

/**
 * 获取系统信息
 * @returns {Promise<object>}
 */
export function getSystemInfo() {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取网络状态
 * @returns {Promise<object>}
 */
export function getNetworkType() {
  return new Promise((resolve, reject) => {
    uni.getNetworkType({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 扫码
 * @param {object} options - 扫码选项
 * @returns {Promise<string>}
 */
export function scanCode(options = {}) {
  return new Promise((resolve, reject) => {
    uni.scanCode({
      onlyFromCamera: false,
      ...options,
      success: res => {
        resolve(res.result)
      },
      fail: reject
    })
  })
}

/**
 * 设置屏幕亮度
 * @param {number} value - 亮度值 0-1
 */
export function setScreenBrightness(value) {
  uni.setScreenBrightness({
    value
  })
}

/**
 * 保持屏幕常亮
 * @param {boolean} keepScreenOn - 是否保持常亮
 */
export function setKeepScreenOn(keepScreenOn = true) {
  uni.setKeepScreenOn({
    keepScreenOn
  })
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {object} options - 下载选项
 * @returns {Promise<object>}
 */
export function downloadFile(url, options = {}) {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url,
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 上传文件
 * @param {string} url - 上传URL
 * @param {string} filePath - 文件路径
 * @param {object} options - 上传选项
 * @returns {Promise<object>}
 */
export function uploadFile(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url,
      filePath,
      name: 'file',
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 保存文件到本地
 * @param {string} tempFilePath - 临时文件路径
 * @returns {Promise<string>}
 */
export function saveFile(tempFilePath) {
  return new Promise((resolve, reject) => {
    uni.saveFile({
      tempFilePath,
      success: res => {
        resolve(res.savedFilePath)
      },
      fail: reject
    })
  })
}

/**
 * RPX转PX
 * @param {number} rpx - rpx值
 * @returns {number} px值
 */
export function rpxToPx(rpx) {
  const systemInfo = uni.getSystemInfoSync()
  return (rpx * systemInfo.windowWidth) / 750
}

/**
 * PX转RPX
 * @param {number} px - px值
 * @returns {number} rpx值
 */
export function pxToRpx(px) {
  const systemInfo = uni.getSystemInfoSync()
  return (px * 750) / systemInfo.windowWidth
}

/**
 * 获取状态栏高度
 * @returns {number} 状态栏高度(px)
 */
export function getStatusBarHeight() {
  const systemInfo = uni.getSystemInfoSync()
  return systemInfo.statusBarHeight || 0
}

/**
 * 获取胶囊按钮位置信息
 * @returns {object} 胶囊按钮位置信息
 */
export function getMenuButtonBoundingClientRect() {
  // #ifdef MP-WEIXIN
  return uni.getMenuButtonBoundingClientRect()
  // #endif

  // #ifndef MP-WEIXIN
  return {
    width: 0,
    height: 0,
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }
  // #endif
}

export default {
  showToast,
  showSuccess,
  showError,
  showLoading,
  hideLoading,
  showModal,
  showActionSheet,
  navigateTo,
  redirectTo,
  switchTab,
  navigateBack,
  setNavigationBarTitle,
  setNavigationBarColor,
  showNavigationBarLoading,
  hideNavigationBarLoading,
  startPullDownRefresh,
  stopPullDownRefresh,
  throttle,
  debounce,
  chooseImage,
  previewImage,
  chooseFile,
  setClipboardData,
  getClipboardData,
  vibrateShort,
  getSystemInfo,
  getNetworkType,
  scanCode,
  setScreenBrightness,
  setKeepScreenOn,
  downloadFile,
  uploadFile,
  saveFile,
  rpxToPx,
  pxToRpx,
  getStatusBarHeight,
  getMenuButtonBoundingClientRect
}
