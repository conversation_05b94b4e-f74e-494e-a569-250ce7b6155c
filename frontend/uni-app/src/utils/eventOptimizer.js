/**
 * 事件监听器优化工具
 * 解决 passive event listener 警告
 */

// 优化触摸事件监听器
export function optimizeEventListeners() {
  // 检查是否支持 passive 事件监听器
  let supportsPassive = false
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      }
    })
    window.addEventListener('testPassive', null, opts)
    window.removeEventListener('testPassive', null, opts)
  } catch (e) {
    // 不支持 passive
  }

  // 如果支持 passive，优化常见的滚动事件
  if (supportsPassive) {
    // 重写原生的 addEventListener 方法
    const originalAddEventListener = EventTarget.prototype.addEventListener

    EventTarget.prototype.addEventListener = function (type, listener, options) {
      // 对于滚动相关事件，默认使用 passive
      const passiveEvents = ['touchstart', 'touchmove', 'touchend', 'wheel', 'scroll']

      if (passiveEvents.includes(type)) {
        if (typeof options === 'boolean') {
          options = { capture: options, passive: true }
        } else if (typeof options === 'object') {
          options = { ...options, passive: options.passive !== false }
        } else {
          options = { passive: true }
        }
      }

      return originalAddEventListener.call(this, type, listener, options)
    }
  }
}

// 为 uni-app 组件添加优化的事件处理
export function addPassiveEventListener(element, event, handler, options = {}) {
  const passiveOptions = {
    ...options,
    passive: true
  }

  element.addEventListener(event, handler, passiveOptions)

  return () => {
    element.removeEventListener(event, handler, passiveOptions)
  }
}

// 优化滚动性能的工具函数
export function throttle(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export function debounce(func, wait, immediate) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}
