import { createStore } from 'vuex'
import user from './modules/user'
import config from './modules/config'
import assessment from './modules/assessment'
import elderly from './modules/elderly'
import scale from './modules/scale'

const store = createStore({
  modules: {
    user,
    config,
    assessment,
    elderly,
    scale
  },

  state: {
    // 全局状态
    loading: false,
    networkStatus: true
  },

  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },

    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = status
    }
  },

  actions: {
    // 设置加载状态
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    },

    // 设置网络状态
    setNetworkStatus({ commit }, status) {
      commit('SET_NETWORK_STATUS', status)
    }
  },

  getters: {
    // 是否正在加载
    isLoading: state => state.loading,

    // 网络是否连接
    isOnline: state => state.networkStatus
  }
})

export default store
