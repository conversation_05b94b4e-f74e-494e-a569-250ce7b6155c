/**
 * 评估量表管理模块
 */

const state = {
  list: [], // 量表列表
  total: 0, // 总数
  current: null, // 当前选中的量表
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0
  },
  loading: false,
  searchParams: {
    keyword: '',
    category: '',
    status: ''
  }
}

const getters = {
  scaleList: state => state.list,
  currentScale: state => state.current,
  scaleTotal: state => state.total,
  scalePagination: state => state.pagination,
  isLoading: state => state.loading,
  searchParams: state => state.searchParams
}

const mutations = {
  SET_SCALE_LIST(state, list) {
    state.list = list
  },

  SET_SCALE_TOTAL(state, total) {
    state.total = total
    state.pagination.total = total
  },

  SET_CURRENT_SCALE(state, scale) {
    state.current = scale
  },

  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },

  SET_LOADING(state, loading) {
    state.loading = loading
  },

  SET_SEARCH_PARAMS(state, params) {
    state.searchParams = { ...state.searchParams, ...params }
  },

  ADD_SCALE(state, scale) {
    state.list.unshift(scale)
    state.total++
  },

  UPDATE_SCALE(state, updatedScale) {
    const index = state.list.findIndex(scale => scale.id === updatedScale.id)
    if (index !== -1) {
      state.list.splice(index, 1, updatedScale)
    }
  },

  REMOVE_SCALE(state, scaleId) {
    const index = state.list.findIndex(scale => scale.id === scaleId)
    if (index !== -1) {
      state.list.splice(index, 1)
      state.total--
    }
  },

  CLEAR_SCALE_LIST(state) {
    state.list = []
    state.total = 0
    state.current = null
  }
}

const actions = {
  // 获取量表列表
  async getScaleList({ commit, state }, params = {}) {
    try {
      commit('SET_LOADING', true)

      const requestParams = {
        page: state.pagination.page,
        pageSize: state.pagination.pageSize,
        ...state.searchParams,
        ...params
      }

      // 模拟API调用，实际应该调用真实API
      const mockData = {
        list: [
          {
            id: 1,
            name: '老年人能力评估量表',
            description: '用于评估老年人基本生活能力',
            category: 'ability',
            status: 'active',
            questionCount: 25,
            createTime: '2024-01-15 10:30:00',
            updateTime: '2024-01-15 10:30:00'
          },
          {
            id: 2,
            name: '认知功能评估量表',
            description: '用于评估老年人认知功能状态',
            category: 'cognitive',
            status: 'active',
            questionCount: 30,
            createTime: '2024-01-16 14:20:00',
            updateTime: '2024-01-16 14:20:00'
          }
        ],
        total: 2,
        page: requestParams.page,
        pageSize: requestParams.pageSize
      }

      commit('SET_SCALE_LIST', mockData.list)
      commit('SET_SCALE_TOTAL', mockData.total)
      commit('SET_PAGINATION', {
        page: mockData.page,
        pageSize: mockData.pageSize
      })

      return mockData
    } catch (error) {
      console.error('获取量表列表失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 获取量表详情
  async getScaleDetail({ commit }, scaleId) {
    try {
      commit('SET_LOADING', true)

      // 模拟API调用
      const mockScale = {
        id: scaleId,
        name: '老年人能力评估量表',
        description: '用于评估老年人基本生活能力',
        category: 'ability',
        status: 'active',
        questionCount: 25,
        questions: [
          {
            id: 1,
            type: 'single_choice',
            content: '您能否独立进行日常洗漱？',
            required: true,
            options: [
              { label: '完全可以', value: 'A', score: 4 },
              { label: '需要少量帮助', value: 'B', score: 3 },
              { label: '需要大量帮助', value: 'C', score: 2 },
              { label: '完全无法完成', value: 'D', score: 1 }
            ]
          }
        ],
        createTime: '2024-01-15 10:30:00',
        updateTime: '2024-01-15 10:30:00'
      }

      commit('SET_CURRENT_SCALE', mockScale)
      return mockScale
    } catch (error) {
      console.error('获取量表详情失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 创建量表
  async createScale({ commit }, scaleData) {
    try {
      commit('SET_LOADING', true)

      // 模拟API调用
      const newScale = {
        id: Date.now(),
        ...scaleData,
        status: 'active',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      commit('ADD_SCALE', newScale)
      return newScale
    } catch (error) {
      console.error('创建量表失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 更新量表
  async updateScale({ commit }, { scaleId, ...scaleData }) {
    try {
      commit('SET_LOADING', true)

      // 模拟API调用
      const updatedScale = {
        id: scaleId,
        ...scaleData,
        updateTime: new Date().toISOString()
      }

      commit('UPDATE_SCALE', updatedScale)
      return updatedScale
    } catch (error) {
      console.error('更新量表失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 删除量表
  async deleteScale({ commit }, scaleId) {
    try {
      commit('SET_LOADING', true)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      commit('REMOVE_SCALE', scaleId)
      return true
    } catch (error) {
      console.error('删除量表失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 搜索量表
  async searchScales({ dispatch, commit }, searchParams) {
    commit('SET_SEARCH_PARAMS', searchParams)
    commit('SET_PAGINATION', { page: 1 })
    return dispatch('getScaleList')
  },

  // 清空量表列表
  clearScaleList({ commit }) {
    commit('CLEAR_SCALE_LIST')
  },

  // 设置当前量表
  setCurrentScale({ commit }, scale) {
    commit('SET_CURRENT_SCALE', scale)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
