const state = {
  // 当前评估信息
  currentAssessment: null,

  // 评估列表
  assessmentList: [],

  // 评估量表列表
  scaleList: [],

  // 当前量表
  currentScale: null,

  // 评估进度
  assessmentProgress: {
    currentSectionIndex: 0,
    currentQuestionIndex: 0,
    totalSections: 0,
    totalQuestions: 0,
    completedQuestions: 0,
    answers: {},
    startTime: null,
    lastSaveTime: null
  },

  // 评估结果
  assessmentResult: null,

  // 评估历史
  assessmentHistory: [],

  // 评估统计
  assessmentStats: {
    totalCount: 0,
    completedCount: 0,
    inProgressCount: 0,
    averageScore: 0,
    averageDuration: 0
  }
}

const mutations = {
  SET_CURRENT_ASSESSMENT(state, assessment) {
    state.currentAssessment = assessment
  },

  SET_ASSESSMENT_LIST(state, list) {
    state.assessmentList = list
  },

  SET_SCALE_LIST(state, list) {
    state.scaleList = list
  },

  SET_CURRENT_SCALE(state, scale) {
    state.currentScale = scale
  },

  SET_ASSESSMENT_PROGRESS(state, progress) {
    state.assessmentProgress = { ...state.assessmentProgress, ...progress }
  },

  SET_ASSESSMENT_RESULT(state, result) {
    state.assessmentResult = result
  },

  SET_ASSESSMENT_HISTORY(state, history) {
    state.assessmentHistory = history
  },

  SET_ASSESSMENT_STATS(state, stats) {
    state.assessmentStats = { ...state.assessmentStats, ...stats }
  },

  // 更新答案
  UPDATE_ANSWER(state, { questionId, answer }) {
    state.assessmentProgress.answers[questionId] = answer

    // 更新完成的题目数量
    state.assessmentProgress.completedQuestions = Object.keys(
      state.assessmentProgress.answers
    ).length

    // 更新最后保存时间
    state.assessmentProgress.lastSaveTime = new Date().toISOString()
  },

  // 更新当前题目索引
  UPDATE_QUESTION_INDEX(state, { sectionIndex, questionIndex }) {
    state.assessmentProgress.currentSectionIndex = sectionIndex
    state.assessmentProgress.currentQuestionIndex = questionIndex
  },

  // 开始评估
  START_ASSESSMENT(state, { scale, elderly }) {
    state.currentScale = scale
    state.assessmentProgress = {
      currentSectionIndex: 0,
      currentQuestionIndex: 0,
      totalSections: scale.sections?.length || 0,
      totalQuestions:
        scale.sections?.reduce((total, section) => total + (section.questions?.length || 0), 0) ||
        0,
      completedQuestions: 0,
      answers: {},
      startTime: new Date().toISOString(),
      lastSaveTime: new Date().toISOString()
    }

    state.currentAssessment = {
      id: null,
      elderlyId: elderly.id,
      elderlyName: elderly.name,
      scaleId: scale.id,
      scaleName: scale.name,
      status: 'in_progress',
      startTime: new Date().toISOString(),
      assessorId: null,
      assessorName: null
    }
  },

  // 重置评估状态
  RESET_ASSESSMENT(state) {
    state.currentAssessment = null
    state.currentScale = null
    state.assessmentProgress = {
      currentSectionIndex: 0,
      currentQuestionIndex: 0,
      totalSections: 0,
      totalQuestions: 0,
      completedQuestions: 0,
      answers: {},
      startTime: null,
      lastSaveTime: null
    }
    state.assessmentResult = null
  },

  // 添加评估记录
  ADD_ASSESSMENT_RECORD(state, assessment) {
    state.assessmentList.unshift(assessment)
  },

  // 更新评估记录
  UPDATE_ASSESSMENT_RECORD(state, assessment) {
    const index = state.assessmentList.findIndex(item => item.id === assessment.id)
    if (index !== -1) {
      state.assessmentList.splice(index, 1, assessment)
    }
  },

  // 删除评估记录
  REMOVE_ASSESSMENT_RECORD(state, assessmentId) {
    const index = state.assessmentList.findIndex(item => item.id === assessmentId)
    if (index !== -1) {
      state.assessmentList.splice(index, 1)
    }
  }
}

const actions = {
  // 获取评估列表
  async getAssessmentList({ commit }, _params = {}) {
    // 这里应该调用API获取评估列表
    // const response = await getAssessmentList(params)
    // commit('SET_ASSESSMENT_LIST', response.data.list)
    // return response

    // 临时模拟数据
    const mockData = {
      list: [],
      total: 0
    }
    commit('SET_ASSESSMENT_LIST', mockData.list)
    return { data: mockData }
  },

  // 获取量表列表
  async getScaleList({ commit }, _params = {}) {
    // 这里应该调用API获取量表列表
    // const response = await getScaleList(params)
    // commit('SET_SCALE_LIST', response.data.list)
    // return response

    // 临时模拟数据
    const mockData = {
      list: [],
      total: 0
    }
    commit('SET_SCALE_LIST', mockData.list)
    return { data: mockData }
  },

  // 获取量表详情
  async getScaleDetail({ commit: _commit }, _scaleId) {
    // 这里应该调用API获取量表详情
    // const response = await getScaleDetail(scaleId)
    // commit('SET_CURRENT_SCALE', response.data)
    // return response

    // 临时返回空数据
    return { data: null }
  },

  // 开始评估
  startAssessment({ commit }, { scale, elderly }) {
    commit('START_ASSESSMENT', { scale, elderly })
  },

  // 保存答案
  async saveAnswer({ commit, state: _state }, { questionId, answer }) {
    commit('UPDATE_ANSWER', { questionId, answer })

    // 自动保存到服务器
    try {
      // 这里应该调用API保存答案
      // await saveAssessmentAnswer({
      //   assessmentId: state.currentAssessment.id,
      //   questionId,
      //   answer
      // })
    } catch (error) {
      console.error('保存答案失败:', error)
    }
  },

  // 下一题
  nextQuestion({ commit, state }) {
    const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
    const currentSection = state.currentScale.sections[currentSectionIndex]

    if (currentQuestionIndex < currentSection.questions.length - 1) {
      // 同一章节的下一题
      commit('UPDATE_QUESTION_INDEX', {
        sectionIndex: currentSectionIndex,
        questionIndex: currentQuestionIndex + 1
      })
    } else if (currentSectionIndex < state.currentScale.sections.length - 1) {
      // 下一章节的第一题
      commit('UPDATE_QUESTION_INDEX', {
        sectionIndex: currentSectionIndex + 1,
        questionIndex: 0
      })
    }
  },

  // 上一题
  previousQuestion({ commit, state }) {
    const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress

    if (currentQuestionIndex > 0) {
      // 同一章节的上一题
      commit('UPDATE_QUESTION_INDEX', {
        sectionIndex: currentSectionIndex,
        questionIndex: currentQuestionIndex - 1
      })
    } else if (currentSectionIndex > 0) {
      // 上一章节的最后一题
      const previousSection = state.currentScale.sections[currentSectionIndex - 1]
      commit('UPDATE_QUESTION_INDEX', {
        sectionIndex: currentSectionIndex - 1,
        questionIndex: previousSection.questions.length - 1
      })
    }
  },

  // 跳转到指定题目
  goToQuestion({ commit }, { sectionIndex, questionIndex }) {
    commit('UPDATE_QUESTION_INDEX', { sectionIndex, questionIndex })
  },

  // 提交评估
  async submitAssessment({ commit, state: _state }) {
    // 这里应该调用API提交评估
    // const response = await submitAssessment({
    //   assessmentId: state.currentAssessment.id,
    //   answers: state.assessmentProgress.answers
    // })
    // commit('SET_ASSESSMENT_RESULT', response.data)
    // return response

    // 临时模拟提交成功
    const mockResult = {
      totalScore: 0,
      sectionScores: [],
      level: 'mild',
      suggestions: []
    }
    commit('SET_ASSESSMENT_RESULT', mockResult)
    return { data: mockResult }
  },

  // 暂停评估
  async pauseAssessment({ commit, state }) {
    // 这里应该调用API暂停评估
    // await pauseAssessment(state.currentAssessment.id)

    // 更新本地状态
    if (state.currentAssessment) {
      const updatedAssessment = {
        ...state.currentAssessment,
        status: 'paused'
      }
      commit('SET_CURRENT_ASSESSMENT', updatedAssessment)
    }
  },

  // 恢复评估
  async resumeAssessment({ commit: _commit }, _assessmentId) {
    // 这里应该调用API恢复评估
    // const response = await resumeAssessment(assessmentId)
    // commit('SET_CURRENT_ASSESSMENT', response.data.assessment)
    // commit('SET_CURRENT_SCALE', response.data.scale)
    // commit('SET_ASSESSMENT_PROGRESS', response.data.progress)
    // return response

    // 临时返回空数据
    return { data: null }
  },

  // 删除评估
  async deleteAssessment({ commit }, assessmentId) {
    // 这里应该调用API删除评估
    // await deleteAssessment(assessmentId)

    commit('REMOVE_ASSESSMENT_RECORD', assessmentId)
  },

  // 获取评估统计
  async getAssessmentStats({ commit }) {
    // 这里应该调用API获取评估统计
    // const response = await getAssessmentStats()
    // commit('SET_ASSESSMENT_STATS', response.data)
    // return response

    // 临时模拟数据
    const mockStats = {
      totalCount: 0,
      completedCount: 0,
      inProgressCount: 0,
      averageScore: 0,
      averageDuration: 0
    }
    commit('SET_ASSESSMENT_STATS', mockStats)
    return { data: mockStats }
  },

  // 重置评估状态
  resetAssessment({ commit }) {
    commit('RESET_ASSESSMENT')
  },

  // 获取评估统计数据（用于首页显示）
  async getAssessmentStatistics({ commit }) {
    try {
      // 临时模拟数据
      const statistics = {
        total: 324,
        completed: 289,
        inProgress: 35,
        averageScore: 78.5
      }

      commit('SET_ASSESSMENT_STATS', {
        totalCount: statistics.total,
        completedCount: statistics.completed,
        inProgressCount: statistics.inProgress,
        averageScore: statistics.averageScore
      })

      return { data: statistics }
    } catch (error) {
      console.error('获取评估统计数据失败:', error)
      const defaultStats = { total: 0, completed: 0, inProgress: 0, averageScore: 0 }
      return { data: defaultStats }
    }
  },

  // 获取最近评估记录
  async getRecentAssessments({ commit }, _params = {}) {
    try {
      // 临时模拟数据
      const recentAssessments = {
        list: [
          {
            id: 1,
            elderlyName: '张三',
            scaleName: '老年人能力评估',
            status: 'completed',
            score: 85,
            createTime: '2024-06-12 10:30:00'
          },
          {
            id: 2,
            elderlyName: '李四',
            scaleName: '情绪快评',
            status: 'in_progress',
            score: null,
            createTime: '2024-06-12 09:15:00'
          },
          {
            id: 3,
            elderlyName: '王五',
            scaleName: 'interRAI评估',
            status: 'completed',
            score: 72,
            createTime: '2024-06-11 16:20:00'
          }
        ],
        total: 3
      }

      commit('SET_ASSESSMENT_LIST', recentAssessments.list)
      return { data: recentAssessments }
    } catch (error) {
      console.error('获取最近评估记录失败:', error)
      const defaultData = { list: [], total: 0 }
      commit('SET_ASSESSMENT_LIST', [])
      return { data: defaultData }
    }
  }
}

const getters = {
  // 当前评估信息
  currentAssessment: state => state.currentAssessment,

  // 当前量表
  currentScale: state => state.currentScale,

  // 评估进度
  assessmentProgress: state => state.assessmentProgress,

  // 评估结果
  assessmentResult: state => state.assessmentResult,

  // 评估列表
  assessmentList: state => state.assessmentList,

  // 量表列表
  scaleList: state => state.scaleList,

  // 评估统计
  assessmentStats: state => state.assessmentStats,

  // 当前题目
  currentQuestion: state => {
    if (!state.currentScale || !state.currentScale.sections) {
      return null
    }

    const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
    const currentSection = state.currentScale.sections[currentSectionIndex]

    if (!currentSection || !currentSection.questions) {
      return null
    }

    return currentSection.questions[currentQuestionIndex]
  },

  // 当前章节
  currentSection: state => {
    if (!state.currentScale || !state.currentScale.sections) {
      return null
    }

    const { currentSectionIndex } = state.assessmentProgress
    return state.currentScale.sections[currentSectionIndex]
  },

  // 评估完成百分比
  completionPercentage: state => {
    const { totalQuestions, completedQuestions } = state.assessmentProgress
    if (totalQuestions === 0) return 0
    return Math.round((completedQuestions / totalQuestions) * 100)
  },

  // 是否为最后一题
  isLastQuestion: state => {
    if (!state.currentScale || !state.currentScale.sections) {
      return false
    }

    const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
    const isLastSection = currentSectionIndex === state.currentScale.sections.length - 1
    const currentSection = state.currentScale.sections[currentSectionIndex]
    const isLastQuestionInSection = currentQuestionIndex === currentSection.questions.length - 1

    return isLastSection && isLastQuestionInSection
  },

  // 是否为第一题
  isFirstQuestion: state => {
    const { currentSectionIndex, currentQuestionIndex } = state.assessmentProgress
    return currentSectionIndex === 0 && currentQuestionIndex === 0
  },

  // 获取指定题目的答案
  getAnswer: state => questionId => {
    return state.assessmentProgress.answers[questionId]
  },

  // 检查是否有未保存的更改
  hasUnsavedChanges: _state => {
    // 这里可以添加逻辑来检查是否有未保存的更改
    return false
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
