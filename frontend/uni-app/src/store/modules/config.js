const state = {
  // API基础URL
  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:8181/api' : '/api',

  // 应用配置
  appConfig: {
    name: '智慧养老评估平台',
    version: '1.0.0',
    description: '专业的养老评估管理系统'
  },

  // 系统设置
  systemSettings: {
    // 主题设置
    theme: 'light', // light, dark

    // 语言设置
    language: 'zh-CN',

    // 字体大小
    fontSize: 'normal', // small, normal, large

    // 是否开启调试模式
    debug: process.env.NODE_ENV === 'development',

    // 请求超时时间（毫秒）
    timeout: 10000,

    // 分页大小
    pageSize: 20,

    // 文件上传限制
    upload: {
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
      allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
    },

    // 缓存设置
    cache: {
      // 缓存过期时间（毫秒）
      expireTime: 30 * 60 * 1000, // 30分钟

      // 最大缓存数量
      maxSize: 100
    }
  },

  // 业务配置
  businessConfig: {
    // 评估相关配置
    assessment: {
      // 自动保存间隔（秒）
      autoSaveInterval: 30,

      // 评估超时时间（分钟）
      timeout: 60,

      // 是否允许暂停评估
      allowPause: true,

      // 是否允许跳过题目
      allowSkip: false
    },

    // 老人管理配置
    elderly: {
      // 照片要求
      photo: {
        required: true,
        maxSize: 2 * 1024 * 1024, // 2MB
        allowedTypes: ['image/jpeg', 'image/png']
      },

      // 信息完整度要求
      requiredFields: ['name', 'gender', 'birthDate', 'idCard', 'phone']
    },

    // 报告配置
    report: {
      // 报告格式
      formats: ['pdf', 'word', 'excel'],

      // 默认格式
      defaultFormat: 'pdf',

      // 是否包含图表
      includeCharts: true,

      // 是否包含建议
      includeSuggestions: true
    }
  },

  // 字典数据
  dictionaries: {
    // 性别
    gender: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' }
    ],

    // 评估状态
    assessmentStatus: [
      { label: '未开始', value: 'not_started', color: '#909399' },
      { label: '进行中', value: 'in_progress', color: '#E6A23C' },
      { label: '已完成', value: 'completed', color: '#67C23A' },
      { label: '已暂停', value: 'paused', color: '#F56C6C' }
    ],

    // 评估等级
    assessmentLevel: [
      { label: '轻度', value: 'mild', color: '#67C23A' },
      { label: '中度', value: 'moderate', color: '#E6A23C' },
      { label: '重度', value: 'severe', color: '#F56C6C' }
    ],

    // 用户角色
    userRoles: [
      { label: '管理员', value: 'admin' },
      { label: '评估师', value: 'assessor' },
      { label: '护理员', value: 'caregiver' },
      { label: '医生', value: 'doctor' }
    ]
  }
}

const mutations = {
  SET_BASE_URL(state, url) {
    state.baseURL = url
  },

  SET_SYSTEM_SETTING(state, { key, value }) {
    state.systemSettings[key] = value
    // 持久化存储
    uni.setStorageSync('systemSettings', state.systemSettings)
  },

  SET_BUSINESS_CONFIG(state, { module, key, value }) {
    if (state.businessConfig[module]) {
      state.businessConfig[module][key] = value
      // 持久化存储
      uni.setStorageSync('businessConfig', state.businessConfig)
    }
  },

  SET_DICTIONARY(state, { key, data }) {
    state.dictionaries[key] = data
  },

  INIT_CONFIG(state) {
    // 从本地存储恢复配置
    const savedSystemSettings = uni.getStorageSync('systemSettings')
    if (savedSystemSettings) {
      state.systemSettings = { ...state.systemSettings, ...savedSystemSettings }
    }

    const savedBusinessConfig = uni.getStorageSync('businessConfig')
    if (savedBusinessConfig) {
      state.businessConfig = { ...state.businessConfig, ...savedBusinessConfig }
    }
  }
}

const actions = {
  // 初始化配置
  initConfig({ commit }) {
    commit('INIT_CONFIG')
  },

  // 更新系统设置
  updateSystemSetting({ commit }, { key, value }) {
    commit('SET_SYSTEM_SETTING', { key, value })
  },

  // 更新业务配置
  updateBusinessConfig({ commit }, { module, key, value }) {
    commit('SET_BUSINESS_CONFIG', { module, key, value })
  },

  // 更新字典数据
  updateDictionary({ commit }, { key, data }) {
    commit('SET_DICTIONARY', { key, data })
  },

  // 重置配置
  resetConfig({ commit }) {
    uni.removeStorageSync('systemSettings')
    uni.removeStorageSync('businessConfig')
    commit('INIT_CONFIG')
  },

  // 切换主题
  toggleTheme({ commit, state }) {
    const newTheme = state.systemSettings.theme === 'light' ? 'dark' : 'light'
    commit('SET_SYSTEM_SETTING', { key: 'theme', value: newTheme })
  },

  // 设置语言
  setLanguage({ commit }, language) {
    commit('SET_SYSTEM_SETTING', { key: 'language', value: language })
  },

  // 设置字体大小
  setFontSize({ commit }, fontSize) {
    commit('SET_SYSTEM_SETTING', { key: 'fontSize', value: fontSize })
  }
}

const getters = {
  // 获取API基础URL
  apiBaseURL: state => state.baseURL,

  // 获取应用信息
  appInfo: state => state.appConfig,

  // 获取系统设置
  systemSettings: state => state.systemSettings,

  // 获取业务配置
  businessConfig: state => state.businessConfig,

  // 获取字典数据
  dictionaries: state => state.dictionaries,

  // 获取指定字典
  getDictionary: state => key => state.dictionaries[key] || [],

  // 获取字典标签
  getDictLabel: state => (dictKey, value) => {
    const dict = state.dictionaries[dictKey]
    if (!dict) return value
    const item = dict.find(item => item.value === value)
    return item ? item.label : value
  },

  // 获取字典颜色
  getDictColor: state => (dictKey, value) => {
    const dict = state.dictionaries[dictKey]
    if (!dict) return ''
    const item = dict.find(item => item.value === value)
    return item ? item.color : ''
  },

  // 是否为调试模式
  isDebug: state => state.systemSettings.debug,

  // 获取当前主题
  currentTheme: state => state.systemSettings.theme,

  // 获取当前语言
  currentLanguage: state => state.systemSettings.language,

  // 获取当前字体大小
  currentFontSize: state => state.systemSettings.fontSize,

  // 获取请求超时时间
  requestTimeout: state => state.systemSettings.timeout,

  // 获取分页大小
  pageSize: state => state.systemSettings.pageSize,

  // 获取上传配置
  uploadConfig: state => state.systemSettings.upload,

  // 获取缓存配置
  cacheConfig: state => state.systemSettings.cache
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
