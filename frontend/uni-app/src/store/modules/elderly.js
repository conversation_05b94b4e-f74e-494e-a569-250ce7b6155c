const state = {
  // 老人列表
  elderlyList: [],

  // 当前选中的老人
  currentElderly: null,

  // 老人详情
  elderlyDetail: null,

  // 老人统计信息
  elderlyStats: {
    totalCount: 0,
    maleCount: 0,
    femaleCount: 0,
    averageAge: 0,
    ageDistribution: {
      '60-70': 0,
      '70-80': 0,
      '80-90': 0,
      '90+': 0
    },
    assessmentStats: {
      totalAssessments: 0,
      completedAssessments: 0,
      averageScore: 0
    }
  },

  // 搜索条件
  searchParams: {
    keyword: '',
    gender: '',
    ageRange: '',
    status: '',
    sortBy: 'createTime',
    sortOrder: 'desc'
  },

  // 分页信息
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  }
}

const mutations = {
  SET_ELDERLY_LIST(state, list) {
    state.elderlyList = list
  },

  SET_CURRENT_ELDERLY(state, elderly) {
    state.currentElderly = elderly
  },

  SET_ELDERLY_DETAIL(state, detail) {
    state.elderlyDetail = detail
  },

  SET_ELDERLY_STATS(state, stats) {
    state.elderlyStats = { ...state.elderlyStats, ...stats }
  },

  SET_SEARCH_PARAMS(state, params) {
    state.searchParams = { ...state.searchParams, ...params }
  },

  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },

  // 添加老人
  ADD_ELDERLY(state, elderly) {
    state.elderlyList.unshift(elderly)
    state.pagination.total += 1
  },

  // 更新老人信息
  UPDATE_ELDERLY(state, elderly) {
    const index = state.elderlyList.findIndex(item => item.id === elderly.id)
    if (index !== -1) {
      state.elderlyList.splice(index, 1, elderly)
    }

    // 如果是当前选中的老人，也要更新
    if (state.currentElderly && state.currentElderly.id === elderly.id) {
      state.currentElderly = elderly
    }

    // 如果是详情页的老人，也要更新
    if (state.elderlyDetail && state.elderlyDetail.id === elderly.id) {
      state.elderlyDetail = elderly
    }
  },

  // 删除老人
  REMOVE_ELDERLY(state, elderlyId) {
    const index = state.elderlyList.findIndex(item => item.id === elderlyId)
    if (index !== -1) {
      state.elderlyList.splice(index, 1)
      state.pagination.total -= 1
    }

    // 如果删除的是当前选中的老人，清空选中状态
    if (state.currentElderly && state.currentElderly.id === elderlyId) {
      state.currentElderly = null
    }

    // 如果删除的是详情页的老人，清空详情
    if (state.elderlyDetail && state.elderlyDetail.id === elderlyId) {
      state.elderlyDetail = null
    }
  },

  // 重置搜索条件
  RESET_SEARCH_PARAMS(state) {
    state.searchParams = {
      keyword: '',
      gender: '',
      ageRange: '',
      status: '',
      sortBy: 'createTime',
      sortOrder: 'desc'
    }
  },

  // 重置分页
  RESET_PAGINATION(state) {
    state.pagination = {
      current: 1,
      pageSize: 20,
      total: 0
    }
  }
}

const actions = {
  // 获取老人列表
  async getElderlyList({ commit, state }, params = {}) {
    const _searchParams = { ...state.searchParams, ...params }
    const pagination = { ...state.pagination, ...params }

    // 这里应该调用API获取老人列表
    // const response = await getElderlyList({
    //   ...searchParams,
    //   page: pagination.current,
    //   pageSize: pagination.pageSize
    // })
    //
    // commit('SET_ELDERLY_LIST', response.data.list)
    // commit('SET_PAGINATION', {
    //   current: response.data.current,
    //   pageSize: response.data.pageSize,
    //   total: response.data.total
    // })
    // return response

    // 临时模拟数据
    const mockData = {
      list: [],
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: 0
    }

    commit('SET_ELDERLY_LIST', mockData.list)
    commit('SET_PAGINATION', {
      current: mockData.current,
      pageSize: mockData.pageSize,
      total: mockData.total
    })

    return { data: mockData }
  },

  // 获取老人详情
  async getElderlyDetail({ commit }, _elderlyId) {
    // 这里应该调用API获取老人详情
    // const response = await getElderlyDetail(elderlyId)
    // commit('SET_ELDERLY_DETAIL', response.data)
    // return response

    // 临时返回空数据
    commit('SET_ELDERLY_DETAIL', null)
    return { data: null }
  },

  // 创建老人
  async createElderly({ commit }, elderlyData) {
    // 这里应该调用API创建老人
    // const response = await createElderly(elderlyData)
    // commit('ADD_ELDERLY', response.data)
    // return response

    // 临时模拟创建成功
    const mockElderly = {
      id: Date.now(),
      ...elderlyData,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    commit('ADD_ELDERLY', mockElderly)
    return { data: mockElderly }
  },

  // 更新老人信息
  async updateElderly({ commit }, { id, ...elderlyData }) {
    // 这里应该调用API更新老人信息
    // const response = await updateElderly(elderlyId, elderlyData)
    // commit('UPDATE_ELDERLY', response.data)
    // return response

    // 临时模拟更新成功
    const mockElderly = {
      id,
      ...elderlyData,
      updateTime: new Date().toISOString()
    }

    commit('UPDATE_ELDERLY', mockElderly)
    return { data: mockElderly }
  },

  // 删除老人
  async deleteElderly({ commit }, elderlyId) {
    // 这里应该调用API删除老人
    // await deleteElderly(elderlyId)

    commit('REMOVE_ELDERLY', elderlyId)
  },

  // 批量删除老人
  async batchDeleteElderly({ commit }, elderlyIds) {
    // 这里应该调用API批量删除老人
    // await batchDeleteElderly(elderlyIds)

    elderlyIds.forEach(id => {
      commit('REMOVE_ELDERLY', id)
    })
  },

  // 导入老人数据
  async importElderlyData({ dispatch }, _file) {
    // 这里应该调用API导入老人数据
    // const response = await importElderlyData(file)
    //
    // // 导入成功后刷新列表
    // await dispatch('getElderlyList')
    // return response

    // 临时模拟导入成功
    await dispatch('getElderlyList')
    return { data: { successCount: 0, failCount: 0, errors: [] } }
  },

  // 导出老人数据
  async exportElderlyData({ state: _state }, _params = {}) {
    // 这里应该调用API导出老人数据
    // const response = await exportElderlyData({
    //   ...state.searchParams,
    //   ...params
    // })
    // return response

    // 临时返回空数据
    return { data: null }
  },

  // 获取老人统计信息
  async getElderlyStats({ commit }) {
    // 这里应该调用API获取老人统计信息
    // const response = await getElderlyStats()
    // commit('SET_ELDERLY_STATS', response.data)
    // return response

    // 临时模拟数据
    const mockStats = {
      totalCount: 0,
      maleCount: 0,
      femaleCount: 0,
      averageAge: 0,
      ageDistribution: {
        '60-70': 0,
        '70-80': 0,
        '80-90': 0,
        '90+': 0
      },
      assessmentStats: {
        totalAssessments: 0,
        completedAssessments: 0,
        averageScore: 0
      }
    }

    commit('SET_ELDERLY_STATS', mockStats)
    return { data: mockStats }
  },

  // 搜索老人
  async searchElderly({ commit, dispatch }, searchParams) {
    commit('SET_SEARCH_PARAMS', searchParams)
    commit('RESET_PAGINATION')
    return await dispatch('getElderlyList')
  },

  // 设置当前老人
  setCurrentElderly({ commit }, elderly) {
    commit('SET_CURRENT_ELDERLY', elderly)
  },

  // 清空当前老人
  clearCurrentElderly({ commit }) {
    commit('SET_CURRENT_ELDERLY', null)
  },

  // 重置搜索条件
  resetSearchParams({ commit }) {
    commit('RESET_SEARCH_PARAMS')
    commit('RESET_PAGINATION')
  },

  // 获取老人的评估历史
  async getElderlyAssessmentHistory({ commit: _commit }, _elderlyId) {
    // 这里应该调用API获取老人的评估历史
    // const response = await getElderlyAssessmentHistory(elderlyId)
    // return response

    // 临时返回空数据
    return { data: { list: [], total: 0 } }
  },

  // 获取老人的最新评估结果
  async getElderlyLatestAssessment({ commit: _commit }, _elderlyId) {
    // 这里应该调用API获取老人的最新评估结果
    // const response = await getElderlyLatestAssessment(elderlyId)
    // return response

    // 临时返回空数据
    return { data: null }
  },

  // 获取老人统计数据（用于首页显示）
  async getElderlyStatistics({ commit }) {
    try {
      // 临时模拟数据
      const statistics = {
        total: 156,
        activeCount: 89,
        newCount: 12
      }

      commit('SET_ELDERLY_STATS', {
        totalCount: statistics.total,
        activeCount: statistics.activeCount,
        newCount: statistics.newCount
      })

      return { data: statistics }
    } catch (error) {
      console.error('获取老人统计数据失败:', error)
      const defaultStats = { total: 0, activeCount: 0, newCount: 0 }
      return { data: defaultStats }
    }
  }
}

const getters = {
  // 老人列表
  elderlyList: state => state.elderlyList,

  // 当前选中的老人
  currentElderly: state => state.currentElderly,

  // 老人详情
  elderlyDetail: state => state.elderlyDetail,

  // 老人统计信息
  elderlyStats: state => state.elderlyStats,

  // 搜索条件
  searchParams: state => state.searchParams,

  // 分页信息
  pagination: state => state.pagination,

  // 根据ID获取老人信息
  getElderlyById: state => id => {
    return state.elderlyList.find(elderly => elderly.id === id)
  },

  // 获取男性老人数量
  maleElderlyCount: state => {
    return state.elderlyList.filter(elderly => elderly.gender === 'male').length
  },

  // 获取女性老人数量
  femaleElderlyCount: state => {
    return state.elderlyList.filter(elderly => elderly.gender === 'female').length
  },

  // 获取平均年龄
  averageAge: state => {
    if (state.elderlyList.length === 0) return 0

    const totalAge = state.elderlyList.reduce((sum, elderly) => {
      const age = elderly.age || 0
      return sum + age
    }, 0)

    return Math.round(totalAge / state.elderlyList.length)
  },

  // 获取年龄分布
  ageDistribution: state => {
    const distribution = {
      '60-70': 0,
      '70-80': 0,
      '80-90': 0,
      '90+': 0
    }

    state.elderlyList.forEach(elderly => {
      const age = elderly.age || 0
      if (age >= 60 && age < 70) {
        distribution['60-70']++
      } else if (age >= 70 && age < 80) {
        distribution['70-80']++
      } else if (age >= 80 && age < 90) {
        distribution['80-90']++
      } else if (age >= 90) {
        distribution['90+']++
      }
    })

    return distribution
  },

  // 检查是否有搜索条件
  hasSearchParams: state => {
    const { keyword, gender, ageRange, status } = state.searchParams
    return !!(keyword || gender || ageRange || status)
  },

  // 获取搜索结果数量
  searchResultCount: state => {
    return state.pagination.total
  },

  // 检查是否为空列表
  isEmptyList: state => {
    return state.elderlyList.length === 0
  },

  // 检查是否正在加载
  isLoading: (state, getters, rootState) => {
    return rootState.loading
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
