// 颜色变量
$primary-color: #007AFF;
$primary-color-dark: #0056B3;
$primary-color-light: #CCE5FF;
$secondary-color: #5856D6;
$success-color: #34C759;
$warning-color: #FF9500;
$danger-color: #FF3B30;
$error-color: #FF3B30;
$error-color-light: #FFE5E5;
$info-color: #5AC8FA;
$color-white: #FFFFFF;

// 文本颜色
$text-color: #333333;
$text-color-primary: #333333;
$text-color-secondary: #666666;
$text-color-regular: #606266;
$text-color-light: #666666;
$text-color-lighter: #999999;
$text-color-white: #FFFFFF;
$text-color-placeholder: #C0C4CC;
$text-color-disabled: #C0C4CC;

// 背景颜色
$bg-color: #F8F8F8;
$bg-color-white: #FFFFFF;
$bg-color-light: #FAFAFA;
$bg-color-dark: #F0F0F0;
$bg-color-page: #F5F5F5;
$bg-color-hover: #F8F8F8;
$bg-color-active: #E8E8E8;
$bg-color-disabled: #F5F5F5;

// 边框颜色
$border-color: #E5E5E5;
$border-color-base: #E5E5E5;
$border-color-light: #F0F0F0;
$border-color-lighter: #F5F5F5;
$border-color-dark: #CCCCCC;

// 字体
$font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-md: 30rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 42rpx;
$font-size-2xl: 40rpx;
$font-size-3xl: 48rpx;

// 行高
$line-height-base: 1.6;
$line-height-sm: 1.4;
$line-height-lg: 1.8;

// 间距
$spacing-xs: 10rpx;
$spacing-sm: 20rpx;
$spacing-md: 30rpx;
$spacing-base: 30rpx;
$spacing-lg: 40rpx;
$spacing-xl: 50rpx;
$spacing-xxl: 60rpx;

// 圆角
$border-radius-xs: 2rpx;
$border-radius-sm: 4rpx;
$border-radius-small: 6rpx;
$border-radius-base: 8rpx;
$border-radius-md: 10rpx;
$border-radius-lg: 12rpx;
$border-radius-xl: 16rpx;
$border-radius-full: 50%;

// 阴影
$box-shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
$box-shadow-xl: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
$box-shadow-hover: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
$box-shadow-light: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);

// 层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 动画时间
$transition-duration-fast: 0.15s;
$transition-duration-base: 0.3s;
$transition-duration-slow: 0.5s;

// 动画函数
$transition-timing-function: ease-in-out;

// 屏幕断点
$breakpoint-sm: 576rpx;
$breakpoint-md: 768rpx;
$breakpoint-lg: 992rpx;
$breakpoint-xl: 1200rpx;

// 容器最大宽度
$container-max-width: 750rpx;

// 表单相关
$input-height: 80rpx;
$input-padding: 20rpx;
$input-border-color: $border-color;
$input-border-radius: $border-radius-base;
$input-bg-color: $bg-color-white;
$input-placeholder-color: $text-color-lighter;

// 按钮相关
$button-height: 80rpx;
$button-padding: 0 30rpx;
$button-border-radius: $border-radius-base;
$button-font-size: $font-size-base;

// 卡片相关
$card-padding: 30rpx;
$card-border-radius: $border-radius-lg;
$card-bg-color: $bg-color-white;
$card-shadow: $box-shadow-base;

// 导航栏相关
$navbar-height: 88rpx;
$navbar-bg-color: $bg-color-white;
$navbar-border-color: $border-color;

// 标签栏相关
$tabbar-height: 100rpx;
$tabbar-bg-color: $bg-color-white;
$tabbar-border-color: $border-color;

// 列表相关
$list-item-height: 100rpx;
$list-item-padding: 30rpx;
$list-item-border-color: $border-color-light;

// 加载相关
$loading-color: $primary-color;
$loading-size: 40rpx;

// 空状态相关
$empty-color: $text-color-lighter;
$empty-size: 200rpx;

// 颜色别名 (兼容性支持)
$color-primary: $primary-color;
$color-primary-light: $primary-color-light;
$color-success: $success-color;
$color-success-light: #f6ffed;
$color-warning: $warning-color;
$color-warning-light: #fff7e6;
$color-danger: $danger-color;
$color-danger-light: $error-color-light;

// 轻度颜色变体
$success-color-light: #f6ffed;
$warning-color-light: #fff7e6;
$danger-color-light: $error-color-light;
$info-color-light: #e6f7ff;

// 额外的颜色变体 (用于各种组件)
$primary-color-disabled: #b3d9ff;
$success-color-disabled: #b8e6c1;
$warning-color-disabled: #ffd699;
$danger-color-disabled: #ffb3b3;
$info-color-disabled: #b3e5fc;

// 评估相关颜色
$assessment-color-excellent: #52c41a;
$assessment-color-good: #1890ff;
$assessment-color-average: #faad14;
$assessment-color-below: #ff7875;
$assessment-color-poor: #f5222d;

// 状态颜色
$status-active: #52c41a;
$status-inactive: #d9d9d9;
$status-pending: #faad14;
$status-completed: #1890ff;
$status-cancelled: #ff4d4f;

// 评分颜色
$score-excellent: #52c41a;  // 90-100分
$score-good: #1890ff;       // 80-89分
$score-average: #faad14;    // 70-79分
$score-below: #ff7875;      // 60-69分
$score-poor: #f5222d;       // 60分以下

// 图表颜色
$chart-color-1: #1890ff;
$chart-color-2: #52c41a;
$chart-color-3: #faad14;
$chart-color-4: #722ed1;
$chart-color-5: #fa541c;
$chart-color-6: #13c2c2;
$chart-color-7: #eb2f96;
$chart-color-8: #fa8c16;