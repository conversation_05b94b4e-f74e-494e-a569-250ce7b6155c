@use './variables.scss' as *;

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 居中对齐
@mixin center($type: both) {
  position: absolute;
  @if $type == both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $type == horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $type == vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

// Flex布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// Flex居中
@mixin flex-center {
  @include flex(row, center, center);
}

// Flex两端对齐
@mixin flex-between {
  @include flex(row, space-between, center);
}

// Flex环绕对齐
@mixin flex-around {
  @include flex(row, space-around, center);
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  } @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  } @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  } @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// 按钮样式
@mixin button-variant($color, $bg-color, $border-color: $bg-color) {
  color: $color;
  background-color: $bg-color;
  border-color: $border-color;
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    opacity: 0.9;
  }
  
  &.disabled,
  &[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card($padding: $card-padding, $radius: $card-border-radius, $shadow: $card-shadow) {
  background-color: $card-bg-color;
  border-radius: $radius;
  padding: $padding;
  box-shadow: $shadow;
}

// 输入框样式
@mixin input-variant($border-color: $input-border-color, $focus-color: $primary-color) {
  height: $input-height;
  padding: 0 $input-padding;
  background-color: $input-bg-color;
  border: 1rpx solid $border-color;
  border-radius: $input-border-radius;
  font-size: $font-size-base;
  color: $text-color;
  
  &::placeholder {
    color: $input-placeholder-color;
  }
  
  &:focus {
    border-color: $focus-color;
    outline: none;
  }
  
  &.disabled,
  &[disabled] {
    background-color: $bg-color-dark;
    color: $text-color-lighter;
    cursor: not-allowed;
  }
}

// 动画过渡
@mixin transition($property: all, $duration: $transition-duration-base, $timing: $transition-timing-function) {
  transition: $property $duration $timing;
}

// 阴影
@mixin box-shadow($shadow: $box-shadow-base) {
  box-shadow: $shadow;
}

// 边框
@mixin border($width: 1rpx, $style: solid, $color: $border-color) {
  border: $width $style $color;
}

// 圆角
@mixin border-radius($radius: $border-radius-base) {
  border-radius: $radius;
}

// 背景渐变
@mixin gradient($start-color, $end-color, $direction: to bottom) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 文字渐变
@mixin text-gradient($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 滚动条样式
@mixin scrollbar($width: 8rpx, $track-color: transparent, $thumb-color: rgba(0, 0, 0, 0.3)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calc($width / 2);
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

// 1px边框解决方案
@mixin hairline($direction: all, $color: $border-color, $radius: 0) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    
    @if $direction == all {
      border: 1px solid $color;
      border-radius: $radius * 2;
    } @else if $direction == top {
      border-top: 1px solid $color;
    } @else if $direction == right {
      border-right: 1px solid $color;
    } @else if $direction == bottom {
      border-bottom: 1px solid $color;
    } @else if $direction == left {
      border-left: 1px solid $color;
    } @else if $direction == horizontal {
      border-left: 1px solid $color;
      border-right: 1px solid $color;
    } @else if $direction == vertical {
      border-top: 1px solid $color;
      border-bottom: 1px solid $color;
    }
  }
}

// 安全区域适配
@mixin safe-area($property: padding, $direction: bottom) {
  #{$property}-#{$direction}: constant(safe-area-inset-#{$direction});
  #{$property}-#{$direction}: env(safe-area-inset-#{$direction});
}

// 动画关键帧定义
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideInUp {
  0% {
    transform: translateY(30rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  0% {
    transform: translateY(-30rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-30rpx);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(30rpx);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// 加载旋转动画
@mixin loading-spin($size: 32rpx, $color: $primary-color) {
  display: inline-block;
  width: $size;
  height: $size;
  border: 2rpx solid transparent;
  border-top: 2rpx solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 脉冲动画
@mixin pulse($scale: 1.05, $duration: 1s) {
  animation: pulse $duration ease-in-out infinite alternate;
}

// 淡入动画
@mixin fade-in($duration: $transition-duration-base) {
  animation: fadeIn $duration ease-in;
}

// 滑入动画
@mixin slide-in($direction: up, $distance: 30rpx, $duration: $transition-duration-base) {
  @if $direction == up {
    animation: slideInUp $duration ease-out;
  } @else if $direction == down {
    animation: slideInDown $duration ease-out;
  } @else if $direction == left {
    animation: slideInLeft $duration ease-out;
  } @else if $direction == right {
    animation: slideInRight $duration ease-out;
  }
}