<template>
  <view id="app">
    <!-- 应用根组件 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch() {
    // 应用启动时的初始化逻辑
    this.initApp()
  },
  onShow() {
    // 应用显示时的逻辑
  },
  onHide() {
    // 应用隐藏时的逻辑
  },
  methods: {
    // 应用初始化
    initApp() {
      // 检查登录状态
      this.checkLoginStatus()

      // 初始化全局配置
      this.initGlobalConfig()
    },

    // 检查登录状态
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      if (token) {
        // 验证token有效性
        this.$store.dispatch('user/validateToken')
      }
    },

    // 初始化全局配置
    initGlobalConfig() {
      // 设置全局请求拦截器
      uni.addInterceptor('request', {
        invoke(args) {
          // 添加token
          const token = uni.getStorageSync('token')
          if (token) {
            args.header = args.header || {}
            args.header.Authorization = `Bearer ${token}`
          }

          // 添加基础URL
          if (!args.url.startsWith('http')) {
            args.url = this.$store.state.config.baseURL + args.url
          }
        },
        success(res) {
          // 统一处理响应
          if (res.statusCode === 401) {
            // token过期，跳转登录
            this.$store.dispatch('user/logout')
            uni.reLaunch({
              url: '/pages/login/index'
            })
          }
        },
        fail(err) {
          console.error('请求失败:', err)
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
      })

      // 设置全局上传拦截器
      uni.addInterceptor('uploadFile', {
        invoke(args) {
          // 添加token
          const token = uni.getStorageSync('token')
          if (token) {
            args.header = args.header || {}
            args.header.Authorization = `Bearer ${token}`
          }

          // 添加基础URL
          if (!args.url.startsWith('http')) {
            args.url = this.$store.state.config.baseURL + args.url
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 全局样式 */
@use './common/styles/variables.scss' as *;
@use './common/styles/mixins.scss' as *;

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: $bg-color;
  font-family: $font-family;
  font-size: $font-size-base;
  color: $text-color;
  line-height: 1.6;
}

/* 通用类 */
.container {
  padding: 20rpx;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

/* 边距类 */
.m-0 {
  margin: 0;
}
.m-1 {
  margin: 10rpx;
}
.m-2 {
  margin: 20rpx;
}
.m-3 {
  margin: 30rpx;
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: 10rpx;
}
.mt-2 {
  margin-top: 20rpx;
}
.mt-3 {
  margin-top: 30rpx;
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: 10rpx;
}
.mb-2 {
  margin-bottom: 20rpx;
}
.mb-3 {
  margin-bottom: 30rpx;
}

.ml-0 {
  margin-left: 0;
}
.ml-1 {
  margin-left: 10rpx;
}
.ml-2 {
  margin-left: 20rpx;
}
.ml-3 {
  margin-left: 30rpx;
}

.mr-0 {
  margin-right: 0;
}
.mr-1 {
  margin-right: 10rpx;
}
.mr-2 {
  margin-right: 20rpx;
}
.mr-3 {
  margin-right: 30rpx;
}

.p-0 {
  padding: 0;
}
.p-1 {
  padding: 10rpx;
}
.p-2 {
  padding: 20rpx;
}
.p-3 {
  padding: 30rpx;
}

.pt-0 {
  padding-top: 0;
}
.pt-1 {
  padding-top: 10rpx;
}
.pt-2 {
  padding-top: 20rpx;
}
.pt-3 {
  padding-top: 30rpx;
}

.pb-0 {
  padding-bottom: 0;
}
.pb-1 {
  padding-bottom: 10rpx;
}
.pb-2 {
  padding-bottom: 20rpx;
}
.pb-3 {
  padding-bottom: 30rpx;
}

.pl-0 {
  padding-left: 0;
}
.pl-1 {
  padding-left: 10rpx;
}
.pl-2 {
  padding-left: 20rpx;
}
.pl-3 {
  padding-left: 30rpx;
}

.pr-0 {
  padding-right: 0;
}
.pr-1 {
  padding-right: 10rpx;
}
.pr-2 {
  padding-right: 20rpx;
}
.pr-3 {
  padding-right: 30rpx;
}

/* 颜色类 */
.text-primary {
  color: $primary-color;
}
.text-success {
  color: $success-color;
}
.text-warning {
  color: $warning-color;
}
.text-danger {
  color: $danger-color;
}
.text-info {
  color: $info-color;
}
.text-muted {
  color: $text-color-light;
}

.bg-primary {
  background-color: $primary-color;
}
.bg-success {
  background-color: $success-color;
}
.bg-warning {
  background-color: $warning-color;
}
.bg-danger {
  background-color: $danger-color;
}
.bg-info {
  background-color: $info-color;
}
.bg-light {
  background-color: $bg-color-light;
}

/* 字体大小类 */
.text-xs {
  font-size: 20rpx;
}
.text-sm {
  font-size: 24rpx;
}
.text-base {
  font-size: 28rpx;
}
.text-lg {
  font-size: 32rpx;
}
.text-xl {
  font-size: 36rpx;
}
.text-2xl {
  font-size: 40rpx;
}

/* 字体粗细类 */
.font-normal {
  font-weight: normal;
}
.font-bold {
  font-weight: bold;
}

/* 圆角类 */
.rounded {
  border-radius: 8rpx;
}
.rounded-lg {
  border-radius: 12rpx;
}
.rounded-full {
  border-radius: 50%;
}

/* 阴影类 */
.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 边框类 */
.border {
  border: 1rpx solid $border-color;
}
.border-t {
  border-top: 1rpx solid $border-color;
}
.border-b {
  border-bottom: 1rpx solid $border-color;
}
.border-l {
  border-left: 1rpx solid $border-color;
}
.border-r {
  border-right: 1rpx solid $border-color;
}

/* 显示隐藏类 */
.hidden {
  display: none;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}

/* 位置类 */
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.sticky {
  position: sticky;
}

/* 溢出类 */
.overflow-hidden {
  overflow: hidden;
}
.overflow-auto {
  overflow: auto;
}
.overflow-scroll {
  overflow: scroll;
}

/* 文本省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
