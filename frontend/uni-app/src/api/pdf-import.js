import { request } from '@/utils/request'

/**
 * PDF导入相关API
 */

/**
 * 上传单个PDF文件
 * @param {File} file PDF文件
 * @param {boolean} preview 是否预览模式
 * @returns {Promise}
 */
export function uploadPDF(file, preview = false) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('preview', preview)

  return request({
    url: '/api/pdf-import/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时
  })
}

/**
 * 批量上传PDF文件
 * @param {File[]} files PDF文件数组
 * @param {boolean} preview 是否预览模式
 * @returns {Promise}
 */
export function batchUploadPDF(files, preview = false) {
  const formData = new FormData()

  // 添加所有文件
  files.forEach(file => {
    formData.append('files', file)
  })
  formData.append('preview', preview)

  return request({
    url: '/api/pdf-import/batch-upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 600000 // 10分钟超时
  })
}

/**
 * 解析PDF内容（仅解析，不保存）
 * @param {File} file PDF文件
 * @returns {Promise}
 */
export function parsePDF(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/api/pdf-import/parse',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 180000 // 3分钟超时
  })
}

/**
 * 获取支持的文件格式信息
 * @returns {Promise}
 */
export function getSupportedFormats() {
  return request({
    url: '/api/pdf-import/supported-formats',
    method: 'GET'
  })
}

/**
 * 获取PDF导入历史记录
 * @param {number} page 页码
 * @param {number} size 每页大小
 * @returns {Promise}
 */
export function getImportHistory(page = 0, size = 20) {
  return request({
    url: '/api/pdf-import/history',
    method: 'GET',
    params: {
      page,
      size
    }
  })
}

/**
 * 验证PDF文件
 * @param {File} file 文件对象
 * @returns {Object} 验证结果
 */
export function validatePDFFile(file) {
  const result = {
    valid: true,
    errors: []
  }

  // 检查文件是否存在
  if (!file) {
    result.valid = false
    result.errors.push('请选择文件')
    return result
  }

  // 检查文件类型
  if (file.type !== 'application/pdf') {
    result.valid = false
    result.errors.push('只支持PDF文件格式')
  }

  // 检查文件扩展名
  const fileName = file.name || ''
  if (!fileName.toLowerCase().endsWith('.pdf')) {
    result.valid = false
    result.errors.push('文件扩展名必须是.pdf')
  }

  // 检查文件大小（50MB限制）
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (file.size > maxSize) {
    result.valid = false
    result.errors.push('文件大小不能超过50MB')
  }

  // 检查文件是否为空
  if (file.size === 0) {
    result.valid = false
    result.errors.push('文件不能为空')
  }

  return result
}

/**
 * 批量验证PDF文件
 * @param {File[]} files 文件数组
 * @returns {Object} 验证结果
 */
export function validatePDFFiles(files) {
  const result = {
    valid: true,
    validFiles: [],
    invalidFiles: [],
    errors: []
  }

  // 检查文件数量
  if (!files || files.length === 0) {
    result.valid = false
    result.errors.push('请选择至少一个文件')
    return result
  }

  // 检查文件数量限制
  const maxFiles = 10
  if (files.length > maxFiles) {
    result.valid = false
    result.errors.push(`最多只能选择${maxFiles}个文件`)
    return result
  }

  // 逐个验证文件
  files.forEach((file, index) => {
    const validation = validatePDFFile(file)

    if (validation.valid) {
      result.validFiles.push({
        index,
        file,
        name: file.name,
        size: file.size
      })
    } else {
      result.invalidFiles.push({
        index,
        file,
        name: file.name,
        errors: validation.errors
      })

      // 添加到总错误列表
      validation.errors.forEach(error => {
        result.errors.push(`文件"${file.name}": ${error}`)
      })
    }
  })

  // 如果有无效文件，整体验证失败
  if (result.invalidFiles.length > 0) {
    result.valid = false
  }

  return result
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 扩展名
 */
export function getFileExtension(filename) {
  if (!filename) return ''

  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1) return ''

  return filename.substring(lastDotIndex + 1).toLowerCase()
}

/**
 * 检查是否为PDF文件
 * @param {File} file 文件对象
 * @returns {boolean} 是否为PDF文件
 */
export function isPDFFile(file) {
  if (!file) return false

  // 检查MIME类型
  if (file.type === 'application/pdf') return true

  // 检查文件扩展名
  const extension = getFileExtension(file.name)
  return extension === 'pdf'
}

/**
 * 创建文件预览信息
 * @param {File} file 文件对象
 * @returns {Object} 预览信息
 */
export function createFilePreview(file) {
  return {
    name: file.name,
    size: file.size,
    sizeFormatted: formatFileSize(file.size),
    type: file.type,
    extension: getFileExtension(file.name),
    lastModified: file.lastModified,
    lastModifiedDate: new Date(file.lastModified),
    isPDF: isPDFFile(file)
  }
}

/**
 * 处理上传进度
 * @param {Function} onProgress 进度回调函数
 * @returns {Function} 配置函数
 */
export function createUploadProgressHandler(onProgress) {
  return progressEvent => {
    if (progressEvent.lengthComputable && onProgress) {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      onProgress(percentCompleted)
    }
  }
}

/**
 * PDF导入状态常量
 */
export const PDF_IMPORT_STATUS = {
  PENDING: 'pending',
  UPLOADING: 'uploading',
  PARSING: 'parsing',
  SUCCESS: 'success',
  ERROR: 'error'
}

/**
 * PDF导入错误类型
 */
export const PDF_IMPORT_ERRORS = {
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FORMAT: 'INVALID_FORMAT',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  PARSE_FAILED: 'PARSE_FAILED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_ERROR: 'SERVER_ERROR'
}

/**
 * 获取错误信息
 * @param {string} errorType 错误类型
 * @returns {string} 错误信息
 */
export function getErrorMessage(errorType) {
  const messages = {
    [PDF_IMPORT_ERRORS.FILE_TOO_LARGE]: '文件大小超过限制',
    [PDF_IMPORT_ERRORS.INVALID_FORMAT]: '不支持的文件格式',
    [PDF_IMPORT_ERRORS.UPLOAD_FAILED]: '文件上传失败',
    [PDF_IMPORT_ERRORS.PARSE_FAILED]: 'PDF解析失败',
    [PDF_IMPORT_ERRORS.NETWORK_ERROR]: '网络连接错误',
    [PDF_IMPORT_ERRORS.SERVER_ERROR]: '服务器错误'
  }

  return messages[errorType] || '未知错误'
}

export default {
  uploadPDF,
  batchUploadPDF,
  parsePDF,
  getSupportedFormats,
  getImportHistory,
  validatePDFFile,
  validatePDFFiles,
  formatFileSize,
  getFileExtension,
  isPDFFile,
  createFilePreview,
  createUploadProgressHandler,
  PDF_IMPORT_STATUS,
  PDF_IMPORT_ERRORS,
  getErrorMessage
}
