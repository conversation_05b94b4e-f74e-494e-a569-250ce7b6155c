import { request } from '@/utils/request'

// 获取老年人列表
export function getElderlyList(params) {
  return request({
    url: '/api/elderly/list',
    method: 'GET',
    params
  })
}

// 获取老年人详情
export function getElderlyDetail(id) {
  return request({
    url: `/api/elderly/${id}`,
    method: 'GET'
  })
}

// 创建老年人信息
export function createElderly(data) {
  return request({
    url: '/api/elderly',
    method: 'POST',
    data
  })
}

// 更新老年人信息
export function updateElderly(id, data) {
  return request({
    url: `/api/elderly/${id}`,
    method: 'PUT',
    data
  })
}

// 删除老年人信息
export function deleteElderly(id) {
  return request({
    url: `/api/elderly/${id}`,
    method: 'DELETE'
  })
}

// 获取老年人统计信息
export function getElderlyStatistics() {
  return request({
    url: '/api/elderly/statistics',
    method: 'GET'
  })
}
