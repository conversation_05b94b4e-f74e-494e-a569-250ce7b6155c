import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import './common/styles/index.scss'
import { optimizeEventListeners } from './utils/eventOptimizer'

export function createApp() {
  const app = createSSRApp(App)

  // 优化事件监听器性能
  if (typeof window !== 'undefined') {
    optimizeEventListeners()
  }

  // 使用store
  app.use(store)

  // 全局配置
  app.config.globalProperties.$store = store

  return {
    app
  }
}
