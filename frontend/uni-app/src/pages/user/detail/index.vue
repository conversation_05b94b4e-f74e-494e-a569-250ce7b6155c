<template>
  <PageContainer :title="userDetail.name || '用户详情'" :show-back="true">
    <Loading v-if="loading" type="spinner" text="加载中..." />

    <Empty
      v-else-if="!userDetail.id"
      type="error"
      description="用户不存在或已被删除"
      :show-button="true"
      button-text="返回列表"
      @button-click="goBack"
    />

    <view v-else class="user-detail">
      <!-- 用户基本信息 -->
      <Card title="基本信息" class="basic-info-card">
        <view class="user-profile">
          <view class="user-avatar">
            <image v-if="userDetail.avatar" :src="userDetail.avatar" mode="aspectFill" />
            <view v-else class="avatar-placeholder">
              <text>{{ userDetail.name ? userDetail.name.charAt(0) : 'U' }}</text>
            </view>
            <view class="status-indicator" :class="userDetail.status" />
          </view>

          <view class="user-info">
            <view class="user-header">
              <text class="user-name">{{ userDetail.name }}</text>
              <view class="user-status">
                <text :class="['status-badge', userDetail.status]">{{
                  formatUserStatus(userDetail.status)
                }}</text>
              </view>
            </view>

            <view class="user-meta">
              <text class="user-role">{{ formatUserRole(userDetail.role) }}</text>
              <text class="user-id">工号：{{ userDetail.employeeId || '未设置' }}</text>
            </view>
          </view>
        </view>

        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">手机号码</text>
            <text class="info-value">{{ userDetail.phone || '未设置' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">邮箱地址</text>
            <text class="info-value">{{ userDetail.email || '未设置' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">所属部门</text>
            <text class="info-value">{{ userDetail.department || '未设置' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">职位</text>
            <text class="info-value">{{ userDetail.position || '未设置' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">创建时间</text>
            <text class="info-value">{{ formatDateTime(userDetail.createdAt) }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">最后登录</text>
            <text class="info-value">{{
              userDetail.lastLoginAt ? formatDateTime(userDetail.lastLoginAt) : '从未登录'
            }}</text>
          </view>
        </view>
      </Card>

      <!-- 权限信息 -->
      <Card title="权限信息" class="permission-card">
        <view class="permission-content">
          <view class="permission-item">
            <text class="permission-label">用户角色</text>
            <text class="permission-value">{{ formatUserRole(userDetail.role) }}</text>
          </view>

          <view
            v-if="userDetail.permissions && userDetail.permissions.length > 0"
            class="permission-item"
          >
            <text class="permission-label">特殊权限</text>
            <view class="permission-tags">
              <text
                v-for="permission in userDetail.permissions"
                :key="permission"
                class="permission-tag"
              >
                {{ formatPermission(permission) }}
              </text>
            </view>
          </view>

          <view class="permission-item">
            <text class="permission-label">数据权限</text>
            <text class="permission-value">{{ formatDataScope(userDetail.dataScope) }}</text>
          </view>
        </view>
      </Card>

      <!-- 统计信息 -->
      <Card title="使用统计" class="stats-card">
        <Loading v-if="statsLoading" type="spinner" size="small" />

        <view v-else class="stats-content">
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-value">{{ userStats.totalAssessments || 0 }}</text>
              <text class="stat-label">总评估次数</text>
            </view>

            <view class="stat-item">
              <text class="stat-value">{{ userStats.monthlyAssessments || 0 }}</text>
              <text class="stat-label">本月评估</text>
            </view>

            <view class="stat-item">
              <text class="stat-value">{{ userStats.managedElderly || 0 }}</text>
              <text class="stat-label">管理老人</text>
            </view>

            <view class="stat-item">
              <text class="stat-value">{{ userStats.loginDays || 0 }}</text>
              <text class="stat-label">登录天数</text>
            </view>
          </view>

          <view class="recent-activity">
            <text class="activity-title">最近活动</text>

            <view
              v-if="userStats.recentActivities && userStats.recentActivities.length > 0"
              class="activity-list"
            >
              <view
                v-for="activity in userStats.recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <view class="activity-icon">
                  <text>{{ getActivityIcon(activity.type) }}</text>
                </view>
                <view class="activity-content">
                  <text class="activity-text">{{ activity.description }}</text>
                  <text class="activity-time">{{ formatDateTime(activity.createdAt) }}</text>
                </view>
              </view>
            </view>

            <view v-else class="no-activity">
              <text>暂无活动记录</text>
            </view>
          </view>
        </view>
      </Card>

      <!-- 操作记录 -->
      <Card title="操作记录" class="logs-card">
        <view class="logs-header">
          <text class="logs-count">最近 {{ userLogs.length }} 条记录</text>
          <Button type="text" size="small" @click="viewAllLogs"> 查看全部 </Button>
        </view>

        <Loading v-if="logsLoading" type="spinner" size="small" />

        <view v-else-if="userLogs.length === 0" class="no-logs">
          <text>暂无操作记录</text>
        </view>

        <view v-else class="logs-list">
          <view v-for="log in userLogs" :key="log.id" class="log-item">
            <view class="log-content">
              <text class="log-action">{{ log.action }}</text>
              <text class="log-description">{{ log.description }}</text>
              <text class="log-time">{{ formatDateTime(log.createdAt) }}</text>
            </view>

            <view v-if="log.result" class="log-result">
              <text :class="['result-badge', log.result]">{{ formatLogResult(log.result) }}</text>
            </view>
          </view>
        </view>
      </Card>
    </view>

    <!-- 底部操作按钮 -->
    <view v-if="userDetail.id" class="bottom-actions">
      <Button type="default" @click="editUser"> 编辑用户 </Button>

      <Button type="primary" @click="showMoreActions"> 更多操作 </Button>
    </view>

    <!-- 操作菜单 -->
    <ActionSheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="handleActionSelect"
    />
  </PageContainer>
</template>

<script>
import { mapActions } from 'vuex'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import ActionSheet from '@/components/Common/ActionSheet.vue'

export default {
  name: 'UserDetail',

  components: {
    PageContainer,
    Card,
    Button,
    Loading,
    Empty,
    ActionSheet
  },

  data() {
    return {
      loading: false,
      statsLoading: false,
      logsLoading: false,
      userId: '',
      userDetail: {},
      userStats: {},
      userLogs: [],

      showActionSheet: false,
      actionSheetActions: []
    }
  },

  onLoad(options) {
    if (options.id) {
      this.userId = options.id
      this.loadUserData()
    }
  },

  onPullDownRefresh() {
    this.loadUserData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    ...mapActions('user', [
      'getUserDetail',
      'getUserStats',
      'getUserLogs',
      'updateUserStatus',
      'resetUserPassword',
      'deleteUser'
    ]),

    async loadUserData() {
      try {
        this.loading = true

        // 并行加载用户详情、统计信息和操作记录
        const [detailResult] = await Promise.all([
          this.getUserDetail(this.userId),
          this.loadUserStats(),
          this.loadUserLogs()
        ])

        this.userDetail = detailResult
      } catch (error) {
        console.error('加载用户数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    async loadUserStats() {
      try {
        this.statsLoading = true
        this.userStats = await this.getUserStats(this.userId)
      } catch (error) {
        console.error('加载用户统计失败:', error)
      } finally {
        this.statsLoading = false
      }
    },

    async loadUserLogs() {
      try {
        this.logsLoading = true
        const result = await this.getUserLogs({ userId: this.userId, limit: 10 })
        this.userLogs = result.list || []
      } catch (error) {
        console.error('加载操作记录失败:', error)
      } finally {
        this.logsLoading = false
      }
    },

    formatUserStatus(status) {
      const statusMap = {
        active: '正常',
        disabled: '禁用',
        pending: '待激活'
      }
      return statusMap[status] || status
    },

    formatUserRole(role) {
      const roleMap = {
        admin: '系统管理员',
        assessor: '评估师',
        caregiver: '护理员',
        doctor: '医生',
        user: '普通用户'
      }
      return roleMap[role] || role
    },

    formatPermission(permission) {
      const permissionMap = {
        user_manage: '用户管理',
        scale_manage: '量表管理',
        data_export: '数据导出',
        system_config: '系统配置'
      }
      return permissionMap[permission] || permission
    },

    formatDataScope(scope) {
      const scopeMap = {
        all: '全部数据',
        department: '本部门数据',
        self: '个人数据'
      }
      return scopeMap[scope] || '未设置'
    },

    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },

    getActivityIcon(type) {
      const iconMap = {
        login: '⚿',
        assessment: '☰',
        elderly_add: '⚭',
        scale_create: '✎',
        data_export: '◰'
      }
      return iconMap[type] || '◉'
    },

    formatLogResult(result) {
      const resultMap = {
        success: '成功',
        failed: '失败',
        warning: '警告'
      }
      return resultMap[result] || result
    },

    editUser() {
      uni.navigateTo({
        url: `/pages/user/create/index?id=${this.userId}`
      })
    },

    viewAllLogs() {
      uni.navigateTo({
        url: `/pages/user/logs/index?userId=${this.userId}`
      })
    },

    showMoreActions() {
      this.actionSheetActions = [
        {
          text: '编辑用户',
          value: 'edit'
        }
      ]

      // 根据用户状态添加不同操作
      if (this.userDetail.status === 'active') {
        this.actionSheetActions.push({
          text: '禁用用户',
          value: 'disable',
          type: 'warn'
        })
      } else if (this.userDetail.status === 'disabled') {
        this.actionSheetActions.push({
          text: '启用用户',
          value: 'enable'
        })
      }

      this.actionSheetActions.push(
        {
          text: '重置密码',
          value: 'resetPassword'
        },
        {
          text: '查看操作记录',
          value: 'viewLogs'
        },
        {
          text: '删除用户',
          value: 'delete',
          type: 'danger'
        }
      )

      this.showActionSheet = true
    },

    async handleActionSelect(action) {
      switch (action.value) {
        case 'edit':
          this.editUser()
          break

        case 'disable':
          await this.handleUpdateUserStatus('disabled')
          break

        case 'enable':
          await this.handleUpdateUserStatus('active')
          break

        case 'resetPassword':
          await this.handleResetPassword()
          break

        case 'viewLogs':
          this.viewAllLogs()
          break

        case 'delete':
          await this.handleDeleteUser()
          break
      }
    },

    async handleUpdateUserStatus(status) {
      const statusText = status === 'active' ? '启用' : '禁用'

      uni.showModal({
        title: '确认操作',
        content: `确定要${statusText}该用户吗？`,
        success: async res => {
          if (res.confirm) {
            try {
              await this.updateUserStatus({ userId: this.userId, status })

              uni.showToast({
                title: `${statusText}成功`,
                icon: 'success'
              })

              // 重新加载用户数据
              this.loadUserData()
            } catch (error) {
              console.error(`${statusText}用户失败:`, error)
              uni.showToast({
                title: `${statusText}失败`,
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleResetPassword() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置该用户的密码吗？重置后将发送新密码到用户手机。',
        success: async res => {
          if (res.confirm) {
            try {
              await this.resetUserPassword(this.userId)

              uni.showToast({
                title: '密码重置成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('重置密码失败:', error)
              uni.showToast({
                title: '重置失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleDeleteUser() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该用户吗？删除后无法恢复。',
        success: async res => {
          if (res.confirm) {
            try {
              await this.deleteUser(this.userId)

              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 返回用户列表
              setTimeout(() => {
                uni.navigateBack()
              }, 1500)
            } catch (error) {
              console.error('删除用户失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.user-detail {
  padding: $spacing-md;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.basic-info-card,
.permission-card,
.stats-card,
.logs-card {
  margin-bottom: $spacing-md;
}

.user-profile {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;

  .user-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    flex-shrink: 0;

    image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $color-primary;
      color: white;
      border-radius: 50%;
      font-size: $font-size-xl;
      font-weight: 600;
    }

    .status-indicator {
      position: absolute;
      bottom: 4px;
      right: 4px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid white;

      &.active {
        background-color: $color-success;
      }

      &.disabled {
        background-color: $color-danger;
      }

      &.pending {
        background-color: $color-warning;
      }
    }
  }

  .user-info {
    flex: 1;

    .user-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-sm;

      .user-name {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-color-primary;
      }

      .user-status {
        .status-badge {
          font-size: $font-size-xs;
          padding: 4px 8px;
          border-radius: $border-radius-xs;

          &.active {
            background-color: $color-success-light;
            color: $color-success;
          }

          &.disabled {
            background-color: $color-danger-light;
            color: $color-danger;
          }

          &.pending {
            background-color: $color-warning-light;
            color: $color-warning;
          }
        }
      }
    }

    .user-meta {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;

      .user-role {
        font-size: $font-size-base;
        color: $color-primary;
        font-weight: 500;
      }

      .user-id {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-md;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;

    .info-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }

    .info-value {
      font-size: $font-size-base;
      color: $text-color-primary;
      font-weight: 500;
    }
  }
}

.permission-content {
  .permission-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-md;

    &:last-child {
      margin-bottom: 0;
    }

    .permission-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      min-width: 80px;
    }

    .permission-value {
      font-size: $font-size-base;
      color: $text-color-primary;
      font-weight: 500;
    }

    .permission-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;

      .permission-tag {
        font-size: $font-size-xs;
        padding: 2px 6px;
        background-color: $color-primary-light;
        color: $color-primary;
        border-radius: $border-radius-xs;
      }
    }
  }
}

.stats-content {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-md;
    margin-bottom: $spacing-lg;

    .stat-item {
      text-align: center;
      padding: $spacing-md;
      background-color: $bg-color-light;
      border-radius: $border-radius-sm;

      .stat-value {
        display: block;
        font-size: $font-size-xxl;
        font-weight: 600;
        color: $color-primary;
        margin-bottom: $spacing-xs;
      }

      .stat-label {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
  }

  .recent-activity {
    .activity-title {
      font-size: $font-size-base;
      font-weight: 600;
      color: $text-color-primary;
      margin-bottom: $spacing-md;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
    }

    .activity-item {
      display: flex;
      gap: $spacing-sm;
      padding: $spacing-sm;
      background-color: $bg-color-light;
      border-radius: $border-radius-sm;

      .activity-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $color-primary-light;
        border-radius: 50%;
        flex-shrink: 0;

        text {
          font-size: $font-size-base;
        }
      }

      .activity-content {
        flex: 1;

        .activity-text {
          font-size: $font-size-base;
          color: $text-color-primary;
          display: block;
          margin-bottom: $spacing-xs;
        }

        .activity-time {
          font-size: $font-size-xs;
          color: $text-color-secondary;
        }
      }
    }

    .no-activity {
      text-align: center;
      padding: $spacing-lg;
      color: $text-color-secondary;
    }
  }
}

.logs-card {
  .logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .logs-count {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }

  .no-logs {
    text-align: center;
    padding: $spacing-lg;
    color: $text-color-secondary;
  }

  .logs-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  .log-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: $spacing-sm;
    background-color: $bg-color-light;
    border-radius: $border-radius-sm;

    .log-content {
      flex: 1;

      .log-action {
        font-size: $font-size-base;
        font-weight: 500;
        color: $text-color-primary;
        display: block;
        margin-bottom: $spacing-xs;
      }

      .log-description {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        display: block;
        margin-bottom: $spacing-xs;
      }

      .log-time {
        font-size: $font-size-xs;
        color: $text-color-placeholder;
      }
    }

    .log-result {
      .result-badge {
        font-size: $font-size-xs;
        padding: 2px 6px;
        border-radius: $border-radius-xs;

        &.success {
          background-color: $color-success-light;
          color: $color-success;
        }

        &.failed {
          background-color: $color-danger-light;
          color: $color-danger;
        }

        &.warning {
          background-color: $color-warning-light;
          color: $color-warning;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;
}

// 响应式设计
@media (max-width: 768px) {
  .user-profile {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .permission-item {
    flex-direction: column;
    gap: $spacing-xs;
  }

  .bottom-actions {
    flex-direction: column;
  }
}
</style>
