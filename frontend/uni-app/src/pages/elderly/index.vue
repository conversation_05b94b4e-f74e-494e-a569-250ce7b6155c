<template>
  <PageContainer title="老年人管理">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <Input
          v-model="searchParams.keyword"
          placeholder="搜索姓名、身份证号"
          prefix-icon="search"
          clearable
          @input="handleSearch"
          @clear="handleClearSearch"
        />
        <Button type="primary" size="medium" @click="showFilterModal = true"> 筛选 </Button>
      </view>

      <!-- 筛选标签 -->
      <view v-if="hasActiveFilters" class="filter-tags">
        <view
          v-for="(tag, index) in activeFilterTags"
          :key="index"
          class="filter-tag"
          @click="removeFilter(tag.key)"
        >
          <text>{{ tag.label }}</text>
          <text class="icon-text" style="font-size: 14px; color: #999">✕</text>
        </view>
        <text class="clear-all" @click="clearAllFilters">清空筛选</text>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="action-left">
        <text class="total-count">共 {{ pagination.total }} 人</text>
      </view>
      <view class="action-right">
        <Button type="default" size="small" @click="handleExport"> 导出 </Button>
        <Button type="default" size="small" @click="handleImport"> 导入 </Button>
        <Button type="primary" size="small" @click="navigateTo('/pages/elderly/create/index')">
          新增
        </Button>
      </view>
    </view>

    <!-- 列表内容 -->
    <view class="list-section">
      <Loading v-if="loading" type="spinner" text="加载中..." />

      <Card v-else-if="elderlyList.length > 0">
        <List>
          <ListItem
            v-for="elderly in elderlyList"
            :key="elderly.id"
            :title="elderly.name"
            :description="getElderlyDescription(elderly)"
            :show-arrow="true"
            @click="handleElderlyClick(elderly)"
          >
            <template #prefix>
              <view class="elderly-avatar">
                <text>{{ elderly.name.charAt(0) }}</text>
              </view>
            </template>

            <template #suffix>
              <view class="elderly-actions">
                <Button type="primary" size="mini" @click.stop="startAssessment(elderly)">
                  评估
                </Button>
                <Button type="default" size="mini" @click.stop="showActionSheet(elderly)">
                  更多
                </Button>
              </view>
            </template>
          </ListItem>
        </List>
      </Card>

      <Empty
        v-else
        type="nodata"
        description="暂无老年人数据"
        button-text="新增老年人"
        @button-click="navigateTo('/pages/elderly/create/index')"
      />
    </view>

    <!-- 分页 -->
    <view v-if="elderlyList.length > 0" class="pagination-section">
      <view class="pagination-info">
        <text>第 {{ pagination.current }} 页，共 {{ totalPages }} 页</text>
      </view>
      <view class="pagination-actions">
        <Button
          type="default"
          size="small"
          :disabled="pagination.current <= 1"
          @click="changePage(pagination.current - 1)"
        >
          上一页
        </Button>
        <Button
          type="default"
          size="small"
          :disabled="pagination.current >= totalPages"
          @click="changePage(pagination.current + 1)"
        >
          下一页
        </Button>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <Modal
      v-model:visible="showFilterModal"
      title="筛选条件"
      :show-cancel="false"
      position="bottom"
    >
      <view class="filter-modal">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <text class="icon-text" style="font-size: 20px" @click="closeFilterModal">✕</text>
        </view>

        <view class="filter-content">
          <!-- 性别筛选 -->
          <view class="filter-group">
            <text class="filter-label">性别</text>
            <view class="filter-options">
              <view
                v-for="option in genderOptions"
                :key="option.value"
                class="filter-option"
                :class="{ active: tempFilters.gender === option.value }"
                @click="tempFilters.gender = option.value"
              >
                <text>{{ option.label }}</text>
              </view>
            </view>
          </view>

          <!-- 年龄筛选 -->
          <view class="filter-group">
            <text class="filter-label">年龄段</text>
            <view class="filter-options">
              <view
                v-for="option in ageOptions"
                :key="option.value"
                class="filter-option"
                :class="{ active: tempFilters.ageRange === option.value }"
                @click="tempFilters.ageRange = option.value"
              >
                <text>{{ option.label }}</text>
              </view>
            </view>
          </view>

          <!-- 护理等级筛选 -->
          <view class="filter-group">
            <text class="filter-label">护理等级</text>
            <view class="filter-options">
              <view
                v-for="option in careLevelOptions"
                :key="option.value"
                class="filter-option"
                :class="{ active: tempFilters.careLevel === option.value }"
                @click="tempFilters.careLevel = option.value"
              >
                <text>{{ option.label }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="filter-footer">
          <Button type="default" @click="resetFilters"> 重置 </Button>
          <Button type="primary" @click="applyFilters"> 确定 </Button>
        </view>
      </view>
    </Modal>
  </PageContainer>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import List from '@/components/Common/List.vue'
import ListItem from '@/components/Common/ListItem.vue'
import Empty from '@/components/Common/Empty.vue'
import Loading from '@/components/Common/Loading.vue'
import Input from '@/components/Form/Input.vue'
import Button from '@/components/Common/Button.vue'
import Modal from '@/components/Common/Modal.vue'
import { debounce } from '@/utils'

export default {
  name: 'ElderlyIndex',

  components: {
    PageContainer,
    Card,
    List,
    ListItem,
    Empty,
    Loading,
    Input,
    Button,
    Modal
  },

  data() {
    return {
      loading: false,
      showFilterModal: false,

      searchParams: {
        keyword: '',
        gender: '',
        ageRange: '',
        careLevel: ''
      },

      tempFilters: {
        gender: '',
        ageRange: '',
        careLevel: ''
      },

      genderOptions: [
        { label: '全部', value: '' },
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],

      ageOptions: [
        { label: '全部', value: '' },
        { label: '60-70岁', value: '60-70' },
        { label: '70-80岁', value: '70-80' },
        { label: '80-90岁', value: '80-90' },
        { label: '90岁以上', value: '90+' }
      ],

      careLevelOptions: [
        { label: '全部', value: '' },
        { label: '自理', value: 'independent' },
        { label: '轻度依赖', value: 'mild' },
        { label: '中度依赖', value: 'moderate' },
        { label: '重度依赖', value: 'severe' }
      ]
    }
  },

  computed: {
    ...mapState('elderly', ['elderlyList', 'pagination']),

    totalPages() {
      return Math.ceil(this.pagination.total / this.pagination.pageSize)
    },

    hasActiveFilters() {
      return this.searchParams.gender || this.searchParams.ageRange || this.searchParams.careLevel
    },

    activeFilterTags() {
      const tags = []

      if (this.searchParams.gender) {
        const option = this.genderOptions.find(opt => opt.value === this.searchParams.gender)
        tags.push({ key: 'gender', label: `性别: ${option?.label}` })
      }

      if (this.searchParams.ageRange) {
        const option = this.ageOptions.find(opt => opt.value === this.searchParams.ageRange)
        tags.push({ key: 'ageRange', label: `年龄: ${option?.label}` })
      }

      if (this.searchParams.careLevel) {
        const option = this.careLevelOptions.find(opt => opt.value === this.searchParams.careLevel)
        tags.push({ key: 'careLevel', label: `护理等级: ${option?.label}` })
      }

      return tags
    }
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadElderlyList()
  },

  onPullDownRefresh() {
    this.loadElderlyList().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.pagination.current < this.totalPages) {
      this.loadMore()
    }
  },

  watch: {
    showFilterModal(val) {
      if (val) {
        this.tempFilters = { ...this.searchParams }
      }
    }
  },

  methods: {
    ...mapActions('elderly', [
      'getElderlyList',
      'deleteElderly',
      'exportElderlyData',
      'importElderlyData'
    ]),

    initPage() {
      this.loadElderlyList()
    },

    async loadElderlyList(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.loading = true
        }

        const params = {
          ...this.searchParams,
          page: isLoadMore ? this.pagination.current + 1 : 1,
          pageSize: this.pagination.pageSize
        }

        await this.getElderlyList(params)
      } catch (error) {
        console.error('加载老年人列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    async loadMore() {
      await this.loadElderlyList(true)
    },

    handleSearch: debounce(function () {
      this.loadElderlyList()
    }, 500),

    handleClearSearch() {
      this.searchParams.keyword = ''
      this.loadElderlyList()
    },

    removeFilter(key) {
      this.searchParams[key] = ''
      this.loadElderlyList()
    },

    clearAllFilters() {
      this.searchParams.gender = ''
      this.searchParams.ageRange = ''
      this.searchParams.careLevel = ''
      this.loadElderlyList()
    },

    closeFilterModal() {
      this.showFilterModal = false
    },

    resetFilters() {
      this.tempFilters = {
        gender: '',
        ageRange: '',
        careLevel: ''
      }
    },

    applyFilters() {
      this.searchParams.gender = this.tempFilters.gender
      this.searchParams.ageRange = this.tempFilters.ageRange
      this.searchParams.careLevel = this.tempFilters.careLevel
      this.closeFilterModal()
      this.loadElderlyList()
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.pagination.current = page
        this.loadElderlyList()
      }
    },

    handleElderlyClick(elderly) {
      this.navigateTo(`/pages/elderly/detail/index?id=${elderly.id}`)
    },

    startAssessment(elderly) {
      this.navigateTo(`/pages/assessment/create/index?elderlyId=${elderly.id}`)
    },

    showActionSheet(elderly) {
      uni.showActionSheet({
        itemList: ['查看详情', '编辑信息', '评估历史', '删除'],
        success: res => {
          switch (res.tapIndex) {
            case 0:
              this.navigateTo(`/pages/elderly/detail/index?id=${elderly.id}`)
              break
            case 1:
              this.navigateTo(`/pages/elderly/edit/index?id=${elderly.id}`)
              break
            case 2:
              this.navigateTo(`/pages/assessment/records/index?elderlyId=${elderly.id}`)
              break
            case 3:
              this.confirmDelete(elderly)
              break
          }
        }
      })
    },

    confirmDelete(elderly) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除 ${elderly.name} 的信息吗？此操作不可恢复。`,
        success: async res => {
          if (res.confirm) {
            try {
              await this.deleteElderly(elderly.id)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.loadElderlyList()
            } catch (error) {
              console.error('删除失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleExport() {
      try {
        uni.showLoading({ title: '导出中...' })
        await this.exportElderlyData(this.searchParams)
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('导出失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    handleImport() {
      this.navigateTo('/pages/elderly/import/index')
    },

    navigateTo(url) {
      uni.navigateTo({ url })
    },

    getElderlyDescription(elderly) {
      const parts = []

      if (elderly.gender) {
        parts.push(elderly.gender === 'male' ? '男' : '女')
      }

      if (elderly.age) {
        parts.push(`${elderly.age}岁`)
      }

      if (elderly.careLevel) {
        const levelMap = {
          independent: '自理',
          mild: '轻度依赖',
          moderate: '中度依赖',
          severe: '重度依赖'
        }
        parts.push(levelMap[elderly.careLevel] || elderly.careLevel)
      }

      if (elderly.phone) {
        parts.push(elderly.phone)
      }

      return parts.join(' · ')
    }
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.search-section {
  margin-bottom: $spacing-md;
}

.search-bar {
  display: flex;
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;

  .input {
    flex: 1;
  }
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
  align-items: center;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  background-color: $primary-color-light;
  color: $primary-color;
  border-radius: $border-radius-small;
  font-size: $font-size-sm;
  cursor: pointer;

  &:hover {
    background-color: color.adjust($primary-color-light, $lightness: -5%);
  }
}

.clear-all {
  color: $text-color-secondary;
  font-size: $font-size-sm;
  cursor: pointer;

  &:hover {
    color: $primary-color;
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  padding: 0 $spacing-sm;
}

.action-left {
  .total-count {
    color: $text-color-secondary;
    font-size: $font-size-sm;
  }
}

.action-right {
  display: flex;
  gap: $spacing-sm;
}

.list-section {
  margin-bottom: $spacing-md;
}

.elderly-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $primary-color;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: $font-size-lg;
}

.elderly-actions {
  display: flex;
  gap: $spacing-xs;
}

.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md $spacing-sm;
  background-color: $bg-color-white;
  border-radius: $border-radius-base;
  border: 1px solid $border-color-light;
}

.pagination-info {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.pagination-actions {
  display: flex;
  gap: $spacing-sm;
}

.filter-modal {
  background-color: $bg-color-white;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg;
  border-bottom: 1px solid $border-color-light;
}

.filter-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
}

.filter-content {
  padding: $spacing-lg;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: $spacing-lg;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  display: block;
  font-size: $font-size-base;
  font-weight: 600;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.filter-option {
  padding: $spacing-sm $spacing-md;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  background-color: $bg-color-white;
  color: $text-color-primary;
  font-size: $font-size-sm;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: $primary-color;
    color: $primary-color;
  }

  &.active {
    border-color: $primary-color;
    background-color: $primary-color;
    color: white;
  }
}

.filter-footer {
  display: flex;
  gap: $spacing-md;
  padding: $spacing-lg;
  border-top: 1px solid $border-color-light;

  .button {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  .action-left,
  .action-right {
    justify-content: center;
  }

  .elderly-actions {
    flex-direction: column;
    gap: $spacing-xs;
  }

  .pagination-section {
    flex-direction: column;
    gap: $spacing-sm;
    text-align: center;
  }
}
</style>
