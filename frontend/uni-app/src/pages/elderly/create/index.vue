<template>
  <PageContainer :title="isEdit ? '编辑老年人' : '新增老年人'">
    <view class="form-container">
      <form @submit="handleSubmit">
        <!-- 基本信息 -->
        <Card class="form-section">
          <template #header>
            <text class="section-title">基本信息</text>
          </template>

          <view class="form-content">
            <!-- 头像上传 -->
            <FormItem label="头像" class="avatar-item">
              <view class="avatar-upload">
                <view class="avatar-preview" @click="chooseAvatar">
                  <image
                    v-if="formData.avatar"
                    :src="formData.avatar"
                    class="avatar-image"
                    mode="aspectFill"
                  />
                  <view v-else class="avatar-placeholder">
                    <text class="icon-text" style="font-size: 24px; color: #c0c4cc">📷</text>
                    <text>点击上传</text>
                  </view>
                </view>
              </view>
            </FormItem>

            <!-- 姓名 -->
            <FormItem label="姓名" required :error="errors.name">
              <Input
                v-model="formData.name"
                placeholder="请输入姓名"
                :maxlength="20"
                @blur="validateField('name')"
              />
            </FormItem>

            <!-- 身份证号 -->
            <FormItem label="身份证号" required :error="errors.idNumber">
              <Input
                v-model="formData.idNumber"
                placeholder="请输入身份证号"
                :maxlength="18"
                @blur="validateField('idNumber')"
              />
            </FormItem>

            <!-- 性别 -->
            <FormItem label="性别" required :error="errors.gender">
              <Picker
                v-model="formData.gender"
                mode="selector"
                :range="genderOptions"
                range-key="label"
                placeholder="请选择性别"
                @change="handleGenderChange"
              />
            </FormItem>

            <!-- 出生日期 -->
            <FormItem label="出生日期" required :error="errors.birthDate">
              <Picker
                v-model="formData.birthDate"
                mode="date"
                :end="maxDate"
                placeholder="请选择出生日期"
                @change="handleBirthDateChange"
              />
            </FormItem>

            <!-- 年龄（自动计算） -->
            <FormItem label="年龄">
              <Input
                :value="formData.age ? `${formData.age}岁` : ''"
                placeholder="根据出生日期自动计算"
                :disabled="true"
              />
            </FormItem>

            <!-- 联系电话 -->
            <FormItem label="联系电话" :error="errors.phone">
              <Input
                v-model="formData.phone"
                placeholder="请输入联系电话"
                type="number"
                :maxlength="11"
                @blur="validateField('phone')"
              />
            </FormItem>

            <!-- 居住地址 -->
            <FormItem label="居住地址" :error="errors.address">
              <Input
                v-model="formData.address"
                placeholder="请输入居住地址"
                type="textarea"
                :maxlength="200"
                :auto-height="true"
              />
            </FormItem>
          </view>
        </Card>

        <!-- 紧急联系人 -->
        <Card class="form-section">
          <template #header>
            <text class="section-title">紧急联系人</text>
          </template>

          <view class="form-content">
            <!-- 紧急联系人姓名 -->
            <FormItem label="联系人姓名" :error="errors.emergencyContact">
              <Input
                v-model="formData.emergencyContact"
                placeholder="请输入紧急联系人姓名"
                :maxlength="20"
              />
            </FormItem>

            <!-- 紧急联系电话 -->
            <FormItem label="联系电话" :error="errors.emergencyPhone">
              <Input
                v-model="formData.emergencyPhone"
                placeholder="请输入紧急联系电话"
                type="number"
                :maxlength="11"
                @blur="validateField('emergencyPhone')"
              />
            </FormItem>

            <!-- 关系 -->
            <FormItem label="关系">
              <Picker
                v-model="formData.emergencyRelation"
                mode="selector"
                :range="relationOptions"
                range-key="label"
                placeholder="请选择关系"
                @change="handleRelationChange"
              />
            </FormItem>
          </view>
        </Card>

        <!-- 健康状况 -->
        <Card class="form-section">
          <template #header>
            <text class="section-title">健康状况</text>
          </template>

          <view class="form-content">
            <!-- 护理等级 -->
            <FormItem label="护理等级">
              <Picker
                v-model="formData.careLevel"
                mode="selector"
                :range="careLevelOptions"
                range-key="label"
                placeholder="请选择护理等级"
                @change="handleCareLevelChange"
              />
            </FormItem>

            <!-- 病史 -->
            <FormItem label="病史">
              <Input
                v-model="formData.medicalHistory"
                placeholder="请输入病史信息"
                type="textarea"
                :maxlength="500"
                :auto-height="true"
              />
            </FormItem>

            <!-- 用药情况 -->
            <FormItem label="用药情况">
              <Input
                v-model="formData.medications"
                placeholder="请输入用药情况"
                type="textarea"
                :maxlength="500"
                :auto-height="true"
              />
            </FormItem>

            <!-- 过敏史 -->
            <FormItem label="过敏史">
              <Input
                v-model="formData.allergies"
                placeholder="请输入过敏史"
                type="textarea"
                :maxlength="300"
                :auto-height="true"
              />
            </FormItem>

            <!-- 备注 -->
            <FormItem label="备注">
              <Input
                v-model="formData.notes"
                placeholder="请输入备注信息"
                type="textarea"
                :maxlength="300"
                :auto-height="true"
              />
            </FormItem>
          </view>
        </Card>
      </form>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <Button type="default" size="large" @click="handleCancel"> 取消 </Button>
      <Button type="primary" size="large" :loading="submitting" @click="handleSubmit">
        {{ isEdit ? '保存' : '创建' }}
      </Button>
    </view>
  </PageContainer>
</template>

<script>
import { mapActions } from 'vuex'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Input from '@/components/Form/Input.vue'
import Picker from '@/components/Form/Picker.vue'
import Button from '@/components/Common/Button.vue'
import { validator } from '@/utils'

export default {
  name: 'ElderlyCreate',

  components: {
    PageContainer,
    Card,
    FormItem,
    Input,
    Picker,
    Button
  },

  data() {
    return {
      isEdit: false,
      elderlyId: '',
      submitting: false,

      formData: {
        avatar: '',
        name: '',
        idNumber: '',
        gender: '',
        birthDate: '',
        age: '',
        phone: '',
        address: '',
        emergencyContact: '',
        emergencyPhone: '',
        emergencyRelation: '',
        careLevel: '',
        medicalHistory: '',
        medications: '',
        allergies: '',
        notes: ''
      },

      errors: {},

      genderOptions: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],

      relationOptions: [
        { label: '子女', value: 'child' },
        { label: '配偶', value: 'spouse' },
        { label: '兄弟姐妹', value: 'sibling' },
        { label: '其他亲属', value: 'relative' },
        { label: '朋友', value: 'friend' },
        { label: '其他', value: 'other' }
      ],

      careLevelOptions: [
        { label: '自理', value: 'independent' },
        { label: '轻度依赖', value: 'mild' },
        { label: '中度依赖', value: 'moderate' },
        { label: '重度依赖', value: 'severe' }
      ]
    }
  },

  computed: {
    maxDate() {
      const today = new Date()
      return today.toISOString().split('T')[0]
    }
  },

  onLoad(options) {
    if (options.id) {
      this.isEdit = true
      this.elderlyId = options.id
      this.loadElderlyDetail()
    }
  },

  methods: {
    ...mapActions('elderly', ['getElderlyDetail', 'createElderly', 'updateElderly']),

    async loadElderlyDetail() {
      try {
        uni.showLoading({ title: '加载中...' })
        const elderly = await this.getElderlyDetail(this.elderlyId)
        this.fillFormData(elderly)
      } catch (error) {
        console.error('加载老年人详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
        this.navigateBack()
      } finally {
        uni.hideLoading()
      }
    },

    fillFormData(elderly) {
      Object.keys(this.formData).forEach(key => {
        if (elderly[key] !== undefined) {
          this.formData[key] = elderly[key]
        }
      })
    },

    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          const tempFilePath = res.tempFilePaths[0]
          this.uploadAvatar(tempFilePath)
        }
      })
    },

    async uploadAvatar(filePath) {
      try {
        uni.showLoading({ title: '上传中...' })
        // 这里应该调用上传接口
        // const result = await uploadFile(filePath)
        // this.formData.avatar = result.url

        // 临时使用本地路径
        this.formData.avatar = filePath

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('上传头像失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    handleGenderChange(e) {
      const index = e.detail.value
      this.formData.gender = this.genderOptions[index].value
      this.validateField('gender')
    },

    handleBirthDateChange(e) {
      this.formData.birthDate = e.detail.value
      this.calculateAge()
      this.validateField('birthDate')
    },

    handleRelationChange(e) {
      const index = e.detail.value
      this.formData.emergencyRelation = this.relationOptions[index].value
    },

    handleCareLevelChange(e) {
      const index = e.detail.value
      this.formData.careLevel = this.careLevelOptions[index].value
    },

    calculateAge() {
      if (this.formData.birthDate) {
        const birthDate = new Date(this.formData.birthDate)
        const today = new Date()
        let age = today.getFullYear() - birthDate.getFullYear()
        const monthDiff = today.getMonth() - birthDate.getMonth()

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--
        }

        this.formData.age = age
      }
    },

    validateField(field) {
      const value = this.formData[field]
      let error = ''

      switch (field) {
        case 'name':
          if (!value) {
            error = '请输入姓名'
          } else if (value.length < 2) {
            error = '姓名至少2个字符'
          }
          break

        case 'idNumber':
          if (!value) {
            error = '请输入身份证号'
          } else if (!validator.isIdCard(value)) {
            error = '身份证号格式不正确'
          }
          break

        case 'gender':
          if (!value) {
            error = '请选择性别'
          }
          break

        case 'birthDate':
          if (!value) {
            error = '请选择出生日期'
          }
          break

        case 'phone':
          if (value && !validator.isMobile(value)) {
            error = '手机号格式不正确'
          }
          break

        case 'emergencyPhone':
          if (value && !validator.isMobile(value)) {
            error = '手机号格式不正确'
          }
          break
      }

      if (error) {
        this.$set(this.errors, field, error)
      } else {
        this.$delete(this.errors, field)
      }

      return !error
    },

    validateForm() {
      const requiredFields = ['name', 'idNumber', 'gender', 'birthDate']
      let isValid = true

      // 清空之前的错误
      this.errors = {}

      // 验证必填字段
      requiredFields.forEach(field => {
        if (!this.validateField(field)) {
          isValid = false
        }
      })

      // 验证可选字段
      const optionalFields = ['phone', 'emergencyPhone']
      optionalFields.forEach(field => {
        if (this.formData[field]) {
          if (!this.validateField(field)) {
            isValid = false
          }
        }
      })

      return isValid
    },

    async handleSubmit() {
      if (!this.validateForm()) {
        uni.showToast({
          title: '请检查表单信息',
          icon: 'error'
        })
        return
      }

      try {
        this.submitting = true

        if (this.isEdit) {
          await this.updateElderly({
            id: this.elderlyId,
            ...this.formData
          })
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        } else {
          await this.createElderly(this.formData)
          uni.showToast({
            title: '创建成功',
            icon: 'success'
          })
        }

        setTimeout(() => {
          this.navigateBack()
        }, 1500)
      } catch (error) {
        console.error('提交失败:', error)
        uni.showToast({
          title: this.isEdit ? '保存失败' : '创建失败',
          icon: 'error'
        })
      } finally {
        this.submitting = false
      }
    },

    handleCancel() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消编辑吗？未保存的内容将丢失。',
        success: res => {
          if (res.confirm) {
            this.navigateBack()
          }
        }
      })
    },

    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.form-container {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.form-section {
  margin-bottom: $spacing-lg;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
}

.form-content {
  padding: $spacing-lg;
}

.avatar-item {
  :deep(.form-item-control) {
    justify-content: center;
  }
}

.avatar-upload {
  display: flex;
  justify-content: center;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px dashed $border-color-base;
  transition: border-color 0.2s;

  &:hover {
    border-color: $primary-color;
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: $bg-color-light;
  color: $text-color-secondary;

  text {
    font-size: $font-size-xs;
    margin-top: $spacing-xs;
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;

  .button {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-content {
    padding: $spacing-md;
  }

  .avatar-preview {
    width: 80px;
    height: 80px;
  }
}
</style>
