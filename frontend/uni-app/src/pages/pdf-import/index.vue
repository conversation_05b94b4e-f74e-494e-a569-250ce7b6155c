<template>
  <view class="pdf-import-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">PDF评估标准导入</text>
      <text class="page-subtitle">上传PDF文件，自动解析生成评估量表</text>
    </view>

    <!-- 功能选项卡 -->
    <view class="tab-container">
      <view
        class="tab-item"
        :class="{ active: activeTab === 'single' }"
        @click="switchTab('single')"
      >
        <text>单文件导入</text>
      </view>
      <view class="tab-item" :class="{ active: activeTab === 'batch' }" @click="switchTab('batch')">
        <text>批量导入</text>
      </view>
      <view class="tab-item" :class="{ active: activeTab === 'parse' }" @click="switchTab('parse')">
        <text>内容解析</text>
      </view>
    </view>

    <!-- 单文件导入 -->
    <view v-if="activeTab === 'single'" class="tab-content">
      <view class="upload-section">
        <view class="upload-area" @click="selectSingleFile">
          <view v-if="!singleFile" class="upload-placeholder">
            <text class="upload-icon">📄</text>
            <text class="upload-text">点击选择PDF文件</text>
            <text class="upload-hint">支持PDF格式，最大50MB</text>
          </view>
          <view v-else class="file-info">
            <text class="file-name">{{ singleFile.name }}</text>
            <text class="file-size">{{ formatFileSize(singleFile.size) }}</text>
          </view>
        </view>

        <!-- 预览模式开关 -->
        <view class="option-row">
          <text class="option-label">预览模式</text>
          <switch :checked="previewMode" color="#007AFF" @change="onPreviewModeChange" />
          <text class="option-hint">开启后仅解析不保存到数据库</text>
        </view>

        <!-- 操作按钮 -->
        <view class="button-row">
          <button class="btn btn-secondary" :disabled="!singleFile" @click="clearSingleFile">
            清除文件
          </button>
          <button
            class="btn btn-primary"
            :disabled="!singleFile || uploading"
            :loading="uploading"
            @click="uploadSingleFile"
          >
            {{ uploading ? '处理中...' : previewMode ? '预览解析' : '导入量表' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 批量导入 -->
    <view v-if="activeTab === 'batch'" class="tab-content">
      <view class="upload-section">
        <view class="upload-area" @click="selectBatchFiles">
          <view v-if="batchFiles.length === 0" class="upload-placeholder">
            <text class="upload-icon">📁</text>
            <text class="upload-text">点击选择多个PDF文件</text>
            <text class="upload-hint">最多选择10个文件，每个最大50MB</text>
          </view>
          <view v-else class="batch-file-list">
            <view v-for="(file, index) in batchFiles" :key="index" class="batch-file-item">
              <text class="file-name">{{ file.name }}</text>
              <text class="file-size">{{ formatFileSize(file.size) }}</text>
              <text class="remove-btn" @click.stop="removeBatchFile(index)">×</text>
            </view>
          </view>
        </view>

        <!-- 批量选项 -->
        <view class="option-row">
          <text class="option-label">预览模式</text>
          <switch :checked="batchPreviewMode" color="#007AFF" @change="onBatchPreviewModeChange" />
          <text class="option-hint">开启后仅解析不保存到数据库</text>
        </view>

        <!-- 操作按钮 -->
        <view class="button-row">
          <button
            class="btn btn-secondary"
            :disabled="batchFiles.length === 0"
            @click="clearBatchFiles"
          >
            清除全部
          </button>
          <button
            class="btn btn-primary"
            :disabled="batchFiles.length === 0 || batchUploading"
            :loading="batchUploading"
            @click="uploadBatchFiles"
          >
            {{
              batchUploading
                ? '批量处理中...'
                : `批量${batchPreviewMode ? '预览' : '导入'}(${batchFiles.length}个文件)`
            }}
          </button>
        </view>
      </view>
    </view>

    <!-- 内容解析 -->
    <view v-if="activeTab === 'parse'" class="tab-content">
      <view class="upload-section">
        <view class="upload-area" @click="selectParseFile">
          <view v-if="!parseFile" class="upload-placeholder">
            <text class="upload-icon">🔍</text>
            <text class="upload-text">选择PDF文件进行内容解析</text>
            <text class="upload-hint">仅提取文本和表格信息，不生成量表</text>
          </view>
          <view v-else class="file-info">
            <text class="file-name">{{ parseFile.name }}</text>
            <text class="file-size">{{ formatFileSize(parseFile.size) }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="button-row">
          <button class="btn btn-secondary" :disabled="!parseFile" @click="clearParseFile">
            清除文件
          </button>
          <button
            class="btn btn-primary"
            :disabled="!parseFile || parsing"
            :loading="parsing"
            @click="parseFileContent"
          >
            {{ parsing ? '解析中...' : '开始解析' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 结果展示区域 -->
    <view v-if="result" class="result-section">
      <view class="result-header">
        <text class="result-title">处理结果</text>
        <text class="close-btn" @click="clearResult">×</text>
      </view>

      <!-- 单文件结果 -->
      <view v-if="result.type === 'single'" class="result-content">
        <view class="result-item">
          <text class="result-label">文件名：</text>
          <text class="result-value">{{ result.data.fileName }}</text>
        </view>
        <view class="result-item">
          <text class="result-label">文件大小：</text>
          <text class="result-value">{{ formatFileSize(result.data.fileSize) }}</text>
        </view>
        <view class="result-item">
          <text class="result-label">页数：</text>
          <text class="result-value">{{ result.data.pageCount }}</text>
        </view>

        <view v-if="result.data.structure" class="structure-info">
          <text class="section-title">量表信息</text>
          <view class="result-item">
            <text class="result-label">标题：</text>
            <text class="result-value">{{ result.data.structure.title }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">类型：</text>
            <text class="result-value">{{ result.data.structure.type }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">章节数：</text>
            <text class="result-value">{{ result.data.structure.sectionCount }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">题目数：</text>
            <text class="result-value">{{ result.data.structure.questionCount }}</text>
          </view>

          <view v-if="result.data.saved" class="success-info">
            <text class="success-text">✅ 量表已成功保存到数据库</text>
            <text class="scale-id">量表ID: {{ result.data.scaleId }}</text>
          </view>
        </view>
      </view>

      <!-- 批量结果 -->
      <view v-if="result.type === 'batch'" class="result-content">
        <view class="batch-summary">
          <text class="summary-text">总文件数: {{ result.data.totalFiles }}</text>
          <text class="summary-text success">成功: {{ result.data.successCount }}</text>
          <text class="summary-text error">失败: {{ result.data.errorCount }}</text>
        </view>

        <view class="batch-results">
          <view
            v-for="(item, index) in result.data.results"
            :key="index"
            class="batch-result-item"
            :class="item.status"
          >
            <text class="file-name">{{ item.fileName }}</text>
            <text class="status-text">{{ item.status === 'success' ? '成功' : '失败' }}</text>
            <text v-if="item.title" class="title-text">{{ item.title }}</text>
            <text v-if="item.error" class="error-text">{{ item.error }}</text>
          </view>
        </view>
      </view>

      <!-- 解析结果 -->
      <view v-if="result.type === 'parse'" class="result-content">
        <view class="result-item">
          <text class="result-label">文件名：</text>
          <text class="result-value">{{ result.data.fileName }}</text>
        </view>
        <view class="result-item">
          <text class="result-label">页数：</text>
          <text class="result-value">{{ result.data.pageCount }}</text>
        </view>
        <view class="result-item">
          <text class="result-label">文本长度：</text>
          <text class="result-value">{{ result.data.textLength }} 字符</text>
        </view>
        <view class="result-item">
          <text class="result-label">表格数量：</text>
          <text class="result-value">{{ result.data.tableCount }}</text>
        </view>

        <view v-if="result.data.textPreview" class="text-preview">
          <text class="section-title">文本预览</text>
          <text class="preview-text">{{ result.data.textPreview }}</text>
        </view>

        <view v-if="result.data.tables && result.data.tables.length > 0" class="table-info">
          <text class="section-title">表格信息</text>
          <view v-for="(table, index) in result.data.tables" :key="index" class="table-item">
            <text class="table-title">{{ table.title || `表格 ${index + 1}` }}</text>
            <text class="table-detail">{{ table.rowCount }}行 × {{ table.columnCount }}列</text>
            <text class="table-page">第{{ table.pageNumber }}页</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 导入历史 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">导入历史</text>
        <text class="refresh-btn" @click="loadHistory">刷新</text>
      </view>

      <view v-if="history.length === 0" class="empty-history">
        <text class="empty-text">暂无导入记录</text>
      </view>

      <view v-else class="history-list">
        <view v-for="(item, index) in history" :key="index" class="history-item">
          <text class="history-title">{{ item.title }}</text>
          <text class="history-time">{{ item.time }}</text>
          <text class="history-status" :class="item.status">{{ item.statusText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'
import { uploadPDF, batchUploadPDF, parsePDF, getImportHistory } from '@/api/pdf-import'
import { showToast, showLoading, hideLoading } from '@/utils/ui'

export default {
  name: 'PDFImport',
  setup() {
    // 响应式数据
    const activeTab = ref('single')
    const singleFile = ref(null)
    const batchFiles = ref([])
    const parseFile = ref(null)
    const previewMode = ref(false)
    const batchPreviewMode = ref(false)
    const uploading = ref(false)
    const batchUploading = ref(false)
    const parsing = ref(false)
    const result = ref(null)
    const history = ref([])

    // 切换选项卡
    const switchTab = tab => {
      activeTab.value = tab
      clearResult()
    }

    // 选择单个文件
    const selectSingleFile = () => {
      uni.chooseFile({
        count: 1,
        type: 'file',
        extension: ['.pdf'],
        success: res => {
          const file = res.tempFiles[0]
          if (file.size > 50 * 1024 * 1024) {
            showToast('文件大小不能超过50MB')
            return
          }
          singleFile.value = file
        },
        fail: err => {
          console.error('选择文件失败:', err)
          showToast('选择文件失败')
        }
      })
    }

    // 选择批量文件
    const selectBatchFiles = () => {
      uni.chooseFile({
        count: 10,
        type: 'file',
        extension: ['.pdf'],
        success: res => {
          const validFiles = res.tempFiles.filter(file => {
            if (file.size > 50 * 1024 * 1024) {
              showToast(`文件 ${file.name} 大小超过50MB，已跳过`)
              return false
            }
            return true
          })

          if (validFiles.length === 0) {
            showToast('没有有效的文件')
            return
          }

          batchFiles.value = validFiles
        },
        fail: err => {
          console.error('选择文件失败:', err)
          showToast('选择文件失败')
        }
      })
    }

    // 选择解析文件
    const selectParseFile = () => {
      uni.chooseFile({
        count: 1,
        type: 'file',
        extension: ['.pdf'],
        success: res => {
          const file = res.tempFiles[0]
          if (file.size > 50 * 1024 * 1024) {
            showToast('文件大小不能超过50MB')
            return
          }
          parseFile.value = file
        },
        fail: err => {
          console.error('选择文件失败:', err)
          showToast('选择文件失败')
        }
      })
    }

    // 清除单个文件
    const clearSingleFile = () => {
      singleFile.value = null
    }

    // 清除批量文件
    const clearBatchFiles = () => {
      batchFiles.value = []
    }

    // 移除批量文件中的某个文件
    const removeBatchFile = index => {
      batchFiles.value.splice(index, 1)
    }

    // 清除解析文件
    const clearParseFile = () => {
      parseFile.value = null
    }

    // 预览模式切换
    const onPreviewModeChange = e => {
      previewMode.value = e.detail.value
    }

    const onBatchPreviewModeChange = e => {
      batchPreviewMode.value = e.detail.value
    }

    // 上传单个文件
    const uploadSingleFile = async () => {
      if (!singleFile.value) return

      uploading.value = true
      showLoading('处理中...')

      try {
        const response = await uploadPDF(singleFile.value, previewMode.value)

        if (response.success) {
          result.value = {
            type: 'single',
            data: response.data
          }

          showToast(previewMode.value ? '预览解析完成' : '导入成功')

          // 刷新历史记录
          if (!previewMode.value) {
            loadHistory()
          }
        } else {
          showToast(response.message || '处理失败')
        }
      } catch (error) {
        console.error('上传失败:', error)
        showToast('上传失败')
      } finally {
        uploading.value = false
        hideLoading()
      }
    }

    // 批量上传文件
    const uploadBatchFiles = async () => {
      if (batchFiles.value.length === 0) return

      batchUploading.value = true
      showLoading('批量处理中...')

      try {
        const response = await batchUploadPDF(batchFiles.value, batchPreviewMode.value)

        if (response.success) {
          result.value = {
            type: 'batch',
            data: response.data
          }

          const { successCount, errorCount } = response.data
          showToast(`批量处理完成：成功${successCount}个，失败${errorCount}个`)

          // 刷新历史记录
          if (!batchPreviewMode.value && successCount > 0) {
            loadHistory()
          }
        } else {
          showToast(response.message || '批量处理失败')
        }
      } catch (error) {
        console.error('批量上传失败:', error)
        showToast('批量上传失败')
      } finally {
        batchUploading.value = false
        hideLoading()
      }
    }

    // 解析文件内容
    const parseFileContent = async () => {
      if (!parseFile.value) return

      parsing.value = true
      showLoading('解析中...')

      try {
        const response = await parsePDF(parseFile.value)

        if (response.success) {
          result.value = {
            type: 'parse',
            data: response.data
          }

          showToast('解析完成')
        } else {
          showToast(response.message || '解析失败')
        }
      } catch (error) {
        console.error('解析失败:', error)
        showToast('解析失败')
      } finally {
        parsing.value = false
        hideLoading()
      }
    }

    // 清除结果
    const clearResult = () => {
      result.value = null
    }

    // 格式化文件大小
    const formatFileSize = bytes => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
    }

    // 加载历史记录
    const loadHistory = async () => {
      try {
        const response = await getImportHistory()
        if (response.success) {
          // 模拟历史数据
          history.value = [
            {
              title: 'ADL日常生活能力评估量表',
              time: '2024-01-15 14:30',
              status: 'success',
              statusText: '导入成功'
            },
            {
              title: 'MMSE认知功能评估',
              time: '2024-01-14 09:15',
              status: 'success',
              statusText: '导入成功'
            }
          ]
        }
      } catch (error) {
        console.error('加载历史失败:', error)
      }
    }

    // 页面加载时获取历史记录
    onMounted(() => {
      loadHistory()
    })

    return {
      activeTab,
      singleFile,
      batchFiles,
      parseFile,
      previewMode,
      batchPreviewMode,
      uploading,
      batchUploading,
      parsing,
      result,
      history,
      switchTab,
      selectSingleFile,
      selectBatchFiles,
      selectParseFile,
      clearSingleFile,
      clearBatchFiles,
      removeBatchFile,
      clearParseFile,
      onPreviewModeChange,
      onBatchPreviewModeChange,
      uploadSingleFile,
      uploadBatchFiles,
      parseFileContent,
      clearResult,
      formatFileSize,
      loadHistory
    }
  }
}
</script>

<style scoped>
.pdf-import-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.tab-container {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  background-color: #f8f9fa;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #007aff;
  color: #fff;
}

.tab-content {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.upload-area {
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  margin-bottom: 30rpx;
  transition: all 0.3s;
}

.upload-area:active {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.file-size {
  font-size: 24rpx;
  color: #666;
}

.batch-file-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.batch-file-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.batch-file-item:last-child {
  border-bottom: none;
}

.batch-file-item .file-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.batch-file-item .file-size {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.remove-btn {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  background-color: #ff4757;
  color: #fff;
  border-radius: 50%;
  font-size: 24rpx;
}

.option-row {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.option-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.option-hint {
  font-size: 24rpx;
  color: #666;
  margin-left: 20rpx;
}

.button-row {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.btn-primary {
  background-color: #007aff;
  color: #fff;
}

.btn-primary:disabled {
  background-color: #ccc;
  color: #999;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #ddd;
}

.btn-secondary:disabled {
  background-color: #f5f5f5;
  color: #ccc;
}

.result-section {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 50%;
  font-size: 24rpx;
}

.result-content {
  padding: 30rpx;
}

.result-item {
  display: flex;
  margin-bottom: 20rpx;
}

.result-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.result-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 0 20rpx 0;
  display: block;
}

.structure-info {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.success-info {
  background-color: #e8f5e8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.success-text {
  font-size: 28rpx;
  color: #28a745;
  display: block;
  margin-bottom: 10rpx;
}

.scale-id {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.batch-summary {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #333;
}

.summary-text.success {
  color: #28a745;
}

.summary-text.error {
  color: #dc3545;
}

.batch-results {
  max-height: 600rpx;
  overflow-y: auto;
}

.batch-result-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  border-left: 4rpx solid #ddd;
}

.batch-result-item.success {
  border-left-color: #28a745;
  background-color: #f8fff8;
}

.batch-result-item.error {
  border-left-color: #dc3545;
  background-color: #fff8f8;
}

.batch-result-item:last-child {
  border-bottom: none;
}

.batch-result-item .file-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.status-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.title-text {
  font-size: 26rpx;
  color: #007aff;
  display: block;
  margin-bottom: 5rpx;
}

.error-text {
  font-size: 24rpx;
  color: #dc3545;
  display: block;
}

.text-preview {
  margin-top: 30rpx;
}

.preview-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  display: block;
}

.table-info {
  margin-top: 30rpx;
}

.table-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
}

.table-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.table-detail {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.table-page {
  font-size: 22rpx;
  color: #999;
  display: block;
}

.history-section {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.refresh-btn {
  font-size: 26rpx;
  color: #007aff;
}

.empty-history {
  padding: 60rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.history-list {
  padding: 0 30rpx 30rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.history-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: #e8f5e8;
  color: #28a745;
}

.history-status.error {
  background-color: #fff0f0;
  color: #dc3545;
}
</style>
