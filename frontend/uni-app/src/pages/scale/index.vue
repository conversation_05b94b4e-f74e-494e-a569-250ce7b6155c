<template>
  <PageContainer title="量表管理" :show-back="true">
    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-bar">
        <Input
          v-model="searchKeyword"
          placeholder="搜索量表名称、分类"
          :clearable="true"
          @input="handleSearch"
          @clear="handleSearchClear"
        >
          <template #prefix>
            <text class="icon-text" style="font-size: 16px; color: #c0c4cc">⌕</text>
          </template>
        </Input>

        <Button type="default" size="small" @click="showFilterModal = true">
          <text class="icon-text" style="font-size: 16px">⚙</text>
          筛选
        </Button>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="count-info">
        <text>共 {{ pagination.total }} 个量表</text>
      </view>

      <view class="action-buttons">
        <Button type="default" size="small" @click="importScale">
          <text class="icon-text" style="font-size: 14px">↑</text>
          导入
        </Button>

        <Button type="primary" size="small" @click="createScale">
          <text class="icon-text" style="font-size: 14px">+</text>
          新建量表
        </Button>
      </view>
    </view>

    <!-- 量表列表 -->
    <view class="scale-list">
      <Loading v-if="loading" type="spinner" text="加载中..." />

      <Empty
        v-else-if="scaleList.length === 0"
        type="nodata"
        description="暂无量表数据"
        :show-button="true"
        button-text="新建量表"
        @button-click="createScale"
      />

      <view v-else class="list-content">
        <Card
          v-for="scale in scaleList"
          :key="scale.id"
          class="scale-item"
          :clickable="true"
          @click="viewScale(scale)"
        >
          <view class="scale-content">
            <view class="scale-header">
              <view class="scale-info">
                <text class="scale-name">{{ scale.name }}</text>
                <view class="scale-meta">
                  <text class="scale-category">{{ scale.category }}</text>
                  <text class="scale-version">v{{ scale.version }}</text>
                  <view class="scale-status" :class="`status-${scale.status}`">
                    <text>{{ formatStatus(scale.status) }}</text>
                  </view>
                </view>
              </view>

              <view class="scale-actions">
                <Button type="default" size="mini" @click.stop="moreActions(scale)">
                  <text class="icon-text" style="font-size: 16px">···</text>
                </Button>
              </view>
            </view>

            <view class="scale-description">
              <text>{{ scale.description || '暂无描述' }}</text>
            </view>

            <view class="scale-stats">
              <view class="stat-item">
                <text class="stat-label">题目数量</text>
                <text class="stat-value">{{ scale.questionCount }}题</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">使用次数</text>
                <text class="stat-value">{{ scale.usageCount }}次</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">创建时间</text>
                <text class="stat-value">{{ formatDate(scale.createdAt) }}</text>
              </view>
            </view>

            <view class="scale-footer">
              <view class="creator-info">
                <text class="creator-label">创建者：</text>
                <text class="creator-name">{{ scale.creatorName }}</text>
              </view>

              <view class="quick-actions">
                <Button type="default" size="mini" @click.stop="previewScale(scale)"> 预览 </Button>

                <Button
                  v-if="scale.status === 'published'"
                  type="primary"
                  size="mini"
                  @click.stop="useScale(scale)"
                >
                  使用
                </Button>

                <Button v-else type="default" size="mini" @click.stop="editScale(scale)">
                  编辑
                </Button>
              </view>
            </view>
          </view>
        </Card>
      </view>
    </view>

    <!-- 分页 -->
    <view v-if="pagination.total > pagination.pageSize" class="pagination-section">
      <Pagination
        :current="pagination.current"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        @change="handlePageChange"
      />
    </view>

    <!-- 筛选弹窗 -->
    <Modal
      v-model="showFilterModal"
      title="筛选条件"
      :show-cancel="true"
      @confirm="applyFilter"
      @cancel="resetFilter"
    >
      <view class="filter-content">
        <FormItem label="量表分类">
          <Picker
            v-model="filterForm.category"
            :options="categoryOptions"
            placeholder="请选择分类"
            :clearable="true"
          />
        </FormItem>

        <FormItem label="量表状态">
          <Picker
            v-model="filterForm.status"
            :options="statusOptions"
            placeholder="请选择状态"
            :clearable="true"
          />
        </FormItem>

        <FormItem label="创建时间">
          <DatePicker
            v-model="filterForm.dateRange"
            type="daterange"
            placeholder="请选择时间范围"
            :clearable="true"
          />
        </FormItem>

        <FormItem label="创建者">
          <Input v-model="filterForm.creator" placeholder="请输入创建者姓名" :clearable="true" />
        </FormItem>
      </view>
    </Modal>

    <!-- 更多操作弹窗 -->
    <ActionSheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="handleActionSelect"
    />
  </PageContainer>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Input from '@/components/Form/Input.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'
import Pagination from '@/components/Common/Pagination.vue'
import Modal from '@/components/Common/Modal.vue'
import FormItem from '@/components/Form/FormItem.vue'
import Picker from '@/components/Form/Picker.vue'
import DatePicker from '@/components/Form/DatePicker.vue'
import ActionSheet from '@/components/Common/ActionSheet.vue'
import { formatDate } from '@/utils'

export default {
  name: 'ScaleManagement',

  components: {
    PageContainer,
    Card,
    Input,
    Button,
    Loading,
    Empty,
    Pagination,
    Modal,
    FormItem,
    Picker,
    DatePicker,
    ActionSheet
  },

  data() {
    return {
      loading: false,

      searchKeyword: '',
      scaleList: [],

      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },

      showFilterModal: false,
      filterForm: {
        category: '',
        status: '',
        dateRange: [],
        creator: ''
      },

      showActionSheet: false,
      currentScale: null,

      categoryOptions: [
        { label: '日常生活能力', value: 'adl' },
        { label: '认知功能', value: 'cognitive' },
        { label: '心理健康', value: 'mental' },
        { label: '社会功能', value: 'social' },
        { label: '身体功能', value: 'physical' },
        { label: '营养评估', value: 'nutrition' },
        { label: '跌倒风险', value: 'fall_risk' },
        { label: '其他', value: 'other' }
      ],

      statusOptions: [
        { label: '草稿', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已停用', value: 'disabled' }
      ]
    }
  },

  computed: {
    ...mapState('user', ['userInfo']),

    actionSheetActions() {
      if (!this.currentScale) return []

      const actions = [
        { text: '查看详情', value: 'detail' },
        { text: '预览量表', value: 'preview' }
      ]

      if (this.currentScale.status === 'draft') {
        actions.push({ text: '编辑', value: 'edit' }, { text: '发布', value: 'publish' })
      } else if (this.currentScale.status === 'published') {
        actions.push(
          { text: '使用评估', value: 'use' },
          { text: '复制', value: 'copy' },
          { text: '停用', value: 'disable' }
        )
      } else if (this.currentScale.status === 'disabled') {
        actions.push(
          { text: '启用', value: 'enable' },
          { text: '删除', value: 'delete', color: '#ff4d4f' }
        )
      }

      actions.push({ text: '导出', value: 'export' }, { text: '分享', value: 'share' })

      return actions
    }
  },

  onLoad() {
    this.loadScaleList()
  },

  onPullDownRefresh() {
    this.loadScaleList().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    ...mapActions('scale', [
      'getScaleList',
      'deleteScale',
      'publishScale',
      'disableScale',
      'enableScale',
      'copyScale',
      'exportScale'
    ]),

    async loadScaleList() {
      try {
        this.loading = true

        const params = {
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          keyword: this.searchKeyword,
          ...this.getFilterParams()
        }

        const response = await this.getScaleList(params)

        this.scaleList = response.list
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载量表列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    getFilterParams() {
      const params = {}

      if (this.filterForm.category) {
        params.category = this.filterForm.category
      }

      if (this.filterForm.status) {
        params.status = this.filterForm.status
      }

      if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        params.startDate = this.filterForm.dateRange[0]
        params.endDate = this.filterForm.dateRange[1]
      }

      if (this.filterForm.creator) {
        params.creator = this.filterForm.creator
      }

      return params
    },

    handleSearch() {
      this.pagination.current = 1
      this.loadScaleList()
    },

    handleSearchClear() {
      this.searchKeyword = ''
      this.handleSearch()
    },

    handlePageChange(page) {
      this.pagination.current = page
      this.loadScaleList()
    },

    applyFilter() {
      this.pagination.current = 1
      this.showFilterModal = false
      this.loadScaleList()
    },

    resetFilter() {
      this.filterForm = {
        category: '',
        status: '',
        dateRange: [],
        creator: ''
      }
      this.showFilterModal = false
      this.pagination.current = 1
      this.loadScaleList()
    },

    viewScale(scale) {
      uni.navigateTo({
        url: `/pages/scale/detail/index?id=${scale.id}`
      })
    },

    previewScale(scale) {
      uni.navigateTo({
        url: `/pages/scale/preview/index?id=${scale.id}`
      })
    },

    editScale(scale) {
      uni.navigateTo({
        url: `/pages/scale/create/index?id=${scale.id}`
      })
    },

    useScale(scale) {
      uni.navigateTo({
        url: `/pages/assessment/create/index?scaleId=${scale.id}`
      })
    },

    createScale() {
      uni.navigateTo({
        url: '/pages/scale/create/index'
      })
    },

    importScale() {
      uni.chooseFile({
        count: 1,
        extension: ['.json', '.xlsx'],
        success: res => {
          // 处理文件导入
          this.handleImportFile(res.tempFiles[0])
        }
      })
    },

    async handleImportFile(file) {
      try {
        uni.showLoading({ title: '导入中...' })

        // 上传文件并导入
        const uploadResult = await uni.uploadFile({
          url: '/api/scale/import',
          filePath: file.path,
          name: 'file'
        })

        const result = JSON.parse(uploadResult.data)

        if (result.success) {
          uni.showToast({
            title: '导入成功',
            icon: 'success'
          })
          this.loadScaleList()
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('导入失败:', error)
        uni.showToast({
          title: '导入失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    moreActions(scale) {
      this.currentScale = scale
      this.showActionSheet = true
    },

    async handleActionSelect(action) {
      const scale = this.currentScale
      if (!scale) return

      switch (action.value) {
        case 'detail':
          this.viewScale(scale)
          break

        case 'preview':
          this.previewScale(scale)
          break

        case 'edit':
          this.editScale(scale)
          break

        case 'use':
          this.useScale(scale)
          break

        case 'publish':
          await this.handlePublishScale(scale)
          break

        case 'disable':
          await this.handleDisableScale(scale)
          break

        case 'enable':
          await this.handleEnableScale(scale)
          break

        case 'copy':
          await this.handleCopyScale(scale)
          break

        case 'delete':
          await this.handleDeleteScale(scale)
          break

        case 'export':
          await this.handleExportScale(scale)
          break

        case 'share':
          this.handleShareScale(scale)
          break
      }
    },

    async handlePublishScale(scale) {
      try {
        await this.publishScale(scale.id)
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        })
        this.loadScaleList()
      } catch (error) {
        uni.showToast({
          title: '发布失败',
          icon: 'error'
        })
      }
    },

    async handleDisableScale(scale) {
      uni.showModal({
        title: '确认停用',
        content: '停用后将无法使用该量表进行评估，确定要停用吗？',
        success: async res => {
          if (res.confirm) {
            try {
              await this.disableScale(scale.id)
              uni.showToast({
                title: '停用成功',
                icon: 'success'
              })
              this.loadScaleList()
            } catch (error) {
              uni.showToast({
                title: '停用失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleEnableScale(scale) {
      try {
        await this.enableScale(scale.id)
        uni.showToast({
          title: '启用成功',
          icon: 'success'
        })
        this.loadScaleList()
      } catch (error) {
        uni.showToast({
          title: '启用失败',
          icon: 'error'
        })
      }
    },

    async handleCopyScale(scale) {
      try {
        const result = await this.copyScale(scale.id)
        uni.showToast({
          title: '复制成功',
          icon: 'success'
        })

        // 跳转到编辑页面
        uni.navigateTo({
          url: `/pages/scale/create/index?id=${result.id}`
        })
      } catch (error) {
        uni.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    },

    async handleDeleteScale(scale) {
      uni.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除该量表吗？',
        success: async res => {
          if (res.confirm) {
            try {
              await this.deleteScale(scale.id)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.loadScaleList()
            } catch (error) {
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    async handleExportScale(scale) {
      try {
        uni.showLoading({ title: '导出中...' })

        const result = await this.exportScale({
          scaleId: scale.id,
          format: 'json'
        })

        // 下载文件
        uni.downloadFile({
          url: result.downloadUrl,
          success: res => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: '导出成功',
                icon: 'success'
              })
            }
          }
        })
      } catch (error) {
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    handleShareScale(scale) {
      // 分享量表
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `pages/scale/detail/index?id=${scale.id}`,
        title: scale.name,
        summary: scale.description || '查看量表详情',
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        }
      })
    },

    formatStatus(status) {
      const statusMap = {
        draft: '草稿',
        published: '已发布',
        disabled: '已停用'
      }
      return statusMap[status] || status
    },

    formatDate
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.search-section {
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;

  .search-bar {
    display: flex;
    gap: $spacing-sm;
    align-items: center;

    .input {
      flex: 1;
    }
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-bottom: 1px solid $border-color-light;

  .count-info {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }

  .action-buttons {
    display: flex;
    gap: $spacing-sm;
  }
}

.scale-list {
  padding: $spacing-md;

  .list-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }
}

.scale-item {
  .scale-content {
    padding: $spacing-lg;
  }

  .scale-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-md;
  }

  .scale-info {
    flex: 1;

    .scale-name {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-xs;
    }

    .scale-meta {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .scale-category {
        font-size: $font-size-xs;
        color: $text-color-secondary;
        padding: 2px 6px;
        background-color: $bg-color-light;
        border-radius: $border-radius-xs;
      }

      .scale-version {
        font-size: $font-size-xs;
        color: $text-color-secondary;
      }

      .scale-status {
        font-size: $font-size-xs;
        padding: 2px 6px;
        border-radius: $border-radius-xs;

        &.status-draft {
          background-color: #f0f0f0;
          color: #666;
        }

        &.status-published {
          background-color: #f6ffed;
          color: #52c41a;
        }

        &.status-disabled {
          background-color: #fff2f0;
          color: #ff4d4f;
        }
      }
    }
  }

  .scale-actions {
    flex-shrink: 0;
  }

  .scale-description {
    margin-bottom: $spacing-md;

    text {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      line-height: 1.5;
    }
  }

  .scale-stats {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-md;
    padding: $spacing-sm 0;
    border-top: 1px solid $border-color-light;
    border-bottom: 1px solid $border-color-light;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-xs;

    .stat-label {
      font-size: $font-size-xs;
      color: $text-color-secondary;
    }

    .stat-value {
      font-size: $font-size-sm;
      font-weight: 600;
      color: $text-color-primary;
    }
  }

  .scale-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .creator-info {
    .creator-label {
      font-size: $font-size-xs;
      color: $text-color-secondary;
    }

    .creator-name {
      font-size: $font-size-xs;
      color: $text-color-primary;
    }
  }

  .quick-actions {
    display: flex;
    gap: $spacing-xs;
  }
}

.pagination-section {
  padding: $spacing-md;
}

.filter-content {
  padding: $spacing-md 0;

  .form-item {
    margin-bottom: $spacing-md;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;

    .action-buttons {
      justify-content: center;
    }
  }

  .scale-header {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  .scale-stats {
    flex-direction: column;
    gap: $spacing-sm;

    .stat-item {
      flex-direction: row;
      justify-content: space-between;
    }
  }

  .scale-footer {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;

    .quick-actions {
      justify-content: center;
    }
  }
}
</style>
