<template>
  <PageContainer :title="scaleDetail.name || '量表预览'" :show-back="true">
    <Loading v-if="loading" type="spinner" text="加载中..." />

    <Empty
      v-else-if="!scaleDetail.id"
      type="error"
      description="量表不存在或已被删除"
      :show-button="true"
      button-text="返回列表"
      @button-click="goBack"
    />

    <view v-else class="scale-preview">
      <!-- 量表信息 -->
      <Card class="scale-info-card">
        <view class="scale-info">
          <view class="scale-header">
            <text class="scale-name">{{ scaleDetail.name }}</text>
            <view class="scale-meta">
              <text class="scale-category">{{ scaleDetail.category }}</text>
              <text class="scale-version">v{{ scaleDetail.version }}</text>
            </view>
          </view>

          <text v-if="scaleDetail.description" class="scale-description">{{
            scaleDetail.description
          }}</text>

          <view class="scale-stats">
            <view class="stat-item">
              <text class="stat-label">题目数量</text>
              <text class="stat-value">{{ questionList.length }}题</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">预计时长</text>
              <text class="stat-value">{{ scaleDetail.estimatedTime }}分钟</text>
            </view>
          </view>

          <text v-if="scaleDetail.instructions" class="scale-instructions">{{
            scaleDetail.instructions
          }}</text>
        </view>
      </Card>

      <!-- 题目列表 -->
      <view class="questions-section">
        <view class="section-header">
          <text class="section-title">题目列表</text>
          <text class="section-subtitle">共 {{ questionList.length }} 道题目</text>
        </view>

        <view class="questions-list">
          <Card v-for="(question, index) in questionList" :key="question.id" class="question-card">
            <view class="question-content">
              <view class="question-header">
                <view class="question-number">
                  <text>{{ index + 1 }}</text>
                </view>
                <view class="question-info">
                  <view class="question-title-row">
                    <text class="question-title">{{ question.title }}</text>
                    <view v-if="question.required" class="question-required">
                      <text>必答</text>
                    </view>
                  </view>
                  <text v-if="question.description" class="question-description">{{
                    question.description
                  }}</text>
                </view>
              </view>

              <view v-if="question.image" class="question-image">
                <image
                  :src="question.image"
                  mode="aspectFit"
                  @click="previewImage(question.image)"
                />
              </view>

              <view class="question-answer">
                <!-- 单选题 -->
                <view v-if="question.type === 'single_choice'" class="choice-answer">
                  <view v-for="option in question.options" :key="option.id" class="option-item">
                    <view class="option-radio">
                      <view class="radio-circle" />
                    </view>
                    <view class="option-content">
                      <text class="option-text">{{ option.text }}</text>
                      <text v-if="option.score !== undefined" class="option-score"
                        >({{ option.score }}分)</text
                      >
                    </view>
                  </view>
                </view>

                <!-- 多选题 -->
                <view v-else-if="question.type === 'multiple_choice'" class="choice-answer">
                  <view v-for="option in question.options" :key="option.id" class="option-item">
                    <view class="option-checkbox">
                      <view class="checkbox-square" />
                    </view>
                    <view class="option-content">
                      <text class="option-text">{{ option.text }}</text>
                      <text v-if="option.score !== undefined" class="option-score"
                        >({{ option.score }}分)</text
                      >
                    </view>
                  </view>
                </view>

                <!-- 评分题 -->
                <view v-else-if="question.type === 'rating'" class="rating-answer">
                  <view class="rating-info">
                    <text class="rating-range"
                      >评分范围: {{ question.minScore }} - {{ question.maxScore }}分</text
                    >
                    <text v-if="question.step" class="rating-step">步长: {{ question.step }}</text>
                  </view>
                  <view class="rating-scale">
                    <view
                      v-for="score in getRatingScores(question)"
                      :key="score"
                      class="rating-item"
                    >
                      <view class="rating-circle" />
                      <text class="rating-label">{{ score }}</text>
                    </view>
                  </view>
                </view>

                <!-- 文本题 -->
                <view v-else-if="question.type === 'text'" class="text-answer">
                  <view class="text-input-placeholder">
                    <text>{{ question.placeholder || '请输入文本答案' }}</text>
                  </view>
                  <text v-if="question.maxLength" class="text-limit"
                    >最大长度: {{ question.maxLength }}字符</text
                  >
                </view>

                <!-- 数字题 -->
                <view v-else-if="question.type === 'number'" class="number-answer">
                  <view class="number-input-placeholder">
                    <text>请输入数字</text>
                    <text v-if="question.unit" class="number-unit">{{ question.unit }}</text>
                  </view>
                  <view class="number-range">
                    <text
                      >范围: {{ question.min || '无限制' }} - {{ question.max || '无限制' }}</text
                    >
                  </view>
                </view>
              </view>
            </view>
          </Card>
        </view>
      </view>

      <!-- 评分规则 -->
      <Card v-if="scaleDetail.scoringRules" title="评分规则" class="scoring-card">
        <view class="scoring-content">
          <view v-if="scaleDetail.scoringRules.totalScore" class="scoring-section">
            <text class="scoring-title">总分范围</text>
            <text class="scoring-text"
              >{{ scaleDetail.scoringRules.totalScore.min }} -
              {{ scaleDetail.scoringRules.totalScore.max }}分</text
            >
          </view>

          <view
            v-if="scaleDetail.scoringRules.levels && scaleDetail.scoringRules.levels.length > 0"
            class="scoring-section"
          >
            <text class="scoring-title">评估等级</text>
            <view class="levels-list">
              <view
                v-for="level in scaleDetail.scoringRules.levels"
                :key="level.id"
                class="level-item"
              >
                <view class="level-header">
                  <text class="level-name">{{ level.name }}</text>
                  <text class="level-range">{{ level.minScore }} - {{ level.maxScore }}分</text>
                </view>
                <text v-if="level.description" class="level-description">{{
                  level.description
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </Card>
    </view>

    <!-- 底部操作按钮 -->
    <view v-if="scaleDetail.id" class="bottom-actions">
      <Button type="default" @click="goBack"> 返回 </Button>

      <Button v-if="scaleDetail.status === 'published'" type="primary" @click="useScale">
        开始评估
      </Button>

      <Button v-else-if="scaleDetail.status === 'draft'" type="primary" @click="editScale">
        编辑量表
      </Button>
    </view>
  </PageContainer>
</template>

<script>
import { mapActions } from 'vuex'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import Button from '@/components/Common/Button.vue'
import Loading from '@/components/Common/Loading.vue'
import Empty from '@/components/Common/Empty.vue'

export default {
  name: 'ScalePreview',

  components: {
    PageContainer,
    Card,
    Button,
    Loading,
    Empty
  },

  data() {
    return {
      loading: false,
      scaleId: '',
      scaleDetail: {},
      questionList: []
    }
  },

  onLoad(options) {
    if (options.id) {
      this.scaleId = options.id
      this.loadScaleData()
    }
  },

  onPullDownRefresh() {
    this.loadScaleData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    ...mapActions('scale', ['getScaleDetail', 'getScaleQuestions']),

    async loadScaleData() {
      try {
        this.loading = true

        // 并行加载量表详情和题目
        const [detailResult, questionsResult] = await Promise.all([
          this.getScaleDetail(this.scaleId),
          this.getScaleQuestions(this.scaleId)
        ])

        this.scaleDetail = detailResult
        this.questionList = questionsResult
      } catch (error) {
        console.error('加载量表数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    getRatingScores(question) {
      const scores = []
      const step = question.step || 1

      for (let i = question.minScore; i <= question.maxScore; i += step) {
        scores.push(i)
      }

      return scores
    },

    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },

    editScale() {
      uni.navigateTo({
        url: `/pages/scale/create/index?id=${this.scaleId}`
      })
    },

    useScale() {
      uni.navigateTo({
        url: `/pages/assessment/create/index?scaleId=${this.scaleId}`
      })
    },

    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.scale-preview {
  padding: $spacing-md;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.scale-info-card {
  margin-bottom: $spacing-md;

  .scale-info {
    .scale-header {
      margin-bottom: $spacing-md;

      .scale-name {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-color-primary;
        display: block;
        margin-bottom: $spacing-xs;
      }

      .scale-meta {
        display: flex;
        gap: $spacing-sm;

        .scale-category {
          font-size: $font-size-sm;
          color: $text-color-secondary;
          padding: 2px 6px;
          background-color: $bg-color-light;
          border-radius: $border-radius-xs;
        }

        .scale-version {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }
    }

    .scale-description {
      font-size: $font-size-base;
      color: $text-color-secondary;
      line-height: 1.6;
      margin-bottom: $spacing-md;
    }

    .scale-stats {
      display: flex;
      gap: $spacing-lg;
      margin-bottom: $spacing-md;
      padding: $spacing-sm 0;
      border-top: 1px solid $border-color-light;
      border-bottom: 1px solid $border-color-light;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-xs;

      .stat-label {
        font-size: $font-size-xs;
        color: $text-color-secondary;
      }

      .stat-value {
        font-size: $font-size-base;
        font-weight: 600;
        color: $text-color-primary;
      }
    }

    .scale-instructions {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      line-height: 1.6;
      padding: $spacing-sm;
      background-color: $bg-color-light;
      border-radius: $border-radius-sm;
    }
  }
}

.questions-section {
  margin-bottom: $spacing-md;

  .section-header {
    margin-bottom: $spacing-md;

    .section-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-color-primary;
      display: block;
      margin-bottom: $spacing-xs;
    }

    .section-subtitle {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }

  .questions-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }
}

.question-card {
  .question-content {
    .question-header {
      display: flex;
      gap: $spacing-sm;
      margin-bottom: $spacing-md;

      .question-number {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $color-primary;
        color: white;
        border-radius: 50%;
        font-size: $font-size-sm;
        font-weight: 600;
        flex-shrink: 0;
      }

      .question-info {
        flex: 1;

        .question-title-row {
          display: flex;
          align-items: flex-start;
          gap: $spacing-xs;
          margin-bottom: $spacing-xs;

          .question-title {
            flex: 1;
            font-size: $font-size-base;
            color: $text-color-primary;
            line-height: 1.5;
            font-weight: 500;
          }

          .question-required {
            background-color: $color-danger;
            color: white;
            font-size: $font-size-xs;
            padding: 2px 6px;
            border-radius: $border-radius-xs;
            flex-shrink: 0;
          }
        }

        .question-description {
          font-size: $font-size-sm;
          color: $text-color-secondary;
          line-height: 1.5;
        }
      }
    }

    .question-image {
      margin-bottom: $spacing-md;

      image {
        width: 100%;
        max-height: 200px;
        border-radius: $border-radius-sm;
        cursor: pointer;
      }
    }

    .question-answer {
      .choice-answer {
        display: flex;
        flex-direction: column;
        gap: $spacing-sm;
      }

      .option-item {
        display: flex;
        align-items: flex-start;
        gap: $spacing-sm;
        padding: $spacing-sm;
        background-color: $bg-color-light;
        border-radius: $border-radius-sm;

        .option-radio,
        .option-checkbox {
          margin-top: 2px;
          flex-shrink: 0;
        }

        .radio-circle {
          width: 16px;
          height: 16px;
          border: 2px solid $border-color-base;
          border-radius: 50%;
          background-color: white;
        }

        .checkbox-square {
          width: 16px;
          height: 16px;
          border: 2px solid $border-color-base;
          border-radius: $border-radius-xs;
          background-color: white;
        }

        .option-content {
          flex: 1;
          display: flex;
          align-items: center;
          gap: $spacing-xs;

          .option-text {
            flex: 1;
            font-size: $font-size-base;
            color: $text-color-primary;
            line-height: 1.5;
          }

          .option-score {
            font-size: $font-size-xs;
            color: $color-primary;
            font-weight: 500;
          }
        }
      }

      .rating-answer {
        .rating-info {
          margin-bottom: $spacing-sm;

          text {
            display: block;
            font-size: $font-size-sm;
            color: $text-color-secondary;
            margin-bottom: $spacing-xs;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .rating-scale {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $spacing-md;
          background-color: $bg-color-light;
          border-radius: $border-radius-sm;
        }

        .rating-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: $spacing-xs;

          .rating-circle {
            width: 24px;
            height: 24px;
            border: 2px solid $border-color-base;
            border-radius: 50%;
            background-color: white;
          }

          .rating-label {
            font-size: $font-size-xs;
            color: $text-color-secondary;
          }
        }
      }

      .text-answer,
      .number-answer {
        .text-input-placeholder,
        .number-input-placeholder {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: $spacing-md;
          background-color: $bg-color-light;
          border: 1px solid $border-color-light;
          border-radius: $border-radius-sm;
          margin-bottom: $spacing-xs;

          text {
            font-size: $font-size-base;
            color: $text-color-placeholder;
          }

          .number-unit {
            color: $text-color-secondary;
            font-size: $font-size-sm;
          }
        }

        .text-limit,
        .number-range {
          font-size: $font-size-xs;
          color: $text-color-secondary;
        }
      }
    }
  }
}

.scoring-card {
  margin-bottom: $spacing-md;

  .scoring-content {
    .scoring-section {
      margin-bottom: $spacing-lg;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .scoring-title {
      display: block;
      font-size: $font-size-base;
      font-weight: 600;
      color: $text-color-primary;
      margin-bottom: $spacing-sm;
    }

    .scoring-text {
      font-size: $font-size-base;
      color: $text-color-secondary;
    }

    .levels-list {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
    }

    .level-item {
      padding: $spacing-sm;
      background-color: $bg-color-light;
      border-radius: $border-radius-sm;
    }

    .level-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-xs;
    }

    .level-name {
      font-size: $font-size-base;
      font-weight: 500;
      color: $text-color-primary;
    }

    .level-range {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }

    .level-description {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      line-height: 1.5;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $bg-color-white;
  border-top: 1px solid $border-color-light;
  z-index: 100;
}

// 响应式设计
@media (max-width: 768px) {
  .scale-stats {
    flex-direction: column;
    gap: $spacing-sm;

    .stat-item {
      flex-direction: row;
      justify-content: space-between;
    }
  }

  .rating-scale {
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  .bottom-actions {
    flex-direction: column;
  }
}
</style>
