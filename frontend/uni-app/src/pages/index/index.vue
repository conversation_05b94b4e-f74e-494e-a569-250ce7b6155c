<template>
  <PageContainer title="老年人能力评估系统" :show-back="false">
    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-grid">
        <Card
          v-for="(stat, index) in stats"
          :key="index"
          class="stat-card"
          :clickable="true"
          @click="handleStatClick(stat.type)"
        >
          <view class="stat-content">
            <view class="stat-icon" :style="{ backgroundColor: stat.color }">
              <text class="icon-text" style="font-size: 24px; color: #fff">◰</text>
            </view>
            <view class="stat-info">
              <text class="stat-number">{{ stat.value }}</text>
              <text class="stat-label">{{ stat.label }}</text>
            </view>
          </view>
        </Card>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="actions-section">
      <view class="section-title">
        <text>快捷操作</text>
      </view>

      <view class="actions-grid">
        <view
          v-for="(action, index) in quickActions"
          :key="index"
          class="action-item"
          @click="handleActionClick(action.path)"
        >
          <view class="action-icon" :style="{ backgroundColor: action.color }">
            <text class="icon-text" style="font-size: 28px; color: #fff">⚡</text>
          </view>
          <text class="action-label">{{ action.label }}</text>
        </view>
      </view>
    </view>

    <!-- 最近评估 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近评估</text>
        <text class="section-more" @click="navigateTo('/pages/assessment/records/index')"
          >查看更多</text
        >
      </view>

      <Card v-if="recentAssessments.length > 0">
        <List>
          <ListItem
            v-for="(assessment, index) in recentAssessments"
            :key="index"
            :title="assessment.elderlyName"
            :description="`${assessment.scaleName} · ${assessment.createTime}`"
            :value="getStatusText(assessment.status)"
            :show-arrow="true"
            @click="handleAssessmentClick(assessment)"
          >
            <template #prefix>
              <view class="assessment-status" :class="`status-${assessment.status}`" />
            </template>
          </ListItem>
        </List>
      </Card>

      <Empty
        v-else
        type="nodata"
        description="暂无评估记录"
        button-text="开始评估"
        @button-click="navigateTo('/pages/assessment/create/index')"
      />
    </view>

    <!-- 系统公告 -->
    <view v-if="notices.length > 0" class="notice-section">
      <view class="section-title">
        <text>系统公告</text>
      </view>

      <Card>
        <List>
          <ListItem
            v-for="(notice, index) in notices"
            :key="index"
            :title="notice.title"
            :description="notice.createTime"
            :show-arrow="true"
            @click="handleNoticeClick(notice)"
          >
            <template #prefix>
              <text class="icon-text" style="font-size: 20px; color: #ff6b35">🔔</text>
            </template>
          </ListItem>
        </List>
      </Card>
    </view>
  </PageContainer>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import PageContainer from '@/components/Layout/PageContainer.vue'
import Card from '@/components/Common/Card.vue'
import List from '@/components/Common/List.vue'
import ListItem from '@/components/Common/ListItem.vue'
import Empty from '@/components/Common/Empty.vue'

export default {
  name: 'IndexPage',

  components: {
    PageContainer,
    Card,
    List,
    ListItem,
    Empty
  },

  data() {
    return {
      stats: [
        {
          type: 'elderly',
          label: '老年人总数',
          value: 0,
          icon: 'person',
          color: '#409eff'
        },
        {
          type: 'assessment',
          label: '评估总数',
          value: 0,
          icon: 'list',
          color: '#67c23a'
        },
        {
          type: 'today',
          label: '今日评估',
          value: 0,
          icon: 'calendar',
          color: '#e6a23c'
        },
        {
          type: 'pending',
          label: '待完成',
          value: 0,
          icon: 'clock',
          color: '#f56c6c'
        }
      ],

      quickActions: [
        {
          label: '新增老年人',
          icon: 'person-add',
          color: '#409eff',
          path: '/pages/elderly/create/index'
        },
        {
          label: '开始评估',
          icon: 'compose',
          color: '#67c23a',
          path: '/pages/assessment/create/index'
        },
        {
          label: '评估记录',
          icon: 'list',
          color: '#e6a23c',
          path: '/pages/assessment/records/index'
        },
        {
          label: '量表管理',
          icon: 'gear',
          color: '#909399',
          path: '/pages/scale/index'
        }
      ],

      recentAssessments: [],
      notices: []
    }
  },

  computed: {
    ...mapState('user', ['userInfo']),
    ...mapState('config', ['systemSettings'])
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    ...mapActions('assessment', ['getAssessmentStatistics', 'getRecentAssessments']),
    ...mapActions('elderly', ['getElderlyStatistics']),

    async initPage() {
      try {
        uni.showLoading({ title: '加载中...' })
        await this.loadStatistics()
        await this.loadRecentAssessments()
        await this.loadNotices()
      } catch (error) {
        console.error('页面初始化失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
      }
    },

    async refreshData() {
      try {
        await Promise.all([this.loadStatistics(), this.loadRecentAssessments()])
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    },

    async loadStatistics() {
      try {
        const [elderlyStats, assessmentStats] = await Promise.all([
          this.getElderlyStatistics(),
          this.getAssessmentStatistics()
        ])

        this.stats[0].value = elderlyStats.total || 0
        this.stats[1].value = assessmentStats.total || 0
        this.stats[2].value = assessmentStats.today || 0
        this.stats[3].value = assessmentStats.pending || 0
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    async loadRecentAssessments() {
      try {
        const result = await this.getRecentAssessments({ limit: 5 })
        this.recentAssessments = result.list || []
      } catch (error) {
        console.error('加载最近评估失败:', error)
        this.recentAssessments = []
      }
    },

    async loadNotices() {
      try {
        // 模拟公告数据
        this.notices = [
          {
            id: 1,
            title: '系统维护通知',
            content: '系统将于今晚22:00-24:00进行维护升级',
            createTime: '2024-01-15 10:30'
          }
        ]
      } catch (error) {
        console.error('加载公告失败:', error)
        this.notices = []
      }
    },

    handleStatClick(type) {
      const routes = {
        elderly: '/pages/elderly/index',
        assessment: '/pages/assessment/records/index',
        today: '/pages/assessment/records/index?filter=today',
        pending: '/pages/assessment/records/index?filter=pending'
      }

      const route = routes[type]
      if (route) {
        this.navigateTo(route)
      }
    },

    handleActionClick(path) {
      this.navigateTo(path)
    },

    handleAssessmentClick(assessment) {
      if (assessment.status === 'pending' || assessment.status === 'in_progress') {
        // 继续评估
        this.navigateTo(`/pages/assessment/conduct/index?id=${assessment.id}`)
      } else {
        // 查看评估结果
        this.navigateTo(`/pages/assessment/result/index?id=${assessment.id}`)
      }
    },

    handleNoticeClick(notice) {
      uni.showModal({
        title: notice.title,
        content: notice.content,
        showCancel: false
      })
    },

    navigateTo(url) {
      uni.navigateTo({ url })
    },

    getStatusText(status) {
      const statusMap = {
        pending: '待开始',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/common/styles/variables.scss' as *;
@use '@/common/styles/mixins.scss' as *;

.stats-section {
  margin-bottom: $spacing-lg;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;

  @media (max-width: 768px) {
    gap: $spacing-sm;
  }
}

.stat-card {
  :deep(.card-body) {
    padding: $spacing-md;
  }
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacing-md;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-number {
  display: block;
  font-size: $font-size-xl;
  font-weight: 600;
  color: $text-color-primary;
  line-height: 1.2;
  margin-bottom: $spacing-xs;
}

.stat-label {
  display: block;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  line-height: 1.2;
}

.actions-section {
  margin-bottom: $spacing-lg;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-color-primary;
  margin-bottom: $spacing-md;
  padding: 0 $spacing-sm;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $spacing-md;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-lg $spacing-sm;
  background-color: $bg-color-white;
  border-radius: $border-radius-base;
  border: 1px solid $border-color-light;
  transition: all 0.2s;
  cursor: pointer;

  &:hover {
    border-color: $primary-color;
    box-shadow: $box-shadow-light;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $spacing-sm;
}

.action-label {
  font-size: $font-size-sm;
  color: $text-color-primary;
  text-align: center;
  line-height: 1.2;
}

.recent-section {
  margin-bottom: $spacing-lg;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-md;
  padding: 0 $spacing-sm;
}

.section-more {
  font-size: $font-size-sm;
  color: $primary-color;
  cursor: pointer;

  &:hover {
    color: color.adjust($primary-color, $lightness: -10%);
  }
}

.assessment-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.status-pending {
    background-color: $warning-color;
  }

  &.status-in_progress {
    background-color: $primary-color;
  }

  &.status-completed {
    background-color: $success-color;
  }

  &.status-cancelled {
    background-color: $info-color;
  }
}

.notice-section {
  margin-bottom: $spacing-lg;
}

// 响应式设计
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-content {
    padding: $spacing-sm;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    margin-right: $spacing-sm;
  }

  .stat-number {
    font-size: $font-size-lg;
  }

  .action-item {
    padding: $spacing-md $spacing-sm;
  }

  .action-icon {
    width: 48px;
    height: 48px;
  }
}
</style>
