import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },
    {
      path: '/pdf-import',
      name: 'pdf-import',
      component: () => import('../pages/pdf-import/index.vue'),
      meta: {
        title: 'PDF量表导入',
        icon: 'Upload',
      },
    },
  ],
});

export default router;
