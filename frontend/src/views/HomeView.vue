<template>
  <div class="home">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <span>欢迎使用智慧养老评估平台</span>
        </div>
      </template>

      <div class="welcome-content">
        <p>
          这是一个面向养老机构、社区服务中心和医疗机构的综合数字化评估解决方案。
        </p>

        <div class="feature-grid">
          <el-card class="feature-card" shadow="hover">
            <el-icon size="32" color="#409eff">
              <DocumentChecked />
            </el-icon>
            <h3>标准化评估</h3>
            <p>支持多种评估量表，保证评估结果的专业性</p>
          </el-card>

          <el-card class="feature-card" shadow="hover">
            <el-icon size="32" color="#67c23a">
              <Monitor />
            </el-icon>
            <h3>智能化管理</h3>
            <p>智能数据分析，提供个性化的照护建议</p>
          </el-card>

          <el-card class="feature-card" shadow="hover">
            <el-icon size="32" color="#e6a23c">
              <Cellphone />
            </el-icon>
            <h3>移动便捷</h3>
            <p>支持移动设备，随时随地进行评估</p>
          </el-card>
        </div>

        <div class="action-buttons">
          <el-button
            type="primary"
            size="large"
            @click="$router.push('/pdf-import')"
          >
            <el-icon><Upload /></el-icon>
            开始使用
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  DocumentChecked,
  Monitor,
  Cellphone,
  Upload,
} from '@element-plus/icons-vue';
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.welcome-content {
  text-align: center;
}

.welcome-content > p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.feature-card {
  text-align: center;
  padding: 20px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  margin: 15px 0 10px 0;
  color: #333;
  font-size: 1.1rem;
}

.feature-card p {
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

.action-buttons {
  margin-top: 40px;
}

.action-buttons .el-button {
  padding: 15px 30px;
  font-size: 1rem;
}
</style>
