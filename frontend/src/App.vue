<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>智慧养老评估平台</h1>
        <nav>
          <router-link to="/">首页</router-link>
          <router-link to="/pdf-import">PDF导入</router-link>
        </nav>
      </el-header>
      <el-main>
        <router-view />
      </el-main>
      <el-footer class="footer">
        <div class="copyright">
          <p>© 2025 海南长小养智能科技有限责任公司 版权所有</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup lang="ts">
// 主应用程序根组件
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  min-height: 100vh;
  font-family:
    'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.el-header {
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.el-header h1 {
  margin: 0;
  font-size: 1.5rem;
}

.el-header nav {
  display: flex;
  gap: 20px;
}

.el-header nav a {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-header nav a:hover,
.el-header nav a.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.el-main {
  padding: 20px;
}

.footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 0;
  height: auto;
}

.copyright {
  text-align: center;
  padding: 16px 0;
}

.copyright p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}
</style>
