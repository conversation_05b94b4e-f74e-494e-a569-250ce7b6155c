<template>
  <div class="pdf-import">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>PDF量表导入</span>
        </div>
      </template>

      <div class="import-content">
        <el-alert
          title="功能正在开发中"
          type="info"
          description="PDF量表导入功能正在积极开发中，敬请期待。"
          show-icon
          :closable="false"
        />

        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF 格式文件，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </div>

        <div class="action-buttons">
          <el-button type="primary" disabled>
            <el-icon><DocumentAdd /></el-icon>
            开始导入
          </el-button>
          <el-button @click="$router.push('/')">
            <el-icon><Back /></el-icon>
            返回首页
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { UploadFilled, DocumentAdd, Back } from '@element-plus/icons-vue';
</script>

<style scoped>
.pdf-import {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.import-content {
  padding: 20px;
}

.upload-area {
  margin: 30px 0;
}

.upload-demo {
  width: 100%;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.action-buttons .el-button {
  margin: 0 10px;
  padding: 12px 24px;
}
</style>
