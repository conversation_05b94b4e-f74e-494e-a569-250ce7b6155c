import { defineStore } from 'pinia';

interface AppState {
  title: string;
  version: string;
  loading: boolean;
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    title: '智慧养老评估平台',
    version: '1.0.0',
    loading: false,
  }),

  getters: {
    appInfo: state => `${state.title} v${state.version}`,
  },

  actions: {
    setLoading(loading: boolean) {
      this.loading = loading;
    },

    setTitle(title: string) {
      this.title = title;
    },
  },
});
