import { defineStore } from 'pinia';

interface User {
  id?: string;
  username?: string;
  email?: string;
  role?: string;
}

interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    currentUser: null,
    isAuthenticated: false,
  }),

  getters: {
    userInfo: state => state.currentUser,
    hasRole: state => (role: string) => state.currentUser?.role === role,
  },

  actions: {
    setUser(user: User) {
      this.currentUser = user;
      this.isAuthenticated = true;
    },

    logout() {
      this.currentUser = null;
      this.isAuthenticated = false;
    },
  },
});
