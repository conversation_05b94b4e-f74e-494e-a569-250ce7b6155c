// 认证测试脚本 - 在浏览器Console中执行

console.log('🧪 开始认证系统测试...');

// 1. 强制清理所有认证数据
localStorage.clear();
sessionStorage.clear();
console.log('✅ 第1步：所有认证数据已清理');

// 2. 检查当前页面
console.log('📍 第2步：当前页面:', window.location.href);

// 3. 强制触发路由检查
setTimeout(() => {
  console.log('🔄 第3步：触发路由重新检查...');
  
  // 尝试访问需要认证的页面
  if (window.location.pathname !== '/login') {
    console.log('🚨 问题：当前不在登录页，路由守卫可能没有工作');
    console.log('🔧 手动跳转到登录页...');
    window.location.href = '/login';
  } else {
    console.log('✅ 正常：已在登录页面');
  }
}, 1000);

// 4. 检查Vue Router是否存在
setTimeout(() => {
  console.log('🔍 第4步：检查Vue Router...');
  
  if (window.__VUE__) {
    console.log('✅ Vue实例存在');
  } else {
    console.log('❌ Vue实例不存在或未在开发模式');
  }
  
  // 检查路由器
  if (window.$router) {
    console.log('✅ 路由器存在');
  } else {
    console.log('⚠️ 无法直接访问路由器，这是正常的');
  }
}, 2000);

console.log('⏳ 测试进行中，请等待结果...');