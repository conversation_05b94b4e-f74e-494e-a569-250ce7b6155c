<template>
  <div class="page-container">
    <el-container class="min-h-screen">
      <el-header class="header-container" height="64px">
        <div class="h-full flex items-center justify-center shadow-lg" style="background-color: var(--el-color-primary);">
          <h1 class="text-2xl font-bold m-0 flex items-center justify-center" style="color: white !important;">
            <BuildingOffice2Icon class="h-6 w-6 mr-2" />
            智慧养老评估平台 - 管理后台
          </h1>
        </div>
      </el-header>
      <el-main class="flex-1 p-0">
        <router-view />
      </el-main>
      <el-footer class="bg-primary-50 border-t border-primary-200 p-0 h-auto">
        <div class="text-center py-4">
          <p class="text-sm text-primary-700 m-0">
            © 2025 海南长小养智能科技有限责任公司 版权所有
          </p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { BuildingOffice2Icon } from '@heroicons/vue/24/outline';
// 简单的App根组件
</script>

<style scoped>
.header-container {
  padding: 0 !important;
  background: none !important;
}

.el-container {
  height: 100vh;
}

.el-main {
  padding: 0;
}
</style>
