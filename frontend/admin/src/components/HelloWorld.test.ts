// Basic Vue component test
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import { createApp } from 'vue';

// Mock component for testing
const HelloWorld = {
  name: 'HelloWorld',
  props: {
    msg: String,
  },
  template: '<div>{{ msg }}</div>',
};

describe('HelloWorld Component', () => {
  it('renders properly', () => {
    const wrapper = mount(HelloWorld, { props: { msg: 'Hello Vitest' } });
    expect(wrapper.text()).toContain('Hello Vitest');
  });

  it('should create Vue app instance', () => {
    const app = createApp({});
    expect(app).toBeDefined();
  });

  it('should handle props correctly', () => {
    const msg = 'Test Message';
    const wrapper = mount(HelloWorld, { props: { msg } });
    expect(wrapper.props('msg')).toBe(msg);
  });
});
