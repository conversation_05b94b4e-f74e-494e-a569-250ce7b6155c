<template>
  <el-dialog
    v-model="visible"
    :title="`编辑字段: ${field?.suggestedName || '未知字段'}`"
    width="800px"
    @close="onClose"
  >
    <el-form
      v-if="field"
      :model="editForm"
      :rules="formRules"
      ref="formRef"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <el-card class="section-card">
        <template #header>
          <h4>基本信息</h4>
        </template>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="字段名称" prop="name" required>
              <el-input
                v-model="editForm.name"
                placeholder="请输入字段名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字段类型" prop="type" required>
              <el-select
                v-model="editForm.type"
                placeholder="选择字段类型"
                style="width: 100%"
                @change="onTypeChange"
              >
                <el-option
                  v-for="type in fieldTypes"
                  :key="type.type"
                  :label="type.displayName"
                  :value="type.type"
                >
                  <span>{{ type.displayName }}</span>
                  <span style="float: right; color: #999; font-size: 12px">
                    {{ type.description }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="是否必填">
              <el-switch
                v-model="editForm.required"
                active-text="必填"
                inactive-text="选填"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字段权重">
              <el-input-number
                v-model="editForm.weight"
                :min="0"
                :max="1"
                :step="0.1"
                :precision="2"
                style="width: 100%"
                placeholder="0.1 - 1.0"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="字段描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入字段描述"
          />
        </el-form-item>
      </el-card>

      <!-- 选项设置（用于选择类型字段） -->
      <el-card class="section-card" v-if="isSelectType(editForm.type)">
        <template #header>
          <div class="card-header">
            <h4>选项设置</h4>
            <el-button type="primary" size="small" @click="addOption">
              + 添加选项
            </el-button>
          </div>
        </template>

        <div class="options-container">
          <div
            v-for="(option, index) in editForm.options"
            :key="index"
            class="option-item"
          >
            <el-row :gutter="16" align="middle">
              <el-col :span="8">
                <el-input v-model="option.label" placeholder="选项文本" />
              </el-col>
              <el-col :span="6">
                <el-input v-model="option.value" placeholder="选项值" />
              </el-col>
              <el-col :span="6">
                <el-input-number
                  v-model="option.score"
                  placeholder="分值"
                  :min="0"
                  style="width: 100%"
                />
              </el-col>
              <el-col :span="4">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeOption(index)"
                  :disabled="editForm.options.length <= 1"
                >
                  删除
                </el-button>
              </el-col>
            </el-row>

            <el-row v-if="option.description !== undefined">
              <el-col :span="20">
                <el-input
                  v-model="option.description"
                  placeholder="选项描述（可选）"
                  size="small"
                  style="margin-top: 8px"
                />
              </el-col>
            </el-row>
          </div>

          <el-empty
            v-if="editForm.options.length === 0"
            description="暂无选项"
            :image-size="80"
          />
        </div>
      </el-card>

      <!-- 验证规则 -->
      <el-card class="section-card">
        <template #header>
          <h4>验证规则</h4>
        </template>

        <!-- 文本类型验证 -->
        <el-row v-if="isTextType(editForm.type)" :gutter="16">
          <el-col :span="8">
            <el-form-item label="最小长度">
              <el-input-number
                v-model="editForm.validation.minLength"
                :min="0"
                placeholder="最小长度"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大长度">
              <el-input-number
                v-model="editForm.validation.maxLength"
                :min="1"
                placeholder="最大长度"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="正则模式">
              <el-input
                v-model="editForm.validation.pattern"
                placeholder="正则表达式"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 数字类型验证 -->
        <el-row v-if="isNumberType(editForm.type)" :gutter="16">
          <el-col :span="12">
            <el-form-item label="最小值">
              <el-input-number
                v-model="editForm.validation.minValue"
                placeholder="最小值"
                style="width: 100%"
                :precision="getNumberPrecision(editForm.type)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大值">
              <el-input-number
                v-model="editForm.validation.maxValue"
                placeholder="最大值"
                style="width: 100%"
                :precision="getNumberPrecision(editForm.type)"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 预设模式 -->
        <el-form-item label="预设模式">
          <el-select
            v-model="selectedPattern"
            placeholder="选择预设验证模式"
            clearable
            @change="applyPresetPattern"
            style="width: 100%"
          >
            <el-option label="中国手机号" value="phone" />
            <el-option label="邮箱地址" value="email" />
            <el-option label="身份证号" value="idCard" />
            <el-option label="URL链接" value="url" />
            <el-option label="IPv4地址" value="ipv4" />
            <el-option label="银行卡号" value="bankCard" />
          </el-select>
        </el-form-item>

        <!-- 自定义规则 -->
        <el-form-item label="自定义规则">
          <el-tag
            v-for="(rule, index) in editForm.validation.customRules"
            :key="index"
            closable
            @close="removeCustomRule(index)"
            style="margin: 2px"
          >
            {{ rule }}
          </el-tag>

          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            style="width: 200px; margin-left: 10px"
            @keyup.enter="addCustomRule"
            @blur="addCustomRule"
          />
          <el-button
            v-else
            size="small"
            @click="showInput"
            style="margin-left: 10px"
          >
            + 添加规则
          </el-button>
        </el-form-item>
      </el-card>

      <!-- 高级设置 -->
      <el-card class="section-card">
        <template #header>
          <h4>高级设置</h4>
        </template>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="占位符文本">
              <el-input
                v-model="editForm.placeholder"
                placeholder="请输入占位符文本"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="帮助提示">
              <el-input
                v-model="editForm.helpText"
                placeholder="请输入帮助提示"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="默认值">
              <el-input
                v-model="editForm.defaultValue"
                placeholder="请输入默认值"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字段分组">
              <el-input v-model="editForm.group" placeholder="请输入字段分组" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="管理员备注">
          <el-input
            v-model="editForm.adminNotes"
            type="textarea"
            :rows="3"
            placeholder="请输入管理员备注"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSave" :loading="saving">
          保存
        </el-button>
        <el-button type="primary" @click="onSaveAndConfirm" :loading="saving">
          保存并确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';

// Props
const props = defineProps({
  modelValue: Boolean,
  field: Object,
  fieldTypes: Array,
});

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'saveAndConfirm']);

// 响应式数据
const visible = ref(false);
const saving = ref(false);
const formRef = ref();
const inputRef = ref();
const inputVisible = ref(false);
const inputValue = ref('');
const selectedPattern = ref('');

// 编辑表单
const editForm = reactive({
  name: '',
  type: 'TEXT',
  description: '',
  required: false,
  weight: 0.1,
  options: [],
  validation: {
    minValue: null,
    maxValue: null,
    minLength: null,
    maxLength: null,
    pattern: '',
    customRules: [],
  },
  placeholder: '',
  helpText: '',
  defaultValue: '',
  group: '',
  adminNotes: '',
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  type: [{ required: true, message: '请选择字段类型', trigger: 'change' }],
};

// 预设验证模式
const presetPatterns = {
  phone: {
    pattern: '^1[3-9]\\d{9}$',
    minLength: 11,
    maxLength: 11,
  },
  email: {
    pattern: '^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$',
    minLength: 5,
    maxLength: 100,
  },
  idCard: {
    pattern:
      '^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$',
    minLength: 18,
    maxLength: 18,
  },
  url: {
    pattern:
      '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$',
  },
  ipv4: {
    pattern:
      '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
  },
  bankCard: {
    pattern: '^[1-9]\\d{12,19}$',
    minLength: 13,
    maxLength: 20,
  },
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal && props.field) {
      initForm();
    }
  }
);

// 监听 visible 变化
watch(visible, newVal => {
  emit('update:modelValue', newVal);
});

// 初始化表单
const initForm = () => {
  const field = props.field;
  if (!field) return;

  Object.assign(editForm, {
    name: field.suggestedName || '',
    type: field.suggestedType || 'TEXT',
    description: field.suggestedDescription || '',
    required: field.required || false,
    weight: field.suggestedWeight || 0.1,
    options: field.suggestedOptions ? [...field.suggestedOptions] : [],
    validation: field.suggestedValidation
      ? {
          ...field.suggestedValidation,
          customRules: field.suggestedValidation.customRules || [],
        }
      : {
          minValue: null,
          maxValue: null,
          minLength: null,
          maxLength: null,
          pattern: '',
          customRules: [],
        },
    placeholder: field.placeholder || '',
    helpText: field.helpText || '',
    defaultValue: field.defaultValue || '',
    group: field.group || '',
    adminNotes: field.adminNotes || '',
  });

  // 确保选择类型字段有默认选项
  if (isSelectType(editForm.type) && editForm.options.length === 0) {
    addOption();
  }
};

// 字段类型变化处理
const onTypeChange = () => {
  // 重置验证规则
  editForm.validation = {
    minValue: null,
    maxValue: null,
    minLength: null,
    maxLength: null,
    pattern: '',
    customRules: [],
  };

  // 为选择类型字段添加默认选项
  if (isSelectType(editForm.type) && editForm.options.length === 0) {
    addOption();
  }

  // 清除非选择类型字段的选项
  if (!isSelectType(editForm.type)) {
    editForm.options = [];
  }

  // 设置默认验证规则
  setDefaultValidation();
};

// 设置默认验证规则
const setDefaultValidation = () => {
  switch (editForm.type) {
    case 'TEXT':
    case 'TEXTAREA':
      editForm.validation.minLength = 1;
      editForm.validation.maxLength = 500;
      break;
    case 'NUMBER':
    case 'INTEGER':
      editForm.validation.minValue = 0;
      editForm.validation.maxValue = 100;
      break;
    case 'RATING':
      editForm.validation.minValue = 1;
      editForm.validation.maxValue = 5;
      break;
    case 'EMAIL':
      editForm.validation.pattern = presetPatterns.email.pattern;
      editForm.validation.minLength = presetPatterns.email.minLength;
      editForm.validation.maxLength = presetPatterns.email.maxLength;
      break;
    case 'PHONE':
      editForm.validation.pattern = presetPatterns.phone.pattern;
      editForm.validation.minLength = presetPatterns.phone.minLength;
      editForm.validation.maxLength = presetPatterns.phone.maxLength;
      break;
  }
};

// 添加选项
const addOption = () => {
  const optionCount = editForm.options.length;
  editForm.options.push({
    label: `选项${optionCount + 1}`,
    value: `option${optionCount + 1}`,
    score: optionCount + 1,
    description: '',
  });
};

// 移除选项
const removeOption = index => {
  editForm.options.splice(index, 1);
};

// 应用预设模式
const applyPresetPattern = () => {
  if (!selectedPattern.value) return;

  const preset = presetPatterns[selectedPattern.value];
  if (preset) {
    Object.assign(editForm.validation, preset);
    ElMessage.success('预设模式已应用');
  }

  selectedPattern.value = '';
};

// 显示自定义规则输入框
const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    inputRef.value?.focus();
  });
};

// 添加自定义规则
const addCustomRule = () => {
  if (inputValue.value.trim()) {
    editForm.validation.customRules.push(inputValue.value.trim());
    inputValue.value = '';
  }
  inputVisible.value = false;
};

// 移除自定义规则
const removeCustomRule = index => {
  editForm.validation.customRules.splice(index, 1);
};

// 保存
const onSave = async () => {
  try {
    await formRef.value?.validate();
    saving.value = true;

    const fieldData = {
      ...editForm,
      originalText: props.field?.originalText,
    };

    emit('save', fieldData);
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    saving.value = false;
  }
};

// 保存并确认
const onSaveAndConfirm = async () => {
  try {
    await formRef.value?.validate();
    saving.value = true;

    const fieldData = {
      ...editForm,
      originalText: props.field?.originalText,
    };

    emit('saveAndConfirm', fieldData);
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    saving.value = false;
  }
};

// 取消
const onCancel = () => {
  visible.value = false;
};

// 关闭
const onClose = () => {
  visible.value = false;
};

// 工具方法
const isSelectType = type => {
  return ['SELECT', 'MULTI_SELECT', 'RADIO', 'CHECKBOX'].includes(type);
};

const isTextType = type => {
  return ['TEXT', 'TEXTAREA', 'EMAIL', 'PHONE', 'URL'].includes(type);
};

const isNumberType = type => {
  return ['NUMBER', 'INTEGER', 'RATING', 'SLIDER'].includes(type);
};

const getNumberPrecision = type => {
  return type === 'INTEGER' ? 0 : 2;
};
</script>

<style scoped>
.section-card {
  margin-bottom: 20px;
}

.section-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h4 {
  margin: 0;
  color: #303133;
}

.options-container {
  min-height: 100px;
}

.option-item {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #fafbfc;
}

.option-item:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__header) {
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
