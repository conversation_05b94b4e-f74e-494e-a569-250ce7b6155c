import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UploadStatusTracker from '../UploadStatusTracker.vue'

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn()
  }
}))

describe('UploadStatusTracker', () => {
  let wrapper: ReturnType<typeof mount>
  
  beforeEach(() => {
    wrapper = mount(UploadStatusTracker)
  })

  it('should render service status panel', () => {
    expect(wrapper.find('.service-status-panel').exists()).toBe(true)
  })

  it('should display service name', () => {
    expect(wrapper.text()).toContain('服务状态')
  })

  it('should handle loading state', async () => {
    expect(wrapper.vm.loading).toBeDefined()
  })

  it('should display status indicators', () => {
    const statusElements = wrapper.findAll('.status-indicator')
    expect(statusElements.length).toBeGreaterThan(0)
  })

  it('should handle refresh action', async () => {
    const refreshButton = wrapper.find('[data-test="refresh-button"]')
    if (refreshButton.exists()) {
      await refreshButton.trigger('click')
      expect(wrapper.vm.loading).toBe(true)
    }
  })
})