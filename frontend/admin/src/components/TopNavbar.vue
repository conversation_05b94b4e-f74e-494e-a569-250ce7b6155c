<template>
  <div class="top-navbar">
    <div class="navbar-left">
      <el-button @click="goHome" size="small" type="primary" link>
        <HomeIcon class="h-4 w-4 mr-1" />
        返回首页
      </el-button>
      <span class="page-title">{{ title }}</span>
    </div>
    <div class="navbar-right">
      <span class="user-welcome">{{ userInfo?.realName || '管理员' }}</span>
      <el-button @click="handleLogout" size="small" type="danger"
        >退出登录</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { HomeIcon } from '@heroicons/vue/24/outline';

// Props
defineProps({
  title: {
    type: String,
    default: '智慧养老评估平台',
  },
});

const router = useRouter();
const userInfo = ref(null);

// 返回首页
const goHome = () => {
  router.push('/');
};

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    // 清理认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('refreshToken');
    sessionStorage.clear();

    ElMessage.success('已退出登录');

    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    // 用户取消
  }
};

// 获取用户信息
onMounted(() => {
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      userInfo.value = JSON.parse(userStr);
    } catch (error) {
      console.error('解析用户信息失败:', error);
    }
  }
});
</script>

<style scoped>
.top-navbar {
  background: white;
  padding: 12px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-welcome {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}
</style>
