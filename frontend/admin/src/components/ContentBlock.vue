<template>
  <div
    :class="[
      'content-block',
      `content-block--${block.type}`,
      { 'content-block--incomplete': !block.isComplete },
    ]"
  >
    <!-- 思考过程块 -->
    <div v-if="block.type === 'thought'" class="thought-block">
      <div class="block-header">
        <el-icon class="block-icon"><Monitor /></el-icon>
        <span class="block-title">AI思考过程</span>
        <el-tag
          v-if="!block.isComplete"
          type="info"
          size="small"
          effect="light"
        >
          <el-icon class="is-loading"><Loading /></el-icon>
          分析中...
        </el-tag>
      </div>
      <div class="block-content">
        <p
          v-for="(line, index) in formattedThought"
          :key="index"
          class="thought-line"
        >
          {{ line }}
        </p>
        <div v-if="!block.isComplete" class="typing-indicator">
          <span class="typing-dot"></span>
          <span class="typing-dot"></span>
          <span class="typing-dot"></span>
        </div>
      </div>
    </div>

    <!-- 代码块 -->
    <div v-else-if="block.type === 'code'" class="code-block">
      <div class="block-header">
        <el-icon class="block-icon"><Document /></el-icon>
        <span class="block-title">{{ getLanguageTitle(block.language) }}</span>
        <el-tag :type="getLanguageTagType(block.language)" size="small">{{
          block.language?.toUpperCase()
        }}</el-tag>
        <div class="block-actions">
          <el-button size="small" text @click="copyCode">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
        </div>
      </div>
      <div class="block-content">
        <pre><code :class="`language-${block.language}`" v-html="highlightedCode"></code></pre>
        <div v-if="!block.isComplete" class="typing-indicator code-typing">
          <span class="typing-cursor">|</span>
        </div>
      </div>
    </div>

    <!-- 普通文本块 -->
    <div v-else class="text-block">
      <div class="block-content">
        <div v-html="formattedText" class="text-content"></div>
        <div v-if="!block.isComplete" class="typing-indicator">
          <span class="typing-dot"></span>
          <span class="typing-dot"></span>
          <span class="typing-dot"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Document,
  CopyDocument,
  Loading,
  Monitor,
} from '@element-plus/icons-vue';
import hljs from 'highlight.js/lib/core';

// 导入常用语言
import javascript from 'highlight.js/lib/languages/javascript';
import sql from 'highlight.js/lib/languages/sql';
import json from 'highlight.js/lib/languages/json';
import python from 'highlight.js/lib/languages/python';
import markdown from 'highlight.js/lib/languages/markdown';

// 注册语言
hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('json', json);
hljs.registerLanguage('python', python);
hljs.registerLanguage('markdown', markdown);

import type { ContentBlock } from '@/types/content-block';

interface Props {
  block: ContentBlock;
}

const props = defineProps<Props>();

// 格式化思考过程
const formattedThought = computed(() => {
  return props.block.content.split('\n').filter(line => line.trim());
});

// 语法高亮
const highlightedCode = computed(() => {
  if (!props.block.content || !props.block.language) {
    return props.block.content;
  }

  try {
    if (hljs.getLanguage(props.block.language)) {
      return hljs.highlight(props.block.content, {
        language: props.block.language,
        ignoreIllegals: true,
      }).value;
    }
  } catch (error) {
    console.warn('代码高亮失败:', error);
  }

  return props.block.content;
});

// 格式化普通文本（支持简单的markdown）
const formattedText = computed(() => {
  let text = props.block.content;

  // 简单的markdown处理
  text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
  text = text.replace(/`(.*?)`/g, '<code>$1</code>');
  text = text.replace(/\n/g, '<br>');

  // 处理标题
  text = text.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  text = text.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  text = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');

  return text;
});

// 获取语言标题
const getLanguageTitle = (language?: string): string => {
  const titles: Record<string, string> = {
    sql: 'SQL建表语句',
    json: 'JSON数据结构',
    javascript: 'JavaScript代码',
    python: 'Python脚本',
    markdown: 'Markdown文档',
  };
  return titles[language || ''] || '代码片段';
};

// 获取语言标签类型
const getLanguageTagType = (language?: string): string => {
  const types: Record<string, string> = {
    sql: 'primary',
    json: 'success',
    javascript: 'warning',
    python: 'info',
    markdown: 'danger',
  };
  return types[language || ''] || 'info';
};

// 复制代码
const copyCode = async (): Promise<void> => {
  try {
    await navigator.clipboard.writeText(props.block.content);
    ElMessage.success('代码已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};
</script>

<style scoped>
.content-block {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.content-block--incomplete {
  border-left: 4px solid #409eff;
}

.block-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  gap: 8px;
}

.block-icon {
  color: #409eff;
}

.block-title {
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.block-actions {
  margin-left: auto;
}

.block-content {
  padding: 16px;
  background: white;
  position: relative;
}

/* 思考块样式 */
.thought-block {
  border: 1px solid #e1f3d8;
  background: #f0f9ff;
}

.thought-block .block-header {
  background: #e1f3d8;
}

.thought-line {
  margin: 8px 0;
  line-height: 1.6;
  color: #606266;
}

/* 代码块样式 */
.code-block {
  border: 1px solid #e4e7ed;
}

.code-block pre {
  margin: 0;
  background: #fafafa;
  border-radius: 4px;
  padding: 16px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.code-block code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
}

/* 文本块样式 */
.text-block {
  border: 1px solid #e4e7ed;
}

.text-content {
  line-height: 1.8;
  color: #303133;
}

.text-content h1,
.text-content h2,
.text-content h3 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.text-content h1 {
  font-size: 1.5em;
}
.text-content h2 {
  font-size: 1.3em;
}
.text-content h3 {
  font-size: 1.1em;
}

.text-content code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 12px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: #409eff;
  border-radius: 50%;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-cursor {
  color: #409eff;
  font-weight: bold;
  animation: cursor-blink 1s infinite;
}

.code-typing {
  position: absolute;
  bottom: 16px;
  right: 16px;
}

@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes cursor-blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 代码高亮主题 */
.hljs {
  background: #fafafa;
  color: #383a42;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-title,
.hljs-section,
.hljs-doctag,
.hljs-name,
.hljs-strong {
  color: #a626a4;
}

.hljs-string,
.hljs-title,
.hljs-section,
.hljs-built_in,
.hljs-literal,
.hljs-type,
.hljs-addition,
.hljs-tag,
.hljs-quote,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #50a14f;
}

.hljs-number,
.hljs-symbol,
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-title {
  color: #986801;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
  color: #a0a1a7;
}
</style>
