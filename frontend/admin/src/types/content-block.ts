/**
 * AI生成内容的块类型定义
 */
export interface ContentBlock {
  id: string;
  type: 'thought' | 'code' | 'text';
  content: string;
  language?: 'json' | 'sql' | 'javascript' | 'python' | 'markdown';
  isComplete: boolean;
  timestamp: number;
}

/**
 * 流式解析器状态
 */
export interface ParserState {
  currentBlock: ContentBlock | null;
  buffer: string;
  inBlock: boolean;
  blockType: ContentBlock['type'] | null;
  blockLanguage?: string;
}

/**
 * 内容块渲染配置
 */
export interface BlockRenderConfig {
  showLineNumbers: boolean;
  highlightTheme: string;
  maxHeight?: string;
}
