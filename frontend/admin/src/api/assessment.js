import request from '@/utils/request';
import { createCompatibleApis } from '@/api/multiTenantAdapter';

// Docling PDF转换相关API
export const pdfUploadApi = {
  // 上传PDF文件并转换
  upload: formData => {
    return request({
      url: '/api/docling/convert-with-info',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // PDF解析可能需要较长时间(3分钟)
    });
  },

  // 简单PDF转换
  convert: formData => {
    return request({
      url: '/api/docling/convert',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 120000, // 2分钟超时
    });
  },

  // 检查Docling服务健康状态
  checkHealth: () => {
    return request({
      url: '/api/docling/health',
      method: 'get',
    });
  },

  // 测试API连通性
  test: () => {
    return request({
      url: '/api/docling/health',
      method: 'get',
    });
  },
};

// 创建多租户兼容的API实例
const compatibleApis = createCompatibleApis();

// 评估量表管理API (多租户适配)
export const assessmentScaleApi = compatibleApis.assessmentScaleApi;

// 评估记录API (多租户适配)
export const assessmentRecordApi = compatibleApis.assessmentRecordApi;

// 评估对象API (多租户适配)
export const elderlyApi = compatibleApis.elderlyApi;

// 机构管理API (保持原有实现，待后续适配)
export const institutionApi = {
  // 获取机构列表
  getInstitutions: params => {
    return request({
      url: '/api/institutions',
      method: 'get',
      params,
    });
  },

  // 获取机构详情
  getInstitution: id => {
    return request({
      url: `/api/institutions/${id}`,
      method: 'get',
    });
  },

  // 创建机构
  createInstitution: data => {
    return request({
      url: '/api/institutions',
      method: 'post',
      data,
    });
  },

  // 更新机构信息
  updateInstitution: (id, data) => {
    return request({
      url: `/api/institutions/${id}`,
      method: 'put',
      data,
    });
  },
};

// 统计分析API
export const analyticsApi = {
  // 获取总体统计
  getOverallStats: () => {
    return request({
      url: '/api/analytics/overview',
      method: 'get',
    });
  },

  // 获取评估趋势
  getAssessmentTrends: params => {
    return request({
      url: '/api/analytics/trends',
      method: 'get',
      params,
    });
  },

  // 获取量表使用统计
  getScaleUsageStats: params => {
    return request({
      url: '/api/analytics/scale-usage',
      method: 'get',
      params,
    });
  },

  // 获取机构统计
  getInstitutionStats: institutionId => {
    return request({
      url: `/api/analytics/institutions/${institutionId}`,
      method: 'get',
    });
  },
};

export default {
  pdfUploadApi,
  assessmentScaleApi,
  assessmentRecordApi,
  elderlyApi,
  institutionApi,
  analyticsApi,
};
