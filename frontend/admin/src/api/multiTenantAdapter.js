/**
 * 多租户API适配器
 * 将现有的单租户API调用适配为多租户API调用
 * 保持现有组件接口不变，实现平滑迁移
 */

import request from '@/utils/request';
import tenantContext from '@/utils/tenantContext';

/**
 * 多租户认证API
 */
export const multiTenantAuthApi = {
  // 多租户登录
  login: ({ tenantCode, username, password }) => {
    return request({
      url: '/api/multi-tenant/auth/login',
      method: 'post',
      data: {
        tenantCode,
        username,
        password
      }
    });
  },

  // 退出登录
  logout: () => {
    return request({
      url: '/api/multi-tenant/auth/logout',
      method: 'post'
    });
  },

  // 刷新令牌
  refresh: () => {
    return request({
      url: '/api/multi-tenant/auth/refresh',
      method: 'post'
    });
  },

  // 获取用户信息
  getProfile: () => {
    return request({
      url: '/api/multi-tenant/auth/profile',
      method: 'get'
    });
  }
};

/**
 * 多租户量表管理API适配器
 * 适配原有的 assessmentScaleApi
 */
export const createScaleApiAdapter = () => {
  return {
    // 获取量表列表 - 适配为获取全局公开量表
    getScales: (params = {}) => {
      return request({
        url: '/api/multi-tenant/scales/public',
        method: 'get',
        params: {
          page: params.page || 0,
          size: params.size || 20,
          category: params.category,
          search: params.search
        }
      });
    },

    // 获取量表详情
    getScale: (id) => {
      return request({
        url: `/api/multi-tenant/scales/${id}`,
        method: 'get'
      });
    },

    // 创建量表 - 需要管理员权限，暂时返回错误提示
    createScale: (data) => {
      return Promise.reject(new Error('创建量表功能需要系统管理员权限'));
    },

    // 更新量表 - 需要管理员权限，暂时返回错误提示
    updateScale: (id, data) => {
      return Promise.reject(new Error('更新量表功能需要系统管理员权限'));
    },

    // 删除量表 - 需要管理员权限，暂时返回错误提示
    deleteScale: (id) => {
      return Promise.reject(new Error('删除量表功能需要系统管理员权限'));
    },

    // 激活/停用量表 - 需要管理员权限，暂时返回错误提示
    toggleScale: (id, isActive) => {
      return Promise.reject(new Error('量表状态管理功能需要系统管理员权限'));
    },

    // 获取量表使用统计 - 记录使用次数
    getUsageStats: (id) => {
      // 记录使用次数
      request({
        url: `/api/multi-tenant/scales/${id}/usage`,
        method: 'post'
      }).catch(e => console.warn('Failed to record scale usage:', e));
      
      // 返回模拟的统计数据
      return Promise.resolve({
        data: {
          usageCount: 0,
          lastUsed: null
        }
      });
    }
  };
};

/**
 * 多租户评估记录API适配器
 * 适配原有的 assessmentRecordApi
 */
export const createRecordApiAdapter = () => {
  const getTenantId = () => {
    const tenantId = tenantContext.getCurrentTenantId();
    if (!tenantId) {
      throw new Error('未设置当前租户，请先登录');
    }
    return tenantId;
  };

  return {
    // 获取评估记录列表
    getRecords: (params = {}) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments`,
        method: 'get',
        params: {
          page: params.page || 0,
          size: params.size || 20,
          subjectId: params.subjectId,
          scaleId: params.scaleId,
          assessorId: params.assessorId,
          status: params.status
        }
      });
    },

    // 获取评估记录详情
    getRecord: (id) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${id}`,
        method: 'get'
      });
    },

    // 创建评估记录
    createRecord: (data) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments`,
        method: 'post',
        data: {
          elderlyId: data.subjectId || data.elderlyId, // 兼容字段名
          scaleId: data.scaleId,
          assessorId: data.assessorId || tenantContext.getCurrentUserId(),
          formData: data.formData
        }
      });
    },

    // 更新评估记录
    updateRecord: (id, data) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${id}`,
        method: 'put',
        data
      });
    },

    // 提交评估记录
    submitRecord: (id) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${id}/submit`,
        method: 'post'
      });
    },

    // 获取评估结果
    getResults: (recordId) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${recordId}`,
        method: 'get'
      }).then(response => ({
        data: {
          totalScore: response.data.totalScore,
          resultLevel: response.data.resultLevel,
          formData: response.data.formData
        }
      }));
    },

    // 导出评估报告
    exportReport: (recordId, format = 'pdf') => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/assessments/${recordId}/export`,
        method: 'get',
        params: { format },
        responseType: 'blob'
      });
    }
  };
};

/**
 * 多租户评估对象API适配器
 * 适配原有的 elderlyApi
 */
export const createSubjectApiAdapter = () => {
  const getTenantId = () => {
    const tenantId = tenantContext.getCurrentTenantId();
    if (!tenantId) {
      throw new Error('未设置当前租户，请先登录');
    }
    return tenantId;
  };

  return {
    // 获取评估对象列表
    getElderly: (params = {}) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects`,
        method: 'get',
        params: {
          page: params.page || 0,
          size: params.size || 20,
          search: params.search,
          isActive: params.isActive
        }
      });
    },

    // 获取评估对象详情
    getElderlyById: (id) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${id}`,
        method: 'get'
      });
    },

    // 创建评估对象档案
    createElderly: (data) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects`,
        method: 'post',
        data: {
          name: data.name,
          gender: data.gender,
          birthDate: data.birthDate,
          idNumber: data.idNumber,
          contactPhone: data.contactPhone,
          contactAddress: data.contactAddress,
          emergencyContactName: data.emergencyContactName,
          emergencyContactPhone: data.emergencyContactPhone,
          medicalHistory: data.medicalHistory,
          personalInfo: data.personalInfo || {}
        }
      });
    },

    // 更新评估对象信息
    updateElderly: (id, data) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${id}`,
        method: 'put',
        data
      });
    },

    // 删除评估对象档案
    deleteElderly: (id) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${id}`,
        method: 'delete'
      });
    },

    // 获取评估对象的评估历史
    getAssessmentHistory: (elderlyId) => {
      const tenantId = getTenantId();
      return request({
        url: `/api/tenants/${tenantId}/subjects/${elderlyId}/history`,
        method: 'get'
      });
    }
  };
};

/**
 * 租户管理API
 */
export const tenantManageApi = {
  // 获取租户列表（仅系统管理员）
  getTenants: (params = {}) => {
    return request({
      url: '/api/system/tenants',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        search: params.search,
        status: params.status
      }
    });
  },

  // 获取租户详情
  getTenant: (id) => {
    return request({
      url: `/api/system/tenants/${id}`,
      method: 'get'
    });
  },

  // 创建租户
  createTenant: (data) => {
    return request({
      url: '/api/system/tenants',
      method: 'post',
      data
    });
  },

  // 更新租户信息
  updateTenant: (id, data) => {
    return request({
      url: `/api/system/tenants/${id}`,
      method: 'put',
      data
    });
  },

  // 启用/禁用租户
  toggleTenant: (id, isActive) => {
    return request({
      url: `/api/system/tenants/${id}/toggle`,
      method: 'patch',
      data: { isActive }
    });
  }
};

/**
 * 系统量表管理API
 */
export const systemScaleApi = {
  // 获取量表列表
  getScales: (params = {}) => {
    return request({
      url: '/api/system/scales',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        search: params.search,
        category: params.category,
        status: params.status,
        visibility: params.visibility,
        publisherType: params.publisherType,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取量表详情
  getScale: (id) => {
    return request({
      url: `/api/system/scales/${id}`,
      method: 'get'
    });
  },

  // 创建量表
  createScale: (data) => {
    return request({
      url: '/api/system/scales',
      method: 'post',
      data
    });
  },

  // 更新量表
  updateScale: (id, data) => {
    return request({
      url: `/api/system/scales/${id}`,
      method: 'put',
      data
    });
  },

  // 删除量表
  deleteScale: (id) => {
    return request({
      url: `/api/system/scales/${id}`,
      method: 'delete'
    });
  },

  // 发布量表
  publishScale: (id) => {
    return request({
      url: `/api/system/scales/${id}/publish`,
      method: 'post'
    });
  },

  // 弃用量表
  deprecateScale: (id) => {
    return request({
      url: `/api/system/scales/${id}/deprecate`,
      method: 'post'
    });
  },

  // 获取量表统计
  getScaleStats: () => {
    return request({
      url: '/api/system/scales/stats',
      method: 'get'
    });
  },

  // 获取量表分类
  getCategories: () => {
    return request({
      url: '/api/system/scales/categories',
      method: 'get'
    });
  }
};

/**
 * 系统评估记录管理API
 */
export const systemAssessmentApi = {
  // 获取评估记录列表
  getAssessments: (params = {}) => {
    return request({
      url: '/api/system/assessments',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        tenantId: params.tenantId,
        status: params.status,
        assessorId: params.assessorId,
        scaleId: params.scaleId,
        startDate: params.startDate,
        endDate: params.endDate,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取评估记录详情
  getAssessment: (id) => {
    return request({
      url: `/api/system/assessments/${id}`,
      method: 'get'
    });
  },

  // 审核评估记录
  reviewAssessment: (id, data) => {
    return request({
      url: `/api/system/assessments/${id}/review`,
      method: 'post',
      data: {
        approved: data.approved,
        reviewNotes: data.reviewNotes,
        reviewerId: data.reviewerId
      }
    });
  },

  // 批量审核评估记录
  batchReviewAssessments: (data) => {
    return request({
      url: '/api/system/assessments/batch-review',
      method: 'post',
      data: {
        recordIds: data.recordIds,
        approved: data.approved,
        reviewNotes: data.reviewNotes,
        reviewerId: data.reviewerId
      }
    });
  },

  // 归档评估记录
  archiveAssessment: (id) => {
    return request({
      url: `/api/system/assessments/${id}/archive`,
      method: 'post'
    });
  },

  // 删除评估记录
  deleteAssessment: (id) => {
    return request({
      url: `/api/system/assessments/${id}`,
      method: 'delete'
    });
  },

  // 获取评估统计
  getAssessmentStats: (params = {}) => {
    return request({
      url: '/api/system/assessments/stats',
      method: 'get',
      params: {
        tenantId: params.tenantId,
        startDate: params.startDate,
        endDate: params.endDate
      }
    });
  },

  // 获取待审核记录
  getPendingReviewRecords: (tenantId) => {
    return request({
      url: '/api/system/assessments/pending-review',
      method: 'get',
      params: { tenantId }
    });
  }
};

/**
 * 平台用户管理API
 */
export const platformUserApi = {
  // 获取平台用户列表
  getUsers: (params = {}) => {
    return request({
      url: '/api/system/users',
      method: 'get',
      params: {
        page: params.page || 0,
        size: params.size || 20,
        search: params.search,
        tenantId: params.tenantId,
        role: params.role,
        sortField: params.sortField,
        sortOrder: params.sortOrder
      }
    });
  },

  // 获取用户详情
  getUser: (id) => {
    return request({
      url: `/api/system/users/${id}`,
      method: 'get'
    });
  },

  // 创建用户
  createUser: (data) => {
    return request({
      url: '/api/system/users',
      method: 'post',
      data
    });
  },

  // 更新用户信息
  updateUser: (id, data) => {
    return request({
      url: `/api/system/users/${id}`,
      method: 'put',
      data
    });
  },

  // 重置用户密码
  resetPassword: (id) => {
    return request({
      url: `/api/system/users/${id}/reset-password`,
      method: 'post'
    });
  },

  // 管理租户用户关系
  manageTenantMembership: (userId, tenantId, data) => {
    return request({
      url: `/api/system/users/${userId}/tenants/${tenantId}`,
      method: 'put',
      data: {
        role: data.role,
        permissions: data.permissions,
        isActive: data.isActive
      }
    });
  },

  // 移除租户用户关系
  removeTenantMembership: (userId, tenantId) => {
    return request({
      url: `/api/system/users/${userId}/tenants/${tenantId}`,
      method: 'delete'
    });
  },

  // 获取用户统计
  getUserStats: () => {
    return request({
      url: '/api/system/users/stats',
      method: 'get'
    });
  }
};

/**
 * 系统监控面板API
 */
export const systemDashboardApi = {
  // 获取系统概览
  getSystemOverview: () => {
    return request({
      url: '/api/system/dashboard/overview',
      method: 'get'
    });
  },

  // 获取租户统计
  getTenantStats: () => {
    return request({
      url: '/api/system/dashboard/tenant-stats',
      method: 'get'
    });
  },

  // 获取用户统计
  getUserStats: () => {
    return request({
      url: '/api/system/dashboard/user-stats',
      method: 'get'
    });
  },

  // 获取评估统计
  getAssessmentStats: () => {
    return request({
      url: '/api/system/dashboard/assessment-stats',
      method: 'get'
    });
  },

  // 获取量表统计
  getScaleStats: () => {
    return request({
      url: '/api/system/dashboard/scale-stats',
      method: 'get'
    });
  },

  // 获取系统性能
  getSystemPerformance: () => {
    return request({
      url: '/api/system/dashboard/performance',
      method: 'get'
    });
  },

  // 获取实时活动
  getRecentActivities: (limit = 50) => {
    return request({
      url: '/api/system/dashboard/activities',
      method: 'get',
      params: { limit }
    });
  }
};

/**
 * 创建兼容的API实例
 * 这些实例保持与原有API相同的接口，但内部使用多租户逻辑
 */
export const createCompatibleApis = () => {
  return {
    assessmentScaleApi: createScaleApiAdapter(),
    assessmentRecordApi: createRecordApiAdapter(),
    elderlyApi: createSubjectApiAdapter()
  };
};

// 导出所有API
export default {
  multiTenantAuthApi,
  tenantManageApi,
  systemScaleApi,
  systemAssessmentApi,
  systemDashboardApi,
  platformUserApi,
  createScaleApiAdapter,
  createRecordApiAdapter,
  createSubjectApiAdapter,
  createCompatibleApis
};