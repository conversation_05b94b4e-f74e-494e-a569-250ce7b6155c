/**
 * 认证清理工具
 * 用于开发环境启动时自动清理认证信息，确保显示登录界面
 */

// 设置开发环境清理标记
export const setDevStartupClear = () => {
  sessionStorage.setItem('dev_startup_clear', 'true');
  console.log('已设置开发环境清理标记');
};

// 检查并清理所有认证信息
export const clearAllAuthData = () => {
  // 清理localStorage
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('userInfo');
  localStorage.removeItem('permissions');

  // 清理sessionStorage
  sessionStorage.removeItem('token');
  sessionStorage.removeItem('user');
  sessionStorage.removeItem('userInfo');

  // 清理可能的其他认证相关数据
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.includes('auth') || key.includes('token') || key.includes('user')) {
      localStorage.removeItem(key);
    }
  });

  console.log('所有认证信息已清理');
};

// 初始化清理机制
export const initAuthClear = async () => {
  // 检查是否是从开发脚本启动（只有明确的dev_start参数才清理）
  const urlParams = new URLSearchParams(window.location.search);
  const isDevStart = urlParams.get('dev_start') === 'true';

  // 检查sessionStorage中的一次性清理标记
  const hasDevClearFlag =
    sessionStorage.getItem('dev_startup_clear') === 'true';

  // 只在明确的开发启动时才清理
  const shouldClear =
    isDevStart ||
    (hasDevClearFlag && !sessionStorage.getItem('clear_executed'));

  if (shouldClear) {
    console.log('检测到开发环境启动，执行认证清理');
    clearAllAuthData();

    // 标记已执行清理，避免重复清理
    sessionStorage.setItem('clear_executed', 'true');

    // 如果当前不在登录页面，重定向到登录页面
    if (
      window.location.pathname !== '/login' &&
      window.location.pathname !== '/'
    ) {
      console.log('重定向到登录页面');
      window.location.href = '/login';
    }
  }
};

// 在页面加载时自动执行
if (typeof window !== 'undefined') {
  // 使用 DOMContentLoaded 确保在路由初始化之前执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAuthClear);
  } else {
    initAuthClear();
  }
}
