import axios, {
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import { ElMessage } from 'element-plus';

// 动态导入tenantContext避免循环依赖
let tenantContext: any = null;
const getTenantContext = async () => {
  if (!tenantContext) {
    const module = await import('@/utils/tenantContext');
    tenantContext = module.default;
  }
  return tenantContext;
};

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8181',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
request.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    try {
      const context = await getTenantContext();
      
      // 添加JWT认证令牌
      const token = context.getToken() || localStorage.getItem('token') || localStorage.getItem('authToken');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加租户上下文信息
      const tenantId = context.getCurrentTenantId();
      if (tenantId && config.headers) {
        config.headers['X-Tenant-ID'] = tenantId;
      }

      const tenantCode = context.getCurrentTenantCode();
      if (tenantCode && config.headers) {
        config.headers['X-Tenant-Code'] = tenantCode;
      }

    } catch (error) {
      console.warn('Failed to add tenant context to request:', error);
      
      // fallback到原有逻辑
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    
    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // 直接返回原始响应，保持数据结构不变
    return response;
  },
  async error => {
    const { response } = error;

    if (response) {
      const { status, data } = response;

      switch (status) {
        case 401:
          ElMessage.error('认证失败，请重新登录');
          
          // 清除多租户上下文
          try {
            const context = await getTenantContext();
            context.clear();
          } catch (e) {
            console.warn('Failed to clear tenant context:', e);
          }
          
          // 清除传统token
          localStorage.removeItem('token');
          localStorage.removeItem('authToken');
          
          // 跳转到登录页
          window.location.href = '/login';
          break;
        case 403:
          ElMessage.error('访问被拒绝，权限不足');
          break;
        case 404:
          ElMessage.error('请求的资源不存在');
          break;
        case 422:
          ElMessage.error(data?.message || '数据验证失败');
          break;
        case 500:
          ElMessage.error('服务器内部错误');
          break;
        default:
          // 优先显示后端返回的错误信息
          const errorMessage = data?.message || data?.error || '请求失败';
          ElMessage.error(errorMessage);
      }
    } else if (error.request) {
      ElMessage.error('网络连接异常，请检查网络状态');
    } else {
      ElMessage.error('请求配置错误');
    }

    return Promise.reject(error);
  }
);

export default request;
