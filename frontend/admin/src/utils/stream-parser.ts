import type { ContentBlock, ParserState } from '@/types/content-block';

/**
 * 流式内容解析器
 * 负责将DeepSeek的流式输出解析为结构化的内容块
 */
export class StreamParser {
  private state: ParserState = {
    currentBlock: null,
    buffer: '',
    inBlock: false,
    blockType: null,
  };

  private blocks: ContentBlock[] = [];
  private onBlockUpdate: (blocks: ContentBlock[]) => void;

  constructor(onBlockUpdate: (blocks: ContentBlock[]) => void) {
    this.onBlockUpdate = onBlockUpdate;
  }

  /**
   * 处理新的内容块
   */
  processChunk(chunk: string): void {
    this.state.buffer += chunk;

    while (this.state.buffer.length > 0) {
      if (!this.state.inBlock) {
        this.tryStartNewBlock();
      } else {
        this.tryCompleteCurrentBlock();
      }

      // 防止无限循环
      if (this.state.buffer === chunk) {
        break;
      }
    }

    this.notifyUpdate();
  }

  /**
   * 尝试开始一个新的内容块
   */
  private tryStartNewBlock(): void {
    // 检测思考标签
    const thinkMatch = this.state.buffer.match(/^(.*?)<think>/s);
    if (thinkMatch) {
      // 处理思考标签前的文本
      if (thinkMatch[1].trim()) {
        this.createTextBlock(thinkMatch[1].trim());
      }

      // 开始思考块
      this.startBlock('thought');
      this.state.buffer = this.state.buffer.replace(/^.*?<think>/s, '');
      return;
    }

    // 检测代码块
    const codeMatch = this.state.buffer.match(/^(.*?)```(\w+)?\n/s);
    if (codeMatch) {
      // 处理代码块前的文本
      if (codeMatch[1].trim()) {
        this.createTextBlock(codeMatch[1].trim());
      }

      // 开始代码块
      const language = codeMatch[2] || 'text';
      this.startBlock('code', language);
      this.state.buffer = this.state.buffer.replace(/^.*?```\w*\n/s, '');
      return;
    }

    // 检查是否有足够的内容形成文本块
    const lines = this.state.buffer.split('\n');
    if (lines.length > 1 || this.state.buffer.length > 100) {
      // 创建文本块，保留最后不完整的行
      const completeLines = lines.slice(0, -1);
      if (completeLines.length > 0) {
        this.createTextBlock(completeLines.join('\n'));
        this.state.buffer = lines[lines.length - 1];
      }
    }
  }

  /**
   * 尝试完成当前内容块
   */
  private tryCompleteCurrentBlock(): void {
    if (!this.state.currentBlock) return;

    let endPattern: RegExp;
    let replacement: string;

    switch (this.state.currentBlock.type) {
      case 'thought':
        endPattern = /^(.*?)<\/think>/s;
        replacement = '$1';
        break;
      case 'code':
        endPattern = /^(.*?)\n```/s;
        replacement = '$1';
        break;
      default:
        return;
    }

    const match = this.state.buffer.match(endPattern);
    if (match) {
      // 完成当前块
      this.state.currentBlock.content += match[1];
      this.state.currentBlock.isComplete = true;

      // 移动到下一个块
      this.state.buffer = this.state.buffer
        .replace(endPattern, '')
        .replace(/^<\/think>|^```/, '');
      this.state.inBlock = false;
      this.state.currentBlock = null;
      this.state.blockType = null;
    } else {
      // 继续累积内容
      this.state.currentBlock.content += this.state.buffer;
      this.state.buffer = '';
    }
  }

  /**
   * 开始一个新的内容块
   */
  private startBlock(type: ContentBlock['type'], language?: string): void {
    this.state.currentBlock = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type,
      content: '',
      language: language as any,
      isComplete: false,
      timestamp: Date.now(),
    };

    this.blocks.push(this.state.currentBlock);
    this.state.inBlock = true;
    this.state.blockType = type;
    this.state.blockLanguage = language;
  }

  /**
   * 创建文本块
   */
  private createTextBlock(content: string): void {
    const textBlock: ContentBlock = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type: 'text',
      content: content.trim(),
      isComplete: true,
      timestamp: Date.now(),
    };

    this.blocks.push(textBlock);
  }

  /**
   * 强制完成当前块（流结束时调用）
   */
  public flush(): void {
    if (this.state.currentBlock) {
      this.state.currentBlock.content += this.state.buffer;
      this.state.currentBlock.isComplete = true;
    } else if (this.state.buffer.trim()) {
      this.createTextBlock(this.state.buffer.trim());
    }

    this.state.buffer = '';
    this.state.inBlock = false;
    this.state.currentBlock = null;
    this.notifyUpdate();
  }

  /**
   * 通知更新
   */
  private notifyUpdate(): void {
    this.onBlockUpdate([...this.blocks]);
  }

  /**
   * 重置解析器
   */
  public reset(): void {
    this.state = {
      currentBlock: null,
      buffer: '',
      inBlock: false,
      blockType: null,
    };
    this.blocks = [];
    this.notifyUpdate();
  }

  /**
   * 获取当前所有块
   */
  public getBlocks(): ContentBlock[] {
    return [...this.blocks];
  }
}
