<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gray-50"
  >
    <div class="w-full max-w-md mx-4">
      <div class="bg-white rounded-lg shadow-lg p-8">
        <!-- 登录头部 -->
        <div class="text-center mb-8">
          <div class="flex items-center justify-center mb-2">
            <BuildingOffice2Icon class="h-8 w-8 text-primary-700 mr-2" />
            <h2 class="text-2xl font-bold text-primary-700">智慧养老评估平台</h2>
          </div>
          <p class="text-gray-600 text-sm">请登录您的账户</p>
        </div>

        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="space-y-6"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              size="large"
              prefix-icon="User"
              clearable
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              size="large"
              prefix-icon="Lock"
              show-password
              clearable
              class="custom-input"
            />
          </el-form-item>

          <el-form-item class="mb-0">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="handleLogin"
              class="w-full h-12 text-base font-medium transition-all duration-200 hover:shadow-md"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 登录提示 -->
        <div class="text-center mt-6 pt-6 border-t border-neutral-200">
          <p class="text-neutral-500 text-xs flex items-center justify-center">
            <LightBulbIcon class="h-4 w-4 mr-1" />
            <span>默认账户: admin / admin123</span>
          </p>
        </div>
      </div>

      <!-- 版本信息 -->
      <div class="text-center mt-6 text-white/80 text-xs">
        <p>智慧养老评估平台 v1.0.0</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import request from '@/utils/request';
import { LightBulbIcon, BuildingOffice2Icon } from '@heroicons/vue/24/outline';

const router = useRouter();
const loading = ref(false);
const loginFormRef = ref();

const loginForm = reactive({
  username: 'admin',
  password: 'admin123',
});

const loginRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
};

const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    loading.value = true;

    const response: any = await request({
      url: '/api/auth/login',
      method: 'post',
      data: loginForm,
    });

    if (response.data.success) {
      // 保存token和用户信息
      localStorage.setItem('token', response.data.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.data.user));
      if (response.data.data.refreshToken) {
        localStorage.setItem('refreshToken', response.data.data.refreshToken);
      }

      ElMessage.success('登录成功');

      // 跳转到首页
      router.push('/');
    } else {
      ElMessage.error(response.data.message || '登录失败');
    }
  } catch (error) {
    console.error('Login error:', error);
    ElMessage.error('登录失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 自定义输入框样式 - 严格遵循三色系统 */
:deep(.custom-input .el-input__wrapper) {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb; /* neutral-200 */
  transition: all 0.2s ease;
}

:deep(.custom-input .el-input__wrapper:hover) {
  border-color: #5357A0; /* 长春花蓝 */
  box-shadow: 0 0 0 1px rgba(83, 87, 160, 0.1);
}

:deep(.custom-input .el-input__wrapper.is-focus) {
  border-color: #5357A0; /* 长春花蓝 */
  box-shadow: 0 0 0 2px rgba(83, 87, 160, 0.1);
}

/* 表单项间距 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 错误信息样式 - 使用现代灰色 */
:deep(.el-form-item__error) {
  font-size: 12px;
  color: #6b7280; /* 现代灰 */
  margin-top: 4px;
}
</style>
