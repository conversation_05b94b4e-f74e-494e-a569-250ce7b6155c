<template>
  <div class="tenant-management">
    <div class="page-header">
      <h1>租户管理</h1>
      <p>管理所有租户（机构）的基本信息和状态</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleCreate"
          v-if="canManageTenants"
        >
          新增租户
        </el-button>
        <el-button
          icon="Refresh"
          @click="refreshData"
        >
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索租户名称或代码"
          style="width: 250px; margin-right: 10px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部" value="" />
          <el-option label="活跃" value="ACTIVE" />
          <el-option label="暂停" value="SUSPENDED" />
          <el-option label="禁用" value="DISABLED" />
        </el-select>
      </div>
    </div>

    <!-- 租户列表 -->
    <el-table
      :data="tenantList"
      v-loading="loading"
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column
        prop="code"
        label="租户代码"
        width="120"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag type="info">{{ row.code }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="name"
        label="租户名称"
        min-width="200"
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="tenant-info">
            <div class="tenant-name">{{ row.name }}</div>
            <div class="tenant-desc" v-if="row.description">
              {{ row.description }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="status"
        label="状态"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getStatusTagType(row.status)"
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="subscriptionPlan"
        label="订阅计划"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getPlanTagType(row.subscriptionPlan)"
            size="small"
          >
            {{ getPlanText(row.subscriptionPlan) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="contactInfo"
        label="联系方式"
        width="180"
      >
        <template #default="{ row }">
          <div class="contact-info">
            <div v-if="row.contactEmail">
              <el-icon><Message /></el-icon>
              {{ row.contactEmail }}
            </div>
            <div v-if="row.contactPhone">
              <el-icon><Phone /></el-icon>
              {{ row.contactPhone }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="createdAt"
        label="创建时间"
        width="160"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          
          <el-button
            type="primary"
            link
            size="small"
            @click="handleEdit(row)"
            v-if="canManageTenants"
          >
            编辑
          </el-button>

          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="canManageTenants"
          >
            <el-button type="primary" link size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :command="`toggle-${row.id}`"
                  :disabled="row.status === 'DISABLED'"
                >
                  {{ row.status === 'ACTIVE' ? '暂停' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  :command="`disable-${row.id}`"
                  :disabled="row.status === 'DISABLED'"
                  divided
                >
                  禁用
                </el-dropdown-item>
                <el-dropdown-item
                  :command="`users-${row.id}`"
                >
                  用户管理
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handlePageSizeChange"
      @current-change="handlePageChange"
      style="margin-top: 20px; justify-content: center"
    />

    <!-- 租户详情/编辑对话框 -->
    <tenant-form-dialog
      v-model:visible="formDialogVisible"
      :tenant="currentTenant"
      :mode="formMode"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Message, Phone, Plus, Refresh, ArrowDown } from '@element-plus/icons-vue';
import { tenantManageApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';
import TenantFormDialog from './components/TenantFormDialog.vue';

// 数据状态
const tenantList = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const statusFilter = ref('');

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
});

// 排序
const sortField = ref('');
const sortOrder = ref('');

// 对话框状态
const formDialogVisible = ref(false);
const formMode = ref<'create' | 'edit' | 'view'>('create');
const currentTenant = ref(null);

// 权限控制
const canManageTenants = computed(() => {
  return tenantContext.isAdmin() || tenantContext.hasPermission('TENANT_MANAGE');
});

// 获取租户列表
const fetchTenantList = async () => {
  try {
    loading.value = true;
    
    const params = {
      page: pagination.current - 1,
      size: pagination.size,
      search: searchQuery.value || undefined,
      status: statusFilter.value || undefined,
      sortField: sortField.value || undefined,
      sortOrder: sortOrder.value || undefined
    };

    const response = await tenantManageApi.getTenants(params);
    const { data } = response;

    if (data.success) {
      tenantList.value = data.data.content || [];
      pagination.total = data.data.totalElements || 0;
    } else {
      ElMessage.error(data.message || '获取租户列表失败');
    }
  } catch (error: any) {
    console.error('获取租户列表失败:', error);
    ElMessage.error(error.message || '获取租户列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchTenantList();
};

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  sortField.value = prop || '';
  sortOrder.value = order === 'ascending' ? 'ASC' : order === 'descending' ? 'DESC' : '';
  fetchTenantList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchTenantList();
};

const handlePageSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchTenantList();
};

// 刷新数据
const refreshData = () => {
  fetchTenantList();
};

// 操作处理
const handleCreate = () => {
  currentTenant.value = null;
  formMode.value = 'create';
  formDialogVisible.value = true;
};

const handleView = (tenant: any) => {
  currentTenant.value = tenant;
  formMode.value = 'view';
  formDialogVisible.value = true;
};

const handleEdit = (tenant: any) => {
  currentTenant.value = tenant;
  formMode.value = 'edit';
  formDialogVisible.value = true;
};

// 下拉菜单操作
const handleDropdownCommand = async (command: string, tenant: any) => {
  const [action, id] = command.split('-');
  
  try {
    switch (action) {
      case 'toggle':
        await handleToggleTenant(tenant);
        break;
      case 'disable':
        await handleDisableTenant(tenant);
        break;
      case 'users':
        // 跳转到用户管理页面
        // router.push(`/system/users?tenantId=${id}`);
        ElMessage.info('用户管理功能开发中...');
        break;
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败');
  }
};

// 切换租户状态
const handleToggleTenant = async (tenant: any) => {
  const newStatus = tenant.status === 'ACTIVE' ? 'SUSPENDED' : 'ACTIVE';
  const actionText = newStatus === 'ACTIVE' ? '启用' : '暂停';
  
  try {
    await ElMessageBox.confirm(
      `确定要${actionText}租户 "${tenant.name}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await tenantManageApi.toggleTenant(tenant.id, newStatus === 'ACTIVE');
    ElMessage.success(`${actionText}成功`);
    fetchTenantList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || `${actionText}失败`);
    }
  }
};

// 禁用租户
const handleDisableTenant = async (tenant: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用租户 "${tenant.name}" 吗？禁用后该租户将无法使用系统。`,
      '确认禁用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    );

    await tenantManageApi.toggleTenant(tenant.id, false);
    ElMessage.success('禁用成功');
    fetchTenantList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '禁用失败');
    }
  }
};

// 表单操作成功
const handleFormSuccess = () => {
  formDialogVisible.value = false;
  fetchTenantList();
};

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'ACTIVE': 'success',
    'SUSPENDED': 'warning',
    'DISABLED': 'danger'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'ACTIVE': '活跃',
    'SUSPENDED': '暂停',
    'DISABLED': '禁用'
  };
  return textMap[status] || status;
};

const getPlanTagType = (plan: string) => {
  const typeMap: Record<string, string> = {
    'BASIC': 'info',
    'STANDARD': 'primary',
    'PREMIUM': 'success',
    'ENTERPRISE': 'warning'
  };
  return typeMap[plan] || 'info';
};

const getPlanText = (plan: string) => {
  const textMap: Record<string, string> = {
    'BASIC': '基础版',
    'STANDARD': '标准版',
    'PREMIUM': '高级版',
    'ENTERPRISE': '企业版'
  };
  return textMap[plan] || plan;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
onMounted(() => {
  fetchTenantList();
});
</script>

<style scoped>
.tenant-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tenant-info {
  display: flex;
  flex-direction: column;
}

.tenant-name {
  font-weight: 500;
  color: #303133;
}

.tenant-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contact-info > div {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.contact-info .el-icon {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .toolbar-right {
    justify-content: space-between;
  }
}
</style>