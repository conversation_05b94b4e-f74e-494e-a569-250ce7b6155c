<template>
  <div class="system-dashboard">
    <div class="dashboard-header">
      <h2>系统监控面板</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          icon="Refresh" 
          @click="refreshAllData"
          :loading="globalLoading"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 系统概览 -->
    <el-card class="overview-card" v-loading="overviewLoading">
      <template #header>
        <div class="card-header">
          <span>系统概览</span>
          <div class="health-status">
            <el-tag 
              :type="getHealthStatusType(systemHealth?.status)"
              effect="dark"
              size="large"
            >
              {{ getHealthStatusText(systemHealth?.status) }}
            </el-tag>
            <span class="health-score">健康评分: {{ systemHealth?.score || 0 }}</span>
          </div>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="3">
          <div class="overview-item">
            <div class="overview-number">{{ overviewData.totalTenants || 0 }}</div>
            <div class="overview-label">总租户数</div>
            <div class="overview-sub">活跃: {{ overviewData.activeTenants || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="overview-item">
            <div class="overview-number">{{ overviewData.totalUsers || 0 }}</div>
            <div class="overview-label">总用户数</div>
            <div class="overview-sub">活跃: {{ overviewData.activeUsers || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="overview-item">
            <div class="overview-number">{{ overviewData.totalScales || 0 }}</div>
            <div class="overview-label">总量表数</div>
            <div class="overview-sub">启用: {{ overviewData.activeScales || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="overview-item">
            <div class="overview-number">{{ overviewData.totalAssessments || 0 }}</div>
            <div class="overview-label">总评估数</div>
            <div class="overview-sub">今日: {{ overviewData.todayAssessments || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="overview-item">
            <div class="overview-number">{{ overviewData.weekAssessments || 0 }}</div>
            <div class="overview-label">本周评估</div>
            <div class="overview-sub">{{ getWeekGrowth() }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="overview-item">
            <div class="overview-number">{{ overviewData.monthAssessments || 0 }}</div>
            <div class="overview-label">本月评估</div>
            <div class="overview-sub">{{ getMonthGrowth() }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="overview-item performance">
            <div class="overview-number">{{ performanceData.memory?.usagePercentage?.toFixed(1) || 0 }}%</div>
            <div class="overview-label">内存使用率</div>
            <div class="overview-sub">{{ performanceData.memory?.used || 0 }}MB / {{ performanceData.memory?.max || 0 }}MB</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计面板 -->
    <el-row :gutter="20" class="stats-row">
      <!-- 租户统计 -->
      <el-col :span="6">
        <el-card class="stats-card" v-loading="tenantStatsLoading">
          <template #header>
            <div class="card-header">
              <span>租户统计</span>
              <el-button 
                text 
                size="small" 
                @click="viewTenantDetails"
              >
                详细信息
              </el-button>
            </div>
          </template>
          
          <div class="stats-content">
            <div class="status-distribution">
              <div class="status-item">
                <span class="status-label">活跃</span>
                <span class="status-value active">{{ tenantStats.statusDistribution?.ACTIVE || 0 }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">暂停</span>
                <span class="status-value suspended">{{ tenantStats.statusDistribution?.SUSPENDED || 0 }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">停用</span>
                <span class="status-value inactive">{{ tenantStats.statusDistribution?.INACTIVE || 0 }}</span>
              </div>
            </div>
            
            <div class="trend-info">
              <el-tag size="small" type="success">
                新增趋势: {{ getTenantTrend() }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 用户统计 -->
      <el-col :span="6">
        <el-card class="stats-card" v-loading="userStatsLoading">
          <template #header>
            <div class="card-header">
              <span>用户统计</span>
              <el-button 
                text 
                size="small" 
                @click="viewUserDetails"
              >
                详细信息
              </el-button>
            </div>
          </template>
          
          <div class="stats-content">
            <div class="user-breakdown">
              <div class="breakdown-item">
                <span>管理员</span>
                <span class="breakdown-value admin">{{ userStats.adminUsers || 0 }}</span>
              </div>
              <div class="breakdown-item">
                <span>普通用户</span>
                <span class="breakdown-value regular">{{ userStats.regularUsers || 0 }}</span>
              </div>
              <div class="breakdown-item">
                <span>活跃用户</span>
                <span class="breakdown-value active">{{ userStats.activeUsers || 0 }}</span>
              </div>
            </div>
            
            <div class="trend-info">
              <el-tag size="small" type="info">
                本月活跃: {{ userStats.activeUsersLastMonth || 0 }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 评估统计 -->
      <el-col :span="6">
        <el-card class="stats-card" v-loading="assessmentStatsLoading">
          <template #header>
            <div class="card-header">
              <span>评估统计</span>
              <el-button 
                text 
                size="small" 
                @click="viewAssessmentDetails"
              >
                详细信息
              </el-button>
            </div>
          </template>
          
          <div class="stats-content">
            <div class="assessment-types">
              <div class="type-item" v-for="(value, key) in assessmentStats.typeDistribution" :key="key">
                <span class="type-label">{{ getAssessmentTypeName(key) }}</span>
                <span class="type-value">{{ value || 0 }}</span>
              </div>
            </div>
            
            <div class="quality-info">
              <el-progress 
                :percentage="(assessmentStats.qualityStats?.highQualityPercentage || 0)"
                :show-text="false"
                stroke-width="8"
                :color="getQualityColor(assessmentStats.qualityStats?.highQualityPercentage || 0)"
              />
              <div class="quality-text">
                高质量评估: {{ (assessmentStats.qualityStats?.highQualityPercentage || 0).toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 量表统计 -->
      <el-col :span="6">
        <el-card class="stats-card" v-loading="scaleStatsLoading">
          <template #header>
            <div class="card-header">
              <span>量表统计</span>
              <el-button 
                text 
                size="small" 
                @click="viewScaleDetails"
              >
                详细信息
              </el-button>
            </div>
          </template>
          
          <div class="stats-content">
            <div class="scale-status">
              <div class="status-item">
                <span class="status-label">启用</span>
                <span class="status-value active">{{ scaleStats.statusDistribution?.ACTIVE || 0 }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">停用</span>
                <span class="status-value inactive">{{ scaleStats.statusDistribution?.INACTIVE || 0 }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">废弃</span>
                <span class="status-value deprecated">{{ scaleStats.statusDistribution?.DEPRECATED || 0 }}</span>
              </div>
            </div>
            
            <div class="popular-scale" v-if="popularScale">
              <el-tag size="small" type="warning">
                热门: {{ popularScale.name }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统性能 -->
    <el-card class="performance-card" v-loading="performanceLoading">
      <template #header>
        <div class="card-header">
          <span>系统性能</span>
          <div class="performance-status">
            <el-tag 
              :type="getDatabaseStatusType()"
              effect="plain"
            >
              数据库: {{ performanceData.database?.status || 'unknown' }}
            </el-tag>
          </div>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="performance-item">
            <div class="performance-title">内存使用情况</div>
            <div class="memory-info">
              <div class="memory-bar">
                <el-progress 
                  :percentage="performanceData.memory?.usagePercentage || 0"
                  :color="getMemoryColor(performanceData.memory?.usagePercentage || 0)"
                  :show-text="false"
                />
              </div>
              <div class="memory-details">
                <span>已使用: {{ performanceData.memory?.used || 0 }}MB</span>
                <span>总计: {{ performanceData.memory?.total || 0 }}MB</span>
                <span>最大: {{ performanceData.memory?.max || 0 }}MB</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="performance-item">
            <div class="performance-title">响应时间</div>
            <div class="response-time">
              <div class="time-item">
                <span class="time-label">平均响应</span>
                <span class="time-value">{{ performanceData.responseTime?.averageResponseTime || 0 }}ms</span>
              </div>
              <div class="time-item">
                <span class="time-label">P95</span>
                <span class="time-value">{{ performanceData.responseTime?.p95ResponseTime || 0 }}ms</span>
              </div>
              <div class="time-item">
                <span class="time-label">P99</span>
                <span class="time-value">{{ performanceData.responseTime?.p99ResponseTime || 0 }}ms</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="performance-item">
            <div class="performance-title">系统信息</div>
            <div class="system-info">
              <div class="info-item">
                <span class="info-label">Java版本</span>
                <span class="info-value">{{ performanceData.system?.javaVersion || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">操作系统</span>
                <span class="info-value">{{ performanceData.system?.osName || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">处理器核心</span>
                <span class="info-value">{{ performanceData.system?.processors || 0 }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实时活动 -->
    <el-card class="activities-card" v-loading="activitiesLoading">
      <template #header>
        <div class="card-header">
          <span>实时活动</span>
          <el-button 
            text 
            size="small" 
            @click="refreshActivities"
            :loading="activitiesLoading"
          >
            刷新
          </el-button>
        </div>
      </template>

      <div class="activities-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <el-icon :color="getActivityColor(activity.type)">
              <DocumentAdd v-if="activity.type === 'ASSESSMENT_CREATED'" />
              <User v-else-if="activity.type === 'USER_LOGIN'" />
              <Setting v-else />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-meta">
              <span class="activity-time">{{ formatActivityTime(activity.timestamp) }}</span>
              <el-tag 
                size="small" 
                :type="getStatusTagType(activity.status)"
                v-if="activity.status"
              >
                {{ activity.status }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <el-empty 
          v-if="!activitiesLoading && recentActivities.length === 0"
          description="暂无活动记录"
          :image-size="80"
        />
      </div>
    </el-card>

    <!-- 详细统计对话框 -->
    <tenant-stats-dialog
      v-model:visible="tenantStatsDialogVisible"
      @refresh="refreshTenantStats"
    />
    
    <user-stats-dialog
      v-model:visible="userStatsDialogVisible"
      @refresh="refreshUserStats"
    />
    
    <assessment-stats-dialog
      v-model:visible="assessmentStatsDialogVisible"
      @refresh="refreshAssessmentStats"
    />
    
    <scale-stats-dialog
      v-model:visible="scaleStatsDialogVisible"
      @refresh="refreshScaleStats"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh, DocumentAdd, User, Setting } from '@element-plus/icons-vue';
import { systemDashboardApi } from '@/api/multiTenantAdapter';
import TenantStatsDialog from './components/TenantStatsDialog.vue';
import UserStatsDialog from './components/UserStatsDialog.vue';
import AssessmentStatsDialog from './components/AssessmentStatsDialog.vue';
import ScaleStatsDialog from './components/ScaleStatsDialog.vue';

// 加载状态
const globalLoading = ref(false);
const overviewLoading = ref(false);
const tenantStatsLoading = ref(false);
const userStatsLoading = ref(false);
const assessmentStatsLoading = ref(false);
const scaleStatsLoading = ref(false);
const performanceLoading = ref(false);
const activitiesLoading = ref(false);

// 数据状态
const overviewData = ref<any>({});
const systemHealth = ref<any>({});
const tenantStats = ref<any>({});
const userStats = ref<any>({});
const assessmentStats = ref<any>({});
const scaleStats = ref<any>({});
const performanceData = ref<any>({});
const recentActivities = ref<any[]>([]);

// 对话框状态
const tenantStatsDialogVisible = ref(false);
const userStatsDialogVisible = ref(false);
const assessmentStatsDialogVisible = ref(false);
const scaleStatsDialogVisible = ref(false);

// 热门量表计算属性
const popularScale = computed(() => {
  return scaleStats.value.popularScales?.[0] || null;
});

// 获取系统概览
const fetchOverview = async () => {
  try {
    overviewLoading.value = true;
    const response = await systemDashboardApi.getSystemOverview();
    const { data } = response;
    
    if (data.success) {
      overviewData.value = data.data;
      systemHealth.value = data.data.systemHealth || {};
    } else {
      ElMessage.error(data.message || '获取系统概览失败');
    }
  } catch (error: any) {
    console.error('获取系统概览失败:', error);
    ElMessage.error(error.message || '获取系统概览失败');
  } finally {
    overviewLoading.value = false;
  }
};

// 获取租户统计
const fetchTenantStats = async () => {
  try {
    tenantStatsLoading.value = true;
    const response = await systemDashboardApi.getTenantStats();
    const { data } = response;
    
    if (data.success) {
      tenantStats.value = data.data;
    }
  } catch (error) {
    console.error('获取租户统计失败:', error);
  } finally {
    tenantStatsLoading.value = false;
  }
};

// 获取用户统计
const fetchUserStats = async () => {
  try {
    userStatsLoading.value = true;
    const response = await systemDashboardApi.getUserStats();
    const { data } = response;
    
    if (data.success) {
      userStats.value = data.data;
    }
  } catch (error) {
    console.error('获取用户统计失败:', error);
  } finally {
    userStatsLoading.value = false;
  }
};

// 获取评估统计
const fetchAssessmentStats = async () => {
  try {
    assessmentStatsLoading.value = true;
    const response = await systemDashboardApi.getAssessmentStats();
    const { data } = response;
    
    if (data.success) {
      assessmentStats.value = data.data;
    }
  } catch (error) {
    console.error('获取评估统计失败:', error);
  } finally {
    assessmentStatsLoading.value = false;
  }
};

// 获取量表统计
const fetchScaleStats = async () => {
  try {
    scaleStatsLoading.value = true;
    const response = await systemDashboardApi.getScaleStats();
    const { data } = response;
    
    if (data.success) {
      scaleStats.value = data.data;
    }
  } catch (error) {
    console.error('获取量表统计失败:', error);
  } finally {
    scaleStatsLoading.value = false;
  }
};

// 获取系统性能
const fetchPerformance = async () => {
  try {
    performanceLoading.value = true;
    const response = await systemDashboardApi.getSystemPerformance();
    const { data } = response;
    
    if (data.success) {
      performanceData.value = data.data;
    }
  } catch (error) {
    console.error('获取系统性能失败:', error);
  } finally {
    performanceLoading.value = false;
  }
};

// 获取实时活动
const fetchActivities = async () => {
  try {
    activitiesLoading.value = true;
    const response = await systemDashboardApi.getRecentActivities(20);
    const { data } = response;
    
    if (data.success) {
      recentActivities.value = data.data || [];
    }
  } catch (error) {
    console.error('获取实时活动失败:', error);
  } finally {
    activitiesLoading.value = false;
  }
};

// 刷新所有数据
const refreshAllData = async () => {
  globalLoading.value = true;
  try {
    await Promise.all([
      fetchOverview(),
      fetchTenantStats(),
      fetchUserStats(),
      fetchAssessmentStats(),
      fetchScaleStats(),
      fetchPerformance(),
      fetchActivities()
    ]);
    ElMessage.success('数据刷新完成');
  } catch (error) {
    ElMessage.error('数据刷新失败');
  } finally {
    globalLoading.value = false;
  }
};

// 刷新个别统计
const refreshTenantStats = () => fetchTenantStats();
const refreshUserStats = () => fetchUserStats();
const refreshAssessmentStats = () => fetchAssessmentStats();
const refreshScaleStats = () => fetchScaleStats();
const refreshActivities = () => fetchActivities();

// 显示详细信息
const viewTenantDetails = () => {
  tenantStatsDialogVisible.value = true;
};

const viewUserDetails = () => {
  userStatsDialogVisible.value = true;
};

const viewAssessmentDetails = () => {
  assessmentStatsDialogVisible.value = true;
};

const viewScaleDetails = () => {
  scaleStatsDialogVisible.value = true;
};

// 工具函数
const getHealthStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'excellent': 'success',
    'good': 'success', 
    'fair': 'warning',
    'poor': 'danger'
  };
  return statusMap[status] || 'info';
};

const getHealthStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'excellent': '优秀',
    'good': '良好',
    'fair': '一般', 
    'poor': '较差'
  };
  return textMap[status] || '未知';
};

const getWeekGrowth = () => {
  // 简化计算，实际应该基于历史数据
  return '+12%';
};

const getMonthGrowth = () => {
  // 简化计算，实际应该基于历史数据
  return '+28%';
};

const getTenantTrend = () => {
  const trend = tenantStats.value.newTenantsTrend || [];
  const recent = trend.slice(-7).reduce((sum: number, day: any) => sum + (day.count || 0), 0);
  return recent > 0 ? `+${recent}` : '0';
};

const getAssessmentTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    'REGULAR': '常规',
    'FOLLOWUP': '随访',
    'EMERGENCY': '紧急',
    'REASSESSMENT': '复评'
  };
  return nameMap[type] || type;
};

const getQualityColor = (percentage: number) => {
  if (percentage >= 80) return '#67C23A';
  if (percentage >= 60) return '#E6A23C';
  return '#F56C6C';
};

const getDatabaseStatusType = () => {
  const status = performanceData.value.database?.status;
  return status === 'healthy' ? 'success' : 'warning';
};

const getMemoryColor = (percentage: number) => {
  if (percentage <= 70) return '#67C23A';
  if (percentage <= 85) return '#E6A23C';
  return '#F56C6C';
};

const getActivityColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'ASSESSMENT_CREATED': '#409EFF',
    'USER_LOGIN': '#67C23A',
    'SYSTEM_EVENT': '#E6A23C'
  };
  return colorMap[type] || '#909399';
};

const formatActivityTime = (timestamp: string) => {
  if (!timestamp) return '';
  const now = new Date();
  const time = new Date(timestamp);
  const diffMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
  
  if (diffMinutes < 1) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`;
  return time.toLocaleDateString('zh-CN');
};

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'COMPLETED': 'success',
    'PENDING': 'warning',
    'FAILED': 'danger'
  };
  return typeMap[status] || 'info';
};

// 初始化
onMounted(() => {
  refreshAllData();
  
  // 设置定时刷新（每5分钟）
  setInterval(() => {
    refreshAllData();
  }, 5 * 60 * 1000);
});
</script>

<style scoped>
.system-dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.overview-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.health-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.health-score {
  font-size: 14px;
  color: #606266;
}

.overview-item {
  text-align: center;
  padding: 10px;
}

.overview-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-item.performance .overview-number {
  color: #E6A23C;
}

.overview-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 3px;
}

.overview-sub {
  font-size: 12px;
  color: #909399;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 200px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 130px;
}

.status-distribution,
.user-breakdown,
.assessment-types,
.scale-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item,
.breakdown-item,
.type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-value.active,
.breakdown-value.active {
  color: #67C23A;
  font-weight: 600;
}

.status-value.suspended {
  color: #E6A23C;
  font-weight: 600;
}

.status-value.inactive,
.status-value.deprecated {
  color: #F56C6C;
  font-weight: 600;
}

.breakdown-value.admin {
  color: #409EFF;
  font-weight: 600;
}

.breakdown-value.regular {
  color: #606266;
  font-weight: 600;
}

.trend-info,
.quality-info,
.popular-scale {
  margin-top: 10px;
  text-align: center;
}

.quality-text {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.performance-card {
  margin-bottom: 20px;
}

.performance-status {
  display: flex;
  gap: 10px;
}

.performance-item {
  text-align: center;
}

.performance-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
}

.memory-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.memory-bar {
  width: 100%;
}

.memory-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.response-time,
.system-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-item,
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-value,
.info-value {
  font-weight: 600;
  color: #303133;
}

.activities-card {
  margin-bottom: 20px;
}

.activities-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 10px 0;
  border-bottom: 1px solid #EBEEF5;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #F5F7FA;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-description {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-item {
    margin-bottom: 15px;
  }
  
  .stats-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .health-status {
    justify-content: center;
  }
}
</style>