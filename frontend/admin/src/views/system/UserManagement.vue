<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理所有平台用户和租户关系</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleCreate"
          v-if="canManageUsers"
        >
          新增用户
        </el-button>
        <el-button
          icon="Refresh"
          @click="refreshData"
        >
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名、邮箱或姓名"
          style="width: 280px; margin-right: 10px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="roleFilter"
          placeholder="角色筛选"
          style="width: 120px; margin-right: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部" value="" />
          <el-option label="管理员" value="ADMIN" />
          <el-option label="用户" value="USER" />
        </el-select>

        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部" value="" />
          <el-option label="活跃" value="true" />
          <el-option label="禁用" value="false" />
        </el-select>
      </div>
    </div>

    <!-- 用户列表 -->
    <el-table
      :data="userList"
      v-loading="loading"
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="user-detail">
            <div class="user-info">
              <h4>基本信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <label>用户名:</label>
                  <span>{{ row.user.username }}</span>
                </div>
                <div class="info-item">
                  <label>邮箱:</label>
                  <span>{{ row.user.email }}</span>
                </div>
                <div class="info-item">
                  <label>手机:</label>
                  <span>{{ row.user.phone || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>平台角色:</label>
                  <el-tag :type="getPlatformRoleTagType(row.user.platformRole)">
                    {{ getPlatformRoleText(row.user.platformRole) }}
                  </el-tag>
                </div>
              </div>
            </div>
            
            <div class="tenant-memberships" v-if="row.memberships.length > 0">
              <h4>租户关系</h4>
              <div class="membership-list">
                <div 
                  v-for="membership in row.memberships" 
                  :key="membership.id"
                  class="membership-card"
                >
                  <div class="membership-info">
                    <div class="tenant-name">租户: {{ membership.tenantId }}</div>
                    <div class="role-info">
                      <el-tag size="small" :type="getTenantRoleTagType(membership.tenantRole)">
                        {{ getTenantRoleText(membership.tenantRole) }}
                      </el-tag>
                    </div>
                    <div class="status-info">
                      <el-tag 
                        size="small" 
                        :type="membership.status === 'ACTIVE' ? 'success' : 'danger'"
                      >
                        {{ membership.status === 'ACTIVE' ? '活跃' : '禁用' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="membership-actions">
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="handleEditMembership(row.user, membership)"
                      v-if="canManageUsers"
                    >
                      编辑
                    </el-button>
                    <el-button
                      type="danger"
                      link
                      size="small"
                      @click="handleRemoveMembership(row.user, membership)"
                      v-if="canManageUsers"
                    >
                      移除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="user.username"
        label="用户名"
        width="150"
        sortable="custom"
      />
      
      <el-table-column
        prop="user.fullName"
        label="姓名"
        width="120"
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="user-name">
            <img 
              v-if="row.user.avatarUrl" 
              :src="row.user.avatarUrl" 
              :alt="row.user.fullName"
              class="user-avatar"
            />
            <div class="name-info">
              <div class="full-name">{{ row.user.fullName || '-' }}</div>
              <div class="first-last-name" v-if="row.user.firstName || row.user.lastName">
                {{ row.user.firstName }} {{ row.user.lastName }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="user.email"
        label="邮箱"
        width="200"
        sortable="custom"
      />

      <el-table-column
        prop="user.phone"
        label="手机"
        width="140"
      >
        <template #default="{ row }">
          {{ row.user.phone || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="user.platformRole"
        label="平台角色"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getPlatformRoleTagType(row.user.platformRole)"
            size="small"
          >
            {{ getPlatformRoleText(row.user.platformRole) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="租户数量"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-badge 
            :value="row.memberships.length" 
            :type="row.memberships.length > 0 ? 'primary' : 'info'"
          >
            <span>{{ row.memberships.length }}</span>
          </el-badge>
        </template>
      </el-table-column>

      <el-table-column
        prop="user.isActive"
        label="状态"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="row.user.isActive ? 'success' : 'danger'"
            size="small"
          >
            {{ row.user.isActive ? '活跃' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="user.createdAt"
        label="创建时间"
        width="160"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.user.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          
          <el-button
            type="primary"
            link
            size="small"
            @click="handleEdit(row)"
            v-if="canManageUsers"
          >
            编辑
          </el-button>

          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="canManageUsers"
          >
            <el-button type="primary" link size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :command="`reset-password-${row.user.id}`"
                >
                  重置密码
                </el-dropdown-item>
                <el-dropdown-item
                  :command="`toggle-${row.user.id}`"
                  :disabled="row.user.platformRole === 'ADMIN'"
                >
                  {{ row.user.isActive ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  :command="`add-tenant-${row.user.id}`"
                  divided
                >
                  添加租户关系
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handlePageSizeChange"
      @current-change="handlePageChange"
      style="margin-top: 20px; justify-content: center"
    />

    <!-- 用户详情/编辑对话框 -->
    <user-form-dialog
      v-model:visible="formDialogVisible"
      :user="currentUser"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- 租户关系管理对话框 -->
    <tenant-membership-dialog
      v-model:visible="membershipDialogVisible"
      :user="currentUser"
      :membership="currentMembership"
      :mode="membershipMode"
      @success="handleMembershipSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus, Refresh, ArrowDown } from '@element-plus/icons-vue';
import { platformUserApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';
import UserFormDialog from './components/UserFormDialog.vue';
import TenantMembershipDialog from './components/TenantMembershipDialog.vue';

// 数据状态
const userList = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const roleFilter = ref('');
const statusFilter = ref('');

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
});

// 排序
const sortField = ref('');
const sortOrder = ref('');

// 对话框状态
const formDialogVisible = ref(false);
const formMode = ref<'create' | 'edit' | 'view'>('create');
const currentUser = ref(null);

const membershipDialogVisible = ref(false);
const membershipMode = ref<'create' | 'edit'>('create');
const currentMembership = ref(null);

// 权限控制
const canManageUsers = computed(() => {
  return tenantContext.isAdmin() || tenantContext.hasPermission('USER_MANAGE');
});

// 获取用户列表
const fetchUserList = async () => {
  try {
    loading.value = true;
    
    const params = {
      page: pagination.current - 1,
      size: pagination.size,
      search: searchQuery.value || undefined,
      role: roleFilter.value || undefined,
      sortField: sortField.value || undefined,
      sortOrder: sortOrder.value || undefined
    };

    const response = await platformUserApi.getUsers(params);
    const { data } = response;

    if (data.success) {
      userList.value = data.data.content || [];
      pagination.total = data.data.totalElements || 0;
    } else {
      ElMessage.error(data.message || '获取用户列表失败');
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error);
    ElMessage.error(error.message || '获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchUserList();
};

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  sortField.value = prop || '';
  sortOrder.value = order === 'ascending' ? 'ASC' : order === 'descending' ? 'DESC' : '';
  fetchUserList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchUserList();
};

const handlePageSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchUserList();
};

// 刷新数据
const refreshData = () => {
  fetchUserList();
};

// 操作处理
const handleCreate = () => {
  currentUser.value = null;
  formMode.value = 'create';
  formDialogVisible.value = true;
};

const handleView = (userWithMemberships: any) => {
  currentUser.value = userWithMemberships;
  formMode.value = 'view';
  formDialogVisible.value = true;
};

const handleEdit = (userWithMemberships: any) => {
  currentUser.value = userWithMemberships;
  formMode.value = 'edit';
  formDialogVisible.value = true;
};

// 下拉菜单操作
const handleDropdownCommand = async (command: string, userWithMemberships: any) => {
  const [action, id] = command.split('-');
  
  try {
    switch (action) {
      case 'reset':
        if (command.startsWith('reset-password')) {
          await handleResetPassword(userWithMemberships.user);
        }
        break;
      case 'toggle':
        await handleToggleUser(userWithMemberships.user);
        break;
      case 'add':
        if (command.startsWith('add-tenant')) {
          handleAddTenantMembership(userWithMemberships);
        }
        break;
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败');
  }
};

// 重置密码
const handleResetPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 "${user.username}" 的密码吗？`,
      '确认重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await platformUserApi.resetPassword(user.id);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success(data.message);
    } else {
      ElMessage.error(data.message || '重置密码失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '重置密码失败');
    }
  }
};

// 切换用户状态
const handleToggleUser = async (user: any) => {
  const actionText = user.isActive ? '禁用' : '启用';
  
  try {
    await ElMessageBox.confirm(
      `确定要${actionText}用户 "${user.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await platformUserApi.updateUser(user.id, {
      ...user,
      isActive: !user.isActive
    });
    
    const { data } = response;
    if (data.success) {
      ElMessage.success(`${actionText}成功`);
      fetchUserList();
    } else {
      ElMessage.error(data.message || `${actionText}失败`);
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || `${actionText}失败`);
    }
  }
};

// 添加租户关系
const handleAddTenantMembership = (userWithMemberships: any) => {
  currentUser.value = userWithMemberships;
  currentMembership.value = null;
  membershipMode.value = 'create';
  membershipDialogVisible.value = true;
};

// 编辑租户关系
const handleEditMembership = (user: any, membership: any) => {
  currentUser.value = { user, memberships: [] };
  currentMembership.value = membership;
  membershipMode.value = 'edit';
  membershipDialogVisible.value = true;
};

// 移除租户关系
const handleRemoveMembership = async (user: any, membership: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除用户 "${user.username}" 在租户中的关系吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await platformUserApi.removeTenantMembership(user.id, membership.tenantId);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('租户关系移除成功');
      fetchUserList();
    } else {
      ElMessage.error(data.message || '移除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '移除失败');
    }
  }
};

// 表单操作成功
const handleFormSuccess = () => {
  formDialogVisible.value = false;
  fetchUserList();
};

// 租户关系操作成功
const handleMembershipSuccess = () => {
  membershipDialogVisible.value = false;
  fetchUserList();
};

// 工具函数
const getPlatformRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'ADMIN': 'danger',
    'USER': 'primary'
  };
  return typeMap[role] || 'info';
};

const getPlatformRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    'ADMIN': '管理员',
    'USER': '用户'
  };
  return textMap[role] || role;
};

const getTenantRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'ADMIN': 'danger',
    'SUPERVISOR': 'warning',
    'ASSESSOR': 'primary',
    'REVIEWER': 'success',
    'VIEWER': 'info'
  };
  return typeMap[role] || 'info';
};

const getTenantRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    'ADMIN': '管理员',
    'SUPERVISOR': '督导员',
    'ASSESSOR': '评估员',
    'REVIEWER': '审核员',
    'VIEWER': '查看员'
  };
  return textMap[role] || role;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
onMounted(() => {
  fetchUserList();
});
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.name-info {
  display: flex;
  flex-direction: column;
}

.full-name {
  font-weight: 500;
  color: #303133;
}

.first-last-name {
  font-size: 12px;
  color: #909399;
}

.user-detail {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin: 10px 0;
}

.user-info h4,
.tenant-memberships h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 60px;
}

.tenant-memberships {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.membership-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.membership-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.membership-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.tenant-name {
  font-weight: 500;
  color: #303133;
}

.membership-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .toolbar-right {
    justify-content: space-between;
  }

  .user-name {
    flex-direction: column;
    align-items: flex-start;
  }

  .membership-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .membership-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>