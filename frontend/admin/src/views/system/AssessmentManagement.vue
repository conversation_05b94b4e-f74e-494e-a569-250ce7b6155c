<template>
  <div class="assessment-management">
    <div class="page-header">
      <h1>评估记录管理</h1>
      <p>管理系统中的所有评估记录</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          icon="Refresh"
          @click="refreshData"
        >
          刷新
        </el-button>
        <el-button
          icon="TrendCharts"
          @click="showStats"
        >
          统计信息
        </el-button>
        <el-button
          type="warning"
          icon="Clock"
          @click="showPendingReviews"
          v-if="canReviewAssessments"
        >
          待审核记录
        </el-button>
        <el-button
          type="primary"
          icon="Check"
          @click="handleBatchReview(true)"
          :disabled="selectedRecords.length === 0"
          v-if="canReviewAssessments"
        >
          批量通过
        </el-button>
        <el-button
          type="danger"
          icon="Close"
          @click="handleBatchReview(false)"
          :disabled="selectedRecords.length === 0"
          v-if="canReviewAssessments"
        >
          批量拒绝
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-select
          v-model="tenantFilter"
          placeholder="选择租户"
          style="width: 200px; margin-right: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部租户" value="" />
          <el-option 
            v-for="tenant in tenants" 
            :key="tenant.id" 
            :label="tenant.name" 
            :value="tenant.id" 
          />
        </el-select>

        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px; margin-right: 10px"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部状态" value="" />
          <el-option label="草稿" value="DRAFT" />
          <el-option label="已提交" value="SUBMITTED" />
          <el-option label="已审核" value="REVIEWED" />
          <el-option label="已批准" value="APPROVED" />
          <el-option label="已归档" value="ARCHIVED" />
          <el-option label="已拒绝" value="REJECTED" />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 300px"
          @change="handleSearch"
        />
      </div>
    </div>

    <!-- 评估记录列表 -->
    <el-table
      :data="assessmentList"
      v-loading="loading"
      style="width: 100%"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column 
        type="selection" 
        width="55"
        v-if="canReviewAssessments"
      />
      
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="assessment-detail">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <label>记录编号:</label>
                  <span>{{ row.record.recordNumber }}</span>
                </div>
                <div class="info-item">
                  <label>租户:</label>
                  <span>{{ row.tenantInfo?.name || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>量表:</label>
                  <span>{{ row.scaleInfo?.name || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>评估员:</label>
                  <span>{{ row.assessorInfo?.fullName || row.assessorInfo?.username || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>评估类型:</label>
                  <span>{{ getAssessmentTypeText(row.record.assessmentType) }}</span>
                </div>
                <div class="info-item">
                  <label>总分:</label>
                  <span>{{ row.record.totalScore || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>结果等级:</label>
                  <span>{{ row.record.resultLevel || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>质量评分:</label>
                  <span>{{ row.record.qualityScore || '-' }}</span>
                </div>
              </div>
            </div>
            
            <div class="detail-section" v-if="row.record.reviewerId">
              <h4>审核信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <label>审核员:</label>
                  <span>{{ row.reviewerInfo?.fullName || row.reviewerInfo?.username || '-' }}</span>
                </div>
                <div class="info-item">
                  <label>审核时间:</label>
                  <span>{{ formatDateTime(row.record.reviewedAt) }}</span>
                </div>
                <div class="info-item" v-if="row.record.reviewNotes">
                  <label>审核备注:</label>
                  <span>{{ row.record.reviewNotes }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4>时间信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <label>评估时间:</label>
                  <span>{{ formatDateTime(row.record.assessmentDate) }}</span>
                </div>
                <div class="info-item">
                  <label>创建时间:</label>
                  <span>{{ formatDateTime(row.record.createdAt) }}</span>
                </div>
                <div class="info-item">
                  <label>更新时间:</label>
                  <span>{{ formatDateTime(row.record.updatedAt) }}</span>
                </div>
                <div class="info-item">
                  <label>评估耗时:</label>
                  <span>{{ row.record.durationMinutes }} 分钟</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="record.recordNumber"
        label="记录编号"
        width="150"
        sortable="custom"
      />
      
      <el-table-column
        label="租户"
        width="120"
      >
        <template #default="{ row }">
          <div class="tenant-info">
            <div class="tenant-name">{{ row.tenantInfo?.name || '-' }}</div>
            <div class="tenant-code">{{ row.tenantInfo?.code || '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="量表"
        width="150"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="scale-info">
            <div class="scale-name">{{ row.scaleInfo?.name || '-' }}</div>
            <div class="scale-category">{{ row.scaleInfo?.category || '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="record.assessorId"
        label="评估员"
        width="120"
      >
        <template #default="{ row }">
          {{ row.assessorInfo?.fullName || row.assessorInfo?.username || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="record.status"
        label="状态"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getStatusTagType(row.record.status)"
            size="small"
          >
            {{ getStatusText(row.record.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="record.assessmentType"
        label="评估类型"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getAssessmentTypeTagType(row.record.assessmentType)"
            size="small"
          >
            {{ getAssessmentTypeText(row.record.assessmentType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="record.totalScore"
        label="总分"
        width="80"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ row.record.totalScore || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="record.qualityScore"
        label="质量评分"
        width="100"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-rate
            v-if="row.record.qualityScore"
            :model-value="row.record.qualityScore"
            :max="5"
            disabled
            size="small"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="record.assessmentDate"
        label="评估时间"
        width="160"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.record.assessmentDate) }}
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, row)"
            v-if="canReviewAssessments"
          >
            <el-button type="primary" link size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="row.record.status === 'SUBMITTED'"
                  :command="`approve-${row.record.id}`"
                >
                  审核通过
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="row.record.status === 'SUBMITTED'"
                  :command="`reject-${row.record.id}`"
                >
                  审核拒绝
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="row.record.status === 'APPROVED'"
                  :command="`archive-${row.record.id}`"
                  divided
                >
                  归档记录
                </el-dropdown-item>
                <el-dropdown-item
                  :command="`delete-${row.record.id}`"
                  divided
                >
                  删除记录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handlePageSizeChange"
      @current-change="handlePageChange"
      style="margin-top: 20px; justify-content: center"
    />

    <!-- 评估记录详情对话框 -->
    <assessment-detail-dialog
      v-model:visible="detailDialogVisible"
      :assessment="currentAssessment"
    />

    <!-- 审核对话框 -->
    <review-dialog
      v-model:visible="reviewDialogVisible"
      :assessment="currentAssessment"
      :mode="reviewMode"
      @success="handleReviewSuccess"
    />

    <!-- 批量审核对话框 -->
    <batch-review-dialog
      v-model:visible="batchReviewDialogVisible"
      :records="selectedRecords"
      :approved="batchReviewApproved"
      @success="handleBatchReviewSuccess"
    />

    <!-- 统计信息对话框 -->
    <assessment-stats-dialog
      v-model:visible="statsDialogVisible"
      @refresh="refreshData"
    />

    <!-- 待审核记录对话框 -->
    <pending-review-dialog
      v-model:visible="pendingDialogVisible"
      @refresh="refreshData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh, TrendCharts, Clock, Check, Close, ArrowDown } from '@element-plus/icons-vue';
import { systemAssessmentApi, tenantManageApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';
import AssessmentDetailDialog from './components/AssessmentDetailDialog.vue';
import ReviewDialog from './components/ReviewDialog.vue';
import BatchReviewDialog from './components/BatchReviewDialog.vue';
import AssessmentStatsDialog from './components/AssessmentStatsDialog.vue';
import PendingReviewDialog from './components/PendingReviewDialog.vue';

// 数据状态
const assessmentList = ref([]);
const tenants = ref([]);
const selectedRecords = ref([]);
const loading = ref(false);
const tenantFilter = ref('');
const statusFilter = ref('');
const dateRange = ref([]);

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
});

// 排序
const sortField = ref('');
const sortOrder = ref('');

// 对话框状态
const detailDialogVisible = ref(false);
const reviewDialogVisible = ref(false);
const batchReviewDialogVisible = ref(false);
const statsDialogVisible = ref(false);
const pendingDialogVisible = ref(false);

const currentAssessment = ref(null);
const reviewMode = ref<'approve' | 'reject'>('approve');
const batchReviewApproved = ref(true);

// 权限控制
const canReviewAssessments = computed(() => {
  return tenantContext.isAdmin() || tenantContext.hasPermission('ASSESSMENT_REVIEW');
});

// 获取评估记录列表
const fetchAssessmentList = async () => {
  try {
    loading.value = true;
    
    const params = {
      page: pagination.current - 1,
      size: pagination.size,
      tenantId: tenantFilter.value || undefined,
      status: statusFilter.value || undefined,
      startDate: dateRange.value?.[0]?.toISOString(),
      endDate: dateRange.value?.[1]?.toISOString(),
      sortField: sortField.value || undefined,
      sortOrder: sortOrder.value || undefined
    };

    const response = await systemAssessmentApi.getAssessments(params);
    const { data } = response;

    if (data.success) {
      assessmentList.value = data.data.content || [];
      pagination.total = data.data.totalElements || 0;
    } else {
      ElMessage.error(data.message || '获取评估记录列表失败');
    }
  } catch (error: any) {
    console.error('获取评估记录列表失败:', error);
    ElMessage.error(error.message || '获取评估记录列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取租户列表
const fetchTenants = async () => {
  try {
    const response = await tenantManageApi.getTenants({ size: 1000 });
    const { data } = response;
    
    if (data.success) {
      tenants.value = data.data.content || [];
    }
  } catch (error) {
    console.warn('获取租户列表失败:', error);
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchAssessmentList();
};

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  sortField.value = prop || '';
  sortOrder.value = order === 'ascending' ? 'ASC' : order === 'descending' ? 'DESC' : '';
  fetchAssessmentList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchAssessmentList();
};

const handlePageSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchAssessmentList();
};

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection;
};

// 刷新数据
const refreshData = () => {
  fetchAssessmentList();
  fetchTenants();
};

// 操作处理
const handleView = (assessment: any) => {
  currentAssessment.value = assessment;
  detailDialogVisible.value = true;
};

const showStats = () => {
  statsDialogVisible.value = true;
};

const showPendingReviews = () => {
  pendingDialogVisible.value = true;
};

// 下拉菜单操作
const handleDropdownCommand = async (command: string, assessment: any) => {
  const [action, id] = command.split('-');
  
  try {
    switch (action) {
      case 'approve':
        currentAssessment.value = assessment;
        reviewMode.value = 'approve';
        reviewDialogVisible.value = true;
        break;
      case 'reject':
        currentAssessment.value = assessment;
        reviewMode.value = 'reject';
        reviewDialogVisible.value = true;
        break;
      case 'archive':
        await handleArchiveAssessment(assessment);
        break;
      case 'delete':
        await handleDeleteAssessment(assessment);
        break;
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败');
  }
};

// 批量审核
const handleBatchReview = (approved: boolean) => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要审核的记录');
    return;
  }
  
  batchReviewApproved.value = approved;
  batchReviewDialogVisible.value = true;
};

// 归档评估记录
const handleArchiveAssessment = async (assessment: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要归档评估记录 "${assessment.record.recordNumber}" 吗？`,
      '确认归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await systemAssessmentApi.archiveAssessment(assessment.record.id);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('评估记录归档成功');
      fetchAssessmentList();
    } else {
      ElMessage.error(data.message || '归档失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '归档失败');
    }
  }
};

// 删除评估记录
const handleDeleteAssessment = async (assessment: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除评估记录 "${assessment.record.recordNumber}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'danger'
      }
    );

    const response = await systemAssessmentApi.deleteAssessment(assessment.record.id);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success('评估记录删除成功');
      fetchAssessmentList();
    } else {
      ElMessage.error(data.message || '删除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败');
    }
  }
};

// 审核成功回调
const handleReviewSuccess = () => {
  reviewDialogVisible.value = false;
  fetchAssessmentList();
};

// 批量审核成功回调
const handleBatchReviewSuccess = () => {
  batchReviewDialogVisible.value = false;
  selectedRecords.value = [];
  fetchAssessmentList();
};

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'DRAFT': 'info',
    'SUBMITTED': 'warning',
    'REVIEWED': 'primary',
    'APPROVED': 'success',
    'ARCHIVED': 'info',
    'REJECTED': 'danger'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'SUBMITTED': '已提交',
    'REVIEWED': '已审核',
    'APPROVED': '已批准',
    'ARCHIVED': '已归档',
    'REJECTED': '已拒绝'
  };
  return textMap[status] || status;
};

const getAssessmentTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'REGULAR': 'primary',
    'FOLLOWUP': 'success',
    'EMERGENCY': 'danger',
    'REASSESSMENT': 'warning'
  };
  return typeMap[type] || 'info';
};

const getAssessmentTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'REGULAR': '常规评估',
    'FOLLOWUP': '跟踪评估',
    'EMERGENCY': '紧急评估',
    'REASSESSMENT': '重新评估'
  };
  return textMap[type] || type;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
onMounted(() => {
  fetchAssessmentList();
  fetchTenants();
});
</script>

<style scoped>
.assessment-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tenant-info,
.scale-info {
  display: flex;
  flex-direction: column;
}

.tenant-name,
.scale-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.tenant-code,
.scale-category {
  font-size: 12px;
  color: #909399;
}

.assessment-detail {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin: 10px 0;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .toolbar-right {
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>