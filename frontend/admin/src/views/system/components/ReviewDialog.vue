<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="getDialogTitle()"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="review-form" v-if="assessment">
      <!-- 评估记录信息 -->
      <el-card shadow="never" class="assessment-info-card">
        <template #header>
          <div class="card-header">
            <span>评估记录信息</span>
          </div>
        </template>
        
        <div class="assessment-summary">
          <div class="summary-item">
            <label>记录编号:</label>
            <span>{{ assessment.record.recordNumber }}</span>
          </div>
          <div class="summary-item">
            <label>租户:</label>
            <span>{{ assessment.tenantInfo?.name || '-' }}</span>
          </div>
          <div class="summary-item">
            <label>量表:</label>
            <span>{{ assessment.scaleInfo?.name || '-' }}</span>
          </div>
          <div class="summary-item">
            <label>评估员:</label>
            <span>{{ assessment.assessorInfo?.fullName || assessment.assessorInfo?.username || '-' }}</span>
          </div>
          <div class="summary-item">
            <label>评估时间:</label>
            <span>{{ formatDateTime(assessment.record.assessmentDate) }}</span>
          </div>
          <div class="summary-item">
            <label>总分:</label>
            <span>{{ assessment.record.totalScore || '-' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 审核表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="review-form-content"
      >
        <el-form-item label="审核结果" prop="approved">
          <el-radio-group v-model="formData.approved">
            <el-radio :label="true" class="approve-radio">
              <el-icon color="#67C23A"><Check /></el-icon>
              审核通过
            </el-radio>
            <el-radio :label="false" class="reject-radio">
              <el-icon color="#F56C6C"><Close /></el-icon>
              审核拒绝
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          :label="formData.approved ? '通过备注' : '拒绝原因'" 
          prop="reviewNotes"
        >
          <el-input
            v-model="formData.reviewNotes"
            type="textarea"
            :rows="4"
            :placeholder="formData.approved ? '请输入审核通过的备注信息（可选）' : '请输入审核拒绝的原因'"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 质量评分（可选） -->
        <el-form-item label="质量评分" prop="qualityScore">
          <div class="quality-score-section">
            <el-rate
              v-model="formData.qualityScore"
              :max="5"
              :allow-half="true"
              :show-text="true"
              :texts="['很差', '较差', '一般', '良好', '优秀']"
            />
            <div class="score-tips">
              <small>根据评估记录的完整性、准确性和规范性进行评分</small>
            </div>
          </div>
        </el-form-item>

        <!-- 审核建议（通过时显示） -->
        <el-form-item 
          v-if="formData.approved" 
          label="后续建议"
        >
          <el-checkbox-group v-model="formData.suggestions">
            <el-checkbox label="archive">建议归档</el-checkbox>
            <el-checkbox label="followup">建议跟踪评估</el-checkbox>
            <el-checkbox label="training">建议评估员培训</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 紧急程度（拒绝时显示） -->
        <el-form-item 
          v-if="!formData.approved" 
          label="问题严重程度"
        >
          <el-radio-group v-model="formData.severity">
            <el-radio label="low">轻微问题</el-radio>
            <el-radio label="medium">一般问题</el-radio>
            <el-radio label="high">严重问题</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <!-- 审核历史（如果有） -->
      <el-card 
        shadow="never" 
        class="history-card"
        v-if="assessment.record.reviewerId"
      >
        <template #header>
          <div class="card-header">
            <span>审核历史</span>
          </div>
        </template>
        
        <div class="review-history">
          <div class="history-item">
            <div class="history-info">
              <span class="reviewer">{{ assessment.reviewerInfo?.fullName || assessment.reviewerInfo?.username || '-' }}</span>
              <span class="review-time">{{ formatDateTime(assessment.record.reviewedAt) }}</span>
            </div>
            <div class="history-notes" v-if="assessment.record.reviewNotes">
              {{ assessment.record.reviewNotes }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          提交审核
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Check, Close } from '@element-plus/icons-vue';
import { systemAssessmentApi } from '@/api/multiTenantAdapter';
import tenantContext from '@/utils/tenantContext';

interface Props {
  visible: boolean;
  assessment?: any;
  mode: 'approve' | 'reject';
}

const props = withDefaults(defineProps<Props>(), {
  assessment: null,
  mode: 'approve'
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用和状态
const formRef = ref();
const submitting = ref(false);

// 表单数据
const formData = reactive({
  approved: true,
  reviewNotes: '',
  qualityScore: 0,
  suggestions: [] as string[],
  severity: 'medium'
});

// 表单验证规则
const formRules = computed(() => ({
  approved: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  reviewNotes: [
    { 
      required: !formData.approved, 
      message: '拒绝审核时必须填写拒绝原因', 
      trigger: 'blur' 
    },
    { 
      min: formData.approved ? 0 : 10, 
      message: '拒绝原因至少需要10个字符', 
      trigger: 'blur' 
    }
  ]
}));

// 监听props变化，初始化表单数据
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      initFormData();
    });
  }
});

// 监听mode变化
watch(() => props.mode, (newMode) => {
  if (newMode) {
    formData.approved = newMode === 'approve';
  }
});

// 初始化表单数据
const initFormData = () => {
  // 根据mode设置初始审核结果
  formData.approved = props.mode === 'approve';
  formData.reviewNotes = '';
  formData.qualityScore = 0;
  formData.suggestions = [];
  formData.severity = 'medium';
  
  // 清除验证状态
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 获取对话框标题
const getDialogTitle = () => {
  const actionText = formData.approved ? '审核通过' : '审核拒绝';
  return `${actionText} - ${props.assessment?.record.recordNumber || ''}`;
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    submitting.value = true;

    // 准备提交数据
    const submitData = {
      approved: formData.approved,
      reviewNotes: formData.reviewNotes.trim(),
      reviewerId: tenantContext.getCurrentUserId()
    };

    const response = await systemAssessmentApi.reviewAssessment(props.assessment.record.id, submitData);
    const { data } = response;
    
    if (data.success) {
      ElMessage.success(data.message || '审核完成');
      emit('success');
    } else {
      ElMessage.error(data.message || '审核失败');
    }
  } catch (error: any) {
    console.error('提交审核失败:', error);
    ElMessage.error(error.message || '审核失败');
  } finally {
    submitting.value = false;
  }
};

// 工具函数
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 初始化
initFormData();
</script>

<style scoped>
.review-form {
  max-height: 70vh;
  overflow-y: auto;
}

.assessment-info-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
}

.assessment-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item label {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
}

.summary-item span {
  color: #303133;
}

.review-form-content {
  margin: 20px 0;
}

.approve-radio,
.reject-radio {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.quality-score-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.score-tips {
  color: #909399;
}

.history-card {
  margin-top: 20px;
}

.review-history {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.history-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reviewer {
  font-weight: 500;
  color: #303133;
}

.review-time {
  font-size: 12px;
  color: #909399;
}

.history-notes {
  color: #606266;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-radio) {
  margin-right: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
  
  .assessment-summary {
    grid-template-columns: 1fr;
  }
  
  .history-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>