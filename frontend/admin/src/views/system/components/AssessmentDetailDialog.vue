<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="评估记录详情"
    width="90%"
    :close-on-click-modal="false"
  >
    <div class="assessment-detail-container" v-if="assessment">
      <!-- 基本信息卡片 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusTagType(assessment.record.status)">
              {{ getStatusText(assessment.record.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-grid">
              <div class="info-item">
                <label>记录编号:</label>
                <span class="value">{{ assessment.record.recordNumber }}</span>
              </div>
              <div class="info-item">
                <label>租户:</label>
                <span class="value">{{ assessment.tenantInfo?.name || '-' }}</span>
              </div>
              <div class="info-item">
                <label>租户代码:</label>
                <span class="value">{{ assessment.tenantInfo?.code || '-' }}</span>
              </div>
              <div class="info-item">
                <label>量表名称:</label>
                <span class="value">{{ assessment.scaleInfo?.name || '-' }}</span>
              </div>
              <div class="info-item">
                <label>量表分类:</label>
                <span class="value">{{ assessment.scaleInfo?.category || '-' }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-grid">
              <div class="info-item">
                <label>评估员:</label>
                <span class="value">{{ assessment.assessorInfo?.fullName || assessment.assessorInfo?.username || '-' }}</span>
              </div>
              <div class="info-item">
                <label>评估类型:</label>
                <el-tag :type="getAssessmentTypeTagType(assessment.record.assessmentType)" size="small">
                  {{ getAssessmentTypeText(assessment.record.assessmentType) }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>评估时间:</label>
                <span class="value">{{ formatDateTime(assessment.record.assessmentDate) }}</span>
              </div>
              <div class="info-item">
                <label>评估耗时:</label>
                <span class="value">{{ assessment.record.durationMinutes }} 分钟</span>
              </div>
              <div class="info-item">
                <label>创建时间:</label>
                <span class="value">{{ formatDateTime(assessment.record.createdAt) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 评估结果卡片 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <div class="card-header">
            <span>评估结果</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="score-display">
              <div class="score-label">总分</div>
              <div class="score-value">{{ assessment.record.totalScore || '-' }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="score-display">
              <div class="score-label">结果等级</div>
              <div class="score-value">{{ assessment.record.resultLevel || '-' }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="score-display">
              <div class="score-label">质量评分</div>
              <div class="score-value">
                <el-rate
                  v-if="assessment.record.qualityScore"
                  :model-value="assessment.record.qualityScore"
                  :max="5"
                  disabled
                  size="small"
                />
                <span v-else>-</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="completeness-info" v-if="assessment.record.completenessScore">
          <label>完整性评分:</label>
          <el-progress 
            :percentage="assessment.record.completenessScore * 100"
            :color="getCompletenessColor(assessment.record.completenessScore)"
          />
        </div>
      </el-card>

      <!-- 审核信息卡片 -->
      <el-card 
        shadow="never" 
        class="detail-card"
        v-if="assessment.record.reviewerId || assessment.record.status === 'SUBMITTED'"
      >
        <template #header>
          <div class="card-header">
            <span>审核信息</span>
            <el-tag 
              v-if="assessment.record.status === 'SUBMITTED'" 
              type="warning"
            >
              待审核
            </el-tag>
          </div>
        </template>
        
        <div class="review-info" v-if="assessment.record.reviewerId">
          <div class="info-grid">
            <div class="info-item">
              <label>审核员:</label>
              <span class="value">{{ assessment.reviewerInfo?.fullName || assessment.reviewerInfo?.username || '-' }}</span>
            </div>
            <div class="info-item">
              <label>审核时间:</label>
              <span class="value">{{ formatDateTime(assessment.record.reviewedAt) }}</span>
            </div>
          </div>
          
          <div class="review-notes" v-if="assessment.record.reviewNotes">
            <label>审核备注:</label>
            <div class="notes-content">{{ assessment.record.reviewNotes }}</div>
          </div>
        </div>
        
        <div class="pending-review" v-else>
          <el-empty description="该记录正在等待审核" :image-size="80" />
        </div>
      </el-card>

      <!-- 表单数据卡片 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <div class="card-header">
            <span>表单数据</span>
            <el-button size="small" @click="showFormDataDialog">查看详细数据</el-button>
          </div>
        </template>
        
        <div class="form-data-preview">
          <el-descriptions :column="2" border>
            <el-descriptions-item 
              v-for="(value, key) in getFormDataPreview(assessment.record.formData)" 
              :key="key"
              :label="key"
            >
              {{ value }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 工作流信息卡片 -->
      <el-card 
        shadow="never" 
        class="detail-card"
        v-if="assessment.record.workflowStage"
      >
        <template #header>
          <div class="card-header">
            <span>工作流信息</span>
          </div>
        </template>
        
        <div class="workflow-info">
          <div class="info-item">
            <label>当前阶段:</label>
            <span class="value">{{ assessment.record.workflowStage }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="assessment?.record.status === 'SUBMITTED' && canReview"
          type="success"
          @click="handleReview(true)"
        >
          审核通过
        </el-button>
        <el-button 
          v-if="assessment?.record.status === 'SUBMITTED' && canReview"
          type="danger"
          @click="handleReview(false)"
        >
          审核拒绝
        </el-button>
      </div>
    </template>

    <!-- 表单数据详情对话框 -->
    <el-dialog
      v-model="formDataDialogVisible"
      title="表单数据详情"
      width="70%"
      append-to-body
    >
      <div class="form-data-detail">
        <el-input
          :model-value="formDataJson"
          type="textarea"
          :rows="20"
          readonly
          class="json-display"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import tenantContext from '@/utils/tenantContext';

interface Props {
  visible: boolean;
  assessment?: any;
}

const props = withDefaults(defineProps<Props>(), {
  assessment: null
});

const emit = defineEmits(['update:visible', 'review']);

// 对话框状态
const formDataDialogVisible = ref(false);

// 权限控制
const canReview = computed(() => {
  return tenantContext.isAdmin() || tenantContext.hasPermission('ASSESSMENT_REVIEW');
});

// 表单数据JSON字符串
const formDataJson = computed(() => {
  if (!props.assessment?.record.formData) return '';
  return JSON.stringify(props.assessment.record.formData, null, 2);
});

// 获取表单数据预览
const getFormDataPreview = (formData: any) => {
  if (!formData) return {};
  
  const preview: Record<string, any> = {};
  let count = 0;
  
  // 只显示前8个字段作为预览
  for (const [key, value] of Object.entries(formData)) {
    if (count >= 8) break;
    
    if (typeof value === 'object' && value !== null) {
      preview[key] = '[对象]';
    } else if (Array.isArray(value)) {
      preview[key] = `[数组: ${value.length}项]`;
    } else {
      preview[key] = String(value).length > 50 ? 
        String(value).substring(0, 50) + '...' : 
        String(value);
    }
    count++;
  }
  
  return preview;
};

// 显示表单数据详情
const showFormDataDialog = () => {
  formDataDialogVisible.value = true;
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 处理审核
const handleReview = (approved: boolean) => {
  emit('review', { assessment: props.assessment, approved });
};

// 获取完整性颜色
const getCompletenessColor = (score: number) => {
  if (score >= 0.8) return '#67C23A';
  if (score >= 0.6) return '#E6A23C';
  return '#F56C6C';
};

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'DRAFT': 'info',
    'SUBMITTED': 'warning',
    'REVIEWED': 'primary',
    'APPROVED': 'success',
    'ARCHIVED': 'info',
    'REJECTED': 'danger'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'SUBMITTED': '已提交',
    'REVIEWED': '已审核',
    'APPROVED': '已批准',
    'ARCHIVED': '已归档',
    'REJECTED': '已拒绝'
  };
  return textMap[status] || status;
};

const getAssessmentTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'REGULAR': 'primary',
    'FOLLOWUP': 'success',
    'EMERGENCY': 'danger',
    'REASSESSMENT': 'warning'
  };
  return typeMap[type] || 'info';
};

const getAssessmentTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'REGULAR': '常规评估',
    'FOLLOWUP': '跟踪评估',
    'EMERGENCY': '紧急评估',
    'REASSESSMENT': '重新评估'
  };
  return textMap[type] || type;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};
</script>

<style scoped>
.assessment-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  word-break: break-all;
}

.score-display {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.score-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.completeness-info {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.completeness-info label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.review-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.review-notes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.review-notes label {
  font-weight: 500;
  color: #606266;
}

.notes-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
  color: #303133;
  white-space: pre-wrap;
}

.pending-review {
  text-align: center;
  padding: 20px;
}

.form-data-preview {
  max-height: 300px;
  overflow-y: auto;
}

.workflow-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dialog-footer {
  text-align: right;
}

.form-data-detail {
  height: 400px;
}

.json-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

:deep(.json-display .el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
  
  .score-display {
    padding: 15px;
  }
  
  .score-value {
    font-size: 20px;
  }
}
</style>