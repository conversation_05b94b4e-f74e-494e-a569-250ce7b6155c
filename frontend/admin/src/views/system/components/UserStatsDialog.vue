<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="用户详细统计"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="stats-container" v-loading="loading">
      <el-empty description="详细统计功能开发中" :image-size="120" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'refresh']);

const loading = ref(false);

const handleClose = () => {
  emit('update:visible', false);
};

const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.stats-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  text-align: right;
}
</style>