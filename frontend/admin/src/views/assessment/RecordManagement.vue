<template>
  <div class="record-management">
    <!-- 顶部导航栏 -->
    <TopNavbar title="评估记录管理" />
    
    <el-card style="margin: 16px;">
      <template #header>
        <div class="card-header">
          <div class="flex items-center">
            <ChartBarIcon class="h-5 w-5 text-primary-700 mr-2" />
            <h3>评估记录管理</h3>
          </div>
          <el-button type="primary" @click="createRecord">
            <div class="flex items-center">
              <PlusIcon class="h-4 w-4 mr-1" />
              <span>新建评估</span>
            </div>
          </el-button>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索老年人姓名或记录号"
              clearable
              @change="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.scaleType"
              placeholder="量表类型"
              clearable
              @change="handleSearch"
            >
              <el-option label="老年人能力评估" value="ELDERLY_ABILITY" />
              <el-option label="情绪快评" value="EMOTIONAL_QUICK" />
              <el-option label="interRAI评估" value="INTER_RAI" />
              <el-option label="长护险评估" value="LONG_CARE_INSURANCE" />
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select
              v-model="searchForm.status"
              placeholder="评估状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="进行中" value="IN_PROGRESS" />
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="已提交" value="SUBMITTED" />
              <el-option label="已审核" value="REVIEWED" />
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <div class="flex items-center">
                <MagnifyingGlassIcon class="h-4 w-4 mr-1" />
                <span>搜索</span>
              </div>
            </el-button>
            <el-button @click="resetSearch">
              <div class="flex items-center">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                <span>重置</span>
              </div>
            </el-button>
            <el-button type="primary" @click="exportRecords">
              <div class="flex items-center">
                <ChartBarIcon class="h-4 w-4 mr-1" />
                <span>导出</span>
              </div>
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <ClipboardDocumentListIcon class="h-8 w-8" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总评估数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <CheckCircleIcon class="h-8 w-8" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <ClockIcon class="h-8 w-8" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon today">
                <ArchiveBoxIcon class="h-8 w-8" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.today }}</div>
                <div class="stat-label">今日新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 记录列表 -->
      <el-table
        :data="records"
        v-loading="loading"
        stripe
        style="width: 100%"
        class="record-table"
      >
        <el-table-column prop="recordNumber" label="记录号" width="150" />
        <el-table-column prop="elderlyName" label="老年人姓名" width="120" />
        <el-table-column
          prop="scaleName"
          label="评估量表"
          width="180"
          show-overflow-tooltip
        />
        <el-table-column prop="assessorName" label="评估员" width="100" />
        <el-table-column prop="totalScore" label="总分" width="80">
          <template #default="{ row }">
            <span v-if="row.totalScore !== null" class="score-text">
              {{ row.totalScore }}/{{ row.maxScore }}
            </span>
            <span v-else class="no-score">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="resultLevel" label="评估等级" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.resultLevel"
              :type="getLevelTagType(row.resultLevel)"
              size="small"
            >
              {{ row.resultLevel }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusDisplayName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assessmentDate" label="评估日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.assessmentDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewRecord(row)">
              <div class="flex items-center">
                <EyeIcon class="h-4 w-4 mr-1" />
                <span>查看</span>
              </div>
            </el-button>
            <el-button
              v-if="row.status === 'IN_PROGRESS'"
              type="primary"
              size="small"
              @click="editRecord(row)"
            >
              <div class="flex items-center">
                <PencilIcon class="h-4 w-4 mr-1" />
                <span>继续</span>
              </div>
            </el-button>
            <el-button type="primary" size="small" @click="exportReport(row)">
              <div class="flex items-center">
                <DocumentIcon class="h-4 w-4 mr-1" />
                <span>报告</span>
              </div>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > pageSize"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import TopNavbar from '@/components/TopNavbar.vue';
import {
  ChartBarIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  ClockIcon,
  ArchiveBoxIcon,
  EyeIcon,
  PencilIcon,
  DocumentIcon,
} from '@heroicons/vue/24/outline';

// 响应式数据
const loading = ref(false);
const records = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

const searchForm = reactive({
  keyword: '',
  scaleType: '',
  status: '',
  dateRange: null,
});

const stats = reactive({
  total: 0,
  completed: 0,
  pending: 0,
  today: 0,
});

// 获取统计数据
const getStats = async () => {
  try {
    // const response = await assessmentRecordApi.getStats()

    // 模拟数据
    Object.assign(stats, {
      total: 1254,
      completed: 987,
      pending: 267,
      today: 23,
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

// 获取记录列表
const getRecords = async () => {
  try {
    loading.value = true;

    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      ...searchForm,
    };

    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0];
      params.endDate = searchForm.dateRange[1];
      delete params.dateRange;
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key];
      }
    });

    // const response = await assessmentRecordApi.getRecords(params)

    // 模拟数据
    records.value = [
      {
        id: '1',
        recordNumber: 'AR202412120001',
        elderlyName: '张三',
        elderlyId: '110101195001011234',
        scaleName: '民政部老年人能力评估标准',
        assessorName: '李医生',
        totalScore: 85,
        maxScore: 100,
        resultLevel: '良好',
        status: 'COMPLETED',
        assessmentDate: '2024-12-12',
        createdAt: new Date().toISOString(),
      },
      {
        id: '2',
        recordNumber: 'AR202412120002',
        elderlyName: '王五',
        elderlyId: '110101194501011234',
        scaleName: 'interRAI综合评估工具',
        assessorName: '陈护士',
        totalScore: null,
        maxScore: 120,
        resultLevel: null,
        status: 'IN_PROGRESS',
        assessmentDate: '2024-12-12',
        createdAt: new Date(Date.now() - 3600000).toISOString(),
      },
      {
        id: '3',
        recordNumber: 'AR202412110001',
        elderlyName: '赵六',
        elderlyId: '110101193801011234',
        scaleName: '长期护理保险评估',
        assessorName: '刘医生',
        totalScore: 72,
        maxScore: 100,
        resultLevel: '中等',
        status: 'SUBMITTED',
        assessmentDate: '2024-12-11',
        createdAt: new Date(Date.now() - 86400000).toISOString(),
      },
    ];
    total.value = 3;
  } catch (error) {
    console.error('获取记录列表失败:', error);
    ElMessage.error('获取记录列表失败!');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  getRecords();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    scaleType: '',
    status: '',
    dateRange: null,
  });
  handleSearch();
};

// 分页处理
const onPageSizeChange = newPageSize => {
  pageSize.value = newPageSize;
  getRecords();
};

const onCurrentPageChange = newPage => {
  currentPage.value = newPage;
  getRecords();
};

// 操作方法
const createRecord = () => {
  ElMessage.info('跳转到新建评估页面');
  // 这里可以跳转到评估创建页面
};

const viewRecord = record => {
  ElMessage.info(`查看评估记录: ${record.recordNumber}`);
  // 这里可以跳转到记录详情页
};

const editRecord = record => {
  ElMessage.info(`继续评估: ${record.recordNumber}`);
  // 这里可以跳转到评估填写页面
};

const exportReport = async record => {
  try {
    ElMessage.info(`导出评估报告: ${record.recordNumber}`);

    // const response = await assessmentRecordApi.exportReport(record.id, 'pdf')
    // 处理文件下载
  } catch (error) {
    ElMessage.error('导出报告失败!');
  }
};

const exportRecords = () => {
  ElMessage.info('导出评估记录列表');
  // 这里可以导出当前筛选条件下的记录列表
};

// 工具方法
const getStatusTagType = status => {
  const statusMap = {
    IN_PROGRESS: 'warning',
    COMPLETED: 'success',
    SUBMITTED: 'primary',
    REVIEWED: 'info',
  };
  return statusMap[status] || 'default';
};

const getStatusDisplayName = status => {
  const statusMap = {
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    SUBMITTED: '已提交',
    REVIEWED: '已审核',
  };
  return statusMap[status] || status;
};

const getLevelTagType = level => {
  const levelMap = {
    优秀: 'success',
    良好: 'primary',
    中等: 'warning',
    较差: 'danger',
  };
  return levelMap[level] || 'default';
};

const formatDate = dateString => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const formatDateTime = dateString => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN');
};

// 组件挂载
onMounted(() => {
  getStats();
  getRecords();
});
</script>

<style scoped>
.record-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.search-filters {
  margin: 20px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.stats-cards {
  margin: 20px 0;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  font-size: 40px;
  margin-right: 15px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.stat-icon.total {
  background-color: #e6f7ff;
}

.stat-icon.completed {
  background-color: #f6ffed;
}

.stat-icon.pending {
  background-color: #fff7e6;
}

.stat-icon.today {
  background-color: #f9f0ff;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.record-table {
  margin: 20px 0;
}

.score-text {
  font-weight: bold;
  color: #409eff;
}

.no-score {
  color: #c0c4cc;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style>
