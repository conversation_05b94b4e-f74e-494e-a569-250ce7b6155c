<template>
  <div class="pdf-upload-page">
    <!-- 流程进度指示器 -->
    <ProcessIndicator 
      :current-stage="workflowStarted ? currentStage : 0" 
      :stage-completions="stageCompletions"
      :start-time="workflowStartTime"
    />
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 第一行：文档上传 (全宽) -->
      <div class="stage-row stage1-row">
        <Stage1UploadSection 
          :upload-url="uploadUrl"
          :upload-data="uploadData"
          :upload-headers="uploadHeaders"
          :docling-available="doclingAvailable"
          :ai-service-available="aiServiceAvailable"
          :checking-docling="checkingDocling"
          :checking-ai="checkingAI"
          :model-info="modelInfo"
          @stage-complete="onStage1Complete"
          @check-docling="checkDoclingStatus"
          @check-ai="checkAIStatus"
          @upload-start="startUploadTracking"
          @upload-progress="onUploadProgress"
          @upload-error="onUploadError"
        />
      </div>
      
      <!-- 第二行：解析编辑 + 量表属性 (并排) -->
      <div class="stage-row stage2-row">
        <el-row :gutter="20">
          <el-col :span="18">
            <Stage2ParseEditSection 
              :enabled="stageCompletions.stage1"
              :upload-result="uploadResult"
              @stage-complete="onStage2Complete" 
            />
          </el-col>
          <el-col :span="6">
            <Stage2_5ScalePropertiesSection 
              :enabled="stageCompletions.stage2"
              :edit-result="editResult"
              @stage-complete="onStage2_5Complete" 
            />
          </el-col>
        </el-row>
      </div>
      
      <!-- 第三行：AI分析 (全宽) -->
      <div class="stage-row stage3-row">
        <Stage3AIAnalysisSection 
          :enabled="stageCompletions.stage2_5"
          :scale-properties="scaleProperties"
          :markdown-content="markdownContent"
          :model-info="modelInfo"
          @stage-complete="onStage3Complete"
          @start-ai-analysis="handleAIAnalysis"
          @open-ai-chat="openAIChat"
        />
      </div>
      
      <!-- 第四行：数据库结构设计 (全宽) -->
      <div class="stage-row stage4-row">
        <Stage4DatabaseDesignSection 
          :enabled="stageCompletions.stage3"
          :analysis-result="analysisResult"
          @stage-complete="onStage4Complete"
        />
      </div>
      
      <!-- 第五行：SQL生成执行 (全宽) -->
      <div class="stage-row stage5-row">
        <Stage5SQLGenerationSection 
          :enabled="stageCompletions.stage4"
          :database-structure="databaseStructure"
          @stage-complete="onStage5Complete"
        />
      </div>
    </div>

    <!-- 上传状态追踪器 -->
    <UploadStatusTracker
      :visible="showUploadTracker"
      :upload-status="uploadStatus"
      :progress="uploadProgress"
      :current-message="uploadMessage"
      :docling-status="doclingStatus"
      :ai-status="aiServiceStatus"
      :error-details="uploadError"
      @close="showUploadTracker = false"
      @retry="retryUpload"
      @cancel="cancelUpload"
    />

    <!-- AI对话助手 -->
    <AIChatDialog
      v-if="showAIChat"
      :visible="showAIChat"
      :messages="chatMessages"
      :streaming="chatStreaming"
      :context="getChatContext()"
      @close="showAIChat = false"
      @send-message="sendChatMessage"
      @clear-chat="clearChatHistory"
    />

    <!-- 完成庆祝对话框 -->
    <el-dialog
      v-model="showCompletionDialog"
      title="🎉 量表数字化转换完成！"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="completion-content">
        <div class="completion-stats">
          <div class="stat-item">
            <div class="stat-number">{{ getTotalTimeSpent() }}</div>
            <div class="stat-label">总耗时</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">5</div>
            <div class="stat-label">完成阶段</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ finalResult?.databaseStructure?.fields?.length || 0 }}</div>
            <div class="stat-label">数据库字段</div>
          </div>
        </div>
        
        <div class="completion-summary">
          <h4>转换摘要</h4>
          <ul>
            <li><strong>量表名称:</strong> {{ finalResult?.scaleProperties?.name }}</li>
            <li><strong>量表类型:</strong> {{ getScaleTypeDisplay(finalResult?.scaleProperties?.type) }}</li>
            <li><strong>数据库表:</strong> {{ finalResult?.databaseStructure?.tableName }}</li>
            <li><strong>SQL执行:</strong> {{ finalResult?.sqlExecution?.success ? '成功' : '失败' }}</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <div class="completion-actions">
          <el-button @click="resetWorkflow">
            <ArrowPathIcon class="h-4 w-4 mr-1" />
            重新开始
          </el-button>
          <el-button type="primary" @click="goToScaleManagement">
            <Squares2X2Icon class="h-4 w-4 mr-1" />
            管理量表
          </el-button>
          <el-button type="success" @click="startAssessment">
            <RocketLaunchIcon class="h-4 w-4 mr-1" />
            开始评估
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import request from '@/utils/request';

// Components
import ProcessIndicator from './components/stages/ProcessIndicator.vue';
import Stage1UploadSection from './components/stages/Stage1UploadSection.vue';
import Stage2ParseEditSection from './components/stages/Stage2ParseEditSection.vue';
import Stage2_5ScalePropertiesSection from './components/stages/Stage2_5ScalePropertiesSection.vue';
import Stage3AIAnalysisSection from './components/stages/Stage3AIAnalysisSection.vue';
import Stage4DatabaseDesignSection from './components/stages/Stage4DatabaseDesignSection.vue';
import Stage5SQLGenerationSection from './components/stages/Stage5SQLGenerationSection.vue';
import AIChatDialog from './components/AIChatDialog.vue';
import UploadStatusTracker from '../../components/UploadStatusTracker.vue';

// Icons
import {
  ArrowPathIcon,
  Squares2X2Icon,
  RocketLaunchIcon,
} from '@heroicons/vue/24/outline';

// Types
interface UploadResult {
  fileName: string;
  fileSize: number;
  uploadSuccess: boolean;
  doclingResult?: any;
  markdownContent?: string;
}

interface EditResult {
  markdownContent: string;
  contentValidated: boolean;
  editMode: 'preview' | 'edit' | 'split';
  wordCount: number;
  hasChanges: boolean;
}

interface ScaleProperties {
  name: string;
  code: string;
  type: string;
  version: string;
  estimatedDuration: number;
  description: string;
  assessmentModes: string[];
  complianceStandard: string;
  isActive: boolean;
}

interface AnalysisResult {
  structure: any;
  recommendations: string[];
  confidence: number;
  timestamp: Date;
}

interface DatabaseStructure {
  tableName: string;
  tableComment: string;
  fields: any[];
  indexes?: any[];
}

interface SQLResult {
  sql: string;
  execution: {
    success: boolean;
    message: string;
    duration?: number;
    affectedRows?: number;
    tableName?: string;
  };
}

// Router
const router = useRouter();

// Reactive state
const currentStage = ref<1 | 2 | 2.5 | 3 | 4 | 5>(1);
const workflowStartTime = ref<Date>(new Date());
const workflowStarted = ref(false);

// Stage completions
const stageCompletions = reactive({
  stage1: false,
  stage2: false,
  stage2_5: false,
  stage3: false,
  stage4: false,
  stage5: false,
});

// Stage results
const uploadResult = ref<UploadResult | null>(null);
const editResult = ref<EditResult | null>(null);
const scaleProperties = ref<ScaleProperties | null>(null);
const analysisResult = ref<AnalysisResult | null>(null);
const databaseStructure = ref<DatabaseStructure | null>(null);
const sqlResult = ref<SQLResult | null>(null);

// Service status
const doclingAvailable = ref(false);
const aiServiceAvailable = ref(false);
const checkingDocling = ref(false);
const checkingAI = ref(false);
const modelInfo = ref<any>(null);

// Upload status tracking
const showUploadTracker = ref(false); // 默认隐藏，只在上传时显示
const uploadStatus = ref<'idle' | 'uploading' | 'processing' | 'completed' | 'error'>('idle');
const uploadProgress = ref(0);
const uploadMessage = ref('准备上传...');
const doclingStatus = ref<'online' | 'offline' | 'checking'>('offline');
const aiServiceStatus = ref<'online' | 'offline' | 'checking'>('offline');
const uploadError = ref<any>(null);

// Chat
const showAIChat = ref(false);
const chatMessages = ref<Array<{ role: string; content: string }>>([]);
const chatStreaming = ref(false);

// Completion
const showCompletionDialog = ref(false);
const finalResult = ref<any>(null);

// Computed
const uploadUrl = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL}/api/pdf-import/upload`;
});

const uploadData = computed(() => ({
  preview: 'true'
}));

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token');
  return {
    'Authorization': token ? `Bearer ${token}` : ''
  };
});

const markdownContent = computed(() => {
  return editResult.value?.markdownContent || uploadResult.value?.markdownContent || '';
});

// Stage event handlers
const onStage1Complete = (result: UploadResult) => {
  uploadResult.value = result;
  stageCompletions.stage1 = true;
  currentStage.value = 2;
  
  // Update upload status
  uploadStatus.value = 'completed';
  uploadProgress.value = 100;
  uploadMessage.value = '文档上传并解析完成！';
  
  ElMessage.success('文档上传完成，可以开始编辑');
  console.log('Stage 1 completed:', result);
  
  // Auto-hide tracker after success
  setTimeout(() => {
    showUploadTracker.value = false;
  }, 3000);
};

const onStage2Complete = (result: EditResult) => {
  editResult.value = result;
  stageCompletions.stage2 = true;
  currentStage.value = 2.5;
  
  ElMessage.success('内容编辑完成，可以配置属性');
  console.log('Stage 2 completed:', result);
};

const onStage2_5Complete = (result: ScaleProperties) => {
  scaleProperties.value = result;
  stageCompletions.stage2_5 = true;
  currentStage.value = 3;
  
  ElMessage.success('属性配置完成，可以开始AI分析');
  console.log('Stage 2.5 completed:', result);
};

const onStage3Complete = (result: AnalysisResult) => {
  analysisResult.value = result;
  stageCompletions.stage3 = true;
  currentStage.value = 4;
  
  ElMessage.success('AI分析完成，可以设计数据库结构');
  console.log('Stage 3 completed:', result);
};

const onStage4Complete = (result: DatabaseStructure) => {
  databaseStructure.value = result;
  stageCompletions.stage4 = true;
  currentStage.value = 5;
  
  ElMessage.success('数据库设计完成，可以生成SQL');
  console.log('Stage 4 completed:', result);
};

const onStage5Complete = (result: SQLResult) => {
  sqlResult.value = result;
  stageCompletions.stage5 = true;
  
  // Collect final results
  finalResult.value = {
    uploadResult: uploadResult.value,
    editResult: editResult.value,
    scaleProperties: scaleProperties.value,
    analysisResult: analysisResult.value,
    databaseStructure: databaseStructure.value,
    sqlExecution: result.execution,
    completedAt: new Date(),
    totalTimeSpent: getTotalTimeSpent(),
  };
  
  ElMessage.success('🎉 量表数字化转换完成！');
  showCompletionDialog.value = true;
  
  console.log('Stage 5 completed:', result);
  console.log('Final workflow result:', finalResult.value);
};

// AI and Chat handlers
const handleAIAnalysis = () => {
  // Handle AI analysis start
  console.log('Starting AI analysis...');
};

const openAIChat = () => {
  showAIChat.value = true;
};

const sendChatMessage = (message: string) => {
  chatMessages.value.push({ role: 'user', content: message });
  
  // Simulate AI response
  chatStreaming.value = true;
  setTimeout(() => {
    chatMessages.value.push({ 
      role: 'assistant', 
      content: `我理解您的问题：${message}。基于当前的量表内容，我建议...` 
    });
    chatStreaming.value = false;
  }, 1500);
};

const clearChatHistory = () => {
  chatMessages.value = [];
  ElMessage.success('对话已清空');
};

const getChatContext = () => {
  return {
    currentStage: currentStage.value,
    scaleProperties: scaleProperties.value,
    markdownContent: markdownContent.value,
    analysisResult: analysisResult.value,
  };
};

// Service status checks
const checkDoclingStatus = async () => {
  checkingDocling.value = true;
  doclingStatus.value = 'checking';
  
  try {
    const response = await request.get('/api/docling/health');
    if (response.data?.success) {
      doclingAvailable.value = true;
      doclingStatus.value = 'online';
      console.log('Docling服务检查成功: 服务在线');
    } else {
      doclingAvailable.value = false;
      doclingStatus.value = 'offline';
      console.warn('Docling服务检查失败: 服务响应异常', response.data);
    }
  } catch (error) {
    console.error('Docling服务检查失败: 无法连接到服务', error);
    doclingAvailable.value = false; // 实际反映服务不可用状态
    doclingStatus.value = 'offline'; // 实际反映服务离线状态
    
    // 显示用户友好的错误信息
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      console.warn('Docling服务: 网络连接错误，请检查服务是否启动');
    }
  } finally {
    checkingDocling.value = false;
  }
};

const checkAIStatus = async () => {
  checkingAI.value = true;
  aiServiceStatus.value = 'checking';
  
  try {
    const response = await request.get('/api/ai/status');
    if (response.data?.success) {
      aiServiceAvailable.value = true;
      aiServiceStatus.value = 'online';
      console.log('AI服务检查成功: 服务在线');
      
      // Get model info
      const modelResponse = await request.get('/api/ai/model-info');
      if (modelResponse.data?.success) {
        modelInfo.value = modelResponse.data.data;
        console.log('AI模型信息获取成功:', modelResponse.data.data);
      }
    } else {
      aiServiceAvailable.value = false;
      aiServiceStatus.value = 'offline';
      console.warn('AI服务检查失败: 服务响应异常', response.data);
    }
  } catch (error) {
    console.error('AI服务检查失败: 无法连接到服务', error);
    aiServiceAvailable.value = false; // 实际反映服务不可用状态
    aiServiceStatus.value = 'offline'; // 实际反映服务离线状态
    modelInfo.value = null; // 清空模型信息
    
    // 显示用户友好的错误信息
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      console.warn('AI服务: 网络连接错误，请检查LM Studio是否启动');
    }
  } finally {
    checkingAI.value = false;
  }
};

// Completion handlers
const resetWorkflow = () => {
  // Reset all state
  Object.keys(stageCompletions).forEach(key => {
    stageCompletions[key as keyof typeof stageCompletions] = false;
  });
  
  currentStage.value = 1;
  workflowStartTime.value = new Date();
  workflowStarted.value = false; // 重置工作流开始标志
  
  // Clear all results
  uploadResult.value = null;
  editResult.value = null;
  scaleProperties.value = null;
  analysisResult.value = null;
  databaseStructure.value = null;
  sqlResult.value = null;
  finalResult.value = null;
  
  showCompletionDialog.value = false;
  ElMessage.success('工作流已重置，可以重新开始');
};

const goToScaleManagement = () => {
  showCompletionDialog.value = false;
  router.push('/assessment/scale-management');
};

const startAssessment = () => {
  showCompletionDialog.value = false;
  router.push('/assessment/start');
};

// Upload status tracking methods
const startUploadTracking = () => {
  workflowStarted.value = true; // 标记工作流已开始
  showUploadTracker.value = true;
  uploadStatus.value = 'uploading';
  uploadProgress.value = 0;
  uploadMessage.value = '开始上传文档...';
  uploadError.value = null;
};

const onUploadProgress = (progress: number, message: string) => {
  uploadProgress.value = progress;
  uploadMessage.value = message;
  
  if (progress >= 99) {
    uploadStatus.value = 'processing';
  }
};

const onUploadError = (error: any) => {
  uploadStatus.value = 'error';
  uploadError.value = {
    title: '上传失败',
    message: error.message || '文件上传过程中发生错误',
    suggestions: [
      '检查网络连接是否正常',
      '确认文件格式是否支持',
      '验证文件大小是否超过限制',
      '重新检查服务状态'
    ]
  };
};

const retryUpload = () => {
  uploadStatus.value = 'idle';
  uploadProgress.value = 0;
  uploadError.value = null;
  ElMessage.info('准备重新上传...');
};

const cancelUpload = () => {
  uploadStatus.value = 'idle';
  showUploadTracker.value = false;
  ElMessage.info('已取消上传');
};

// Utility functions
const getTotalTimeSpent = () => {
  if (!workflowStartTime.value) return '0分钟';
  
  const diffMs = Date.now() - workflowStartTime.value.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟`;
  } else {
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    return `${hours}小时${minutes}分钟`;
  }
};

const getScaleTypeDisplay = (type?: string) => {
  const typeMap: Record<string, string> = {
    ELDERLY_ABILITY: '老年人能力评估',
    EMOTIONAL_QUICK: '情绪快评',
    INTER_RAI: 'interRAI评估',
    LONG_CARE_INSURANCE: '长护险评估',
    CUSTOM: '自定义量表',
  };
  return typeMap[type || ''] || type || '未知类型';
};

// Lifecycle
onMounted(() => {
  workflowStartTime.value = new Date();
  checkDoclingStatus();
  checkAIStatus();
});
</script>

<style scoped>
/* 品牌色彩定义 */
:root {
  --changchun-blue: #5357A0;      /* 长春花蓝 - 主色 */
  --foshou-yellow: #fed81f;       /* 佛手黄 - 配色 */
  --light-blue: #eaebf8;          /* 浅蓝 - 背景色 */
  --dark-blue: #434683;           /* 深蓝 - 强调色 */
  --warm-white: #FEFEFE;          /* 温白 - 卡片背景 */
  --soft-gray: #F8F9FA;           /* 柔灰 - 页面背景 */
  --text-primary: #5357A0;        /* 主要文字 - 长春花蓝，用于标题和重要内容 */
  --text-secondary: #718096;      /* 次要文字 - 中性灰，用于描述和辅助信息 */
  --success: #48BB78;             /* 成功状态 */
  --warning: #ED8936;             /* 警告状态 */
  --error: #F56565;               /* 错误状态 */
}

.pdf-upload-page {
  min-height: calc(100vh - 120px);
  background: var(--soft-gray);
  padding: 24px;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
}

.stage-row {
  margin-bottom: 24px;
}

.stage1-row,
.stage3-row,
.stage4-row,
.stage5-row {
  width: 100%;
}

.stage2-row {
  width: 100%;
}

.stage2-row .el-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.stage2-row .el-col {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.stage2-row .el-col:first-child {
  padding-right: 4px !important;
}

.stage2-row .el-col:last-child {
  padding-left: 4px !important;
}

.completion-content {
  text-align: center;
}

.completion-stats {
  display: flex;
  justify-content: center;
  gap: 48px;
  margin-bottom: 32px;
  padding: 24px 0;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.completion-summary {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.completion-summary h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.completion-summary ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.completion-summary li {
  margin: 8px 0;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  color: #4b5563;
}

.completion-summary li:last-child {
  border-bottom: none;
}

.completion-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pdf-upload-page {
    padding: 16px;
  }
  
  .completion-stats {
    gap: 24px;
  }
  
  .stat-number {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .completion-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .completion-actions {
    flex-direction: column;
  }
  
  .completion-actions .el-button {
    width: 100%;
  }
}
</style>