<template>
  <div class="field-mapping-container">
    <!-- 头部信息 -->
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <div class="flex items-center">
            <ArrowPathIcon class="h-5 w-5 text-primary-700 mr-2" />
            <h3>字段映射确认</h3>
          </div>
          <span class="header-subtitle">
            确认PDF解析出的字段映射，调整字段类型和验证规则
          </span>
        </div>
      </template>

      <div class="scale-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="量表名称">
            {{ fieldMapping?.scaleName || '加载中...' }}
          </el-descriptions-item>
          <el-descriptions-item label="字段数量">
            {{ fieldMapping?.parsedFields?.length || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="置信度">
            <el-tag :type="getConfidenceTagType(fieldMapping?.confidenceScore)">
              {{ formatConfidence(fieldMapping?.confidenceScore) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="action-card">
      <div class="action-bar">
        <div class="left-actions">
          <el-button
            type="primary"
            @click="batchConfirm"
            :disabled="selectedFields.length === 0"
          >
            <div class="flex items-center">
              <CheckCircleIcon class="h-4 w-4 mr-1" />
              <span>批量确认 ({{ selectedFields.length }})</span>
            </div>
          </el-button>
          <el-button type="primary" @click="selectAllFields">
            <div class="flex items-center">
              <ClipboardDocumentListIcon class="h-4 w-4 mr-1" />
              <span>全选</span>
            </div>
          </el-button>
          <el-button @click="clearSelection">
            <div class="flex items-center">
              <ArrowPathIcon class="h-4 w-4 mr-1" />
              <span>清除选择</span>
            </div>
          </el-button>
        </div>
        <div class="right-actions">
          <el-button type="primary" @click="applyMapping" :loading="applying">
            <div class="flex items-center">
              <RocketLaunchIcon class="h-4 w-4 mr-1" />
              <span>应用映射</span>
            </div>
          </el-button>
          <el-button @click="refreshMapping" :loading="loading">
            <div class="flex items-center">
              <ArrowPathIcon class="h-4 w-4 mr-1" />
              <span>刷新</span>
            </div>
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 字段列表 -->
    <el-card class="fields-card">
      <template #header>
        <div class="card-header">
          <div class="flex items-center"><DocumentTextIcon class="h-4 w-4 mr-1" /><span>字段列表</span></div>
          <el-switch
            v-model="showOnlyPending"
            active-text="仅显示待确认"
            inactive-text="显示全部"
          />
        </div>
      </template>

      <div v-loading="loading" class="fields-container">
        <div
          v-for="field in filteredFields"
          :key="field.fieldId"
          class="field-item"
          :class="{
            'field-selected': selectedFields.includes(field.fieldId),
            'field-confirmed': field.confirmationStatus === 'CONFIRMED',
            'field-modified': field.confirmationStatus === 'MODIFIED',
            'field-rejected': field.confirmationStatus === 'REJECTED',
          }"
        >
          <!-- 字段选择框 -->
          <div class="field-checkbox">
            <el-checkbox
              v-model="selectedFields"
              :value="field.fieldId"
              :disabled="field.confirmationStatus === 'CONFIRMED'"
            />
          </div>

          <!-- 字段基本信息 -->
          <div class="field-basic">
            <div class="field-title">
              <span class="field-name">{{ field.suggestedName }}</span>
              <el-tag
                :type="getStatusTagType(field.confirmationStatus)"
                size="small"
              >
                {{ getStatusDisplayName(field.confirmationStatus) }}
              </el-tag>
              <el-tag
                :type="getConfidenceTagType(field.confidence)"
                size="small"
              >
                置信度: {{ formatConfidence(field.confidence) }}
              </el-tag>
            </div>
            <div class="field-original">
              <strong>原始文本:</strong> {{ field.originalText }}
            </div>
            <div class="field-description" v-if="field.suggestedDescription">
              <strong>描述:</strong> {{ field.suggestedDescription }}
            </div>
          </div>

          <!-- 字段编辑区域 -->
          <div class="field-edit" v-if="editingField === field.fieldId">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="字段名称" required>
                  <el-input
                    v-model="fieldEditForm.name"
                    placeholder="请输入字段名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="字段类型" required>
                  <el-select
                    v-model="fieldEditForm.type"
                    placeholder="选择字段类型"
                    @change="onFieldTypeChange"
                  >
                    <el-option
                      v-for="type in fieldTypes"
                      :key="type.type"
                      :label="type.displayName"
                      :value="type.type"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否必填">
                  <el-switch
                    v-model="fieldEditForm.required"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="字段描述">
                  <el-input
                    v-model="fieldEditForm.description"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入字段描述"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管理员备注">
                  <el-input
                    v-model="fieldEditForm.adminNotes"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入管理员备注"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 选项编辑（用于选择类型字段） -->
            <div v-if="isSelectType(fieldEditForm.type)" class="options-edit">
              <el-form-item label="选项设置">
                <div class="options-container">
                  <div
                    v-for="(option, index) in fieldEditForm.options"
                    :key="index"
                    class="option-row"
                  >
                    <el-input
                      v-model="option.label"
                      placeholder="选项文本"
                      style="width: 200px; margin-right: 10px"
                    />
                    <el-input-number
                      v-model="option.score"
                      placeholder="分值"
                      :min="0"
                      style="width: 100px; margin-right: 10px"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeOption(index)"
                    >
                      删除
                    </el-button>
                  </div>
                  <el-button type="primary" size="small" @click="addOption">
                    + 添加选项
                  </el-button>
                </div>
              </el-form-item>
            </div>

            <!-- 验证规则编辑 -->
            <div class="validation-edit">
              <el-form-item label="验证规则">
                <el-row :gutter="16">
                  <el-col :span="6" v-if="isNumberType(fieldEditForm.type)">
                    <el-input-number
                      v-model="fieldEditForm.validation.minValue"
                      placeholder="最小值"
                      controls-position="right"
                      style="width: 100%"
                    />
                    <div class="form-hint">最小值</div>
                  </el-col>
                  <el-col :span="6" v-if="isNumberType(fieldEditForm.type)">
                    <el-input-number
                      v-model="fieldEditForm.validation.maxValue"
                      placeholder="最大值"
                      controls-position="right"
                      style="width: 100%"
                    />
                    <div class="form-hint">最大值</div>
                  </el-col>
                  <el-col :span="6" v-if="isTextType(fieldEditForm.type)">
                    <el-input-number
                      v-model="fieldEditForm.validation.minLength"
                      placeholder="最小长度"
                      :min="0"
                      controls-position="right"
                      style="width: 100%"
                    />
                    <div class="form-hint">最小长度</div>
                  </el-col>
                  <el-col :span="6" v-if="isTextType(fieldEditForm.type)">
                    <el-input-number
                      v-model="fieldEditForm.validation.maxLength"
                      placeholder="最大长度"
                      :min="1"
                      controls-position="right"
                      style="width: 100%"
                    />
                    <div class="form-hint">最大长度</div>
                  </el-col>
                  <el-col :span="12">
                    <el-input
                      v-model="fieldEditForm.validation.pattern"
                      placeholder="正则表达式（可选）"
                    />
                    <div class="form-hint">正则表达式</div>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>

            <!-- 编辑操作按钮 -->
            <div class="edit-actions">
              <el-button type="primary" @click="saveFieldEdit(field)">
                <div class="flex items-center">
                  <ArchiveBoxIcon class="h-4 w-4 mr-1" />
                  <span>保存</span>
                </div>
              </el-button>
              <el-button @click="cancelFieldEdit">
                <div class="flex items-center">
                  <XMarkIcon class="h-4 w-4 mr-1" />
                  <span>取消</span>
                </div>
              </el-button>
            </div>
          </div>

          <!-- 字段详情展示 -->
          <div class="field-details" v-else>
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="detail-item">
                  <strong>字段类型:</strong>
                  <el-tag size="small">
                    {{ getFieldTypeDisplayName(field.suggestedType) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <strong>是否必填:</strong>
                  {{ field.required ? '是' : '否' }}
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item" v-if="field.suggestedWeight">
                  <strong>权重:</strong>
                  {{ field.suggestedWeight }}
                </div>
              </el-col>
            </el-row>

            <!-- 显示选项 -->
            <div
              class="options-display"
              v-if="field.suggestedOptions && field.suggestedOptions.length > 0"
            >
              <strong>选项:</strong>
              <el-tag
                v-for="option in field.suggestedOptions"
                :key="option.value"
                size="small"
                style="margin: 2px"
              >
                {{ option.label }}
                <span v-if="option.score !== undefined"
                  >({{ option.score }}分)</span
                >
              </el-tag>
            </div>

            <!-- 显示验证规则 -->
            <div
              class="validation-display"
              v-if="hasValidationRules(field.suggestedValidation)"
            >
              <strong>验证规则:</strong>
              <span class="validation-info">
                {{ formatValidationRules(field.suggestedValidation) }}
              </span>
            </div>
          </div>

          <!-- 字段操作按钮 -->
          <div class="field-actions">
            <el-button
              v-if="field.confirmationStatus === 'PENDING'"
              type="primary"
              size="small"
              @click="confirmField(field)"
            >
              <div class="flex items-center">
                <CheckCircleIcon class="h-4 w-4 mr-1" />
                <span>确认</span>
              </div>
            </el-button>
            <el-button type="primary" size="small" @click="editField(field)">
              <div class="flex items-center">
                <PencilIcon class="h-4 w-4 mr-1" />
                <span>编辑</span>
              </div>
            </el-button>
            <el-button type="danger" size="small" @click="rejectField(field)">
              <div class="flex items-center">
                <XMarkIcon class="h-4 w-4 mr-1" />
                <span>拒绝</span>
              </div>
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty
          v-if="filteredFields.length === 0 && !loading"
          description="没有找到字段数据"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  ClipboardDocumentListIcon,
  RocketLaunchIcon,
  ArchiveBoxIcon,
  PencilIcon,
  XMarkIcon,
  DocumentTextIcon,
} from '@heroicons/vue/24/outline';

const route = useRoute();

// 响应式数据
const loading = ref(false);
const applying = ref(false);
const fieldMapping = ref(null);
const fieldTypes = ref([]);
const selectedFields = ref([]);
const editingField = ref(null);
const showOnlyPending = ref(true);

// 字段编辑表单
const fieldEditForm = ref({
  name: '',
  type: 'TEXT',
  description: '',
  required: false,
  adminNotes: '',
  options: [],
  validation: {
    minValue: null,
    maxValue: null,
    minLength: null,
    maxLength: null,
    pattern: '',
  },
});

// 计算属性
const filteredFields = computed(() => {
  if (!fieldMapping.value?.parsedFields) return [];

  if (showOnlyPending.value) {
    return fieldMapping.value.parsedFields.filter(
      field => field.confirmationStatus === 'PENDING'
    );
  }

  return fieldMapping.value.parsedFields;
});

// 获取字段映射数据
const fetchFieldMapping = async () => {
  try {
    loading.value = true;
    const scaleId = route.params.scaleId;

    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/field-mapping/${scaleId}`
    );

    const result = await response.json();

    if (result.success) {
      fieldMapping.value = result.data;
    } else {
      ElMessage.error(result.message || '获取字段映射失败');
    }
  } catch (error) {
    console.error('Fetch field mapping error:', error);
    ElMessage.error('获取字段映射失败');
  } finally {
    loading.value = false;
  }
};

// 获取字段类型列表
const fetchFieldTypes = async () => {
  try {
    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/field-mapping/field-types`
    );

    const result = await response.json();

    if (result.success) {
      fieldTypes.value = result.data;
    }
  } catch (error) {
    console.error('Fetch field types error:', error);
  }
};

// 确认单个字段
const confirmField = async field => {
  try {
    const scaleId = route.params.scaleId;

    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/field-mapping/${scaleId}/fields/${field.fieldId}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          originalText: field.originalText,
          name: field.suggestedName,
          type: field.suggestedType,
          description: field.suggestedDescription,
          required: field.required,
          adminNotes: '已确认',
        }),
      }
    );

    const result = await response.json();

    if (result.success) {
      field.confirmationStatus = 'CONFIRMED';
      ElMessage.success('字段确认成功');
    } else {
      ElMessage.error(result.message || '字段确认失败');
    }
  } catch (error) {
    console.error('Confirm field error:', error);
    ElMessage.error('字段确认失败');
  }
};

// 批量确认字段
const batchConfirm = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量确认选中的 ${selectedFields.value.length} 个字段吗？`,
      '批量确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success',
      }
    );

    const scaleId = route.params.scaleId;

    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/field-mapping/${scaleId}/confirm-batch`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(selectedFields.value),
      }
    );

    const result = await response.json();

    if (result.success) {
      // 更新本地状态
      selectedFields.value.forEach(fieldId => {
        const field = fieldMapping.value.parsedFields.find(
          f => f.fieldId === fieldId
        );
        if (field) {
          field.confirmationStatus = 'CONFIRMED';
        }
      });

      selectedFields.value = [];
      ElMessage.success(result.message || '批量确认成功');
    } else {
      ElMessage.error(result.message || '批量确认失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Batch confirm error:', error);
      ElMessage.error('批量确认失败');
    }
  }
};

// 编辑字段
const editField = field => {
  editingField.value = field.fieldId;

  // 初始化编辑表单
  fieldEditForm.value = {
    name: field.suggestedName,
    type: field.suggestedType,
    description: field.suggestedDescription || '',
    required: field.required || false,
    adminNotes: field.adminNotes || '',
    options: field.suggestedOptions ? [...field.suggestedOptions] : [],
    validation: field.suggestedValidation
      ? { ...field.suggestedValidation }
      : {
          minValue: null,
          maxValue: null,
          minLength: null,
          maxLength: null,
          pattern: '',
        },
  };
};

// 保存字段编辑
const saveFieldEdit = async field => {
  try {
    const scaleId = route.params.scaleId;

    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/field-mapping/${scaleId}/fields/${field.fieldId}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(fieldEditForm.value),
      }
    );

    const result = await response.json();

    if (result.success) {
      // 更新本地数据
      Object.assign(field, {
        suggestedName: fieldEditForm.value.name,
        suggestedType: fieldEditForm.value.type,
        suggestedDescription: fieldEditForm.value.description,
        required: fieldEditForm.value.required,
        adminNotes: fieldEditForm.value.adminNotes,
        suggestedOptions: fieldEditForm.value.options,
        suggestedValidation: fieldEditForm.value.validation,
        confirmationStatus: 'MODIFIED',
      });

      editingField.value = null;
      ElMessage.success('字段保存成功');
    } else {
      ElMessage.error(result.message || '字段保存失败');
    }
  } catch (error) {
    console.error('Save field edit error:', error);
    ElMessage.error('字段保存失败');
  }
};

// 取消字段编辑
const cancelFieldEdit = () => {
  editingField.value = null;
};

// 拒绝字段
const rejectField = async field => {
  try {
    await ElMessageBox.confirm(
      `确定要拒绝字段 "${field.suggestedName}" 吗？`,
      '拒绝字段',
      {
        confirmButtonText: '拒绝',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    field.confirmationStatus = 'REJECTED';
    ElMessage.success('字段已拒绝');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Reject field error:', error);
    }
  }
};

// 应用字段映射
const applyMapping = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要应用当前的字段映射配置吗？这将生成最终的量表Schema。',
      '应用字段映射',
      {
        confirmButtonText: '应用',
        cancelButtonText: '取消',
        type: 'success',
      }
    );

    applying.value = true;
    const scaleId = route.params.scaleId;

    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/field-mapping/${scaleId}/apply`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(fieldMapping.value),
      }
    );

    const result = await response.json();

    if (result.success) {
      ElMessage.success('字段映射应用成功！');
      // 可以跳转到量表管理页面
      // router.push('/assessment/scales');
    } else {
      ElMessage.error(result.message || '字段映射应用失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Apply mapping error:', error);
      ElMessage.error('字段映射应用失败');
    }
  } finally {
    applying.value = false;
  }
};

// 全选字段
const selectAllFields = () => {
  const pendingFields = filteredFields.value
    .filter(field => field.confirmationStatus === 'PENDING')
    .map(field => field.fieldId);
  selectedFields.value = pendingFields;
};

// 清除选择
const clearSelection = () => {
  selectedFields.value = [];
};

// 刷新映射
const refreshMapping = () => {
  fetchFieldMapping();
};

// 字段类型变化处理
const onFieldTypeChange = () => {
  // 根据字段类型重置相关配置
  if (isSelectType(fieldEditForm.value.type)) {
    if (
      !fieldEditForm.value.options ||
      fieldEditForm.value.options.length === 0
    ) {
      fieldEditForm.value.options = [
        { label: '选项1', value: 'option1', score: 1 },
      ];
    }
  }
};

// 添加选项
const addOption = () => {
  fieldEditForm.value.options.push({
    label: `选项${fieldEditForm.value.options.length + 1}`,
    value: `option${fieldEditForm.value.options.length + 1}`,
    score: fieldEditForm.value.options.length + 1,
  });
};

// 移除选项
const removeOption = index => {
  fieldEditForm.value.options.splice(index, 1);
};

// 工具方法
const isSelectType = type => {
  return ['SELECT', 'MULTI_SELECT', 'RADIO', 'CHECKBOX'].includes(type);
};

const isNumberType = type => {
  return ['NUMBER', 'INTEGER', 'RATING', 'SLIDER'].includes(type);
};

const isTextType = type => {
  return ['TEXT', 'TEXTAREA', 'EMAIL', 'PHONE', 'URL'].includes(type);
};

const getConfidenceTagType = confidence => {
  if (!confidence) return 'info';
  if (confidence >= 0.8) return 'success';
  if (confidence >= 0.6) return 'warning';
  return 'danger';
};

const formatConfidence = confidence => {
  if (!confidence) return '未知';
  return `${(confidence * 100).toFixed(0)}%`;
};

const getStatusTagType = status => {
  const statusMap = {
    PENDING: 'warning',
    CONFIRMED: 'success',
    MODIFIED: 'primary',
    REJECTED: 'danger',
  };
  return statusMap[status] || 'info';
};

const getStatusDisplayName = status => {
  const statusMap = {
    PENDING: '待确认',
    CONFIRMED: '已确认',
    MODIFIED: '已修改',
    REJECTED: '已拒绝',
  };
  return statusMap[status] || status;
};

const getFieldTypeDisplayName = type => {
  const typeMap = {
    TEXT: '文本',
    TEXTAREA: '长文本',
    NUMBER: '数字',
    INTEGER: '整数',
    SELECT: '单选',
    MULTI_SELECT: '多选',
    RADIO: '单选按钮',
    CHECKBOX: '复选框',
    DATE: '日期',
    TIME: '时间',
    RATING: '评分',
    SLIDER: '滑块',
    BOOLEAN: '布尔值',
    EMAIL: '邮箱',
    PHONE: '电话',
    URL: '链接',
  };
  return typeMap[type] || type;
};

const hasValidationRules = validation => {
  if (!validation) return false;
  return (
    validation.minValue ||
    validation.maxValue ||
    validation.minLength ||
    validation.maxLength ||
    validation.pattern
  );
};

const formatValidationRules = validation => {
  const rules = [];
  if (validation.minValue) rules.push(`最小值: ${validation.minValue}`);
  if (validation.maxValue) rules.push(`最大值: ${validation.maxValue}`);
  if (validation.minLength) rules.push(`最小长度: ${validation.minLength}`);
  if (validation.maxLength) rules.push(`最大长度: ${validation.maxLength}`);
  if (validation.pattern) rules.push(`格式: ${validation.pattern}`);
  return rules.join(', ');
};

// 组件挂载
onMounted(() => {
  fetchFieldMapping();
  fetchFieldTypes();
});
</script>

<style scoped>
.field-mapping-container {
  padding: 20px;
}

.header-card,
.action-card,
.fields-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-subtitle {
  color: #909399;
  font-size: 14px;
}

.scale-info {
  margin-top: 15px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 10px;
}

.fields-container {
  min-height: 400px;
}

.field-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  background-color: #fafbfc;
  transition: all 0.3s;
}

.field-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.field-selected {
  border-color: #5357A0;
  background-color: #f0f1fc;
}

.field-confirmed {
  border-color: #5357A0;
  background-color: #f0f1fc;
}

.field-modified {
  border-color: #5357A0;
  background-color: #f0f1fc;
}

.field-rejected {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.field-checkbox {
  float: right;
  margin-left: 10px;
}

.field-basic {
  margin-bottom: 15px;
}

.field-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.field-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.field-original,
.field-description {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.field-edit,
.field-details {
  margin: 15px 0;
}

.detail-item {
  margin: 5px 0;
  font-size: 14px;
}

.options-display,
.validation-display {
  margin: 10px 0;
  font-size: 14px;
}

.validation-info {
  color: #606266;
}

.field-actions {
  text-align: right;
  margin-top: 15px;
}

.options-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
}

.option-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.option-row:last-child {
  margin-bottom: 0;
}

.validation-edit {
  margin: 15px 0;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.edit-actions {
  text-align: right;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}
</style>
