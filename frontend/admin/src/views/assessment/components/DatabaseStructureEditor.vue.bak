<template>
  <div class="database-structure-section">
    <!-- 数据库结构编辑 -->
    <el-card
      v-if="databaseStructure && databaseStructure.fields?.length > 0"
      class="structure-card"
      shadow="hover"
    >
      <template #header>
        <div class="structure-header">
          <div class="flex items-center">
            <CircleStackIcon class="h-5 w-5 mr-2" />
            <span>数据库结构设计</span>
          </div>
          <div class="structure-actions">
            <el-button size="small" @click="generateSQL" type="primary">
              <div class="flex items-center">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                <span>生成SQL</span>
              </div>
            </el-button>
            <el-button
              size="small"
              @click="copySQL"
              :disabled="!generatedSQL"
              type="primary"
            >
              <div class="flex items-center">
                <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
                <span>复制SQL</span>
              </div>
            </el-button>
            <el-button
              size="small"
              @click="executeSQL"
              :disabled="!generatedSQL"
              type="primary"
            >
              <div class="flex items-center">
                <BoltIcon class="h-4 w-4 mr-1" />
                <span>执行建表</span>
              </div>
            </el-button>
          </div>
        </div>
      </template>

      <!-- 表基本信息 -->
      <div class="table-info-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              :model-value="databaseStructure.tableName"
              @update:model-value="updateTableName"
              placeholder="表名"
            >
              <template #prepend>表名</template>
            </el-input>
          </el-col>
          <el-col :span="16">
            <el-input
              :model-value="databaseStructure.tableComment"
              @update:model-value="updateTableComment"
              placeholder="表说明"
            >
              <template #prepend>说明</template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 字段列表 -->
      <div class="fields-section">
        <div class="fields-header">
          <div class="flex items-center">
            <ClipboardDocumentListIcon class="h-5 w-5 mr-2" />
            <h4>字段列表 ({{ databaseStructure.fields.length }}个)</h4>
          </div>
          <el-button size="small" @click="addField" type="primary">
            <div class="flex items-center">
              <PlusIcon class="h-4 w-4 mr-1" />
              <span>添加字段</span>
            </div>
          </el-button>
        </div>

        <el-table
          :data="databaseStructure.fields"
          border
          stripe
          size="small"
          class="fields-table"
        >
          <el-table-column prop="name" label="字段名" width="150">
            <template #default="{ row, $index }">
              <el-input
                :model-value="row.name"
                @update:model-value="
                  updateFieldProperty($index, 'name', $event)
                "
                size="small"
                placeholder="字段名"
              />
            </template>
          </el-table-column>

          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row, $index }">
              <el-select
                :model-value="row.type"
                @update:model-value="
                  updateFieldProperty($index, 'type', $event)
                "
                size="small"
                placeholder="类型"
              >
                <el-option value="VARCHAR" label="VARCHAR" />
                <el-option value="TEXT" label="TEXT" />
                <el-option value="INT" label="INT" />
                <el-option value="BIGINT" label="BIGINT" />
                <el-option value="DECIMAL" label="DECIMAL" />
                <el-option value="DATETIME" label="DATETIME" />
                <el-option value="DATE" label="DATE" />
                <el-option value="TIME" label="TIME" />
                <el-option value="BOOLEAN" label="BOOLEAN" />
                <el-option value="JSON" label="JSON" />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="length" label="长度" width="80">
            <template #default="{ row, $index }">
              <el-input
                :model-value="row.length"
                @update:model-value="
                  updateFieldProperty($index, 'length', $event)
                "
                size="small"
                placeholder="长度"
                v-if="['VARCHAR', 'CHAR', 'DECIMAL'].includes(row.type)"
              />
            </template>
          </el-table-column>

          <el-table-column prop="nullable" label="可空" width="60">
            <template #default="{ row, $index }">
              <el-checkbox
                :model-value="row.nullable"
                @update:model-value="
                  updateFieldProperty($index, 'nullable', $event)
                "
              />
            </template>
          </el-table-column>

          <el-table-column prop="defaultValue" label="默认值" width="100">
            <template #default="{ row, $index }">
              <el-input
                :model-value="row.defaultValue"
                @update:model-value="
                  updateFieldProperty($index, 'defaultValue', $event)
                "
                size="small"
                placeholder="默认值"
              />
            </template>
          </el-table-column>

          <el-table-column prop="comment" label="说明" min-width="150">
            <template #default="{ row, $index }">
              <el-input
                :model-value="row.comment"
                @update:model-value="
                  updateFieldProperty($index, 'comment', $event)
                "
                size="small"
                placeholder="字段说明"
              />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100">
            <template #default="{ row, $index }">
              <el-button
                size="small"
                type="danger"
                @click="removeField($index)"
                :disabled="databaseStructure.fields.length <= 1"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 生成的SQL预览 -->
      <div v-if="generatedSQL" class="sql-preview-section">
        <div class="sql-header">
          <div class="flex items-center"><DocumentTextIcon class="h-4 w-4 mr-1" /><span>生成的SQL语句</span></div>
          <div class="sql-actions">
            <el-button size="small" @click="copySQL" type="primary">
              <div class="flex items-center">
                <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
                <span>复制SQL</span>
              </div>
            </el-button>
            <el-button size="small" @click="executeSQL" type="primary">
              <div class="flex items-center">
                <BoltIcon class="h-4 w-4 mr-1" />
                <span>执行建表</span>
              </div>
            </el-button>
          </div>
        </div>

        <el-input
          :model-value="generatedSQL"
          type="textarea"
          :rows="12"
          readonly
          class="sql-textarea"
          placeholder="生成的SQL语句将显示在这里..."
        />
      </div>

      <!-- 确认按钮 -->
      <div class="confirm-section">
        <el-button
          type="success"
          size="large"
          @click="confirmDatabaseStructure"
          :disabled="!databaseStructure.fields.length"
        >
          <div class="flex items-center">
            <CheckCircleIcon class="h-4 w-4 mr-1" />
            <span>确认数据库结构并保存配置</span>
          </div>
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watchEffect } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ArrowPathIcon,
  ClipboardDocumentIcon,
  BoltIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  CircleStackIcon,
  PlusIcon,
  DocumentTextIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  databaseStructure: any;
  generatedSQL: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits([
  'update:databaseStructure',
  'update:generatedSQL',
  'structure-change',
  'generate-sql',
  'copy-sql',
  'execute-sql',
  'confirm-structure',
]);

// Methods
const onStructureChange = () => {
  emit('update:databaseStructure', props.databaseStructure);
  emit('structure-change');
};

const updateTableName = value => {
  const updatedStructure = {
    ...props.databaseStructure,
    tableName: value,
  };
  emit('update:databaseStructure', updatedStructure);
  emit('structure-change');
};

const updateTableComment = value => {
  const updatedStructure = {
    ...props.databaseStructure,
    tableComment: value,
  };
  emit('update:databaseStructure', updatedStructure);
  emit('structure-change');
};

const updateFieldProperty = (index, property, value) => {
  const updatedFields = [...props.databaseStructure.fields];
  updatedFields[index] = {
    ...updatedFields[index],
    [property]: value,
  };

  const updatedStructure = {
    ...props.databaseStructure,
    fields: updatedFields,
  };

  emit('update:databaseStructure', updatedStructure);
  emit('structure-change');
};

const addField = () => {
  const newField = {
    name: '',
    type: 'VARCHAR',
    length: '255',
    nullable: true,
    defaultValue: '',
    comment: '',
  };

  const updatedStructure = {
    ...props.databaseStructure,
    fields: [...props.databaseStructure.fields, newField],
  };

  emit('update:databaseStructure', updatedStructure);
  emit('structure-change');
};

const removeField = (index: number) => {
  const updatedStructure = {
    ...props.databaseStructure,
    fields: props.databaseStructure.fields.filter((_, i) => i !== index),
  };

  emit('update:databaseStructure', updatedStructure);
  emit('structure-change');
};

const generateSQL = () => {
  emit('generate-sql');
};

const copySQL = () => {
  emit('copy-sql');
};

const executeSQL = () => {
  emit('execute-sql');
};

const confirmDatabaseStructure = () => {
  emit('confirm-structure');
};
</script>

<style scoped>
.database-structure-section {
  margin-bottom: 20px;
}

.structure-card {
  border: 1px solid #e4e7ed;
}

.structure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.structure-actions {
  display: flex;
  gap: 8px;
}

.table-info-section {
  margin-bottom: 20px;
}

.fields-section {
  margin-bottom: 20px;
}

.fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.fields-header h4 {
  margin: 0;
  color: #303133;
}

.fields-table {
  width: 100%;
}

.sql-preview-section {
  margin-bottom: 20px;
}

.sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sql-header h4 {
  margin: 0;
  color: #303133;
}

.sql-actions {
  display: flex;
  gap: 8px;
}

.sql-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
}

.confirm-section {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}
</style>
