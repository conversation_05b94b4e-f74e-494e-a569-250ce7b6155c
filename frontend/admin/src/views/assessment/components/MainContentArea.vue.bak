<template>
  <div class="flex-1 flex gap-4">
    <!-- 左侧：文档编辑区 -->
    <div class="flex-1">
      <el-card class="card card-hover h-full">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <DocumentTextIcon class="h-5 w-5 text-primary-700 mr-2" />
              <span class="text-xl font-bold text-primary-700">文档内容编辑</span>
            </div>
            <div class="space-x-2">
              <el-button
                size="small"
                @click="$emit('save-content')"
                :loading="saving"
                type="primary"
              >
                <div class="flex items-center">
                  <ArchiveBoxIcon class="h-4 w-4 mr-1" />
                  <span>保存内容</span>
                </div>
              </el-button>
            </div>
          </div>
        </template>

        <!-- 编辑模式切换 -->
        <div class="mb-4">
          <el-radio-group
            :model-value="editMode"
            @update:model-value="$emit('edit-mode-change', $event)"
            size="small"
            class="mode-selector"
          >
            <el-radio-button value="preview">
              <div class="flex items-center">
                <EyeIcon class="h-4 w-4 mr-1" />
                <span>预览模式</span>
              </div>
            </el-radio-button>
            <el-radio-button value="edit">
              <div class="flex items-center">
                <PencilIcon class="h-4 w-4 mr-1" />
                <span>编辑模式</span>
              </div>
            </el-radio-button>
            <el-radio-button value="split">
              <div class="flex items-center">
                <RectangleStackIcon class="h-4 w-4 mr-1" />
                <span>分屏模式</span>
              </div>
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 内容编辑器 -->
        <MarkdownEditor
          :parse-result="parseResult"
          :editable-markdown="editableMarkdown"
          :raw-markdown="editableMarkdown"
          :edit-mode="editMode"
          :saving="saving"
          @update:content="$emit('update:content', $event)"
        />
      </el-card>
    </div>

    <!-- 右侧：功能面板 -->
    <div class="w-1/3 space-y-4">
      <!-- 量表属性面板 -->
      <ScalePropertiesPanel
        v-if="parseResult"
        :scale-data="parseResult"
        @update:scale-data="$emit('update:scale-data', $event)"
      />

      <!-- AI分析面板 -->
      <AIAnalysisSection
        :parse-result="parseResult"
        :ai-analyzing="false"
        :stream-output="streamOutput"
        :content-blocks="[]"
        :ai-generated-content="streamOutput"
        :ai-analysis-result="aiAnalysisResult || ''"
        :custom-prompt="customPrompt"
        :model-info="{}"
        @analyze="$emit('analyze-content')"
        @update:prompt="$emit('update:prompt', $event)"
        @save-prompt="$emit('save-prompt')"
        @clear-output="$emit('clear-output')"
        @copy-output="$emit('copy-output')"
      />

      <!-- 数据库设计面板 -->
      <DatabaseStructureEditor
        :database-structure="databaseStructure"
        :generatedSQL="sqlStatement || ''"
        @update:structure="$emit('update:structure', $event)"
        @auto-detect="$emit('auto-detect-fields')"
        @generate-sql="$emit('generate-sql')"
        @parse-sql="$emit('parse-sql')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import MarkdownEditor from './MarkdownEditor.vue';
// @ts-ignore
import ScalePropertiesPanel from './ScalePropertiesPanel.vue';
import AIAnalysisSection from './AIAnalysisSection.vue';
import DatabaseStructureEditor from './DatabaseStructureEditor.vue';
import {
  DocumentTextIcon,
  ArchiveBoxIcon,
  EyeIcon,
  PencilIcon,
  RectangleStackIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  editMode: 'preview' | 'edit' | 'split';
  editableMarkdown: string;
  parseResult: any;
  aiAnalysisResult: any;
  aiStreaming: boolean;
  streamOutput: string;
  customPrompt: string;
  databaseStructure: any;
  sqlStatement: string;
  detectingFields: boolean;
  loading: boolean;
  saving: boolean;
}

defineProps<Props>();

// Emits
defineEmits<{
  'load-test-data': [];
  'save-content': [];
  'edit-mode-change': [mode: string];
  'update:content': [content: string];
  'update:scale-data': [data: any];
  'analyze-content': [];
  'update:prompt': [prompt: string];
  'save-prompt': [];
  'clear-output': [];
  'copy-output': [];
  'update:structure': [structure: any];
  'auto-detect-fields': [];
  'generate-sql': [];
  'parse-sql': [];
}>();
</script>

<style scoped>
.mode-selector {
  display: flex;
  gap: 2px;
}

.mode-selector :deep(.el-radio-button) {
  margin: 0;
}

.mode-selector :deep(.el-radio-button__inner) {
  padding: 8px 16px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  min-height: 32px;
  white-space: nowrap;
}

.mode-selector :deep(.el-radio-button__inner svg) {
  color: #5b7ad8 !important; /* 使用主色长春花蓝 */
}

.mode-selector :deep(.el-radio-button__inner:hover) {
  background: #ecf5ff;
  border-color: #5b7ad8;
  color: #5b7ad8;
}

.mode-selector :deep(.el-radio-button__inner:hover svg) {
  color: #5b7ad8 !important;
}

.mode-selector :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: #5b7ad8;
  border-color: #5b7ad8;
  color: #fff;
  box-shadow: none;
}

.mode-selector :deep(.el-radio-button.is-active .el-radio-button__inner svg) {
  color: #fff !important;
}

.card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.text-primary-700 {
  color: #1d4ed8;
}
</style>
