<template>
  <div class="ai-analysis-section">
    <!-- AI分析操作区域 -->
    <el-card class="ai-control-card" shadow="never">
      <template #header>
        <div class="ai-header">
          <div class="flex items-center">
            <CpuChipIcon class="h-5 w-5 text-primary-700 mr-2" />
            <span class="text-xl font-bold text-primary-700">AI智能分析与提示词编辑</span>
          </div>
          <div class="ai-actions">
            <el-button
              type="primary"
              :disabled="!parseResult || aiAnalyzing"
              :loading="aiAnalyzing"
              @click="startAIAnalysis"
              size="default"
            >
              <div class="flex items-center">
                <CogIcon v-if="aiAnalyzing" class="h-4 w-4 mr-1 animate-spin" />
                <RocketLaunchIcon v-else class="h-4 w-4 mr-1" />
                <span>{{ aiAnalyzing ? 'AI分析中...' : 'AI智能分析' }}</span>
              </div>
            </el-button>
            <el-button
              type="info"
              :disabled="!parseResult"
              @click="openAIChat"
              size="default"
              style="margin-left: 8px"
            >
              <div class="flex items-center">
                <ChatBubbleLeftRightIcon class="h-4 w-4 mr-1" />
                <span>AI对话助手</span>
              </div>
            </el-button>
          </div>
        </div>
      </template>

      <!-- 提示词编辑区域 -->
      <div class="prompt-edit-section">
        <div class="prompt-controls">
          <el-button-group>
            <el-button size="small" type="info" @click="loadDefaultPrompt">
              <div class="flex items-center">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                <span>重置默认提示词</span>
              </div>
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="previewCurrentPrompt"
            >
              <div class="flex items-center">
                <EyeIcon class="h-4 w-4 mr-1" />
                <span>预览当前提示词</span>
              </div>
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="saveCustomPrompt"
              :disabled="!customPrompt.trim()"
            >
              <div class="flex items-center">
                <ArchiveBoxIcon class="h-4 w-4 mr-1" />
                <span>保存自定义提示词</span>
              </div>
            </el-button>
          </el-button-group>
        </div>

        <el-input
          :model-value="customPrompt"
          @update:model-value="$emit('update:customPrompt', $event)"
          type="textarea"
          :rows="15"
          placeholder="在此编辑AI分析提示词..."
          class="prompt-textarea"
          resize="vertical"
          style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace"
        >
          <template #prepend>
            <div class="flex items-center px-2">
              <CpuChipIcon class="h-4 w-4 mr-1" />
              <span>提示词内容</span>
            </div>
          </template>
        </el-input>

        <div class="flex items-center gap-4 mt-2 text-xs text-gray-600">
          <div class="flex items-center">
            <ChartBarIcon class="h-3 w-3 mr-1" />
            <span>字符数: {{ customPrompt.length }}</span>
          </div>
          <div class="flex items-center">
            <DocumentTextIcon class="h-3 w-3 mr-1" />
            <span>预估Token: {{ Math.ceil(customPrompt.length / 3) }}</span>
          </div>
          <div class="flex items-center">
            <ClockIcon class="h-3 w-3 mr-1" />
            <span>预估处理时间: {{ Math.max(2, Math.ceil(customPrompt.length / 8000)) }}分钟</span>
          </div>
        </div>
      </div>

      <!-- AI功能介绍 -->
      <div v-if="!parseResult" class="ai-intro-section">
        <el-alert
          :title="titleWithIcon"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="ai-intro">
              <div class="feature-highlight">
                <div class="flex items-center">
                  <TagIcon class="h-5 w-5 text-black mr-2" />
                  <h4 class="text-black font-bold">
                  智能解析评估量表并生成数据库表结构</h4>
                </div>
                <p class="feature-desc">
                  通过AI分析PDF文档中的评估量表，自动识别字段、选项、数据类型，生成标准化的数据库表结构，实现评估数据的规范化存储。
                </p>
              </div>

              <div class="capabilities-grid">
                <div class="capability-item">
                  <span class="capability-icon">
                    <ClipboardDocumentListIcon class="h-6 w-6" />
                  </span>
                  <div>
                    <strong>字段识别</strong>
                    <p>自动识别量表中的评估项目、选项和权重</p>
                  </div>
                </div>
                <div class="capability-item">
                  <span class="capability-icon">
                    <MagnifyingGlassIcon class="h-6 w-6" />
                  </span>
                  <div>
                    <strong>类型推断</strong>
                    <p>智能推断字段数据类型和合适的字段长度</p>
                  </div>
                </div>
                <div class="capability-item">
                  <span class="capability-icon">
                    <DocumentTextIcon class="h-6 w-6" />
                  </span>
                  <div>
                    <strong>规范命名</strong>
                    <p>生成标准的表名、字段名和详细注释</p>
                  </div>
                </div>
                <div class="capability-item">
                  <span class="capability-icon">
                    <BoltIcon class="h-6 w-6" />
                  </span>
                  <div>
                    <strong>SQL生成</strong>
                    <p>一键生成完整的CREATE TABLE语句</p>
                  </div>
                </div>
              </div>

              <div class="usage-steps">
                <h5 class="flex items-center">
                  <DocumentTextIcon class="h-4 w-4 mr-1" />
                  使用步骤：
                </h5>
                <ol>
                  <li>上传包含评估量表的多格式文档</li>
                  <li>等待Docling AI完成文档解析</li>
                  <li>点击"AI智能分析"按钮分析表结构</li>
                  <li>查看并确认生成的数据库结构</li>
                  <li>点击"确认入库"保存量表配置</li>
                </ol>
              </div>

              <div class="tech-info">
                <template v-if="modelInfo">
                  <p v-if="modelInfo.displayName">
                    <div class="flex items-center">
                      <RocketLaunchIcon class="h-4 w-4 mr-1" />
                      <strong>技术栈:</strong> LM Studio +
                      {{ modelInfo.displayName }}本地大模型 ({{
                        getHostFromUrl(modelInfo.url)
                      }})
                    </div>
                  </p>
                  <p v-else-if="modelInfo.id">
                    <div class="flex items-center">
                      <RocketLaunchIcon class="h-4 w-4 mr-1" />
                      <strong>技术栈:</strong> LM Studio +
                      {{ modelInfo.id }}本地大模型 ({{
                        getHostFromUrl(modelInfo.url)
                      }})
                    </div>
                  </p>
                  <div v-else class="flex items-center">
                    <RocketLaunchIcon class="h-4 w-4 mr-1" />
                    <strong>技术栈:</strong> LM Studio 本地大模型 ({{ getHostFromUrl(modelInfo.url) }})
                  </div>
                </template>
                <div v-else>
                  <div class="flex items-center">
                    <RocketLaunchIcon class="h-4 w-4 mr-1" />
                    <strong>技术栈:</strong> LM Studio 本地大模型
                    <span style="margin-left: 4px">(正在获取模型信息...)</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- AI流式分析输出 -->
    <div v-if="aiAnalyzing || streamOutput" class="ai-stream-section">
      <el-card class="stream-card" shadow="hover">
        <template #header>
          <div class="stream-header">
            <span class="stream-title flex items-center">
              <CpuChipIcon class="h-4 w-4 mr-2" />
              AI实时分析过程
            </span>
            <el-tag
              v-if="aiAnalyzing"
              type="primary"
              effect="plain"
              size="small"
            >
              <i class="el-icon-loading" style="margin-right: 4px"></i>
              分析中...
            </el-tag>
            <el-tag v-else type="info" effect="plain" size="small">
              分析完成
            </el-tag>
          </div>
        </template>

        <div class="stream-content">
          <div class="stream-output" ref="streamOutputRef">
            <pre class="stream-text">{{
              streamOutput || '等待AI开始分析...'
            }}</pre>
          </div>

          <!-- AI实时生成内容区域 - 块状显示 -->
          <div v-if="contentBlocks.length > 0" class="ai-generated-section">
            <el-divider content-position="left">
              <span style="color: #fed81f; font-weight: bold" class="flex items-center">
                <CpuChipIcon class="h-4 w-4 mr-1" />
                AI智能分析结果
              </span>
            </el-divider>
            <div class="ai-generated-content">
              <ContentBlock
                v-for="block in contentBlocks"
                :key="block.id"
                :block="block"
              />
            </div>
            <div
              class="ai-content-actions"
              style="margin-top: 16px; text-align: right"
            >
              <el-button size="small" @click="copyAllContent"
                >
                  <div class="flex items-center">
                    <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
                    <span>复制全部</span>
                  </div>
                </el-button
              >
              <el-button
                size="small"
                @click="copyCodeBlocks"
                :disabled="!hasCodeBlocks"
                >
                  <div class="flex items-center">
                    <DocumentTextIcon class="h-4 w-4 mr-1" />
                    <span>复制代码</span>
                  </div>
                </el-button
              >
              <el-button size="small" type="primary" @click="parseAIResult"
                >
                  <div class="flex items-center">
                    <ArrowPathIcon class="h-4 w-4 mr-1" />
                    <span>解析结果</span>
                  </div>
                </el-button
              >
            </div>
          </div>

          <!-- 兼容性：保留原始AI内容显示 -->
          <div v-else-if="aiGeneratedContent" class="ai-generated-section">
            <el-divider content-position="left">
              <span style="color: #fed81f; font-weight: bold" class="flex items-center">
                <CpuChipIcon class="h-4 w-4 mr-1" />
                AI实时生成内容
              </span>
            </el-divider>
            <div class="ai-generated-content">
              <pre class="ai-generated-text">{{ aiGeneratedContent }}</pre>
            </div>
          </div>

          <div
            class="stream-actions"
            style="margin-top: 12px; text-align: right"
          >
            <el-button
              size="small"
              type="info"
              @click="clearStreamOutput"
              :disabled="aiAnalyzing"
              icon="el-icon-delete"
            >
              清空输出
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="copyStreamOutput"
              :disabled="!streamOutput.trim()"
              icon="el-icon-document-copy"
            >
              复制输出
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- AI分析建议 -->
    <div v-if="aiAnalysisResult" class="ai-analysis-result">
      <el-alert type="info" :closable="false" show-icon>
        <template #title>
          <div class="flex items-center">
            <CpuChipIcon class="h-4 w-4 mr-1" />
            AI分析建议
          </div>
        </template>
        <template #default>
          <div class="ai-analysis-content">
            <pre class="ai-analysis-text">{{ aiAnalysisResult }}</pre>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import ContentBlock from '../../../components/ContentBlock.vue';
import {
  CpuChipIcon,
  CogIcon,
  RocketLaunchIcon,
  ChatBubbleLeftRightIcon,
  ArrowPathIcon,
  EyeIcon,
  ArchiveBoxIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ClockIcon,
  TagIcon,
  ClipboardDocumentListIcon,
  MagnifyingGlassIcon,
  BoltIcon,
  ClipboardDocumentIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  parseResult: any;
  aiAnalyzing: boolean;
  streamOutput: string;
  contentBlocks: any[];
  aiGeneratedContent: string;
  aiAnalysisResult: string;
  customPrompt: string;
  modelInfo: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits([
  'start-ai-analysis',
  'load-default-prompt',
  'preview-current-prompt',
  'save-custom-prompt',
  'clear-stream-output',
  'copy-stream-output',
  'copy-all-content',
  'copy-code-blocks',
  'parse-ai-result',
  'open-ai-chat',
  'update:customPrompt',
]);

// Computed
const hasCodeBlocks = computed(() => {
  return props.contentBlocks.some(block => block.type === 'code');
});

const titleWithIcon = computed(() => {
  return 'AI智能数据库结构分析 - 自动将评估量表转换为数据库表结构';
});

// Methods
const startAIAnalysis = () => {
  emit('start-ai-analysis');
};

const loadDefaultPrompt = () => {
  emit('load-default-prompt');
};

const previewCurrentPrompt = () => {
  emit('preview-current-prompt');
};

const saveCustomPrompt = () => {
  emit('save-custom-prompt');
};

const clearStreamOutput = () => {
  emit('clear-stream-output');
};

const copyStreamOutput = () => {
  emit('copy-stream-output');
};

const copyAllContent = () => {
  emit('copy-all-content');
};

const copyCodeBlocks = () => {
  emit('copy-code-blocks');
};

const parseAIResult = () => {
  emit('parse-ai-result');
};

const openAIChat = () => {
  emit('open-ai-chat');
};

const getHostFromUrl = (url: string) => {
  try {
    return new URL(url).host;
  } catch {
    return url;
  }
};

// Stream output ref
const streamOutputRef = ref();
</script>

<style scoped>
.ai-analysis-section {
  margin-bottom: 20px;
}

.ai-control-card {
  margin-bottom: 20px;
}

.ai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.prompt-edit-section {
  margin-bottom: 20px;
}

.prompt-controls {
  margin-bottom: 12px;
}

.prompt-textarea {
  margin-bottom: 8px;
}

.prompt-stats {
  display: flex;
  gap: 16px;
}

.ai-intro-section {
  margin-top: 20px;
}

.ai-intro {
  padding: 12px 0;
}

.feature-highlight {
  margin-bottom: 20px;
}

.feature-highlight h4 {
  margin: 0 0 8px 0;
  color: #000000 !important;
}

.feature-desc {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.capability-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.capability-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.capability-item strong {
  display: block;
  margin-bottom: 4px;
  color: #333;
}

.capability-item p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.usage-steps {
  margin: 20px 0;
}

.usage-steps h5 {
  margin: 0 0 12px 0;
  color: #333;
}

.usage-steps ol {
  margin: 0;
  padding-left: 20px;
}

.usage-steps li {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

.tech-info {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
}

.tech-info p {
  margin: 4px 0;
  color: #5357A0;
  font-size: 14px;
}

.ai-stream-section {
  margin-bottom: 20px;
}

.stream-card {
  border: 1px solid #e4e7ed;
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stream-title {
  font-weight: 500;
  color: #303133;
}

.stream-content {
  max-height: 600px;
  overflow-y: auto;
}

.stream-output {
  background: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.stream-text {
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ai-generated-section {
  margin-top: 20px;
}

.ai-generated-content {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.ai-generated-text {
  color: #333;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ai-content-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.stream-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
}

.ai-analysis-result {
  margin-top: 20px;
}

.ai-analysis-content {
  max-height: 300px;
  overflow-y: auto;
}

.ai-analysis-text {
  color: #333;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
