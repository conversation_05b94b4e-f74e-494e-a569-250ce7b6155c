<template>
  <div class="text-center">
    <div class="space-y-2">
      <!-- Docling引擎状态 -->
      <el-tag
        :type="doclingAvailable ? 'success' : 'warning'"
        size="large"
        class="mr-2"
      >
        <div class="flex items-center">
          <component :is="doclingAvailable ? CheckCircleIcon : ClockIcon" class="h-4 w-4 mr-1" />
          <span>{{ doclingAvailable ? 'Docling引擎就绪' : 'Docling引擎检查中' }}</span>
        </div>
      </el-tag>

      <!-- AI分析服务状态 -->
      <el-tag
        :type="aiServiceAvailable ? 'success' : 'warning'"
        size="large"
        class="mr-2"
      >
        <template v-if="aiServiceAvailable">
          <CpuChipIcon class="h-4 w-4 mr-1 inline" />
          LM Studio AI就绪
        </template>
        <template v-else>
          <ClockIcon class="h-4 w-4 mr-1 inline" />
          LM Studio AI检查中
        </template>
      </el-tag>

      <!-- 操作按钮 -->
      <div class="mt-2 space-x-2">
        <el-button
          size="small"
          @click="$emit('check-docling')"
          :loading="checkingDocling"
        >
          <div class="flex items-center">
            <ArrowPathIcon class="h-4 w-4 mr-1" />
            <span>检查Docling</span>
          </div>
        </el-button>
        <el-button
          size="small"
          @click="$emit('check-ai')"
          :loading="checkingAi"
          type="primary"
        >
          <div class="flex items-center">
            <CpuChipIcon class="h-4 w-4 mr-1" />
            <span>检查AI服务</span>
          </div>
        </el-button>
      </div>

      <!-- AI服务详细信息 -->
      <div class="mt-2 text-xs text-neutral-600 text-center leading-relaxed">
        <template v-if="modelInfo">
          <div v-if="modelInfo.displayName">
            <div class="flex items-center">
              <RocketLaunchIcon class="h-4 w-4 mr-1" />
              <span>{{ modelInfo.displayName }} @ {{ getHostFromUrl(modelInfo.url) }}</span>
            </div>
          </div>
          <div v-else-if="modelInfo.id">
            <div class="flex items-center">
              <RocketLaunchIcon class="h-4 w-4 mr-1" />
              <span>{{ modelInfo.id }} @ {{ getHostFromUrl(modelInfo.url) }}</span>
            </div>
          </div>
          <div v-else class="flex items-center">
            <RocketLaunchIcon class="h-4 w-4 mr-1" />
            <span>模型已连接 @ {{ getHostFromUrl(modelInfo.url) }}</span>
          </div>
        </template>
        <div v-else class="flex items-center">
          <RocketLaunchIcon class="h-4 w-4 mr-1" />
          <span>正在获取模型信息...</span>
        </div>
        <div class="flex items-center">
          <WrenchScrewdriverIcon class="h-4 w-4 mr-1" />
          <span>Docling @ localhost:8088</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowPathIcon,
  CpuChipIcon,
  RocketLaunchIcon,
  WrenchScrewdriverIcon,
  ClockIcon,
  CheckCircleIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  doclingAvailable: boolean;
  aiServiceAvailable: boolean;
  checkingDocling: boolean;
  checkingAi: boolean;
  modelInfo: any;
}

defineProps<Props>();

// Emits
defineEmits<{
  'check-docling': [];
  'check-ai': [];
}>();

// Utility function
const getHostFromUrl = (url: string) => {
  if (!url) return 'Unknown';
  try {
    return new URL(url).host;
  } catch {
    return url;
  }
};
</script>
