<template>
  <el-card class="properties-card" style="height: calc(100vh - 200px)">
    <template #header>
      <div class="flex items-center">
        <Cog6ToothIcon class="h-4 w-4 mr-1" />
        <span>量表属性</span>
      </div>
    </template>

    <div class="properties-content">
      <!-- 基本信息编辑 -->
      <el-form
        v-if="parseResult"
        :model="parseResult"
        label-width="80px"
        size="small"
      >
        <el-form-item label="量表名称" prop="name">
          <el-input
            :model-value="parseResult.name"
            @update:model-value="updateProperty('name', $event)"
            placeholder="请输入量表名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="量表代码" prop="code">
          <el-input
            :model-value="parseResult.code"
            @update:model-value="updateProperty('code', $event)"
            placeholder="例如：ELDERLY_ABILITY_V1"
            clearable
          >
            <template #append>
              <el-button @click="generateCode">生成</el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="量表类型" prop="type">
          <el-select
            :model-value="parseResult.type"
            @update:model-value="updateProperty('type', $event)"
            placeholder="选择量表类型"
            style="width: 100%"
          >
            <el-option label="老年人能力评估" value="ELDERLY_ABILITY">
              <span style="float: left">老年人能力评估</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >国家标准</span
              >
            </el-option>
            <el-option label="情绪快评" value="EMOTIONAL_QUICK">
              <span style="float: left">情绪快评</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >快速筛查</span
              >
            </el-option>
            <el-option label="interRAI评估" value="INTER_RAI">
              <span style="float: left">interRAI评估</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >国际标准</span
              >
            </el-option>
            <el-option label="长护险评估" value="LONG_CARE_INSURANCE">
              <span style="float: left">长护险评估</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >保险资格</span
              >
            </el-option>
            <el-option label="自定义量表" value="CUSTOM">
              <span style="float: left">自定义量表</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >灵活配置</span
              >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="版本号" prop="version">
          <el-input
            :model-value="parseResult.version"
            @update:model-value="updateProperty('version', $event)"
            placeholder="例如：1.0.0"
            clearable
          >
            <template #prepend>v</template>
          </el-input>
        </el-form-item>

        <el-form-item label="预估时长" prop="estimatedDuration">
          <el-input-number
            :model-value="parseResult.estimatedDuration"
            @update:model-value="updateProperty('estimatedDuration', $event)"
            :min="1"
            :max="180"
            :step="5"
            controls-position="right"
            style="width: 100%"
          >
            <template #suffix>
              <span style="color: #909399">分钟</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            :model-value="parseResult.description"
            @update:model-value="updateProperty('description', $event)"
            type="textarea"
            :rows="3"
            placeholder="请输入量表的详细描述和适用场景"
            show-word-limit
            maxlength="500"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="saveProperties"
            :loading="savingProperties"
            style="width: 100%"
          >
            <div class="flex items-center justify-center">
              <ArchiveBoxIcon class="h-4 w-4 mr-1" />
              <span>保存量表属性</span>
            </div>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 量表配置选项 -->
      <div v-if="parseResult" class="scale-config-section">
        <el-divider content-position="left">
          <div class="flex items-center">
            <Cog6ToothIcon class="h-4 w-4 mr-1" />
            <span>高级配置</span>
          </div>
        </el-divider>

        <!-- 评估模式 -->
        <el-form-item label="评估模式" label-width="80px" size="small">
          <el-checkbox-group
            :model-value="parseResult.assessmentModes || []"
            @update:model-value="updateProperty('assessmentModes', $event)"
          >
            <el-checkbox value="MOBILE">
              <div class="flex items-center">
                <DevicePhoneMobileIcon class="h-4 w-4 mr-1" />
                <span>移动端</span>
              </div>
            </el-checkbox>
            <el-checkbox value="WEB">
              <div class="flex items-center">
                <ComputerDesktopIcon class="h-4 w-4 mr-1" />
                <span>网页端</span>
              </div>
            </el-checkbox>
            <el-checkbox value="OFFLINE">
              <div class="flex items-center">
                <ClipboardDocumentListIcon class="h-4 w-4 mr-1" />
                <span>离线评估</span>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 合规标准 -->
        <el-form-item label="合规标准" label-width="80px" size="small">
          <el-select
            :model-value="parseResult.complianceStandard"
            @update:model-value="updateProperty('complianceStandard', $event)"
            placeholder="选择合规标准"
            clearable
            style="width: 100%"
          >
            <el-option label="国家标准GB/T" value="GB_T" />
            <el-option label="民政部标准" value="MZ_T" />
            <el-option label="地方标准" value="LOCAL" />
            <el-option label="机构内部标准" value="INTERNAL" />
          </el-select>
        </el-form-item>

        <!-- 启用状态 -->
        <el-form-item label="启用状态" label-width="80px" size="small">
          <el-switch
            :model-value="parseResult.isActive"
            @update:model-value="updateProperty('isActive', $event)"
            active-text="启用"
            inactive-text="停用"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <!-- 最近文件列表 -->
      <el-divider content-position="left">
        <div class="flex items-center">
          <FolderIcon class="h-4 w-4 mr-1" />
          <span>最近文件</span>
          <el-button
            size="small"
            text
            @click="refreshRecentScales"
            :loading="loadingRecent"
            style="margin-left: 8px"
          >
            <div class="flex items-center">
              <ArrowPathIcon class="h-4 w-4 mr-1" />
              <span>刷新</span>
            </div>
          </el-button>
        </div>
      </el-divider>
      <div class="recent-files">
        <el-scrollbar height="200px">
          <div v-if="recentScales.length === 0" class="empty-recent">
            <el-empty :image-size="60" description="暂无最近文件" />
          </div>
          <div
            v-for="(scale, index) in recentScales.slice(0, 5)"
            :key="index"
            class="recent-file-item"
          >
            <div class="file-content" @click="loadRecentScale(scale)">
              <div class="file-name">{{ scale.name }}</div>
              <div class="file-meta">
                <el-tag size="small" :type="getTypeTagType(scale.type)">
                  {{ getTypeDisplayName(scale.type) }}
                </el-tag>
                <span class="file-time">{{
                  formatDateTime(scale.createdAt)
                }}</span>
              </div>
            </div>
            <div class="file-actions">
              <el-button
                size="small"
                type="danger"
                text
                @click="deleteRecentScale(scale, index)"
              >
                <TrashIcon class="h-4 w-4" />
              </el-button>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Cog6ToothIcon,
  ArchiveBoxIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  ClipboardDocumentListIcon,
  FolderIcon,
  ArrowPathIcon,
  TrashIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  parseResult: {
    type: Object,
    default: null,
  },
  savingProperties: {
    type: Boolean,
    default: false,
  },
  recentScales: {
    type: Array,
    default: () => [],
  },
  loadingRecent: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits([
  'update:parseResult',
  'save-properties',
  'refresh-recent-scales',
  'load-recent-scale',
  'delete-recent-scale',
]);

// Methods
const updateProperty = (key, value) => {
  const updated = {
    ...props.parseResult,
    [key]: value,
  };
  emit('update:parseResult', updated);
};

const generateCode = () => {
  if (props.parseResult?.name) {
    const code = `${props.parseResult.name
      .toUpperCase()
      .replace(/[^A-Z0-9\u4e00-\u9fa5]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')}_V1`;
    updateProperty('code', code);
    ElMessage.success('代码已生成');
  } else {
    ElMessage.warning('请先输入量表名称');
  }
};

const saveProperties = () => {
  emit('save-properties');
};

const refreshRecentScales = () => {
  emit('refresh-recent-scales');
};

const loadRecentScale = scale => {
  emit('load-recent-scale', scale);
};

const deleteRecentScale = (scale, index) => {
  emit('delete-recent-scale', scale, index);
};

// Utility methods
const getTypeTagType = type => {
  const typeMap = {
    ELDERLY_ABILITY: 'success',
    EMOTIONAL_QUICK: 'warning',
    INTER_RAI: 'info',
    LONG_CARE_INSURANCE: 'primary',
    CUSTOM: 'danger',
  };
  return typeMap[type] || 'info';
};

const getTypeDisplayName = type => {
  const typeMap = {
    ELDERLY_ABILITY: '老年人能力评估',
    EMOTIONAL_QUICK: '情绪快评',
    INTER_RAI: 'interRAI评估',
    LONG_CARE_INSURANCE: '长护险评估',
    CUSTOM: '自定义量表',
  };
  return typeMap[type] || type;
};

const formatDateTime = dateStr => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    })}`;
  } catch {
    return dateStr;
  }
};
</script>

<style scoped>
.properties-card {
  border: 1px solid #e4e7ed;
}

.properties-content {
  height: calc(100vh - 280px);
  overflow-y: auto;
}

.scale-config-section {
  margin-top: 20px;
}

.recent-files {
  margin-top: 16px;
}

.empty-recent {
  text-align: center;
  padding: 20px 0;
}

.recent-file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s;
}

.recent-file-item:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.file-content {
  flex: 1;
  cursor: pointer;
}

.file-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-time {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  margin-left: 8px;
}
</style>
