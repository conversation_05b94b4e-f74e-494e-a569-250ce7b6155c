<template>
  <!-- 编辑器内容区 -->
    <div class="editor-content" :class="`editor-mode-${editMode}`">
      <!-- 空状态 -->
      <div v-if="!parseResult" class="empty-state">
        <el-empty 
          description="请上传PDF文件开始解析" 
          :image-size="200"
        >
          <template #description>
            <p>支持上传多种格式的评估量表文档</p>
            <p>系统将使用AI引擎自动解析文档结构</p>
          </template>
        </el-empty>
      </div>
      
      <!-- 预览模式 -->
      <div v-else-if="editMode === 'preview'" class="preview-section">
        <div class="content-preview" v-html="formattedContent"></div>
      </div>

      <!-- 编辑模式 -->
      <div v-else-if="editMode === 'edit'" class="edit-section">
        <el-input
          :model-value="localContent"
          type="textarea"
          :rows="20"
          placeholder="在此编辑Markdown内容..."
          @input="localContent = $event"
          @blur="onContentChange"
        />
      </div>

      <!-- 分屏模式 -->
      <div v-else class="split-section">
        <div class="split-editor">
          <div class="flex items-center mb-2">
            <DocumentTextIcon class="h-4 w-4 mr-1" />
            <h4 class="m-0">编辑</h4>
          </div>
          <el-input
            :model-value="localContent"
            type="textarea"
            :rows="18"
            placeholder="在此编辑Markdown内容..."
            @input="localContent = $event; onContentChange()"
          />
        </div>
        <div class="split-preview">
          <div class="flex items-center mb-2">
            <EyeIcon class="h-4 w-4 mr-1" />
            <h4 class="m-0">预览</h4>
          </div>
          <div class="content-preview" v-html="formattedContent"></div>
        </div>
      </div>
    </div>

    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar">
      <el-button size="small" @click="insertText('**粗体**')">粗体</el-button>
      <el-button size="small" @click="insertText('*斜体*')">斜体</el-button>
      <el-button size="small" @click="insertText('### 标题')">标题</el-button>
      <el-button size="small" @click="insertText('- 列表项')">列表</el-button>
      <el-button size="small" @click="insertText('`代码`')">代码</el-button>
      <el-divider direction="vertical" />
      <el-button size="small" type="danger" @click="resetContent"
        >重置为原始内容</el-button
      >
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { DocumentTextIcon, EyeIcon } from '@heroicons/vue/24/outline';

// Props
interface Props {
  parseResult: any;
  editableMarkdown: string;
  rawMarkdown: string;
  editMode: 'preview' | 'edit' | 'split';
  saving: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits([
  'update:editableMarkdown',
  'content-change',
]);

// Local state
const localContent = ref(props.editableMarkdown);

// Computed
const formattedContent = computed(() => {
  let html = localContent.value;

  // 简单的Markdown转HTML
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');
  html = html.replace(/\n/g, '<br>');

  // 处理表格
  html = html.replace(/\|(.+)\|/g, (match, content) => {
    const cells = content.split('|').map((cell: string) => cell.trim());
    return `<tr>${cells
      .map((cell: string) => `<td>${cell}</td>`)
      .join('')}</tr>`;
  });

  return html;
});

// Watchers
watch(
  () => props.editableMarkdown,
  newValue => {
    console.log('MarkdownEditor: editableMarkdown changed:', newValue);
    localContent.value = newValue;
  },
  { immediate: true }
);

watch(
  () => props.parseResult,
  newValue => {
    console.log('MarkdownEditor: parseResult changed:', newValue);
  },
  { immediate: true }
);

watch(localContent, newValue => {
  emit('update:editableMarkdown', newValue);
});

// Methods
const onContentChange = () => {
  emit('content-change', localContent.value);
};

const insertText = (text: string) => {
  const textarea = document.querySelector(
    '.el-textarea__inner'
  ) as HTMLTextAreaElement;
  if (textarea) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const before = localContent.value.substring(0, start);
    const after = localContent.value.substring(end);

    localContent.value = before + text + after;

    // 设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + text.length, start + text.length);
    }, 0);
  }
};

const resetContent = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置为原始内容吗？当前的修改将会丢失。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    localContent.value = props.rawMarkdown;
    ElMessage.success('已重置为原始内容');
  } catch {
    // 用户取消
  }
};
</script>

<style scoped>
.editor-card {
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.editor-content {
  min-height: 400px;
}

.preview-section,
.edit-section {
  height: 500px;
  overflow-y: auto;
}

.split-section {
  display: flex;
  gap: 20px;
  height: 500px;
}

.split-editor,
.split-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.split-editor h4,
.split-preview h4 {
  color: #303133;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.m-0 {
  margin: 0;
}

.h-4 {
  height: 1rem;
}

.w-4 {
  width: 1rem;
}

.content-preview {
  flex: 1;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  overflow-y: auto;
  line-height: 1.8;
}

.content-preview h1,
.content-preview h2,
.content-preview h3 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.content-preview code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.content-preview strong {
  font-weight: 600;
}

.content-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.content-preview td {
  border: 1px solid #e4e7ed;
  padding: 8px 12px;
}

.editor-toolbar {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
