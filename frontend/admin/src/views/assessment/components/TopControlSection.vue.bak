<template>
  <el-card class="mb-4">
    <template #header>
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-xl font-bold text-primary-700 mb-1 flex items-center">
            <DocumentIcon class="h-5 w-5 mr-2" />
            文档解析与编辑
          </h3>
          <span class="text-sm text-gray-600"
            >上传多格式文档，Docling AI智能解析，人工精确校对</span
          >
        </div>
      </div>
    </template>
    <el-row :gutter="16" align="middle">
      <!-- 文件上传区域 -->
      <el-col :span="8">
        <div class="flex items-center justify-center">
          <el-upload
            ref="uploadRef"
            class="w-full"
            :action="uploadUrl"
            :data="uploadData"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :on-progress="onUploadProgress"
            :file-list="fileList"
            :limit="1"
            :accept="acceptedFileTypes"
            drag
          >
            <el-button type="primary" size="large">
              <el-icon><Upload /></el-icon>
              选择文档文件
            </el-button>
            <template #tip>
              <div class="mt-2 text-xs text-gray-500 space-y-1">
                <div class="flex items-center">
                  <FolderIcon class="h-3 w-3 mr-1" />
                  <span>支持格式：PDF, DOCX, XLSX, HTML, 图片文件</span>
                </div>
                <div class="flex items-center">
                  <ChartBarIcon class="h-3 w-3 mr-1" />
                  <span>最大大小：50MB</span>
                </div>
                <div class="flex items-center gap-2 mt-1">
                  <div class="flex items-center">
                    <RocketLaunchIcon class="h-4 w-4 mr-1" />
                    <span>输出格式：</span>
                  </div>
                  <el-select
                    :model-value="outputFormat"
                    placeholder="选择输出格式"
                    size="small"
                    class="w-60"
                    @change="$emit('format-change', $event)"
                  >
                    <el-option value="markdown">
                      <div class="flex items-center">
                        <DocumentTextIcon class="h-4 w-4 mr-1" />
                        <span>Markdown (推荐)</span>
                      </div>
                    </el-option>
                    <el-option value="html">
                      <div class="flex items-center">
                        <GlobeAltIcon class="h-4 w-4 mr-1" />
                        <span>HTML</span>
                      </div>
                    </el-option>
                    <el-option value="json">
                      <div class="flex items-center">
                        <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
                        <span>JSON</span>
                      </div>
                    </el-option>
                  </el-select>
                </div>
              </div>
            </template>
          </el-upload>
        </div>
      </el-col>

      <!-- 服务状态区域 -->
      <el-col :span="8">
        <div class="text-center">
          <div class="space-y-2">
            <!-- Docling引擎状态 -->
            <el-tag
              :type="checkingDocling ? 'info' : (doclingAvailable ? 'success' : 'danger')"
              size="large"
              class="mr-2"
            >
              <div class="flex items-center">
                <component :is="checkingDocling ? ClockIcon : (doclingAvailable ? CheckCircleIcon : ClockIcon)" class="h-4 w-4 mr-1" />
                <span>{{ checkingDocling ? 'Docling引擎检查中' : (doclingAvailable ? 'Docling引擎就绪' : 'Docling引擎不可用') }}</span>
              </div>
            </el-tag>

            <!-- AI分析服务状态 -->
            <el-tag
              :type="checkingAI ? 'info' : (aiServiceAvailable ? 'success' : 'danger')"
              size="large"
              class="mr-2"
            >
              <template v-if="checkingAI">
                <ClockIcon class="h-4 w-4 mr-1 inline" />
                LM Studio AI检查中
              </template>
              <template v-else-if="aiServiceAvailable">
                <CpuChipIcon class="h-4 w-4 mr-1 inline" />
                LM Studio AI就绪
              </template>
              <template v-else>
                <ClockIcon class="h-4 w-4 mr-1 inline" />
                LM Studio AI不可用
              </template>
            </el-tag>

            <!-- 操作按钮 -->
            <div class="mt-2 space-x-2">
              <el-button
                size="small"
                @click="$emit('check-docling')"
                :loading="checkingDocling"
              >
                <div class="flex items-center">
                  <ArrowPathIcon class="h-4 w-4 mr-1" />
                  <span>检查Docling</span>
                </div>
              </el-button>
              <el-button
                size="small"
                @click="$emit('check-ai')"
                :loading="checkingAI"
                type="primary"
              >
                <div class="flex items-center">
                  <CpuChipIcon class="h-4 w-4 mr-1" />
                  <span>检查AI服务</span>
                </div>
              </el-button>
            </div>

            <!-- AI服务详细信息 -->
            <div class="mt-2 text-xs text-gray-600 text-center leading-relaxed">
              <template v-if="modelInfo">
                <div v-if="modelInfo.displayName">
                  <div class="flex items-center">
                    <RocketLaunchIcon class="h-4 w-4 mr-1" />
                    <span>{{ modelInfo.displayName }} @
                  {{ getHostFromUrl(modelInfo.serverUrl) }}</span>
                  </div>
                </div>
                <div v-else-if="modelInfo.id">
                  <div class="flex items-center">
                    <RocketLaunchIcon class="h-4 w-4 mr-1" />
                    <span>{{ modelInfo.id }} @ {{ getHostFromUrl(modelInfo.serverUrl) }}</span>
                  </div>
                </div>
                <div v-else>
                  <div class="flex items-center">
                    <RocketLaunchIcon class="h-4 w-4 mr-1" />
                    <span>模型已连接 @ {{ getHostFromUrl(modelInfo.serverUrl) }}</span>
                  </div>
                </div>
              </template>
              <div v-else class="flex items-center">
                <RocketLaunchIcon class="h-4 w-4 mr-1" />
                <span>正在获取模型信息...</span>
              </div>
              <div class="flex items-center">
                <WrenchScrewdriverIcon class="h-4 w-4 mr-1" />
                <span>Docling @ localhost:8088</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import { Upload } from '@element-plus/icons-vue';
import {
  RocketLaunchIcon,
  ClipboardDocumentIcon,
  ArrowPathIcon,
  CpuChipIcon,
  WrenchScrewdriverIcon,
  FolderIcon,
  ChartBarIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  DocumentIcon,
  ClockIcon,
  CheckCircleIcon,
} from '@heroicons/vue/24/outline';
// import ServiceStatusPanel from './ServiceStatusPanel.vue';

// Props
interface Props {
  uploadUrl: string;
  uploadData: Record<string, any>;
  uploadHeaders: Record<string, string>;
  fileList: any[];
  acceptedFileTypes: string;
  outputFormat: string;
  doclingAvailable: boolean;
  aiServiceAvailable: boolean;
  checkingDocling: boolean;
  checkingAI: boolean;
  modelInfo: any;
}

defineProps<Props>();

// Emits
const emit = defineEmits<{
  'before-upload': [file: File];
  'upload-success': [response: any, file: any, fileList: any[]];
  'upload-error': [error: any, file: any, fileList: any[]];
  'upload-progress': [event: any, file: any, fileList: any[]];
  'format-change': [format: string];
  'check-docling': [];
  'check-ai': [];
}>();

// Methods
const beforeUpload = (file: File) => {
  emit('before-upload', file);
};

const onUploadSuccess = (response: any, file: any, fileList: any[]) => {
  emit('upload-success', response, file, fileList);
};

const onUploadError = (error: any, file: any, fileList: any[]) => {
  emit('upload-error', error, file, fileList);
};

const onUploadProgress = (event: any, file: any, fileList: any[]) => {
  emit('upload-progress', event, file, fileList);
};

// Utility function
const getHostFromUrl = (url: string) => {
  if (!url) return 'Unknown';
  try {
    return new URL(url).host;
  } catch {
    return url;
  }
};
</script>
