<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="AI数据库设计助手"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    top="5vh"
  >
    <div class="ai-chat-container">
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="chatMessagesRef">
        <div
          v-for="(message, index) in chatMessages"
          :key="index"
          :class="['message', message.role]"
        >
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">
                <div class="flex items-center">
                  <UserIcon v-if="message.role === 'user'" class="h-4 w-4 mr-1" />
                  <CpuChipIcon v-else class="h-4 w-4 mr-1" />
                  <span>{{ message.role === 'user' ? '您' : 'AI助手' }}</span>
                </div>
              </span>
              <span class="message-time">
                {{ new Date(message.timestamp).toLocaleTimeString() }}
              </span>
            </div>
            <div
              class="message-text"
              v-html="formatMessageContent(message.content)"
            ></div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="chatLoading" class="message assistant loading">
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">
                <div class="flex items-center">
                  <CpuChipIcon class="h-4 w-4 mr-1" />
                  <span>AI助手</span>
                </div>
              </span>
              <span class="message-time">思考中...</span>
            </div>
            <div class="message-text">
              <el-icon class="is-loading"><Loading /></el-icon>
              正在分析您的问题...
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area">
        <el-input
          :model-value="chatInput"
          type="textarea"
          :rows="3"
          placeholder="请输入您关于数据库设计的问题..."
          @keydown.ctrl.enter="sendChatMessage"
          @input="chatInput = $event"
          :disabled="chatLoading"
        />
        <div class="chat-input-actions">
          <span class="input-tip">Ctrl + Enter 发送</span>
          <div>
            <el-button size="small" @click="clearChat" :disabled="chatLoading">
              清空对话
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="sendChatMessage"
              :loading="chatLoading"
              :disabled="!chatInput.trim()"
            >
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="chat-footer">
        <span class="chat-info">
          <div class="flex items-center">
            <LightBulbIcon class="h-4 w-4 mr-1" />
            <span>提示：您可以询问关于字段类型、表结构设计、SQL优化等问题</span>
          </div>
        </span>
        <el-button @click="closeDialog">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import {
  CpuChipIcon,
  UserIcon,
  LightBulbIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  chatMessages: {
    type: Array,
    default: () => [],
  },
  chatLoading: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(['update:visible', 'send-message', 'clear-chat']);

// Local state
const chatInput = ref('');
const chatMessagesRef = ref();

// Methods
const closeDialog = () => {
  emit('update:visible', false);
};

const sendChatMessage = () => {
  if (!chatInput.value.trim() || props.chatLoading) return;

  emit('send-message', chatInput.value.trim());
  chatInput.value = '';
};

const clearChat = () => {
  emit('clear-chat');
};

const formatMessageContent = content => {
  // 简单的内容格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>');
};

// Watch for messages change to auto-scroll
watch(
  () => props.chatMessages,
  () => {
    nextTick(() => {
      if (chatMessagesRef.value) {
        chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
      }
    });
  },
  { deep: true }
);

// Watch for loading change to auto-scroll
watch(
  () => props.chatLoading,
  () => {
    nextTick(() => {
      if (chatMessagesRef.value) {
        chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
      }
    });
  }
);
</script>

<style scoped>
/* AI对话窗口样式 */
.ai-chat-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 16px;
}

.message {
  margin-bottom: 16px;
}

.message-content {
  max-width: 80%;
}

.message.user .message-content {
  margin-left: auto;
  background: #409eff;
  color: white;
  border-radius: 18px 18px 4px 18px;
  padding: 12px 16px;
}

.message.assistant .message-content {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 18px 18px 18px 4px;
  padding: 12px 16px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.message.user .message-header {
  color: rgba(255, 255, 255, 0.8);
}

.message.assistant .message-header {
  color: #909399;
}

.message-role {
  font-weight: 600;
}

.message-time {
  opacity: 0.7;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.message.user .message-text {
  color: white;
}

.message.assistant .message-text {
  color: #303133;
}

.message.assistant.loading .message-text {
  color: #909399;
  font-style: italic;
}

.chat-input-area {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.chat-input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.input-tip {
  font-size: 12px;
  color: #909399;
}

.chat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-info {
  font-size: 13px;
  color: #606266;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

/* 代码样式 */
.message-text code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 0.9em;
}

.message.user .message-text code {
  background: rgba(255, 255, 255, 0.2);
}
</style>
