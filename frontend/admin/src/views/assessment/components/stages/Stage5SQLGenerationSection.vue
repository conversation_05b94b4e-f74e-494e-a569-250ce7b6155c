<template>
  <el-card class="stage-card stage5-sql-generation" :class="{ disabled: !enabled }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ completed: isCompleted, active: enabled }">
          <el-icon v-if="isCompleted"><Check /></el-icon>
          <span v-else>5</span>
        </div>
        <div class="stage-title">
          <h3>SQL生成执行</h3>
          <span class="stage-description">生成、验证并执行SQL建表语句</span>
        </div>
        <div class="stage-actions">
          <el-tag v-if="isCompleted" type="success">执行完成</el-tag>
          <el-tag v-else-if="!enabled" type="info">等待设计</el-tag>
          <el-tag v-else-if="sqlExecuting" type="warning">
            <i class="el-icon-loading mr-1"></i>执行中
          </el-tag>
          <el-tag v-else type="primary">待生成</el-tag>
        </div>
      </div>
    </template>

    <div class="sql-content" :class="{ disabled: !enabled }">
      <!-- SQL生成控制区域 -->
      <div v-if="enabled && databaseStructure" class="sql-control-section">
        <!-- 生成选项配置 -->
        <div class="generation-options">
          <div class="section-header">
            <div class="flex items-center">
              <CodeBracketIcon class="h-5 w-5 mr-2 text-blue-600" />
              <span class="font-semibold text-gray-700">SQL生成选项</span>
            </div>
            <div class="generation-actions">
              <el-button size="small" @click="generateSQL" type="primary" :loading="generating">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                {{ generating ? '生成中...' : '生成SQL' }}
              </el-button>
              <el-button size="small" @click="loadTemplate" type="info">
                <DocumentDuplicateIcon class="h-4 w-4 mr-1" />
                加载模板
              </el-button>
            </div>
          </div>

          <el-row :gutter="16" class="options-form">
            <el-col :span="8">
              <el-form-item label="数据库类型">
                <el-select v-model="sqlOptions.dbType" @change="onOptionsChange">
                  <el-option label="MySQL 8.0" value="mysql8" />
                  <el-option label="MySQL 5.7" value="mysql57" />
                  <el-option label="PostgreSQL" value="postgresql" />
                  <el-option label="Oracle" value="oracle" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="字符集">
                <el-select v-model="sqlOptions.charset" @change="onOptionsChange">
                  <el-option label="utf8mb4" value="utf8mb4" />
                  <el-option label="utf8" value="utf8" />
                  <el-option label="latin1" value="latin1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="存储引擎">
                <el-select v-model="sqlOptions.engine" @change="onOptionsChange">
                  <el-option label="InnoDB" value="InnoDB" />
                  <el-option label="MyISAM" value="MyISAM" />
                  <el-option label="Memory" value="Memory" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="additional-options">
            <el-checkbox v-model="sqlOptions.includeComments" @change="onOptionsChange">
              包含字段注释
            </el-checkbox>
            <el-checkbox v-model="sqlOptions.includeIndexes" @change="onOptionsChange">
              包含索引定义
            </el-checkbox>
            <el-checkbox v-model="sqlOptions.dropIfExists" @change="onOptionsChange">
              添加DROP IF EXISTS
            </el-checkbox>
            <el-checkbox v-model="sqlOptions.addTimestamps" @change="onOptionsChange">
              自动添加时间戳字段
            </el-checkbox>
          </div>
        </div>

        <!-- SQL代码编辑器 -->
        <div class="sql-editor-section">
          <div class="section-header">
            <div class="flex items-center">
              <DocumentTextIcon class="h-5 w-5 mr-2 text-green-600" />
              <span class="font-semibold text-gray-700">SQL语句</span>
              <el-tag v-if="generatedSQL" type="success" size="small" class="ml-2">
                {{ getSQLStats().lines }} 行, {{ getSQLStats().chars }} 字符
              </el-tag>
            </div>
            <div class="editor-actions">
              <el-button size="small" @click="formatSQL" :disabled="!generatedSQL">
                <DocumentCheckIcon class="h-4 w-4 mr-1" />
                格式化
              </el-button>
              <el-button size="small" @click="validateSQL" :disabled="!generatedSQL">
                <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
                语法检查
              </el-button>
              <el-button size="small" @click="copySQL" :disabled="!generatedSQL">
                <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
                复制
              </el-button>
            </div>
          </div>

          <el-input
            v-model="generatedSQL"
            type="textarea"
            :rows="15"
            placeholder="生成的SQL语句将显示在这里..."
            class="sql-textarea"
            :readonly="!sqlEditable"
          />

          <div class="sql-controls">
            <el-checkbox v-model="sqlEditable">
              允许编辑SQL
            </el-checkbox>
            <div class="sql-info">
              <span class="text-xs text-gray-500">
                支持MySQL、PostgreSQL等主流数据库语法
              </span>
            </div>
          </div>
        </div>

        <!-- SQL预览和验证 -->
        <div v-if="generatedSQL" class="sql-preview-section">
          <div class="section-header">
            <div class="flex items-center">
              <EyeIcon class="h-5 w-5 mr-2 text-purple-600" />
              <span class="font-semibold text-gray-700">SQL预览和分析</span>
            </div>
            <div class="preview-actions">
              <el-button size="small" @click="analyzeSQL" type="info">
                <ChartBarIcon class="h-4 w-4 mr-1" />
                分析语句
              </el-button>
            </div>
          </div>

          <div class="sql-analysis">
            <div class="analysis-grid">
              <div class="analysis-item">
                <div class="analysis-label">表数量</div>
                <div class="analysis-value">{{ sqlAnalysis.tableCount }}</div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">字段数量</div>
                <div class="analysis-value">{{ sqlAnalysis.fieldCount }}</div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">索引数量</div>
                <div class="analysis-value">{{ sqlAnalysis.indexCount }}</div>
              </div>
              <div class="analysis-item">
                <div class="analysis-label">语法检查</div>
                <div class="analysis-value" :class="sqlAnalysis.syntaxValid ? 'text-green-600' : 'text-red-600'">
                  {{ sqlAnalysis.syntaxValid ? '通过' : '有误' }}
                </div>
              </div>
            </div>

            <div v-if="sqlAnalysis.warnings.length > 0" class="sql-warnings">
              <el-alert
                title="SQL建议"
                type="warning"
                :closable="false"
                show-icon
              >
                <ul class="warnings-list">
                  <li v-for="warning in sqlAnalysis.warnings" :key="warning">
                    {{ warning }}
                  </li>
                </ul>
              </el-alert>
            </div>
          </div>
        </div>

        <!-- SQL执行区域 -->
        <div class="sql-execution-section">
          <div class="section-header">
            <div class="flex items-center">
              <BoltIcon class="h-5 w-5 mr-2 text-orange-600" />
              <span class="font-semibold text-gray-700">SQL执行</span>
            </div>
            <div class="execution-actions">
              <el-button 
                size="small" 
                @click="executeSQL" 
                type="primary"
                :disabled="!canExecuteSQL"
                :loading="sqlExecuting"
              >
                <BoltIcon class="h-4 w-4 mr-1" />
                {{ sqlExecuting ? '执行中...' : '执行建表' }}
              </el-button>
              <el-button size="small" @click="previewExecution" :disabled="!generatedSQL">
                <EyeIcon class="h-4 w-4 mr-1" />
                预览执行
              </el-button>
            </div>
          </div>

          <div class="execution-info">
            <el-alert
              title="执行说明"
              type="info"
              :closable="false"
              show-icon
            >
              <p class="execution-description">
                执行SQL将在数据库中创建表结构。请确保：
              </p>
              <ul class="execution-checklist">
                <li>数据库连接正常</li>
                <li>拥有CREATE TABLE权限</li>
                <li>表名不与现有表冲突</li>
                <li>SQL语法检查通过</li>
              </ul>
            </el-alert>
          </div>

          <!-- 执行结果 -->
          <div v-if="executionResult" class="execution-result">
            <el-card :class="executionResult.success ? 'success-card' : 'error-card'">
              <template #header>
                <div class="flex items-center">
                  <CheckCircleIcon v-if="executionResult.success" class="h-5 w-5 mr-2 text-green-600" />
                  <XCircleIcon v-else class="h-5 w-5 mr-2 text-red-600" />
                  <span class="font-semibold">
                    {{ executionResult.success ? '执行成功' : '执行失败' }}
                  </span>
                </div>
              </template>
              
              <div class="result-content">
                <pre class="result-text">{{ executionResult.message }}</pre>
                <div v-if="executionResult.details" class="result-details">
                  <p><strong>执行时间:</strong> {{ executionResult.duration }}ms</p>
                  <p><strong>影响行数:</strong> {{ executionResult.affectedRows }}</p>
                  <p v-if="executionResult.tableName"><strong>创建表名:</strong> {{ executionResult.tableName }}</p>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 完成确认 -->
        <div class="completion-section">
          <el-button
            type="success"
            size="large"
            @click="completeWorkflow"
            :disabled="!canComplete"
            class="complete-button"
          >
            <CheckCircleIcon class="h-5 w-5 mr-2" />
            完成量表数字化转换
          </el-button>
        </div>
      </div>

      <!-- 禁用状态 -->
      <div v-else-if="!enabled" class="disabled-state">
        <el-empty
          :image-size="120"
          description="请先完成数据库结构设计"
        >
          <template #image>
            <CodeBracketIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>

      <!-- 等待数据库结构 -->
      <div v-else class="waiting-state">
        <el-empty
          :image-size="120"
          description="等待数据库结构设计完成"
        >
          <template #image>
            <CircleStackIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Check } from '@element-plus/icons-vue';
import {
  CodeBracketIcon,
  ArrowPathIcon,
  DocumentDuplicateIcon,
  DocumentTextIcon,
  DocumentCheckIcon,
  ExclamationTriangleIcon,
  ClipboardDocumentIcon,
  EyeIcon,
  ChartBarIcon,
  BoltIcon,
  CheckCircleIcon,
  XCircleIcon,
  CircleStackIcon,
} from '@heroicons/vue/24/outline';

// Types
interface DatabaseStructure {
  tableName: string;
  tableComment: string;
  fields: Array<{
    name: string;
    type: string;
    length?: string;
    nullable: boolean;
    primaryKey: boolean;
    defaultValue?: string;
    comment: string;
  }>;
  indexes?: Array<{
    name: string;
    fields: string[];
    type: 'INDEX' | 'UNIQUE';
  }>;
}

interface SQLOptions {
  dbType: string;
  charset: string;
  engine: string;
  includeComments: boolean;
  includeIndexes: boolean;
  dropIfExists: boolean;
  addTimestamps: boolean;
}

interface SQLAnalysis {
  tableCount: number;
  fieldCount: number;
  indexCount: number;
  syntaxValid: boolean;
  warnings: string[];
}

interface ExecutionResult {
  success: boolean;
  message: string;
  duration?: number;
  affectedRows?: number;
  tableName?: string;
  details?: any;
}

// Props
interface Props {
  enabled?: boolean;
  databaseStructure?: DatabaseStructure | null;
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
  databaseStructure: null,
});

// Emits
const emit = defineEmits<{
  'stage-complete': [result: { sql: string; execution: ExecutionResult }];
}>();

// Refs
const isCompleted = ref(false);
const generating = ref(false);
const sqlExecuting = ref(false);
const sqlEditable = ref(false);
const generatedSQL = ref('');
const executionResult = ref<ExecutionResult | null>(null);

// SQL options
const sqlOptions = reactive<SQLOptions>({
  dbType: 'mysql8',
  charset: 'utf8mb4',
  engine: 'InnoDB',
  includeComments: true,
  includeIndexes: true,
  dropIfExists: true,
  addTimestamps: false,
});

// SQL analysis
const sqlAnalysis = reactive<SQLAnalysis>({
  tableCount: 0,
  fieldCount: 0,
  indexCount: 0,
  syntaxValid: false,
  warnings: [],
});

// Computed
const canExecuteSQL = computed(() => {
  return generatedSQL.value && sqlAnalysis.syntaxValid && !sqlExecuting.value;
});

const canComplete = computed(() => {
  return executionResult.value?.success && isCompleted.value;
});

// Watch for database structure changes
watch(() => props.databaseStructure, (newStructure) => {
  if (newStructure) {
    // 自动生成SQL
    setTimeout(() => {
      generateSQL();
    }, 500);
  }
}, { immediate: true });

// Methods
const generateSQL = async () => {
  if (!props.databaseStructure) {
    ElMessage.warning('没有可用的数据库结构');
    return;
  }

  generating.value = true;

  try {
    // 模拟SQL生成过程
    await new Promise(resolve => setTimeout(resolve, 1500));

    const { tableName, tableComment, fields, indexes } = props.databaseStructure;
    
    let sql = '';
    
    // DROP IF EXISTS
    if (sqlOptions.dropIfExists) {
      sql += `DROP TABLE IF EXISTS \`${tableName}\`;\n\n`;
    }
    
    // CREATE TABLE
    sql += `CREATE TABLE \`${tableName}\` (\n`;
    
    // 字段定义
    const fieldDefinitions = fields.map(field => {
      let def = `  \`${field.name}\` ${field.type}`;
      
      // 长度
      if (field.length && ['VARCHAR', 'CHAR', 'DECIMAL'].includes(field.type)) {
        def += `(${field.length})`;
      }
      
      // NOT NULL
      if (!field.nullable) {
        def += ' NOT NULL';
      }
      
      // DEFAULT
      if (field.defaultValue) {
        def += ` DEFAULT ${field.defaultValue}`;
      }
      
      // AUTO_INCREMENT for primary key
      if (field.primaryKey && field.type.includes('INT')) {
        def += ' AUTO_INCREMENT';
      }
      
      // COMMENT
      if (sqlOptions.includeComments && field.comment) {
        def += ` COMMENT '${field.comment}'`;
      }
      
      return def;
    });
    
    sql += fieldDefinitions.join(',\n');
    
    // 主键
    const primaryKeys = fields.filter(f => f.primaryKey).map(f => f.name);
    if (primaryKeys.length > 0) {
      sql += `,\n  PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`;
    }
    
    // 索引
    if (sqlOptions.includeIndexes && indexes && indexes.length > 0) {
      indexes.forEach(index => {
        const indexType = index.type === 'UNIQUE' ? 'UNIQUE KEY' : 'KEY';
        sql += `,\n  ${indexType} \`${index.name}\` (\`${index.fields.join('`, `')}\`)`;
      });
    }
    
    sql += '\n)';
    
    // 表选项
    if (sqlOptions.dbType.startsWith('mysql')) {
      sql += ` ENGINE=${sqlOptions.engine} DEFAULT CHARSET=${sqlOptions.charset}`;
      if (sqlOptions.includeComments && tableComment) {
        sql += ` COMMENT='${tableComment}'`;
      }
    }
    
    sql += ';';
    
    generatedSQL.value = sql;
    
    // 自动分析SQL
    analyzeSQL();
    
    ElMessage.success('SQL生成成功');
    
  } catch (error) {
    ElMessage.error('SQL生成失败');
  } finally {
    generating.value = false;
  }
};

const loadTemplate = () => {
  const templates = [
    {
      name: '基础评估表模板',
      sql: `CREATE TABLE \`assessment_basic\` (
  \`id\` bigint(20) NOT NULL AUTO_INCREMENT,
  \`elderly_id\` bigint(20) NOT NULL,
  \`assessor_id\` bigint(20) NOT NULL,
  \`assessment_date\` datetime NOT NULL,
  \`total_score\` decimal(10,2) DEFAULT NULL,
  \`status\` varchar(20) NOT NULL DEFAULT 'DRAFT',
  \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (\`id\`),
  KEY \`idx_elderly_id\` (\`elderly_id\`),
  KEY \`idx_assessor_id\` (\`assessor_id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;`
    }
  ];
  
  ElMessageBox.confirm(
    '选择模板将覆盖当前的SQL内容，是否继续？',
    '加载模板',
    {
      confirmButtonText: '确认加载',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    generatedSQL.value = templates[0].sql;
    analyzeSQL();
    ElMessage.success('模板加载成功');
  }).catch(() => {});
};

const formatSQL = () => {
  if (!generatedSQL.value) return;
  
  // 简单的SQL格式化
  let formatted = generatedSQL.value
    .replace(/,\s*/g, ',\n  ')
    .replace(/\(\s*/g, '(\n  ')
    .replace(/\s*\)/g, '\n)')
    .replace(/;\s*/g, ';\n');
  
  generatedSQL.value = formatted;
  ElMessage.success('SQL格式化完成');
};

const validateSQL = () => {
  if (!generatedSQL.value) return;
  
  const sql = generatedSQL.value.trim();
  const errors: string[] = [];
  
  // 基本语法检查
  if (!sql.toUpperCase().includes('CREATE TABLE')) {
    errors.push('缺少CREATE TABLE语句');
  }
  
  if (!sql.includes('PRIMARY KEY')) {
    errors.push('建议添加主键');
  }
  
  if (sql.includes('``')) {
    errors.push('存在空的字段名或表名');
  }
  
  sqlAnalysis.syntaxValid = errors.length === 0;
  
  if (errors.length > 0) {
    ElMessage.error(`语法检查失败: ${errors.join(', ')}`);
  } else {
    ElMessage.success('语法检查通过');
  }
};

const copySQL = async () => {
  if (!generatedSQL.value) return;
  
  try {
    await navigator.clipboard.writeText(generatedSQL.value);
    ElMessage.success('SQL已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败，请手动复制');
  }
};

const analyzeSQL = () => {
  if (!generatedSQL.value) return;
  
  const sql = generatedSQL.value.toLowerCase();
  
  // 分析SQL
  sqlAnalysis.tableCount = (sql.match(/create table/g) || []).length;
  sqlAnalysis.fieldCount = (sql.match(/`\w+`\s+\w+/g) || []).length;
  sqlAnalysis.indexCount = (sql.match(/(key|index)/g) || []).length;
  
  // 简单的语法验证
  sqlAnalysis.syntaxValid = sql.includes('create table') && sql.includes('primary key');
  
  // 生成建议
  sqlAnalysis.warnings = [];
  
  if (!sql.includes('not null')) {
    sqlAnalysis.warnings.push('建议为重要字段添加NOT NULL约束');
  }
  
  if (!sql.includes('comment')) {
    sqlAnalysis.warnings.push('建议为字段添加注释说明');
  }
  
  if (sqlAnalysis.indexCount === 0) {
    sqlAnalysis.warnings.push('建议为常用查询字段添加索引');
  }
};

const previewExecution = () => {
  ElMessageBox.alert(
    `即将执行以下SQL语句:\n\n${generatedSQL.value}`,
    'SQL执行预览',
    {
      confirmButtonText: '关闭',
      type: 'info',
    }
  );
};

const executeSQL = async () => {
  if (!canExecuteSQL.value) {
    ElMessage.warning('请先完成SQL生成和验证');
    return;
  }
  
  sqlExecuting.value = true;
  
  try {
    ElMessage.info('开始执行SQL...');
    
    // 模拟SQL执行
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟执行结果
    const mockResult: ExecutionResult = {
      success: true,
      message: '表创建成功',
      duration: 156,
      affectedRows: 1,
      tableName: props.databaseStructure?.tableName,
      details: {
        database: 'assessment_db',
        engine: sqlOptions.engine,
        charset: sqlOptions.charset,
      }
    };
    
    executionResult.value = mockResult;
    isCompleted.value = true;
    
    ElMessage.success('SQL执行成功');
    
  } catch (error) {
    const errorResult: ExecutionResult = {
      success: false,
      message: 'SQL执行失败: ' + (error as Error).message,
    };
    
    executionResult.value = errorResult;
    ElMessage.error('SQL执行失败');
    
  } finally {
    sqlExecuting.value = false;
  }
};

const completeWorkflow = () => {
  if (!canComplete.value) {
    ElMessage.warning('请先完成SQL执行');
    return;
  }
  
  ElMessageBox.confirm(
    '确认完成整个量表数字化转换流程？\n\n这将保存所有配置并可以开始使用该量表进行评估。',
    '完成转换',
    {
      confirmButtonText: '确认完成',
      cancelButtonText: '取消',
      type: 'success',
    }
  ).then(() => {
    emit('stage-complete', {
      sql: generatedSQL.value,
      execution: executionResult.value!,
    });
    
    ElMessage.success('量表数字化转换完成！');
  }).catch(() => {});
};

const onOptionsChange = () => {
  // 选项变更时重新生成SQL
  if (generatedSQL.value) {
    generateSQL();
  }
};

// Utility methods
const getSQLStats = () => {
  const lines = generatedSQL.value.split('\n').length;
  const chars = generatedSQL.value.length;
  return { lines, chars };
};
</script>

<style scoped>
.stage-card {
  height: 100%;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stage-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stage-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
}

.stage-number.active {
  background: #3b82f6;
  color: white;
}

.stage-number.completed {
  background: #10b981;
  color: white;
}

.stage-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stage-description {
  font-size: 14px;
  color: #6b7280;
}

.sql-content {
  padding: 8px 0;
  max-height: 1000px;
  overflow-y: auto;
}

.sql-content.disabled {
  opacity: 0.5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.generation-options,
.sql-editor-section,
.sql-preview-section,
.sql-execution-section {
  margin-bottom: 24px;
}

.options-form {
  margin-bottom: 16px;
}

.additional-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.sql-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  margin-bottom: 8px;
}

.sql-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sql-analysis {
  background: #f8fafc;
  border-radius: 6px;
  padding: 16px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.analysis-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.analysis-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.analysis-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.warnings-list {
  margin: 0;
  padding-left: 20px;
}

.execution-info {
  margin-bottom: 16px;
}

.execution-description {
  margin: 0 0 8px 0;
}

.execution-checklist {
  margin: 0;
  padding-left: 20px;
}

.execution-result {
  margin-top: 16px;
}

.success-card {
  border-color: #10b981;
  background: #f0fdf4;
}

.error-card {
  border-color: #ef4444;
  background: #fef2f2;
}

.result-content {
  max-height: 200px;
  overflow-y: auto;
}

.result-text {
  color: #333;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  margin: 0 0 12px 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.result-details p {
  margin: 4px 0;
  font-size: 14px;
}

.completion-section {
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #e5e7eb;
}

.complete-button {
  width: 100%;
  max-width: 350px;
}

.disabled-state,
.waiting-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.generation-actions,
.editor-actions,
.preview-actions,
.execution-actions {
  display: flex;
  gap: 8px;
}
</style>