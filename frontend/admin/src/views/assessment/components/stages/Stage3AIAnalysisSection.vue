<template>
  <el-card class="stage-card stage3-ai-analysis" :class="{ disabled: !enabled }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ completed: isCompleted, active: enabled }">
          <el-icon v-if="isCompleted"><Check /></el-icon>
          <span v-else>3</span>
        </div>
        <div class="stage-title">
          <h3>AI智能分析</h3>
          <span class="stage-description">AI分析生成数据库结构建议</span>
        </div>
        <div class="stage-actions">
          <el-tag v-if="isCompleted" type="success">分析完成</el-tag>
          <el-tag v-else-if="!enabled" type="info">等待属性</el-tag>
          <el-tag v-else-if="aiAnalyzing" type="warning">
            <i class="el-icon-loading mr-1"></i>分析中
          </el-tag>
          <el-tag v-else type="primary">待分析</el-tag>
        </div>
      </div>
    </template>

    <div class="analysis-content" :class="{ disabled: !enabled }">
      <!-- AI分析操作区域 -->
      <div v-if="enabled" class="ai-control-section">
        <!-- 提示词编辑 -->
        <div class="prompt-section">
          <div class="prompt-header">
            <div class="flex items-center">
              <CpuChipIcon class="h-5 w-5 mr-2 text-blue-600" />
              <span class="font-semibold text-gray-700">AI分析提示词</span>
            </div>
            <div class="prompt-controls">
              <el-button-group>
                <el-button size="small" @click="loadDefaultPrompt">
                  <ArrowPathIcon class="h-4 w-4 mr-1" />
                  默认提示词
                </el-button>
                <el-button size="small" type="primary" @click="previewPrompt">
                  <EyeIcon class="h-4 w-4 mr-1" />
                  预览
                </el-button>
              </el-button-group>
            </div>
          </div>
          
          <el-input
            v-model="customPrompt"
            type="textarea"
            :rows="6"
            placeholder="编辑AI分析提示词..."
            class="prompt-textarea"
            resize="vertical"
          >
            <template #prepend>
              <div class="flex items-center px-2">
                <CpuChipIcon class="h-4 w-4 mr-1" />
                <span>提示词</span>
              </div>
            </template>
          </el-input>
          
          <div class="prompt-stats">
            <div class="stat-item">
              <ChartBarIcon class="h-3 w-3 mr-1" />
              <span>{{ customPrompt.length }} 字符</span>
            </div>
            <div class="stat-item">
              <DocumentTextIcon class="h-3 w-3 mr-1" />
              <span>{{ Math.ceil(customPrompt.length / 3) }} Token</span>
            </div>
            <div class="stat-item">
              <ClockIcon class="h-3 w-3 mr-1" />
              <span>{{ Math.max(2, Math.ceil(customPrompt.length / 8000)) }} 分钟</span>
            </div>
          </div>
        </div>

        <!-- AI服务信息 -->
        <div v-if="modelInfo" class="model-info">
          <div class="info-header">
            <RocketLaunchIcon class="h-4 w-4 mr-1" />
            <span class="text-sm font-medium">AI服务状态</span>
          </div>
          <div class="info-content">
            <span v-if="modelInfo.displayName">
              {{ modelInfo.displayName }} ({{ getHostFromUrl(modelInfo.url) }})
            </span>
            <span v-else-if="modelInfo.id">
              {{ modelInfo.id }} ({{ getHostFromUrl(modelInfo.url) }})
            </span>
            <span v-else>
              LM Studio ({{ getHostFromUrl(modelInfo.url) }})
            </span>
          </div>
        </div>

        <!-- 分析按钮 -->
        <div class="analysis-actions">
          <el-button
            type="primary"
            size="large"
            :disabled="!canStartAnalysis"
            :loading="aiAnalyzing"
            @click="startAIAnalysis"
            class="analysis-button"
          >
            <div class="flex items-center">
              <CogIcon v-if="aiAnalyzing" class="h-5 w-5 mr-2 animate-spin" />
              <CpuChipIcon v-else class="h-5 w-5 mr-2" />
              <span>{{ aiAnalyzing ? 'AI分析中...' : '开始AI智能分析' }}</span>
            </div>
          </el-button>
          
          <el-button
            type="info"
            size="large"
            :disabled="!scaleProperties"
            @click="openAIChat"
            class="chat-button"
          >
            <ChatBubbleLeftRightIcon class="h-5 w-5 mr-2" />
            AI对话助手
          </el-button>
        </div>
      </div>

      <!-- 禁用状态 -->
      <div v-else class="disabled-state">
        <el-empty
          :image-size="120"
          description="请先完成量表属性配置"
        >
          <template #image>
            <CpuChipIcon class="h-24 w-24 text-gray-400" />
          </template>
        </el-empty>
      </div>

      <!-- AI分析输出 -->
      <div v-if="aiAnalyzing || streamOutput || contentBlocks.length > 0" class="analysis-output">
        <el-card class="stream-card" shadow="hover">
          <template #header>
            <div class="stream-header">
              <div class="flex items-center">
                <CpuChipIcon class="h-4 w-4 mr-2" />
                <span class="font-medium">AI实时分析过程</span>
              </div>
              <el-tag
                v-if="aiAnalyzing"
                type="warning"
                effect="plain"
                size="small"
              >
                <i class="el-icon-loading mr-1"></i>分析中
              </el-tag>
              <el-tag v-else type="success" effect="plain" size="small">
                分析完成
              </el-tag>
            </div>
          </template>

          <!-- 流式输出 -->
          <div v-if="streamOutput" class="stream-output">
            <pre class="stream-text">{{ streamOutput }}</pre>
          </div>

          <!-- AI生成内容块 -->
          <div v-if="contentBlocks.length > 0" class="content-blocks">
            <el-divider content-position="left">
              <div class="flex items-center">
                <CpuChipIcon class="h-4 w-4 mr-1 text-orange-500" />
                <span class="text-orange-500 font-bold">AI分析结果</span>
              </div>
            </el-divider>
            
            <div class="blocks-container">
              <ContentBlock
                v-for="block in contentBlocks"
                :key="block.id"
                :block="block"
              />
            </div>
            
            <div class="content-actions">
              <el-button size="small" @click="copyAllContent">
                <ClipboardDocumentIcon class="h-4 w-4 mr-1" />
                复制全部
              </el-button>
              <el-button
                size="small"
                @click="copyCodeBlocks"
                :disabled="!hasCodeBlocks"
              >
                <DocumentTextIcon class="h-4 w-4 mr-1" />
                复制代码
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="parseAIResult"
              >
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                解析结果
              </el-button>
            </div>
          </div>

          <!-- 流式输出操作 -->
          <div class="stream-actions">
            <el-button
              size="small"
              type="info"
              @click="clearStreamOutput"
              :disabled="aiAnalyzing"
            >
              清空输出
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="copyStreamOutput"
              :disabled="!streamOutput?.trim()"
            >
              复制输出
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 分析结果预览 -->
      <div v-if="analysisResult" class="result-preview">
        <el-card class="result-card">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <CheckCircleIcon class="h-5 w-5 mr-2 text-green-600" />
                <span class="font-semibold">分析结果摘要</span>
              </div>
              <el-button
                size="small"
                type="primary"
                @click="confirmAnalysis"
                :disabled="!analysisResult"
              >
                确认并进入设计
              </el-button>
            </div>
          </template>
          
          <div class="result-content">
            <pre class="result-text">{{ analysisResult }}</pre>
          </div>
        </el-card>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Check } from '@element-plus/icons-vue';
import ContentBlock from '../../../../components/ContentBlock.vue';
import {
  CpuChipIcon,
  CogIcon,
  ArrowPathIcon,
  EyeIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ClockIcon,
  RocketLaunchIcon,
  ChatBubbleLeftRightIcon,
  ClipboardDocumentIcon,
  CheckCircleIcon,
} from '@heroicons/vue/24/outline';

// Types
interface ScaleProperties {
  name: string;
  code: string;
  type: string;
  version: string;
  estimatedDuration: number;
  description: string;
  assessmentModes: string[];
  complianceStandard: string;
  isActive: boolean;
}

interface ContentBlock {
  id: string;
  type: string;
  content: string;
}

interface AnalysisResult {
  structure: any;
  recommendations: string[];
  confidence: number;
  timestamp: Date;
}

// Props
interface Props {
  enabled?: boolean;
  scaleProperties?: ScaleProperties | null;
  markdownContent?: string;
  modelInfo?: any;
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
  scaleProperties: null,
  markdownContent: '',
  modelInfo: null,
});

// Emits
const emit = defineEmits<{
  'stage-complete': [result: AnalysisResult];
  'start-ai-analysis': [];
  'open-ai-chat': [];
  'copy-all-content': [];
  'copy-code-blocks': [];
  'copy-stream-output': [];
  'clear-stream-output': [];
  'parse-ai-result': [];
}>();

// Refs
const isCompleted = ref(false);
const aiAnalyzing = ref(false);
const streamOutput = ref('');
const contentBlocks = ref<ContentBlock[]>([]);
const analysisResult = ref<AnalysisResult | null>(null);
const customPrompt = ref(`请分析以下评估量表的结构，并为其设计合适的数据库表结构：

1. 识别量表中的所有评估项目和选项
2. 推断每个字段的数据类型和约束
3. 设计标准化的表名和字段名
4. 生成完整的数据库结构建议

量表信息：
- 名称：{{ scaleName }}
- 类型：{{ scaleType }}
- 描述：{{ scaleDescription }}

量表内容：
{{ markdownContent }}

请以JSON格式输出数据库结构设计建议。`);

// Computed
const canStartAnalysis = computed(() => {
  return props.enabled && props.scaleProperties && props.markdownContent && !aiAnalyzing.value;
});

const hasCodeBlocks = computed(() => {
  return contentBlocks.value.some(block => block.type === 'code');
});

// Watch for props changes
watch(() => props.scaleProperties, (newProps) => {
  if (newProps) {
    updatePromptWithScaleInfo();
  }
}, { immediate: true });

// Methods
const updatePromptWithScaleInfo = () => {
  if (!props.scaleProperties) return;
  
  let prompt = customPrompt.value;
  prompt = prompt.replace('{{ scaleName }}', props.scaleProperties.name || '未命名量表');
  prompt = prompt.replace('{{ scaleType }}', getTypeDisplayName(props.scaleProperties.type));
  prompt = prompt.replace('{{ scaleDescription }}', props.scaleProperties.description || '无描述');
  prompt = prompt.replace('{{ markdownContent }}', props.markdownContent || '');
  
  customPrompt.value = prompt;
};

const loadDefaultPrompt = () => {
  customPrompt.value = `请分析以下评估量表的结构，并为其设计合适的数据库表结构：

1. 识别量表中的所有评估项目和选项
2. 推断每个字段的数据类型和约束
3. 设计标准化的表名和字段名
4. 生成完整的数据库结构建议

量表信息：
- 名称：{{ scaleName }}
- 类型：{{ scaleType }}
- 描述：{{ scaleDescription }}

量表内容：
{{ markdownContent }}

请以JSON格式输出数据库结构设计建议。`;

  updatePromptWithScaleInfo();
  ElMessage.success('已重置为默认提示词');
};

const previewPrompt = () => {
  ElMessageBox.alert(customPrompt.value, '提示词预览', {
    confirmButtonText: '关闭',
    type: 'info',
  });
};

const startAIAnalysis = () => {
  if (!canStartAnalysis.value) {
    ElMessage.warning('请确保已完成前置步骤');
    return;
  }
  
  aiAnalyzing.value = true;
  streamOutput.value = '正在连接AI服务...\n';
  contentBlocks.value = [];
  
  // 模拟AI分析过程
  simulateAIAnalysis();
  
  emit('start-ai-analysis');
};

const simulateAIAnalysis = async () => {
  const steps = [
    '解析量表结构...',
    '识别评估项目...',
    '分析数据类型...',
    '设计表结构...',
    '生成建议方案...',
    '完成分析'
  ];
  
  for (let i = 0; i < steps.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 1500));
    streamOutput.value += `${steps[i]}\n`;
    
    if (i === steps.length - 1) {
      // 模拟生成分析结果
      const mockResult: AnalysisResult = {
        structure: {
          tableName: `assessment_${props.scaleProperties?.code?.toLowerCase() || 'scale'}`,
          fields: [
            { name: 'id', type: 'BIGINT', primaryKey: true },
            { name: 'elderly_id', type: 'BIGINT', nullable: false },
            { name: 'assessor_id', type: 'BIGINT', nullable: false },
            { name: 'assessment_date', type: 'DATETIME', nullable: false },
            { name: 'total_score', type: 'DECIMAL(10,2)', nullable: true },
            { name: 'status', type: 'VARCHAR(20)', nullable: false },
          ]
        },
        recommendations: [
          '建议为每个评估项目创建独立字段',
          '使用DECIMAL类型存储分数以保证精度',
          '添加审核状态字段便于流程管理',
          '建议创建关联表存储详细评估项目'
        ],
        confidence: 0.92,
        timestamp: new Date()
      };
      
      analysisResult.value = mockResult;
      
      // 添加内容块
      contentBlocks.value = [
        {
          id: '1',
          type: 'text',
          content: `分析完成！为 "${props.scaleProperties?.name}" 生成了数据库结构建议。`
        },
        {
          id: '2',
          type: 'code',
          content: JSON.stringify(mockResult.structure, null, 2)
        }
      ];
      
      aiAnalyzing.value = false;
      ElMessage.success('AI分析完成');
    }
  }
};

const confirmAnalysis = () => {
  if (!analysisResult.value) return;
  
  ElMessageBox.confirm(
    '确认AI分析结果并进入数据库结构设计阶段？',
    '确认分析结果',
    {
      confirmButtonText: '确认进入设计',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    isCompleted.value = true;
    emit('stage-complete', analysisResult.value!);
    ElMessage.success('已确认分析结果，可以进入结构设计');
  }).catch(() => {});
};

const openAIChat = () => {
  emit('open-ai-chat');
};

const copyAllContent = () => {
  emit('copy-all-content');
};

const copyCodeBlocks = () => {
  emit('copy-code-blocks');
};

const copyStreamOutput = () => {
  emit('copy-stream-output');
};

const clearStreamOutput = () => {
  streamOutput.value = '';
  contentBlocks.value = [];
  emit('clear-stream-output');
};

const parseAIResult = () => {
  emit('parse-ai-result');
};

// Utility methods
const getHostFromUrl = (url: string) => {
  try {
    return new URL(url).host;
  } catch {
    return url;
  }
};

const getTypeDisplayName = (type: string) => {
  const typeMap: Record<string, string> = {
    ELDERLY_ABILITY: '老年人能力评估',
    EMOTIONAL_QUICK: '情绪快评',
    INTER_RAI: 'interRAI评估',
    LONG_CARE_INSURANCE: '长护险评估',
    CUSTOM: '自定义量表',
  };
  return typeMap[type] || type;
};
</script>

<style scoped>
.stage-card {
  height: 100%;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stage-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stage-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
}

.stage-number.active {
  background: #3b82f6;
  color: white;
}

.stage-number.completed {
  background: #10b981;
  color: white;
}

.stage-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stage-description {
  font-size: 14px;
  color: #6b7280;
}

.analysis-content {
  padding: 8px 0;
  max-height: 800px;
  overflow-y: auto;
}

.analysis-content.disabled {
  opacity: 0.5;
}

.ai-control-section {
  margin-bottom: 20px;
}

.prompt-section {
  margin-bottom: 20px;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.prompt-controls {
  display: flex;
  gap: 8px;
}

.prompt-textarea {
  margin-bottom: 8px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.prompt-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.stat-item {
  display: flex;
  align-items: center;
}

.model-info {
  background: #f0f9ff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #1e40af;
}

.info-content {
  font-size: 14px;
  color: #1e40af;
}

.analysis-actions {
  display: flex;
  gap: 12px;
}

.analysis-button,
.chat-button {
  flex: 1;
}

.disabled-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.analysis-output {
  margin-top: 20px;
}

.stream-card {
  border: 1px solid #e4e7ed;
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stream-output {
  background: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.stream-text {
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.content-blocks {
  margin-top: 16px;
}

.blocks-container {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.content-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

.stream-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.result-preview {
  margin-top: 20px;
}

.result-card {
  border: 1px solid #10b981;
  background: #f0fdf4;
}

.result-content {
  max-height: 300px;
  overflow-y: auto;
}

.result-text {
  color: #333;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>