<template>
  <el-card class="process-indicator">
    <div class="process-header">
      <h2 class="process-title">智慧养老评估平台 - 量表数字化转换</h2>
      <div class="process-subtitle">将传统量表转换为数字化评估工具的完整流程</div>
    </div>
    
    <div class="process-steps">
      <div class="steps-container">
        <div 
          v-for="(step, index) in steps"
          :key="step.id"
          class="step-item"
          :class="getStepClass(step.stage)"
        >
          <!-- 步骤圆圈 -->
          <div class="step-circle">
            <div class="step-number" :class="getStepNumberClass(step.stage)">
              <el-icon v-if="isStageCompleted(step.stage)" class="step-icon">
                <Check />
              </el-icon>
              <el-icon v-else-if="isStageActive(step.stage)" class="step-icon animate-pulse">
                <component :is="step.icon" />
              </el-icon>
              <span v-else class="step-text">{{ step.display }}</span>
            </div>
          </div>
          
          <!-- 步骤内容 -->
          <div class="step-content">
            <div class="step-title">{{ step.title }}</div>
            <div class="step-description">{{ step.description }}</div>
            <div class="step-status">
              <el-tag 
                :type="getStepTagType(step.stage)" 
                size="small"
                class="status-tag"
              >
                {{ getStepStatusText(step.stage) }}
              </el-tag>
              <span v-if="getStepDuration(step.stage)" class="step-duration">
                {{ getStepDuration(step.stage) }}
              </span>
            </div>
          </div>
          
          <!-- 连接线 -->
          <div 
            v-if="index < steps.length - 1" 
            class="step-connector"
            :class="{ 'completed': isStageCompleted(step.stage) }"
          >
            <div class="connector-line"></div>
            <ArrowRightIcon class="connector-arrow" />
          </div>
        </div>
      </div>
    </div>

    <!-- 进度统计 -->
    <div class="progress-stats">
      <div class="stats-item">
        <div class="stats-number">{{ completedStagesCount }}</div>
        <div class="stats-label">已完成</div>
      </div>
      <div class="stats-item">
        <div class="stats-number">{{ totalStagesCount }}</div>
        <div class="stats-label">总步骤</div>
      </div>
      <div class="stats-item">
        <div class="stats-number">{{ progressPercentage }}%</div>
        <div class="stats-label">完成率</div>
      </div>
      <div class="stats-item">
        <div class="stats-number">{{ estimatedTimeRemaining }}</div>
        <div class="stats-label">预计剩余</div>
      </div>
    </div>

    <!-- 整体进度条 -->
    <div class="overall-progress">
      <div class="progress-header">
        <span class="progress-text">整体进度</span>
        <span class="progress-percentage">{{ progressPercentage }}%</span>
      </div>
      <el-progress 
        :percentage="progressPercentage" 
        :stroke-width="8"
        :show-text="false"
        :color="progressColor"
        class="progress-bar"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Check } from '@element-plus/icons-vue';
import {
  ArrowRightIcon,
  CloudArrowUpIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  CpuChipIcon,
  CircleStackIcon,
  CodeBracketIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  currentStage: 0 | 1 | 2 | 2.5 | 3 | 4 | 5;
  stageCompletions: {
    stage1: boolean;
    stage2: boolean;
    stage2_5: boolean;
    stage3: boolean;
    stage4: boolean;
    stage5: boolean;
  };
  startTime?: Date;
}

const props = withDefaults(defineProps<Props>(), {
  currentStage: 0,
  stageCompletions: () => ({
    stage1: false,
    stage2: false,
    stage2_5: false,
    stage3: false,
    stage4: false,
    stage5: false,
  }),
});

// Steps configuration
const steps = [
  {
    id: 'stage1',
    stage: 1,
    display: '1',
    title: '文档上传',
    description: '上传多格式文档，AI智能解析',
    icon: CloudArrowUpIcon,
    estimatedMinutes: 2,
  },
  {
    id: 'stage2',
    stage: 2,
    display: '2',
    title: '解析编辑',
    description: 'Markdown内容编辑与预览',
    icon: DocumentTextIcon,
    estimatedMinutes: 5,
  },
  {
    id: 'stage2_5',
    stage: 2.5,
    display: '2.5',
    title: '量表属性',
    description: '配置量表基本信息和元数据',
    icon: Cog6ToothIcon,
    estimatedMinutes: 3,
  },
  {
    id: 'stage3',
    stage: 3,
    display: '3',
    title: 'AI分析',
    description: '智能分析生成数据库结构建议',
    icon: CpuChipIcon,
    estimatedMinutes: 4,
  },
  {
    id: 'stage4',
    stage: 4,
    display: '4',
    title: '结构设计',
    description: '数据库表结构设计和字段定义',
    icon: CircleStackIcon,
    estimatedMinutes: 6,
  },
  {
    id: 'stage5',
    stage: 5,
    display: '5',
    title: 'SQL生成',
    description: 'SQL语句生成、验证和执行',
    icon: CodeBracketIcon,
    estimatedMinutes: 3,
  },
];

// Computed properties
const completedStagesCount = computed(() => {
  return Object.values(props.stageCompletions).filter(Boolean).length;
});

const totalStagesCount = computed(() => {
  return Object.keys(props.stageCompletions).length;
});

const progressPercentage = computed(() => {
  return Math.round((completedStagesCount.value / totalStagesCount.value) * 100);
});

const progressColor = computed(() => {
  const percentage = progressPercentage.value;
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
});

const estimatedTimeRemaining = computed(() => {
  const currentStageIndex = steps.findIndex(step => step.stage === props.currentStage);
  if (currentStageIndex === -1) return '0分钟';
  
  const remainingSteps = steps.slice(currentStageIndex);
  const totalMinutes = remainingSteps.reduce((sum, step) => {
    const stageKey = `stage${step.stage}` as keyof typeof props.stageCompletions;
    return props.stageCompletions[stageKey] ? sum : sum + step.estimatedMinutes;
  }, 0);
  
  if (totalMinutes === 0) return '已完成';
  if (totalMinutes < 60) return `${totalMinutes}分钟`;
  
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hours}小时${minutes}分钟`;
});

// Methods
const isStageCompleted = (stage: number) => {
  const stageKey = `stage${stage}` as keyof typeof props.stageCompletions;
  return props.stageCompletions[stageKey];
};

const isStageActive = (stage: number) => {
  return props.currentStage === stage && props.currentStage > 0;
};

const getStepClass = (stage: number) => {
  if (isStageCompleted(stage)) return 'completed';
  if (isStageActive(stage)) return 'active';
  return 'pending';
};

const getStepNumberClass = (stage: number) => {
  if (isStageCompleted(stage)) return 'completed';
  if (isStageActive(stage)) return 'active';
  return 'pending';
};

const getStepTagType = (stage: number) => {
  if (isStageCompleted(stage)) return 'success';
  if (isStageActive(stage)) return 'warning';
  if (props.currentStage === 0 && stage === 1) return 'primary';
  return 'info';
};

const getStepStatusText = (stage: number) => {
  if (isStageCompleted(stage)) return '已完成';
  if (isStageActive(stage)) return '进行中';
  if (props.currentStage === 0 && stage === 1) return '准备就绪';
  return '等待中';
};

const getStepDuration = (stage: number) => {
  const step = steps.find(s => s.stage === stage);
  if (!step) return '';
  
  if (isStageCompleted(stage)) {
    return '已完成';
  } else if (isStageActive(stage)) {
    return `约${step.estimatedMinutes}分钟`;
  } else {
    return `预计${step.estimatedMinutes}分钟`;
  }
};
</script>

<style scoped>
/* 品牌色彩定义 */
:root {
  --changchun-blue: #5357A0;      /* 长春花蓝 - 主色 */
  --foshou-yellow: #fed81f;       /* 佛手黄 - 配色 */
  --light-blue: #eaebf8;          /* 浅蓝 - 背景色 */
  --dark-blue: #434683;           /* 深蓝 - 强调色 */
  --warm-white: #FEFEFE;          /* 温白 - 卡片背景 */
  --text-primary: #5357A0;        /* 主要文字 - 长春花蓝，用于标题和重要内容 */
  --text-secondary: #718096;      /* 次要文字 - 中性灰，用于描述和辅助信息 */
  --success: #48BB78;             /* 成功状态 */
  --warning: #ED8936;             /* 警告状态 */
  --error: #F56565;               /* 错误状态 */
}

.process-indicator {
  margin-bottom: 24px;
  border: 2px solid var(--foshou-yellow);
  background: var(--warm-white);
  box-shadow: 0 2px 8px rgba(254, 216, 31, 0.1);
}

.process-header {
  text-align: center;
  margin-bottom: 32px;
}

.process-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--dark-blue);
  margin: 0 0 8px 0;
}

.process-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

.process-steps {
  margin-bottom: 32px;
}

.steps-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 16px;
}

.step-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-height: 140px;
}

.step-circle {
  margin-bottom: 16px;
  z-index: 2;
  position: relative;
}

.step-number {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  border: 3px solid;
  background: white;
  transition: all 0.3s ease;
}

.step-number.pending {
  border-color: var(--foshou-yellow);
  color: var(--text-secondary);
}

.step-number.active {
  border-color: var(--changchun-blue);
  color: var(--changchun-blue);
  box-shadow: 0 0 0 4px rgba(83, 87, 160, 0.1);
}

.step-number.completed {
  border-color: var(--success);
  background: var(--success);
  color: white;
}

.step-icon {
  font-size: 20px;
}

.step-text {
  font-size: 16px;
  font-weight: 600;
}

.step-content {
  text-align: center;
  max-width: 160px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.step-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 8px;
}

.step-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-tag {
  font-size: 11px;
}

/* 等待中状态使用绿色 */
.status-tag.el-tag--info {
  background-color: #22c55e !important;
  color: white !important;
  border-color: #22c55e !important;
}

/* 准备就绪状态使用绿色 */
.status-tag.el-tag--primary {
  background-color: #22c55e !important;
  color: white !important;
  border-color: #22c55e !important;
}

.step-duration {
  font-size: 11px;
  color: #9ca3af;
}

.step-connector {
  position: absolute;
  top: 24px;
  left: calc(50% + 24px);
  right: calc(-50% + 24px);
  display: flex;
  align-items: center;
  z-index: 1;
}

.connector-line {
  flex: 1;
  height: 2px;
  background: var(--foshou-yellow);
  transition: background-color 0.3s ease;
}

.step-connector.completed .connector-line {
  background: var(--success);
}

.connector-arrow {
  width: 16px;
  height: 16px;
  color: var(--foshou-yellow);
  margin-left: 8px;
  transition: color 0.3s ease;
}

.step-connector.completed .connector-arrow {
  color: var(--success);
}

.progress-stats {
  display: flex;
  justify-content: center;
  gap: 48px;
  margin-bottom: 24px;
  padding: 20px 0;
  border-top: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--changchun-blue);
  line-height: 1;
}

.stats-label {
  font-size: 12px;
  color: var(--changchun-blue);
  margin-top: 4px;
}

.overall-progress {
  max-width: 400px;
  margin: 0 auto;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--changchun-blue);
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: var(--changchun-blue);
}

.progress-bar {
  width: 100%;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .steps-container {
    flex-direction: column;
    align-items: center;
  }
  
  .step-item {
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    min-height: auto;
    margin-bottom: 20px;
  }
  
  .step-circle {
    margin-right: 16px;
    margin-bottom: 0;
  }
  
  .step-content {
    text-align: left;
    max-width: none;
    flex: 1;
  }
  
  .step-connector {
    display: none;
  }
  
  .progress-stats {
    gap: 24px;
  }
}

@media (max-width: 640px) {
  .process-title {
    font-size: 20px;
  }
  
  .progress-stats {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .stats-item {
    flex: 1;
    min-width: 80px;
  }
}
</style>