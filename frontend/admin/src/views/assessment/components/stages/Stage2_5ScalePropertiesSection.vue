<template>
  <el-card class="stage-card stage2-5-scale-properties">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ completed: isCompleted, active: !isCompleted }">
          <el-icon v-if="isCompleted"><Check /></el-icon>
          <span v-else>2.5</span>
        </div>
        <div class="stage-title">
          <h3>量表属性</h3>
          <span class="stage-description">配置量表基本信息和元数据</span>
        </div>
        <div class="stage-actions">
          <el-tag v-if="isCompleted" type="success">已配置</el-tag>
          <el-tag v-else-if="hasAutoFilled" type="info">已自动填充</el-tag>
          <el-tag v-else type="warning">待配置</el-tag>
        </div>
      </div>
    </template>

    <div class="properties-content">
      <!-- 基本信息表单 -->
      <el-form 
        :model="scaleForm" 
        :rules="formRules"
        label-width="80px" 
        size="small"
        ref="formRef"
      >
        <!-- 量表名称 -->
        <el-form-item label="量表名称" prop="name">
          <el-input 
            v-model="scaleForm.name" 
            placeholder="请输入量表名称"
            clearable
            @input="handleFormChange"
          />
        </el-form-item>
        
        <!-- 量表代码 -->
        <el-form-item label="量表代码" prop="code">
          <el-input 
            v-model="scaleForm.code" 
            placeholder="自动生成或手动输入"
            clearable
            @input="handleFormChange"
          >
            <template #append>
              <el-button @click="generateCode" :disabled="!scaleForm.type">
                生成
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        
        <!-- 量表类型 -->
        <el-form-item label="量表类型" prop="type">
          <el-select 
            v-model="scaleForm.type" 
            placeholder="选择量表类型"
            style="width: 100%"
            @change="onTypeChange"
          >
            <el-option 
              v-for="type in scaleTypes"
              :key="type.value"
              :label="type.label" 
              :value="type.value"
            >
              <div class="flex justify-between items-center w-full">
                <span>{{ type.label }}</span>
                <el-tag 
                  :type="type.tagType" 
                  size="small"
                  class="ml-2"
                >
                  {{ type.tag }}
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <!-- 版本号 -->
        <el-form-item label="版本号" prop="version">
          <el-input 
            v-model="scaleForm.version" 
            placeholder="例如：1.0.0"
            clearable
            @input="handleFormChange"
          >
            <template #prepend>v</template>
          </el-input>
        </el-form-item>
        
        <!-- 预估时长 -->
        <el-form-item label="预估时长" prop="estimatedDuration">
          <el-input-number 
            v-model="scaleForm.estimatedDuration" 
            :min="1" 
            :max="180"
            :step="5"
            controls-position="right"
            style="width: 100%;"
            @change="handleFormChange"
          />
          <div class="form-hint">单位：分钟</div>
        </el-form-item>

        <!-- 描述 -->
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="scaleForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入量表的详细描述和适用场景"
            maxlength="500"
            show-word-limit
            @input="handleFormChange"
          />
        </el-form-item>

        <!-- 高级配置 -->
        <el-divider content-position="left">
          <div class="flex items-center">
            <Cog6ToothIcon class="h-4 w-4 mr-1 text-gray-600" />
            <span class="text-sm text-gray-600">高级配置</span>
          </div>
        </el-divider>
        
        <!-- 评估模式 -->
        <el-form-item label="评估模式" prop="assessmentModes">
          <el-checkbox-group v-model="scaleForm.assessmentModes" @change="handleFormChange">
            <el-checkbox value="MOBILE">
              <div class="flex items-center">
                <DevicePhoneMobileIcon class="h-4 w-4 mr-1" />
                移动端
              </div>
            </el-checkbox>
            <el-checkbox value="WEB">
              <div class="flex items-center">
                <ComputerDesktopIcon class="h-4 w-4 mr-1" />
                网页端
              </div>
            </el-checkbox>
            <el-checkbox value="OFFLINE">
              <div class="flex items-center">
                <DocumentIcon class="h-4 w-4 mr-1" />
                离线评估
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <!-- 合规标准 -->
        <el-form-item label="合规标准" prop="complianceStandard">
          <el-select 
            v-model="scaleForm.complianceStandard" 
            placeholder="选择合规标准"
            clearable
            style="width: 100%"
            @change="handleFormChange"
          >
            <el-option label="国家标准GB/T" value="GB_T" />
            <el-option label="民政部标准" value="MZ_T" />
            <el-option label="地方标准" value="LOCAL" />
            <el-option label="机构内部标准" value="INTERNAL" />
          </el-select>
        </el-form-item>
        
        <!-- 启用状态 -->
        <el-form-item label="启用状态" prop="isActive">
          <el-switch
            v-model="scaleForm.isActive"
            active-text="启用"
            inactive-text="停用"
            @change="handleFormChange"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <div class="form-actions">
            <el-button 
              type="primary" 
              @click="handleSave"
              :loading="saving"
            >
              <ArchiveBoxIcon class="h-4 w-4 mr-1" />
              保存属性
            </el-button>
            <el-button @click="handleReset">
              <ArrowPathIcon class="h-4 w-4 mr-1" />
              重置
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <!-- 智能识别提示 -->
      <div v-if="hasAutoFilled" class="auto-fill-notice">
        <el-alert
          title="智能识别完成"
          type="success"
          :closable="false"
          show-icon
          size="small"
        >
          <div class="text-sm">
            系统已根据文档内容自动填充量表属性，请检查并确认信息是否正确。
          </div>
        </el-alert>
      </div>
      
      <!-- 默认提示 -->
      <div v-else class="default-notice">
        <el-alert
          title="配置量表属性"
          type="info"
          :closable="false"
          show-icon
          size="small"
        >
          <div class="text-sm">
            请配置量表的基本信息和元数据。上传文档后，系统将自动识别并填充相关属性。
          </div>
        </el-alert>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Check } from '@element-plus/icons-vue';
import {
  ArchiveBoxIcon,
  ArrowPathIcon,
  Cog6ToothIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DocumentIcon,
} from '@heroicons/vue/24/outline';

// Types
interface EditResult {
  markdownContent: string;
  contentValidated: boolean;
  editMode: 'preview' | 'edit' | 'split';
  wordCount: number;
  hasChanges: boolean;
}

interface ScaleProperties {
  name: string;
  code: string;
  type: 'ELDERLY_ABILITY' | 'EMOTIONAL_QUICK' | 'INTER_RAI' | 'LONG_CARE_INSURANCE' | 'CUSTOM';
  version: string;
  estimatedDuration: number;
  description: string;
  assessmentModes: string[];
  complianceStandard: string;
  isActive: boolean;
}


// Props
interface Props {
  enabled?: boolean;
  editResult?: EditResult | null;
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
  editResult: null,
});

// Emits
const emit = defineEmits<{
  'stage-complete': [result: ScaleProperties];
}>();

// Refs
const formRef = ref();
const saving = ref(false);
const isCompleted = ref(false);

// Form data
const scaleForm = reactive<ScaleProperties>({
  name: '',
  code: '',
  type: 'CUSTOM',
  version: '1.0.0',
  estimatedDuration: 20,
  description: '',
  assessmentModes: ['MOBILE', 'WEB'],
  complianceStandard: 'INTERNAL',
  isActive: true,
});

// Form rules
const formRules = {
  name: [
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  type: [
    { message: '请选择量表类型', trigger: 'change' },
  ],
};

// Scale types configuration
const scaleTypes = [
  {
    value: 'ELDERLY_ABILITY',
    label: '老年人能力评估',
    tag: '国家标准',
    tagType: 'success' as const,
  },
  {
    value: 'EMOTIONAL_QUICK',
    label: '情绪快评',
    tag: '快速筛查',
    tagType: 'warning' as const,
  },
  {
    value: 'INTER_RAI',
    label: 'interRAI评估',
    tag: '国际标准',
    tagType: 'primary' as const,
  },
  {
    value: 'LONG_CARE_INSURANCE',
    label: '长护险评估',
    tag: '保险资格',
    tagType: 'info' as const,
  },
  {
    value: 'CUSTOM',
    label: '自定义量表',
    tag: '灵活配置',
    tagType: 'info' as const,
  },
];

// Auto-fill tracking
const hasAutoFilled = ref(false);

// Watch for edit result changes
watch(() => props.editResult, (newResult) => {
  if (newResult) {
    // 尝试从markdown内容中提取量表名称
    autoFillFromMarkdown(newResult.markdownContent);
  }
}, { immediate: true });

// Methods
const generateCode = () => {
  if (!scaleForm.type) {
    ElMessage.warning('请先选择量表类型');
    return;
  }
  
  const timestamp = new Date().getTime().toString().slice(-6);
  const version = scaleForm.version.replace(/\./g, '');
  
  scaleForm.code = `${scaleForm.type}_${timestamp}_V${version}`;
  ElMessage.success('已生成量表代码');
  
  handleFormChange();
};

const onTypeChange = (value: string) => {
  // 根据类型设置默认的预估时长
  const defaultDurations: Record<string, number> = {
    'ELDERLY_ABILITY': 30,
    'EMOTIONAL_QUICK': 10,
    'INTER_RAI': 45,
    'LONG_CARE_INSURANCE': 25,
    'CUSTOM': 20
  };
  
  if (!scaleForm.estimatedDuration || scaleForm.estimatedDuration === 20) {
    scaleForm.estimatedDuration = defaultDurations[value] || 20;
  }
  
  // 设置默认的合规标准
  const defaultStandards: Record<string, string> = {
    'ELDERLY_ABILITY': 'GB_T',
    'EMOTIONAL_QUICK': 'MZ_T',
    'INTER_RAI': 'GB_T',
    'LONG_CARE_INSURANCE': 'MZ_T',
    'CUSTOM': 'INTERNAL'
  };
  
  scaleForm.complianceStandard = defaultStandards[value] || 'INTERNAL';
  
  // 自动生成代码
  if (!scaleForm.code) {
    generateCode();
  }
  
  handleFormChange();
};

const handleFormChange = () => {
  // 检查是否可以完成阶段
  checkStageCompletion();
};

const handleSave = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    saving.value = true;
    
    // 确认对话框
    await ElMessageBox.confirm(
      `请确认以下信息是否正确：

量表名称：${scaleForm.name}
量表代码：${scaleForm.code}  
量表类型：${getTypeDisplayName(scaleForm.type)}
预估时长：${scaleForm.estimatedDuration}分钟
评估模式：${scaleForm.assessmentModes.map(mode => getModeDisplayName(mode)).join(', ')}

确认保存量表属性？`,
      '确认保存',
      {
        confirmButtonText: '确认保存',
        cancelButtonText: '取消',
        type: 'info',
      }
    );
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    isCompleted.value = true;
    ElMessage.success('量表属性保存成功');
    
    // 通知父组件阶段完成
    emit('stage-complete', { ...scaleForm });
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存失败，请检查表单信息');
    }
  } finally {
    saving.value = false;
  }
};

const handleReset = () => {
  Object.assign(scaleForm, {
    name: '',
    code: '',
    type: 'CUSTOM',
    version: '1.0.0',
    estimatedDuration: 20,
    description: '',
    assessmentModes: ['MOBILE', 'WEB'],
    complianceStandard: 'INTERNAL',
    isActive: true,
  });
  
  isCompleted.value = false;
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
  
  ElMessage.info('表单已重置');
};


const autoFillFromMarkdown = (markdownContent: string) => {
  let autoFillCount = 0;
  
  // 1. 尝试从markdown中提取标题作为量表名称
  const titleMatch = markdownContent.match(/^#\s+(.+)$/m);
  if (titleMatch && !scaleForm.name) {
    scaleForm.name = titleMatch[1].trim();
    autoFillCount++;
  }
  
  // 2. 根据内容关键词智能推断量表类型
  const content = markdownContent.toLowerCase();
  let detectedType = 'CUSTOM'; // 默认值
  
  // 优先级匹配：更具体的关键词优先
  if (content.includes('长护险') || content.includes('护理保险') || content.includes('长期护理保险')) {
    detectedType = 'LONG_CARE_INSURANCE';
  } else if (content.includes('interrai') || content.includes('inter-rai')) {
    detectedType = 'INTER_RAI';
  } else if (content.includes('情绪') && (content.includes('快评') || content.includes('筛查'))) {
    detectedType = 'EMOTIONAL_QUICK';
  } else if (content.includes('老年') && content.includes('能力评估')) {
    detectedType = 'ELDERLY_ABILITY';
  } else if (content.includes('elderly') || content.includes('老年')) {
    detectedType = 'ELDERLY_ABILITY';
  }
  
  if (detectedType !== scaleForm.type) {
    scaleForm.type = detectedType as 'ELDERLY_ABILITY' | 'EMOTIONAL_QUICK' | 'INTER_RAI' | 'LONG_CARE_INSURANCE' | 'CUSTOM';
    autoFillCount++;
  }
  
  // 3. 智能推断预估时长（基于内容量）
  const contentLength = markdownContent.length;
  const questionCount = (markdownContent.match(/\d+\./g) || []).length;
  
  let estimatedDuration = 20; // 默认值
  if (questionCount > 50 || contentLength > 5000) {
    estimatedDuration = 45; // 长量表
  } else if (questionCount > 20 || contentLength > 2000) {
    estimatedDuration = 30; // 中等量表
  } else if (questionCount < 10 && contentLength < 1000) {
    estimatedDuration = 10; // 快速评估
  }
  
  if (estimatedDuration !== scaleForm.estimatedDuration) {
    scaleForm.estimatedDuration = estimatedDuration;
    autoFillCount++;
  }
  
  // 4. 根据检测到的类型设置其他属性
  onTypeChange(scaleForm.type);
  
  // 5. 自动生成代码
  if (!scaleForm.code) {
    generateCode();
    autoFillCount++;
  }
  
  // 标记已自动填充
  if (autoFillCount > 0) {
    hasAutoFilled.value = true;
    ElMessage.success(`智能识别完成，已自动填充 ${autoFillCount} 项属性`);
  }
};

const checkStageCompletion = () => {
  if (scaleForm.name && scaleForm.type && scaleForm.code) {
    // 可以标记为基本完成，但还未保存
  }
};

// Utility methods
const getTypeDisplayName = (type: string) => {
  const typeMap: Record<string, string> = {
    ELDERLY_ABILITY: '老年人能力评估',
    EMOTIONAL_QUICK: '情绪快评',
    INTER_RAI: 'interRAI评估',
    LONG_CARE_INSURANCE: '长护险评估',
    CUSTOM: '自定义量表',
  };
  return typeMap[type] || type;
};


const getModeDisplayName = (mode: string) => {
  const modeMap: Record<string, string> = {
    MOBILE: '移动端',
    WEB: '网页端',
    OFFLINE: '离线评估',
  };
  return modeMap[mode] || mode;
};
</script>

<style scoped>
.stage-card {
  height: 950px; /* 增加高度确保完全显示 */
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stage-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.stage-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stage-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
}

.stage-number.active {
  background: #3b82f6;
  color: white;
}

.stage-number.completed {
  background: #10b981;
  color: white;
}

.stage-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stage-description {
  font-size: 14px;
  color: #6b7280;
}

.properties-content {
  padding: 8px 0;
  height: calc(100% - 80px); /* 减去header高度 */
  /* 移除overflow-y，确保内容完全显示 */
}


.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  width: 100%;
  justify-content: center; /* 居中按钮 */
}

/* 强制按钮表单项居中 */
.properties-content .el-form-item:last-child .el-form-item__content {
  justify-content: center !important;
}

/* 紧凑表单样式 */
.properties-content .el-form-item {
  margin-bottom: 16px !important; /* 适当增加间距，避免重叠 */
}

.properties-content .el-divider {
  margin: 20px 0 16px 0 !important; /* 分隔线上方更多空间，下方正常 */
}

/* 特别处理描述字段，给textarea更多空间 */
.properties-content .el-form-item:has(.el-textarea) {
  margin-bottom: 24px !important; /* 描述字段需要更多底部空间 */
}

.form-actions .el-button {
  min-width: 90px; /* 固定最小宽度，不再占满 */
}

.auto-fill-notice,
.default-notice {
  margin-top: 12px;
  padding: 0 8px;
}


.recent-templates {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.template-card {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-card:hover {
  border-color: #3b82f6;
  background: #f8faff;
}

.template-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.template-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.template-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-divider {
  margin: 24px 0 16px 0;
}

.el-checkbox-group {
  width: 100%;
}

.el-checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}
</style>