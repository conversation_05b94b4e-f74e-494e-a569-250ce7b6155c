<template>
  <el-card class="stage-card stage1-upload">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ completed: isCompleted, active: !isCompleted, uploading: uploading }">
          <el-icon v-if="isCompleted"><Check /></el-icon>
          <span v-else>1</span>
        </div>
        <div class="stage-title">
          <h3>文档上传</h3>
          <span class="stage-description">上传多格式文档，Docling AI智能解析</span>
        </div>
        <div class="stage-status">
          <el-tag v-if="isCompleted" type="success">已完成</el-tag>
          <el-tag v-else-if="uploading" type="warning">上传中</el-tag>
          <el-tag v-else type="info">待上传</el-tag>
        </div>
      </div>
    </template>

    <div class="upload-content">
      <!-- 上传进度显示 -->
      <div v-if="uploading" class="upload-progress mb-4">
        <div class="progress-info">
          <span class="text-sm font-medium">{{ progressMessage }}</span>
          <span class="text-sm text-gray-600">{{ Math.round(uploadProgress) }}%</span>
        </div>
        <el-progress
          :percentage="uploadProgress"
          :stroke-width="6"
          :show-text="false"
          :status="uploadProgress === 100 ? 'success' : undefined"
        />
        <div v-if="doclingProcessing" class="mt-2">
          <div class="text-xs text-gray-600">{{ doclingStage }}</div>
        </div>
      </div>

      <el-row :gutter="24">
        <!-- 文件上传区域 -->
        <el-col :span="12">
          <div class="upload-zone">
            <el-upload
              ref="uploadRef"
              class="w-full"
              :action="computedUploadUrl"
              :data="computedUploadData"
              :headers="computedUploadHeaders"
              :before-upload="handleBeforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-progress="handleUploadProgress"
              :file-list="fileList"
              :limit="1"
              :accept="acceptedFileTypes"
              :disabled="uploading || isCompleted"
              drag
            >
              <div class="upload-dragger">
                <el-icon class="upload-icon" size="48">
                  <Upload v-if="!isCompleted" />
                  <Check v-else />
                </el-icon>
                <div class="upload-text">
                  <p v-if="!isCompleted">拖拽文件到此处，或<em>点击上传</em></p>
                  <p v-else>{{ uploadedFileName }}</p>
                </div>
                <div class="upload-hint">
                  <div class="flex items-center justify-center gap-4 text-xs text-gray-500">
                    <div class="flex items-center">
                      <FolderIcon class="h-3 w-3 mr-1" />
                      支持格式：PDF, DOCX, XLSX, PPTX, HTML, CSV, MD, PNG, GIF, TIFF, WebP, JPEG
                    </div>
                    <div class="flex items-center">
                      <ChartBarIcon class="h-3 w-3 mr-1" />
                      最大50MB
                    </div>
                  </div>
                </div>
              </div>
            </el-upload>
            
            <!-- 输出格式选择 -->
            <div class="format-selector mt-3">
              <label class="text-sm font-medium text-gray-700 mb-2 block">输出格式</label>
              <el-select
                v-model="outputFormat"
                placeholder="选择输出格式"
                size="default"
                class="w-full"
                :disabled="uploading || isCompleted"
                @change="handleFormatChange"
              >
                <el-option value="markdown">
                  <div class="flex items-center">
                    <DocumentTextIcon class="h-4 w-4 mr-2" />
                    <span>Markdown (推荐)</span>
                  </div>
                </el-option>
                <el-option value="html">
                  <div class="flex items-center">
                    <GlobeAltIcon class="h-4 w-4 mr-2" />
                    <span>HTML</span>
                  </div>
                </el-option>
                <el-option value="json">
                  <div class="flex items-center">
                    <ClipboardDocumentIcon class="h-4 w-4 mr-2" />
                    <span>JSON</span>
                  </div>
                </el-option>
              </el-select>
            </div>
          </div>
        </el-col>

        <!-- 服务状态区域 -->
        <el-col :span="12">
          <div class="service-status">
            <h4 class="text-sm font-medium text-gray-700 mb-3 text-center">服务状态</h4>
            
            <!-- Docling引擎状态 -->
            <div class="status-item">
              <el-tag
                :type="getDoclingTagType()"
                size="default"
                class="w-full justify-between"
              >
                <div class="flex items-center">
                  <component 
                    :is="getDoclingIcon()" 
                    class="h-4 w-4 mr-2" 
                    :class="{ 'animate-spin': checkingDocling }"
                  />
                  <span>Docling引擎</span>
                </div>
                <span>{{ getDoclingStatusText() }}</span>
              </el-tag>
              <el-button 
                size="small" 
                text 
                @click="handleCheckDocling"
                :loading="checkingDocling"
                class="mt-1 w-full justify-center"
              >
                <ArrowPathIcon class="h-3 w-3 mr-1" />
                检查状态
              </el-button>
            </div>

            <!-- AI分析服务状态 -->
            <div class="status-item mt-3">
              <el-tag
                :type="getAITagType()"
                size="default"
                class="w-full justify-between"
              >
                <div class="flex items-center">
                  <component 
                    :is="getAIIcon()" 
                    class="h-4 w-4 mr-2"
                    :class="{ 'animate-spin': checkingAI }"
                  />
                  <span>LM Studio AI</span>
                </div>
                <span>{{ getAIStatusText() }}</span>
              </el-tag>
              <el-button 
                size="small" 
                text 
                @click="handleCheckAI"
                :loading="checkingAI"
                class="mt-1 w-full justify-center"
              >
                <CpuChipIcon class="h-3 w-3 mr-1" />
                检查服务
              </el-button>
            </div>

            <!-- 模型信息 -->
            <div v-if="modelInfo" class="model-info mt-4 p-3 bg-gray-50 rounded">
              <h5 class="text-xs font-medium text-gray-600 mb-2 text-center">当前模型</h5>
              <div class="text-xs text-gray-700 text-center">
                <div>{{ modelInfo.displayName || modelInfo.id || '模型已连接' }}</div>
                <div class="text-gray-500">{{ getHostFromUrl(modelInfo.serverUrl) }}</div>
              </div>
            </div>

            <!-- 重新上传按钮 -->
            <div v-if="isCompleted" class="mt-4">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleReset"
                class="w-full"
              >
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                重新上传
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Upload, Check } from '@element-plus/icons-vue';
import {
  FolderIcon,
  ChartBarIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  ClipboardDocumentIcon,
  ArrowPathIcon,
  CpuChipIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  uploadUrl?: string;
  uploadData?: Record<string, any>;
  uploadHeaders?: Record<string, string>;
  acceptedFileTypes?: string;
  doclingAvailable?: boolean;
  aiServiceAvailable?: boolean;
  checkingDocling?: boolean;
  checkingAI?: boolean;
  modelInfo?: any;
}

const props = withDefaults(defineProps<Props>(), {
  uploadUrl: '/api/docling/convert-with-info',
  uploadData: () => ({}),
  uploadHeaders: () => ({}),
  acceptedFileTypes: '.pdf,.docx,.xlsx,.pptx,.html,.csv,.md,.png,.jpg,.jpeg,.gif,.tiff,.webp',
  doclingAvailable: false,
  aiServiceAvailable: false,
  checkingDocling: false,
  checkingAI: false,
});

// Computed properties for upload configuration
const computedUploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8181';
  return `${baseUrl}/api/docling/convert-with-info`;
});

const computedUploadData = computed(() => {
  return {
    outputFormat: outputFormat.value,
    ...props.uploadData
  };
});

const computedUploadHeaders = computed(() => {
  const token = localStorage.getItem('token');
  return {
    'Authorization': token ? `Bearer ${token}` : '',
    ...props.uploadHeaders
  };
});

// Emits
interface UploadResult {
  fileName: string;
  fileSize: number;
  uploadSuccess: boolean;
  doclingResult?: any;
  markdownContent?: string;
}

const emit = defineEmits<{
  'stage-complete': [result: UploadResult];
  'check-docling': [];
  'check-ai': [];
  'upload-start': [];
  'upload-progress': [progress: number, message: string];
  'upload-error': [error: any];
}>();

// Reactive data
const uploading = ref(false);
const uploadProgress = ref(0);
const doclingProcessing = ref(false);
const doclingStage = ref('');
const progressMessage = ref('');
const fileList = ref<any[]>([]);
const outputFormat = ref('markdown');
const isCompleted = ref(false);
const uploadedFileName = ref('');
const uploadResult = ref<UploadResult | null>(null);

// Computed
const uploadRef = ref();

// Methods
const handleBeforeUpload = (file: File) => {
  const isValidType = props.acceptedFileTypes
    .split(',')
    .some(type => file.name.toLowerCase().endsWith(type.replace('.', '')));

  if (!isValidType) {
    ElMessage.error('不支持的文件格式');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < 50;
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过50MB');
    return false;
  }

  // 重置状态
  uploading.value = true;
  uploadProgress.value = 0;
  doclingProcessing.value = false;
  progressMessage.value = `正在上传${file.name}...`;
  
  // 触发上传开始事件
  emit('upload-start');
  
  return true;
};

const handleUploadSuccess = (response: any, file: any, fileList: any) => {
  uploading.value = false;
  doclingProcessing.value = false;
  
  console.log('Upload response:', response); // Debug log
  console.log('Response type:', typeof response); // Debug log
  
  // Handle different response structures
  // Element Plus may return the response as a string if content-type is not json
  let responseData = response;
  if (typeof response === 'string') {
    try {
      responseData = JSON.parse(response);
    } catch (e) {
      console.error('Failed to parse response:', e);
      handleUploadError('响应格式错误');
      return;
    }
  }
  
  // Check if response is wrapped
  if (responseData.data && typeof responseData.data === 'object') {
    responseData = responseData.data;
  }
  
  // Check for success in the original response first
  const isSuccess = response?.success || responseData?.success;
  
  if (isSuccess) {
    uploadProgress.value = 100;
    isCompleted.value = true;
    
    // Get the actual data from the correct level
    const data = response?.data || responseData?.data || responseData;
    uploadedFileName.value = data.fileName || data.originalFileName || data.name || file.name || '文档';
    
    // 创建结果对象
    uploadResult.value = {
      fileName: data.fileName || data.originalFileName || data.name || file.name || 'unknown',
      fileSize: data.fileSize || data.size || file.size || 0,
      uploadSuccess: true,
      doclingResult: data,
      markdownContent: data.markdownContent || data.content || '',
    };
    
    progressMessage.value = '文档上传并解析成功！';
    ElMessage.success('文件上传并解析成功');
    
    // 通知父组件阶段完成
    emit('stage-complete', uploadResult.value);
  } else {
    const errorMessage = response?.message || responseData?.message || '解析失败';
    handleUploadError(errorMessage);
  }
};

const handleUploadError = (error?: any) => {
  uploading.value = false;
  uploadProgress.value = 0;
  doclingProcessing.value = false;
  
  console.error('Upload error:', error); // Debug log
  
  let errorMessage = '文件上传失败';
  
  if (error) {
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.data?.message) {
      errorMessage = error.data.message;
    }
  }
  
  progressMessage.value = errorMessage;
  ElMessage.error(errorMessage);
  
  // 触发错误事件
  emit('upload-error', { message: errorMessage, error });
};

const handleUploadProgress = (event: any) => {
  const percentage = Math.round((event.loaded / event.total) * 100);
  uploadProgress.value = percentage;
  
  if (percentage === 100) {
    progressMessage.value = '文件上传完成，开始Docling AI解析...';
    doclingProcessing.value = true;
    doclingStage.value = '正在分析文档结构...';
    
    // 触发进度事件
    emit('upload-progress', percentage, progressMessage.value);
    
    // 模拟解析进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 95) {
        uploadProgress.value += Math.random() * 5;
        
        if (uploadProgress.value >= 30 && uploadProgress.value < 60) {
          doclingStage.value = '正在识别文档内容...';
        } else if (uploadProgress.value >= 60 && uploadProgress.value < 90) {
          doclingStage.value = '正在生成结构化数据...';
        } else if (uploadProgress.value >= 90) {
          doclingStage.value = '正在完成最后处理...';
        }
        
        // 触发进度更新事件
        emit('upload-progress', Math.round(uploadProgress.value), doclingStage.value);
      } else {
        clearInterval(progressInterval);
      }
    }, 200);
  } else {
    progressMessage.value = `正在上传文档... ${percentage}%`;
    emit('upload-progress', percentage, progressMessage.value);
  }
};

const handleFormatChange = (format: string) => {
  outputFormat.value = format;
  ElMessage.info(`已切换到 ${format} 格式`);
};

const handleCheckDocling = () => {
  emit('check-docling');
};

const handleCheckAI = () => {
  emit('check-ai');
};

const handleReset = () => {
  isCompleted.value = false;
  uploading.value = false;
  uploadProgress.value = 0;
  doclingProcessing.value = false;
  uploadedFileName.value = '';
  uploadResult.value = null;
  fileList.value = [];
  progressMessage.value = '';
  
  // 重置上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
  
  ElMessage.info('已重置，可以重新上传文档');
};

// Status methods
const getDoclingTagType = () => {
  if (props.checkingDocling) return 'info';
  return props.doclingAvailable ? 'success' : 'danger';
};

const getDoclingIcon = () => {
  if (props.checkingDocling) return ClockIcon;
  return props.doclingAvailable ? CheckCircleIcon : XCircleIcon;
};

const getDoclingStatusText = () => {
  if (props.checkingDocling) return '检查中';
  return props.doclingAvailable ? '就绪' : '不可用';
};

const getAITagType = () => {
  if (props.checkingAI) return 'info';
  return props.aiServiceAvailable ? 'success' : 'danger';
};

const getAIIcon = () => {
  if (props.checkingAI) return ClockIcon;
  return props.aiServiceAvailable ? CheckCircleIcon : XCircleIcon;
};

const getAIStatusText = () => {
  if (props.checkingAI) return '检查中';
  return props.aiServiceAvailable ? '就绪' : '不可用';
};

const getHostFromUrl = (url: string) => {
  if (!url) return 'Unknown';
  try {
    return new URL(url).host;
  } catch {
    return url;
  }
};
</script>

<style scoped>
/* 品牌色彩定义 */
:root {
  --changchun-blue: #5357A0;      /* 长春花蓝 - 主色 */
  --foshou-yellow: #fed81f;       /* 佛手黄 - 配色 */
  --light-blue: #eaebf8;          /* 浅蓝 - 背景色 */
  --dark-blue: #434683;           /* 深蓝 - 强调色 */
  --warm-white: #FEFEFE;          /* 温白 - 卡片背景 */
  --text-primary: #5357A0;        /* 主要文字 - 长春花蓝，用于标题和重要内容 */
  --text-secondary: #718096;      /* 次要文字 - 中性灰，用于描述和辅助信息 */
  --success: #48BB78;             /* 成功状态 */
  --warning: #ED8936;             /* 警告状态 */
  --error: #F56565;               /* 错误状态 */
}

.stage-card {
  height: 100%;
  border: 2px solid var(--foshou-yellow);
  background: var(--warm-white);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(254, 216, 31, 0.1);
}

.stage-card:hover {
  border-color: var(--changchun-blue);
  box-shadow: 0 4px 16px rgba(83, 87, 160, 0.15);
  transform: translateY(-1px);
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stage-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  background: var(--light-blue);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.stage-number.active {
  background: var(--changchun-blue);
  color: white;
  box-shadow: 0 0 0 3px rgba(83, 87, 160, 0.2);
}

.stage-number.completed {
  background: var(--success);
  color: white;
}

.stage-number.uploading {
  background: var(--foshou-yellow);
  color: var(--dark-blue);
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 3px rgba(254, 216, 31, 0.3);
}

.stage-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.stage-description {
  font-size: 14px;
  color: var(--text-secondary);
}

.upload-content {
  padding: 8px 0;
}

.upload-dragger {
  padding: 40px 20px;
  text-align: center;
  border: 2px dashed var(--foshou-yellow);
  border-radius: 8px;
  transition: all 0.3s ease;
  background: var(--light-blue);
}

.upload-dragger:hover {
  border-color: var(--changchun-blue);
  background: var(--warm-white);
  box-shadow: 0 2px 8px rgba(83, 87, 160, 0.1);
}

.upload-icon {
  color: var(--changchun-blue);
  margin-bottom: 16px;
}

.upload-text p {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.upload-text em {
  color: var(--changchun-blue);
  font-style: normal;
  font-weight: 600;
}

.upload-hint {
  margin-top: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.service-status {
  height: 100%;
  text-align: center;
}

.service-status h4 {
  color: var(--text-primary) !important;
  font-weight: 600;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-item .el-tag {
  padding: 8px 12px;
  height: auto;
  background: transparent !important;
  border: none !important;
}

.status-item .el-tag.el-tag--success {
  color: #22c55e !important;
  background: transparent !important;
}

.status-item .el-tag.el-tag--danger {
  color: #ef4444 !important;
  background: transparent !important;
}

.status-item .el-tag.el-tag--warning {
  color: #f59e0b !important;
  background: transparent !important;
}

.status-item .el-tag.el-tag--info {
  color: #3b82f6 !important;
  background: transparent !important;
}

.model-info {
  border: 1px solid #e5e7eb;
  text-align: center;
}

.format-selector .el-select {
  width: 100%;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>