<template>
  <el-card class="upload-card">
    <template #header>
      <div class="upload-header">
        <div class="flex items-center">
          <DocumentIcon class="h-4 w-4 mr-2" />
          <span>PDF文档上传</span>
        </div>
        <el-button
          size="small"
          @click="checkDoclingStatus"
          :loading="checkingDocling"
          type="primary"
        >
          检查Docling服务
        </el-button>
      </div>
    </template>

    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      action=""
      :file-list="fileList"
      :before-upload="handleBeforeUpload"
      :on-success="onUploadSuccess"
      :on-error="onUploadError"
      :on-progress="onUploadProgress"
      :on-remove="onRemove"
      :auto-upload="false"
      accept=".pdf"
      :limit="1"
    >
      <el-icon class="el-icon--upload"><Upload /></el-icon>
      <div class="el-upload__text">
        将PDF文件拖拽到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">只能上传PDF文件，且不超过10MB</div>
      </template>
    </el-upload>

    <!-- 格式选择器 -->
    <div class="format-selector">
      <span class="format-label">输出格式:</span>
      <div class="format-selector-inline">
        <span>Markdown</span>
        <el-switch
          :model-value="isMarkdownFormat"
          @change="onFormatChange"
          active-text=""
          inactive-text=""
        />
        <span>JSON</span>
      </div>
    </div>

    <!-- 上传按钮和进度 -->
    <div class="upload-actions">
      <el-button
        type="primary"
        @click="submitUpload"
        :disabled="fileList.length === 0 || uploading"
        :loading="uploading"
      >
        {{ uploading ? '上传中...' : '开始上传' }}
      </el-button>

      <div v-if="uploading" class="upload-progress">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadProgress === 100 ? 'success' : 'warning'"
        />
        <span class="progress-text">{{ progressMessage }}</span>
      </div>
    </div>

    <!-- Docling处理进度 -->
    <div v-if="processingDocling" class="docling-progress">
      <el-alert
        title="正在使用Docling处理PDF文档"
        type="info"
        :closable="false"
      >
        <div class="processing-steps">
          <el-steps :active="currentStep" direction="vertical" size="small">
            <el-step
              v-for="(step, index) in processingSteps"
              :key="index"
              :title="step.title"
              :description="step.description"
              :status="step.status"
            />
          </el-steps>
        </div>
      </el-alert>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineProps } from 'vue';
import { ElMessage } from 'element-plus';
import { Upload } from '@element-plus/icons-vue';
import { DocumentIcon } from '@heroicons/vue/24/outline';

// Props
interface Props {
  fileList: any[];
  uploading: boolean;
  uploadProgress: number;
  outputFormat: string;
  checkingDocling: boolean;
  processingDocling: boolean;
  processingSteps: any[];
  progressMessage: string;
  doclingAvailable: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  fileList: () => [],
  uploading: false,
  uploadProgress: 0,
  outputFormat: 'markdown',
  checkingDocling: false,
  processingDocling: false,
  processingSteps: () => [],
  progressMessage: '正在解析PDF文件...',
  doclingAvailable: false,
});

// Emits
const emit = defineEmits([
  'upload-success',
  'upload-error',
  'upload-progress',
  'before-upload',
  'remove-file',
  'format-change',
  'submit-upload',
  'check-docling',
]);

// Computed
const isMarkdownFormat = computed({
  get: () => props.outputFormat === 'markdown',
  set: value => emit('format-change', value ? 'markdown' : 'json'),
});

const currentStep = computed(() => {
  return props.processingSteps.findIndex(step => step.status === 'process');
});

// Methods
const handleBeforeUpload = (file: File) => {
  emit('before-upload', file);
  return false; // 阻止自动上传
};

const onUploadSuccess = (response: any) => {
  emit('upload-success', response);
};

const onUploadError = (error: any) => {
  emit('upload-error', error);
};

const onUploadProgress = (event: any) => {
  emit('upload-progress', event);
};

const onRemove = (file: any) => {
  emit('remove-file', file);
};

const onFormatChange = (value: boolean) => {
  emit('format-change', value ? 'markdown' : 'json');
};

const submitUpload = () => {
  emit('submit-upload');
};

const checkDoclingStatus = () => {
  emit('check-docling');
};
</script>

<style scoped>
.upload-card {
  margin-bottom: 20px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.h-4 {
  height: 1rem;
}

.w-4 {
  width: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-demo {
  margin-bottom: 20px;
}

.format-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.format-label {
  font-weight: 500;
  color: #606266;
}

.format-selector-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.format-selector-inline span {
  font-size: 14px;
  color: #606266;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.docling-progress {
  margin-top: 20px;
}

.processing-steps {
  margin-top: 12px;
}
</style>
