<template>
  <div>
    <el-table
      :data="recentScales"
      v-loading="loading"
      element-loading-text="加载中..."
      class="w-full"
    >
      <el-table-column prop="name" label="量表名称" min-width="200">
        <template #default="{ row }">
          <div class="flex items-center space-x-2">
            <ClipboardDocumentListIcon class="h-5 w-5 text-primary-600" />
            <div>
              <div class="font-medium text-primary-700">{{ row.name }}</div>
              <div class="text-xs text-gray-500">{{ row.code }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="类型" width="120">
        <template #default="{ row }">
          <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full">{{ getTypeLabel(row.type) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="version" label="版本" width="80">
        <template #default="{ row }">
          <span class="text-xs text-gray-600">v{{ row.version }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="创建时间" width="160">
        <template #default="{ row }">
          <span class="text-xs text-gray-600">
            {{ formatDate(row.createdAt) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <div class="space-x-1">
            <el-button
              size="small"
              type="primary"
              @click="$emit('load-scale', row)"
            >
              <div class="flex items-center">
                <BookOpenIcon class="h-4 w-4 mr-1" />
                <span>加载</span>
              </div>
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              <div class="flex items-center">
                <TrashIcon class="h-4 w-4 mr-1" />
                <span>删除</span>
              </div>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-center mt-4" v-if="total > pageSize">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="$emit('page-change', $event)"
        @size-change="$emit('size-change', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus';
import { 
  ClipboardDocumentListIcon, 
  BookOpenIcon, 
  TrashIcon 
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  recentScales: any[];
  loading: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
}

defineProps<Props>();

// Emits
const emit = defineEmits<{
  'load-scale': [scale: any];
  'delete-scale': [id: string];
  'page-change': [page: number];
  'size-change': [size: number];
}>();

// Methods
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    ELDERLY_ABILITY: '老年人能力',
    EMOTION_QUICK: '情绪快评',
    INTERRAI: 'interRAI',
    LONG_CARE: '长护险',
    CUSTOM: '自定义',
  };
  return typeMap[type] || type;
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除量表"${row.name}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    emit('delete-scale', row.id);
  } catch {
    // 用户取消删除
  }
};
</script>
