<template>
  <div class="scale-management">
    <!-- 顶部导航栏 -->
    <TopNavbar title="评估量表管理" />
    
    <el-card style="margin: 16px;">
      <template #header>
        <div class="card-header">
          <div class="flex items-center">
            <ClipboardDocumentListIcon class="h-5 w-5 text-primary-700 mr-2" />
            <h3>评估量表管理</h3>
          </div>
          <el-button type="primary" @click="goToUpload">
            <div class="flex items-center">
              <ArrowUpTrayIcon class="h-4 w-4 mr-1" />
              <span>上传新量表</span>
            </div>
          </el-button>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索量表名称或代码"
              clearable
              @change="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.type"
              placeholder="量表类型"
              clearable
              @change="handleSearch"
            >
              <el-option label="老年人能力评估" value="ELDERLY_ABILITY" />
              <el-option label="情绪快评" value="EMOTIONAL_QUICK" />
              <el-option label="interRAI评估" value="INTER_RAI" />
              <el-option label="长护险评估" value="LONG_CARE_INSURANCE" />
              <el-option label="自定义量表" value="CUSTOM" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.status"
              placeholder="解析状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="解析成功" value="SUCCESS" />
              <el-option label="待解析" value="PENDING" />
              <el-option label="解析中" value="PARSING" />
              <el-option label="解析失败" value="FAILED" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.isActive"
              placeholder="启用状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="已启用" :value="true" />
              <el-option label="已停用" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <div class="flex items-center">
                <MagnifyingGlassIcon class="h-4 w-4 mr-1" />
                <span>搜索</span>
              </div>
            </el-button>
            <el-button @click="resetSearch">
              <div class="flex items-center">
                <ArrowPathIcon class="h-4 w-4 mr-1" />
                <span>重置</span>
              </div>
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 量表列表 -->
      <el-table
        :data="scales"
        v-loading="loading"
        stripe
        style="width: 100%"
        class="scale-table"
      >
        <el-table-column
          prop="name"
          label="量表名称"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="code" label="代码" width="150" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" size="small">
              {{ getTypeDisplayName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column prop="estimatedDuration" label="时长" width="80">
          <template #default="{ row }">
            {{ row.estimatedDuration }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="usageCount" label="使用次数" width="100" />
        <el-table-column prop="parseStatus" label="解析状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.parseStatus)" size="small">
              {{ getStatusDisplayName(row.parseStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="toggleActive(row)"
              :loading="row.switching"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewScale(row)">
              <div class="flex items-center">
                <EyeIcon class="h-4 w-4 mr-1" />
                <span>查看</span>
              </div>
            </el-button>
            <el-button type="primary" size="small" @click="editScale(row)">
              <div class="flex items-center">
                <PencilIcon class="h-4 w-4 mr-1" />
                <span>编辑</span>
              </div>
            </el-button>
            <el-button type="danger" size="small" @click="deleteScale(row)">
              <div class="flex items-center">
                <TrashIcon class="h-4 w-4 mr-1" />
                <span>删除</span>
              </div>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > pageSize"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="onPageSizeChange"
        @current-change="onCurrentPageChange"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import TopNavbar from '@/components/TopNavbar.vue';
import {
  ClipboardDocumentListIcon,
  ArrowUpTrayIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/vue/24/outline';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const scales = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

const searchForm = reactive({
  keyword: '',
  type: '',
  status: '',
  isActive: null,
});

// 获取量表列表
const getScales = async () => {
  try {
    loading.value = true;

    const params = {
      page: currentPage.value - 1, // 后端从0开始
      size: pageSize.value,
      ...searchForm,
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key];
      }
    });

    // const response = await assessmentScaleApi.getScales(params)

    // 模拟数据
    scales.value = [
      {
        id: '1',
        name: 'Test Assessment PDF',
        code: 'TESTASSESSMENTPDF',
        type: 'CUSTOM',
        version: '1.0',
        estimatedDuration: 5,
        usageCount: 0,
        parseStatus: 'SUCCESS',
        isActive: true,
        createdAt: new Date().toISOString(),
        switching: false,
      },
      {
        id: '2',
        name: '民政部老年人能力评估标准',
        code: 'ELDERLY_ABILITY_STANDARD',
        type: 'ELDERLY_ABILITY',
        version: '2.1',
        estimatedDuration: 45,
        usageCount: 156,
        parseStatus: 'SUCCESS',
        isActive: true,
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        switching: false,
      },
      {
        id: '3',
        name: 'interRAI综合评估工具',
        code: 'INTER_RAI_COMPREHENSIVE',
        type: 'INTER_RAI',
        version: '1.0',
        estimatedDuration: 60,
        usageCount: 89,
        parseStatus: 'SUCCESS',
        isActive: true,
        createdAt: new Date(Date.now() - 172800000).toISOString(),
        switching: false,
      },
    ];
    total.value = 3;
  } catch (error) {
    console.error('获取量表列表失败:', error);
    ElMessage.error('获取量表列表失败!');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  getScales();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    type: '',
    status: '',
    isActive: null,
  });
  handleSearch();
};

// 分页处理
const onPageSizeChange = newPageSize => {
  pageSize.value = newPageSize;
  getScales();
};

const onCurrentPageChange = newPage => {
  currentPage.value = newPage;
  getScales();
};

// 切换启用状态
const toggleActive = async scale => {
  try {
    scale.switching = true;

    // await assessmentScaleApi.toggleScale(scale.id, scale.isActive)

    ElMessage.success(`量表已${scale.isActive ? '启用' : '停用'}`);
  } catch (error) {
    // 恢复原状态
    scale.isActive = !scale.isActive;
    ElMessage.error('状态切换失败!');
  } finally {
    scale.switching = false;
  }
};

// 操作方法
const goToUpload = () => {
  router.push('/assessment/pdf-upload');
};

const viewScale = scale => {
  ElMessage.info(`查看量表: ${scale.name}`);
  // 这里可以跳转到量表详情页或打开对话框
};

const editScale = scale => {
  ElMessage.info(`编辑量表: ${scale.name}`);
  // 这里可以跳转到量表编辑页或打开对话框
};

const deleteScale = async scale => {
  try {
    await ElMessageBox.confirm(
      `确定要删除量表 "${scale.name}" 吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // await assessmentScaleApi.deleteScale(scale.id)

    ElMessage.success('删除成功!');
    getScales();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败!');
    }
  }
};

// 工具方法
const getTypeTagType = type => {
  const typeMap = {
    ELDERLY_ABILITY: 'primary',
    EMOTIONAL_QUICK: 'success',
    INTER_RAI: 'warning',
    LONG_CARE_INSURANCE: 'info',
    CUSTOM: 'default',
  };
  return typeMap[type] || 'default';
};

const getTypeDisplayName = type => {
  const typeMap = {
    ELDERLY_ABILITY: '老年人能力评估',
    EMOTIONAL_QUICK: '情绪快评',
    INTER_RAI: 'interRAI评估',
    LONG_CARE_INSURANCE: '长护险评估',
    CUSTOM: '自定义量表',
  };
  return typeMap[type] || type;
};

const getStatusTagType = status => {
  const statusMap = {
    SUCCESS: 'success',
    PENDING: 'warning',
    PARSING: 'primary',
    FAILED: 'danger',
    REVIEWING: 'info',
    APPROVED: 'success',
  };
  return statusMap[status] || 'default';
};

const getStatusDisplayName = status => {
  const statusMap = {
    SUCCESS: '解析成功',
    PENDING: '待解析',
    PARSING: '解析中',
    FAILED: '解析失败',
    REVIEWING: '人工审核中',
    APPROVED: '已审核通过',
  };
  return statusMap[status] || status;
};

const formatDateTime = dateString => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// 组件挂载
onMounted(() => {
  getScales();
});
</script>

<style scoped>
.scale-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.search-filters {
  margin: 20px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.scale-table {
  margin: 20px 0;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #67c23a;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style>
