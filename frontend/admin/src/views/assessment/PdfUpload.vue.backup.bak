<template>
  <div class="pdf-upload-container">
    
    <!-- 紧凑的顶部控制区域 -->
    <el-card class="upload-header-card" style="margin-bottom: 16px;">
      <el-row :gutter="16" align="middle">
        <el-col :span="8">
          <div class="page-title">
            <h3>📄 文档解析与编辑</h3>
            <span class="subtitle">上传多格式文档，Docling AI智能解析，人工精确校对</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="upload-section">
            <!-- 文件上传 -->
            <el-upload
              ref="uploadRef"
              class="compact-upload"
              :action="uploadUrl"
              :data="uploadData"
              :headers="uploadHeaders"
              :before-upload="beforeUpload"
              :on-success="onUploadSuccess"
              :on-error="onUploadError"
              :on-progress="onUploadProgress"
              :file-list="fileList"
              :limit="1"
              :accept="acceptedFileTypes"
              drag
            >
              <el-button type="primary" size="large">
                <el-icon><Upload /></el-icon>
                选择文档文件
              </el-button>
              <template #tip>
                <div class="upload-tip">
                  <div>📁 支持格式：PDF, DOCX, XLSX, HTML, 图片文件</div>
                  <div>📊 最大大小：50MB</div>
                  <div class="format-selector-inline">
                    <span>🚀 输出格式：</span>
                    <el-select v-model="outputFormat" placeholder="选择输出格式" size="small" style="width: 240px;">
                      <el-option label="📝 Markdown (推荐)" value="markdown" />
                      <el-option label="🌐 HTML" value="html" />
                      <el-option label="📋 JSON" value="json" />
                    </el-select>
                  </div>
                </div>
              </template>
            </el-upload>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="status-section">
            <!-- 服务状态区域 -->
            <div class="status-actions">
              <!-- Docling引擎状态 -->
              <el-tag 
                :type="doclingAvailable ? 'success' : 'warning'" 
                size="large"
                class="status-tag"
                style="margin-right: 8px;"
              >
                {{ doclingAvailable ? '🟢 Docling引擎就绪' : '🟡 Docling引擎检查中' }}
              </el-tag>
              
              <!-- AI分析服务状态 -->
              <el-tag 
                :type="aiServiceAvailable ? 'success' : 'warning'" 
                size="large"
                class="status-tag"
                style="margin-right: 8px;"
              >
                {{ aiServiceAvailable ? '🤖 LM Studio AI就绪' : '🟡 LM Studio AI检查中' }}
              </el-tag>
              
              <div class="action-buttons" style="margin-top: 8px;">
                <el-button size="small" @click="checkDoclingStatus" :loading="checkingDocling">
                  🔄 检查Docling
                </el-button>
                <el-button size="small" @click="checkAIStatus" :loading="checkingAI" type="primary">
                  🧠 检查AI服务
                </el-button>
              </div>
              
              <!-- AI服务详细信息 -->
              <div class="ai-service-info" style="margin-top: 8px; font-size: 12px; color: #666;">
                <template v-if="modelInfo">
                  <div v-if="modelInfo.displayName">
                    🚀 {{ modelInfo.displayName }} @ {{ getHostFromUrl(modelInfo.url) }}
                  </div>
                  <div v-else-if="modelInfo.id">
                    🚀 {{ modelInfo.id }} @ {{ getHostFromUrl(modelInfo.url) }}
                  </div>
                  <div v-else>
                    🚀 模型已连接 @ {{ getHostFromUrl(modelInfo.url) }}
                  </div>
                </template>
                <div v-else>🚀 正在获取模型信息...</div>
                <div>🔧 Docling @ localhost:8088</div>
              </div>
            </div>
            
            <!-- 快速开始指南 -->
            <div class="quick-start-guide">
              <el-divider content-position="left">📝 快速开始</el-divider>
              <div class="quick-steps">
                <div class="step-item">
                  <span class="step-number">1️⃣</span>
                  <span class="step-text">上传PDF、Word、Excel或图片文件</span>
                </div>
                <div class="step-item">
                  <span class="step-number">2️⃣</span>
                  <span class="step-text">AI自动解析并识别量表结构</span>
                </div>
                <div class="step-item">
                  <span class="step-number">3️⃣</span>
                  <span class="step-text">编辑完善量表信息和字段</span>
                </div>
                <div class="step-item">
                  <span class="step-number">4️⃣</span>
                  <span class="step-text">保存到数据库供评估使用</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 上传和解析进度 -->
      <div v-if="uploading || processingDocling" class="upload-progress-compact">
        <!-- 上传进度 -->
        <div v-if="uploading" class="progress-section">
          <div class="progress-header">
            <span class="progress-label">📤 文件上传进度</span>
            <span class="progress-percentage">{{ Math.round(uploadProgress) }}%</span>
          </div>
          <el-progress
            :percentage="uploadProgress"
            :stroke-width="8"
            :show-text="false"
            :status="progressStatus"
            class="progress-bar"
          />
        </div>

        <!-- 解析进度 -->
        <div v-if="processingDocling" class="progress-section">
          <div class="progress-header">
            <span class="progress-label">🤖 AI解析进度</span>
            <div class="progress-info">
              <span class="progress-percentage">{{ Math.round(parseProgress) }}%</span>
              <span class="progress-time">{{ parseElapsedTime }}s</span>
            </div>
          </div>
          <el-progress
            :percentage="parseProgress"
            :stroke-width="8"
            :show-text="false"
            :status="parseProgress === 100 ? 'success' : ''"
            class="progress-bar"
          />
          <div class="progress-stage">
            <span class="stage-text">{{ parseStage }}</span>
          </div>
        </div>

        <!-- 处理步骤时间线 -->
        <div v-if="processingSteps.length > 0" class="processing-timeline">
          <el-collapse v-model="timelineExpanded" accordion>
            <el-collapse-item name="timeline">
              <template #title>
                <span class="timeline-title">
                  📋 处理步骤详情 ({{ processingSteps.length }}步)
                </span>
              </template>
              <el-timeline size="small">
                <el-timeline-item
                  v-for="(step, index) in processingSteps"
                  :key="index"
                  :timestamp="step.timestamp"
                  :type="getValidTimelineType(step.type)"
                  :hollow="step.status === 'pending'"
                >
                  <template #dot>
                    <span class="timeline-icon">{{ step.icon }}</span>
                  </template>
                  <div class="timeline-content">
                    <div class="step-message">{{ step.message }}</div>
                    <div v-if="step.detail" class="step-detail">{{ step.detail }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-card>

    <!-- 主要工作区域 -->
    <el-row :gutter="16" class="main-workspace" style="margin: 0 16px; flex: 1;">
      <!-- 左侧：解析内容编辑器 (主要区域) -->
      <el-col :span="18">
        <el-card class="editor-card" style="height: calc(100vh - 200px);">
          <template #header>
            <div class="editor-header">
              <div class="editor-title">
                <span v-if="!parseResult">📄 等待解析结果...</span>
                <span v-else>📝 {{ parseResult.name || '未命名量表' }} - 智能解析结果</span>
              </div>
              <div class="editor-actions">
                <el-button-group>
                  <el-button 
                    size="small" 
                    :type="editMode === 'preview' ? 'primary' : ''"
                    @click="editMode = 'preview'"
                  >
                    👁️ 预览
                  </el-button>
                  <el-button 
                    size="small" 
                    :type="editMode === 'edit' ? 'primary' : ''"
                    @click="editMode = 'edit'"
                  >
                    ✏️ 编辑
                  </el-button>
                  <el-button 
                    size="small" 
                    :type="editMode === 'split' ? 'primary' : ''"
                    @click="editMode = 'split'"
                  >
                    🔀 分屏
                  </el-button>
                </el-button-group>
                
                <el-button 
                  v-if="parseResult && rawMarkdown"
                  size="small" 
                  type="success" 
                  @click="downloadMarkdown"
                >
                  📥 下载MD文件
                </el-button>
                
                <el-button 
                  v-if="parseResult"
                  size="small" 
                  type="primary" 
                  @click="confirmSave"
                  :loading="saving"
                >
                  💾 保存到数据库
                </el-button>
              </div>
            </div>
          </template>

          <!-- 内容编辑区域 -->
          <div class="editor-content" v-if="!parseResult">
            <el-empty 
              description="请上传PDF文件开始解析" 
              :image-size="200"
            >
              <template #description>
                <p>支持上传多种格式的评估量表文档</p>
                <p>系统将使用AI引擎自动解析文档结构</p>
              </template>
            </el-empty>
          </div>

          <!-- 编辑模式 -->
          <div v-else-if="editMode === 'edit'" class="editor-content">
            <el-input
              v-model="editableMarkdown"
              type="textarea"
              :rows="30"
              placeholder="Markdown内容编辑区域..."
              class="markdown-editor"
              resize="none"
            />
          </div>

          <!-- 预览模式 -->
          <div v-else-if="editMode === 'preview'" class="editor-content">
            <div class="markdown-preview" v-html="renderedMarkdown"></div>
          </div>

          <!-- 分屏模式 -->
          <div v-else-if="editMode === 'split'" class="editor-content split-mode">
            <div class="split-left">
              <div class="split-header">📝 编辑区</div>
              <el-input
                v-model="editableMarkdown"
                type="textarea"
                :rows="25"
                placeholder="Markdown内容编辑区域..."
                class="markdown-editor"
                resize="none"
              />
            </div>
            <div class="split-right">
              <div class="split-header">👁️ 预览区</div>
              <div class="markdown-preview" v-html="renderedMarkdown"></div>
            </div>
          </div>
        </el-card>

        <!-- 数据库结构确认面板 - 始终显示 -->
        <el-card class="database-structure-card" style="margin-top: 16px;">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <span>🗄️ 数据库结构分析</span>
                <el-tag size="small" type="success" style="margin-left: 8px;">
                  🤖 DeepSeek-R1 + LM Studio
                </el-tag>
                <el-tag size="small" type="info" style="margin-left: 8px;">
                  📊 已验证 136/136分
                </el-tag>
              </div>
              <div>
                <el-button 
                  size="small" 
                  @click="togglePromptEditor"
                  :type="showPromptEditor ? 'warning' : 'info'"
                >
                  {{ showPromptEditor ? '🔒 收起提示词' : '📝 编辑提示词' }}
                </el-button>
                <el-button 
                  v-if="!aiAnalyzing"
                  size="small" 
                  type="primary" 
                  @click="analyzeWithAI"
                  :disabled="!parseResult || !editableMarkdown"
                  icon="el-icon-magic-stick"
                >
                  AI智能分析
                </el-button>
                <el-button 
                  v-else
                  size="small" 
                  type="danger" 
                  @click="cancelAIAnalysis"
                  icon="el-icon-close"
                >
                  取消分析
                </el-button>
                <el-button 
                  size="small" 
                  type="info" 
                  @click="openAIChat"
                  :disabled="!parseResult || !editableMarkdown"
                  icon="el-icon-chat-dot-square"
                >
                  AI对话助手
                </el-button>
                <el-button 
                  size="small" 
                  type="success" 
                  @click="confirmDatabaseStructure"
                  :disabled="!databaseStructure.fields.length"
                >
                  确认入库
                </el-button>
              </div>
            </div>
          </template>

          <div class="database-content">
            <!-- 提示词编辑器 -->
            <div v-show="showPromptEditor" class="prompt-editor-section" style="margin-bottom: 16px;">
              <el-alert
                title="🎯 AI提示词配置 - 使用已验证的高性能提示词"
                type="success"
                :closable="false"
                show-icon
                style="margin-bottom: 12px;"
              >
                <template #default>
                  <div class="prompt-info">
                    <p><strong>🏆 当前提示词状态:</strong> 已验证最佳版本 (136/136分满分)</p>
                    <p><strong>🚀 模型配置:</strong> deepseek-r1-0528-qwen3-8b-mlx@8bit + LM Studio官方参数</p>
                    <p><strong>⚡ 处理时间:</strong> 约3-4分钟 (充分利用深度推理)</p>
                    <p><strong>💡 提示:</strong> 可以根据特定需求微调提示词，但当前版本已经过充分测试</p>
                  </div>
                </template>
              </el-alert>
              
              <div class="prompt-controls" style="margin-bottom: 12px;">
                <el-button-group>
                  <el-button 
                    size="small" 
                    type="success"
                    @click="resetToOptimalPrompt"
                  >
                    📋 使用最佳提示词
                  </el-button>
                  <el-button 
                    size="small" 
                    type="primary"
                    @click="previewCurrentPrompt"
                  >
                    👁️ 预览当前提示词
                  </el-button>
                  <el-button 
                    size="small" 
                    type="warning"
                    @click="saveCustomPrompt"
                    :disabled="!customPrompt.trim()"
                  >
                    💾 保存自定义提示词
                  </el-button>
                </el-button-group>
              </div>
              
              <el-input
                v-model="customPrompt"
                type="textarea"
                :rows="15"
                placeholder="在此编辑AI分析提示词..."
                class="prompt-textarea"
                resize="vertical"
                style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace;"
              >
                <template #prepend>
                  <span style="padding: 0 8px;">🤖 提示词内容</span>
                </template>
              </el-input>
              
              <div class="prompt-stats" style="margin-top: 8px; font-size: 12px; color: #666;">
                <span>📊 字符数: {{ customPrompt.length }}</span>
                <span style="margin-left: 16px;">📝 预估Token: {{ Math.ceil(customPrompt.length / 3) }}</span>
                <span style="margin-left: 16px;">⏱️ 预估处理时间: {{ Math.max(2, Math.ceil(customPrompt.length / 8000)) }}分钟</span>
              </div>
            </div>
            
            <!-- AI功能介绍 -->
            <div v-if="!parseResult" class="ai-intro-section">
              <el-alert
                title="🧠 AI智能数据库结构分析 - 自动将评估量表转换为数据库表结构"
                type="success"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div class="ai-intro">
                    <div class="feature-highlight">
                      <h4>核心功能：智能解析评估量表并生成数据库表结构</h4>
                      <p class="feature-desc">通过AI分析PDF文档中的评估量表，自动识别字段、选项、数据类型，生成标准化的数据库表结构，实现评估数据的规范化存储。</p>
                    </div>
                    
                    <div class="capabilities-grid">
                      <div class="capability-item">
                        <span class="capability-icon">📋</span>
                        <div>
                          <strong>字段识别</strong>
                          <p>自动识别量表中的评估项目、选项和权重</p>
                        </div>
                      </div>
                      <div class="capability-item">
                        <span class="capability-icon">🔍</span>
                        <div>
                          <strong>类型推断</strong>
                          <p>智能推断字段数据类型和合适的字段长度</p>
                        </div>
                      </div>
                      <div class="capability-item">
                        <span class="capability-icon">📝</span>
                        <div>
                          <strong>规范命名</strong>
                          <p>生成标准的表名、字段名和详细注释</p>
                        </div>
                      </div>
                      <div class="capability-item">
                        <span class="capability-icon">⚡</span>
                        <div>
                          <strong>SQL生成</strong>
                          <p>一键生成完整的CREATE TABLE语句</p>
                        </div>
                      </div>
                    </div>
                    
                    <div class="usage-steps">
                      <h5>📝 使用步骤：</h5>
                      <ol>
                        <li>上传包含评估量表的多格式文档</li>
                        <li>等待Docling AI完成文档解析</li>
                        <li>点击"AI智能分析"按钮分析表结构</li>
                        <li>查看并确认生成的数据库结构</li>
                        <li>点击"确认入库"保存量表配置</li>
                      </ol>
                    </div>
                    
                    <div class="tech-info">
                      <template v-if="modelInfo">
                        <p v-if="modelInfo.displayName">
                          <strong>🚀 技术栈:</strong> LM Studio + {{ modelInfo.displayName }}本地大模型 ({{ getHostFromUrl(modelInfo.url) }})
                        </p>
                        <p v-else-if="modelInfo.id">
                          <strong>🚀 技术栈:</strong> LM Studio + {{ modelInfo.id }}本地大模型 ({{ getHostFromUrl(modelInfo.url) }})
                        </p>
                        <p v-else>
                          <strong>🚀 技术栈:</strong> LM Studio 本地大模型 ({{ getHostFromUrl(modelInfo.url) }})
                        </p>
                      </template>
                      <p v-else>
                        <strong>🚀 技术栈:</strong> LM Studio 本地大模型 (正在获取模型信息...)
                      </p>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>

            <!-- AI流式分析输出 -->
            <div v-if="aiAnalyzing || streamOutput" class="ai-stream-section">
              <el-card class="stream-card" shadow="hover">
                <template #header>
                  <div class="stream-header">
                    <span class="stream-title">
                      <i class="el-icon-cpu" style="margin-right: 8px;"></i>
                      🤖 AI实时分析过程
                    </span>
                    <el-tag v-if="aiAnalyzing" type="success" effect="plain" size="small">
                      <i class="el-icon-loading" style="margin-right: 4px;"></i>
                      分析中...
                    </el-tag>
                    <el-tag v-else type="info" effect="plain" size="small">
                      分析完成
                    </el-tag>
                  </div>
                </template>
                
                <div class="stream-content">
                  <div class="stream-output" ref="streamOutputRef">
                    <pre class="stream-text">{{ streamOutput || '等待AI开始分析...' }}</pre>
                  </div>
                  
                  <!-- AI实时生成内容区域 - 块状显示 -->
                  <div v-if="contentBlocks.length > 0" class="ai-generated-section">
                    <el-divider content-position="left">
                      <span style="color: #1976d2; font-weight: bold;">🤖 AI智能分析结果</span>
                    </el-divider>
                    <div class="ai-generated-content">
                      <ContentBlock 
                        v-for="block in contentBlocks" 
                        :key="block.id" 
                        :block="block"
                      />
                    </div>
                    <div class="ai-content-actions" style="margin-top: 16px; text-align: right;">
                      <el-button size="small" @click="copyAllContent">📋 复制全部</el-button>
                      <el-button size="small" @click="copyCodeBlocks" :disabled="!hasCodeBlocks">📝 复制代码</el-button>
                      <el-button size="small" type="primary" @click="parseAIResult">🔄 解析结果</el-button>
                    </div>
                  </div>
                  
                  <!-- 兼容性：保留原始AI内容显示 -->
                  <div v-else-if="aiGeneratedContent" class="ai-generated-section">
                    <el-divider content-position="left">
                      <span style="color: #1976d2; font-weight: bold;">🤖 AI实时生成内容</span>
                    </el-divider>
                    <div class="ai-generated-content">
                      <pre class="ai-generated-text">{{ aiGeneratedContent }}</pre>
                    </div>
                  </div>
                  
                  <div class="stream-actions" style="margin-top: 12px; text-align: right;">
                    <el-button 
                      size="small" 
                      type="info" 
                      @click="clearStreamOutput"
                      :disabled="aiAnalyzing"
                      icon="el-icon-delete"
                    >
                      清空输出
                    </el-button>
                    <el-button 
                      size="small" 
                      type="primary" 
                      @click="copyStreamOutput"
                      :disabled="!streamOutput.trim()"
                      icon="el-icon-document-copy"
                    >
                      复制输出
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>

            <!-- AI分析建议 -->
            <div v-if="aiAnalysisResult" class="ai-analysis-section">
              <el-alert
                title="🤖 AI分析建议"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div class="ai-suggestions">
                    <p><strong>量表类型:</strong> {{ aiAnalysisResult.scaleType }}</p>
                    <p><strong>建议表名:</strong> {{ aiAnalysisResult.tableName }}</p>
                    <p><strong>识别字段:</strong> {{ aiAnalysisResult.fieldCount }} 个</p>
                    <p><strong>分析置信度:</strong> {{ aiAnalysisResult.confidence }}%</p>
                    <p><strong>分析时间:</strong> {{ aiAnalysisResult.analysisTimeMs }}ms</p>
                  </div>
                </template>
              </el-alert>
              
              <!-- AI生成的SQL语句 -->
              <div v-if="aiAnalysisResult.sqlStatements" class="ai-sql-section" style="margin-top: 16px;">
                <el-card>
                  <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                      <span>🗃️ AI生成的PostgreSQL建表语句</span>
                      <div>
                        <el-button size="small" @click="copyAISQL" type="primary">
                          <el-icon><CopyDocument /></el-icon>
                          复制SQL
                        </el-button>
                        <el-button size="small" @click="applySQLToFields" type="success">
                          <el-icon><Check /></el-icon>
                          应用到字段
                        </el-button>
                      </div>
                    </div>
                  </template>
                  <div class="sql-display">
                    <pre><code>{{ aiAnalysisResult.sqlStatements }}</code></pre>
                  </div>
                </el-card>
              </div>
            </div>

            <!-- 表基本信息 -->
            <el-form :model="databaseStructure" label-width="80px" size="small" style="margin-top: 16px;">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="表名">
                    <el-input 
                      v-model="databaseStructure.tableName" 
                      placeholder="如: assessment_scale_xxx"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="表注释">
                    <el-input 
                      v-model="databaseStructure.tableComment" 
                      placeholder="量表描述"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <!-- 字段列表 -->
            <el-divider content-position="left">📋 字段定义</el-divider>
            <div class="fields-section">
              <div class="fields-toolbar">
                <el-button size="small" @click="addField" icon="el-icon-plus">添加字段</el-button>
                <el-button size="small" @click="autoDetectFields" :loading="detectingFields">自动检测字段</el-button>
                <span class="field-count">共 {{ databaseStructure.fields.length }} 个字段</span>
              </div>

              <el-table 
                :data="databaseStructure.fields" 
                size="small" 
                border
                style="margin-top: 12px;"
                max-height="300"
              >
                <el-table-column prop="name" label="字段名" width="120">
                  <template #default="{ row, $index }">
                    <el-input 
                      v-model="row.name" 
                      size="small" 
                      placeholder="field_name"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="{ row, $index }">
                    <el-select v-model="row.type" size="small">
                      <el-option label="VARCHAR" value="VARCHAR" />
                      <el-option label="TEXT" value="TEXT" />
                      <el-option label="INT" value="INT" />
                      <el-option label="DECIMAL" value="DECIMAL" />
                      <el-option label="DATE" value="DATE" />
                      <el-option label="DATETIME" value="DATETIME" />
                      <el-option label="BOOLEAN" value="BOOLEAN" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="length" label="长度" width="80">
                  <template #default="{ row, $index }">
                    <el-input 
                      v-model="row.length" 
                      size="small" 
                      v-if="['VARCHAR', 'DECIMAL'].includes(row.type)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="nullable" label="允许空" width="80" align="center">
                  <template #default="{ row, $index }">
                    <el-checkbox v-model="row.nullable" />
                  </template>
                </el-table-column>
                <el-table-column prop="comment" label="注释" min-width="150">
                  <template #default="{ row, $index }">
                    <el-input 
                      v-model="row.comment" 
                      size="small" 
                      placeholder="字段说明"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="default" label="默认值" width="100">
                  <template #default="{ row, $index }">
                    <el-input 
                      v-model="row.defaultValue" 
                      size="small" 
                      placeholder="默认值"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template #default="{ row, $index }">
                    <el-button 
                      size="small" 
                      type="danger" 
                      @click="removeField($index)"
                      icon="el-icon-delete"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- SQL预览 -->
            <el-divider content-position="left" v-if="databaseStructure.fields.length">💻 SQL预览</el-divider>
            <div v-if="databaseStructure.fields.length" class="sql-preview">
              <el-input
                v-model="generatedSQL"
                type="textarea"
                :rows="8"
                readonly
                placeholder="生成的SQL语句将在这里显示..."
              />
              <div class="sql-actions" style="margin-top: 8px;">
                <el-button size="small" @click="copySQL">复制SQL</el-button>
                <el-button size="small" type="primary" @click="executeSQL">执行建表</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：属性编辑和操作面板 -->
      <el-col :span="6">
        <el-card class="properties-card" style="height: calc(100vh - 200px);">
          <template #header>
            <span>⚙️ 量表属性</span>
          </template>

          <div class="properties-content">

            <!-- 基本信息编辑 -->
            <el-form v-if="parseResult" :model="parseResult" label-width="80px" size="small">
              <el-form-item label="量表名称" prop="name">
                <el-input 
                  v-model="parseResult.name" 
                  placeholder="请输入量表名称"
                  clearable
                />
              </el-form-item>
              
              <el-form-item label="量表代码" prop="code">
                <el-input 
                  v-model="parseResult.code" 
                  placeholder="例如：ELDERLY_ABILITY_V1"
                  clearable
                >
                  <template #append>
                    <el-button @click="generateCode">生成</el-button>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="量表类型" prop="type">
                <el-select 
                  v-model="parseResult.type" 
                  placeholder="选择量表类型"
                  @change="onTypeChange"
                >
                  <el-option label="老年人能力评估" value="ELDERLY_ABILITY">
                    <span style="float: left">老年人能力评估</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">国家标准</span>
                  </el-option>
                  <el-option label="情绪快评" value="EMOTIONAL_QUICK">
                    <span style="float: left">情绪快评</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">快速筛查</span>
                  </el-option>
                  <el-option label="interRAI评估" value="INTER_RAI">
                    <span style="float: left">interRAI评估</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">国际标准</span>
                  </el-option>
                  <el-option label="长护险评估" value="LONG_CARE_INSURANCE">
                    <span style="float: left">长护险评估</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">保险资格</span>
                  </el-option>
                  <el-option label="自定义量表" value="CUSTOM">
                    <span style="float: left">自定义量表</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">灵活配置</span>
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="版本号" prop="version">
                <el-input 
                  v-model="parseResult.version" 
                  placeholder="例如：1.0.0"
                  clearable
                >
                  <template #prepend>v</template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="预估时长" prop="estimatedDuration">
                <el-input-number 
                  v-model="parseResult.estimatedDuration" 
                  :min="1" 
                  :max="180"
                  :step="5"
                  controls-position="right"
                  style="width: 100%;"
                >
                  <template #suffix>
                    <span style="color: #909399;">分钟</span>
                  </template>
                </el-input-number>
              </el-form-item>
              
              <el-form-item label="描述" prop="description">
                <el-input 
                  v-model="parseResult.description" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入量表的详细描述和适用场景"
                  show-word-limit
                  maxlength="500"
                />
              </el-form-item>
              
              <!-- 操作按钮 -->
              <el-form-item>
                <el-button 
                  type="primary" 
                  @click="saveScaleProperties"
                  :loading="savingProperties"
                  style="width: 100%;"
                >
                  💾 保存量表属性
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 量表配置选项 -->
            <div v-if="parseResult" class="scale-config-section">
              <el-divider content-position="left">⚙️ 高级配置</el-divider>
              
              <!-- 评估模式 -->
              <el-form-item label="评估模式" label-width="80px" size="small">
                <el-checkbox-group v-model="parseResult.assessmentModes">
                  <el-checkbox value="MOBILE">📱 移动端</el-checkbox>
                  <el-checkbox value="WEB">💻 网页端</el-checkbox>
                  <el-checkbox value="OFFLINE">📋 离线评估</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <!-- 合规标准 -->
              <el-form-item label="合规标准" label-width="80px" size="small">
                <el-select 
                  v-model="parseResult.complianceStandard" 
                  placeholder="选择合规标准"
                  clearable
                  style="width: 100%;"
                >
                  <el-option label="国家标准GB/T" value="GB_T" />
                  <el-option label="民政部标准" value="MZ_T" />
                  <el-option label="地方标准" value="LOCAL" />
                  <el-option label="机构内部标准" value="INTERNAL" />
                </el-select>
              </el-form-item>
              
              <!-- 启用状态 -->
              <el-form-item label="启用状态" label-width="80px" size="small">
                <el-switch
                  v-model="parseResult.isActive"
                  active-text="启用"
                  inactive-text="停用"
                  style="width: 100%;"
                />
              </el-form-item>
            </div>

            <!-- 最近文件列表 -->
            <el-divider content-position="left">
              📁 最近文件
              <el-button 
                size="small" 
                text 
                @click="refreshRecentScales" 
                :loading="loadingRecent"
                style="margin-left: 8px;"
              >
                🔄 刷新
              </el-button>
            </el-divider>
            <div class="recent-files">
              <el-scrollbar height="200px">
                <div v-if="recentScales.length === 0" class="empty-recent">
                  <el-empty 
                    :image-size="60" 
                    description="暂无最近文件"
                  />
                </div>
                <div 
                  v-for="(scale, index) in recentScales.slice(0, 5)" 
                  :key="index"
                  class="recent-file-item"
                >
                  <div class="file-content" @click="loadRecentScale(scale)">
                    <div class="file-name">{{ scale.name }}</div>
                    <div class="file-meta">
                      <el-tag size="small" :type="getTypeTagType(scale.type)">
                        {{ getTypeDisplayName(scale.type) }}
                      </el-tag>
                      <span class="file-time">{{ formatDateTime(scale.createdAt) }}</span>
                    </div>
                  </div>
                  <div class="file-actions">
                    <el-button
                      size="small"
                      type="danger"
                      text
                      @click="deleteRecentScale(scale, index)"
                      :icon="Delete"
                    />
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>

  <!-- AI对话窗口 -->
  <el-dialog
    v-model="showAIChat"
    title="🤖 AI数据库设计助手"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    top="5vh"
  >
    <div class="ai-chat-container">
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="chatMessagesRef">
        <div
          v-for="(message, index) in chatMessages"
          :key="index"
          :class="['message', message.role]"
        >
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">
                {{ message.role === 'user' ? '👤 您' : '🤖 AI助手' }}
              </span>
              <span class="message-time">
                {{ new Date(message.timestamp).toLocaleTimeString() }}
              </span>
            </div>
            <div class="message-text" v-html="formatMessageContent(message.content)"></div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="chatLoading" class="message assistant loading">
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">🤖 AI助手</span>
              <span class="message-time">思考中...</span>
            </div>
            <div class="message-text">
              <el-icon class="is-loading"><loading /></el-icon>
              正在分析您的问题...
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="chat-input-area">
        <el-input
          v-model="chatInput"
          type="textarea"
          :rows="3"
          placeholder="请输入您关于数据库设计的问题..."
          @keydown.ctrl.enter="sendChatMessage"
          :disabled="chatLoading"
        />
        <div class="chat-input-actions">
          <span class="input-tip">Ctrl + Enter 发送</span>
          <div>
            <el-button size="small" @click="clearChat" :disabled="chatLoading">
              清空对话
            </el-button>
            <el-button 
              type="primary" 
              size="small" 
              @click="sendChatMessage"
              :loading="chatLoading"
              :disabled="!chatInput.trim()"
            >
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="chat-footer">
        <span class="chat-info">
          💡 提示：您可以询问关于字段类型、表结构设计、SQL优化等问题
        </span>
        <el-button @click="showAIChat = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watchEffect, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Upload, CopyDocument, Check, Loading } from '@element-plus/icons-vue';
import { Delete } from '@element-plus/icons-vue';
import { pdfUploadApi } from '@/api/assessment';
import request from '@/utils/request';
import ContentBlock from '@/components/ContentBlock.vue';
import { StreamParser } from '@/utils/stream-parser';
import type { ContentBlock as ContentBlockType } from '@/types/content-block';

// 响应式数据
const previewMode = ref(true);
const uploading = ref(false);
const uploadProgress = ref(0);
const saving = ref(false);
const loadingRecent = ref(false);
const parseResult = ref(null);
const fileList = ref([]);
const recentScales = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 编辑器相关状态
const editMode = ref('preview'); // 'preview', 'edit', 'split'
const editableMarkdown = ref('');
const rawMarkdown = ref('');

// 文件格式相关状态
const outputFormat = ref('markdown'); // 输出格式

// 解析进度相关状态
const parseProgress = ref(0);
const parseStage = ref('');
const parseStartTime = ref(null);
const parseElapsedTime = ref(0);
const timelineExpanded = ref(['timeline']);
let parseTimer = null;

// Docling服务状态
const doclingAvailable = ref(false);
const checkingDocling = ref(false);
const processingDocling = ref(false);
const processingSteps = ref([]);
const progressMessage = ref('正在解析PDF文件...');
const progressStatus = ref('');

// 数据库结构分析相关状态
const aiAnalyzing = ref(false);
const abortController = ref(null);
const streamOutput = ref('');
const aiGeneratedContent = ref('');

// AI内容块状显示相关状态
const contentBlocks = ref<ContentBlockType[]>([]);
const streamParser = ref<StreamParser | null>(null);
const detectingFields = ref(false);
const checkingAI = ref(false);
const aiServiceAvailable = ref(false);

// AI内容流式处理相关状态
const aiContentBuffer = ref('');
const lastContentFlushTime = ref(0);
const contentFlushThreshold = 50; // 字符数阈值
const timeFlushThreshold = 1000; // 时间阈值(ms)
const modelInfo = ref(null);
const aiAnalysisResult = ref(null);
const databaseStructure = ref({
  tableName: '',
  tableComment: '',
  fields: []
});
const generatedSQL = ref('');

// 提示词编辑相关状态
const showPromptEditor = ref(false);
const customPrompt = ref('');

// 已验证的最佳提示词（来自成功测试）
const OPTIMAL_PROMPT = `你是一个经验丰富的PostgreSQL数据库设计师，当前时间是{当前日期}。
专门负责将中文文档内容转换为高质量的数据库设计。
请使用专业的数据库知识和最佳实践来完成任务。

特别注意：
- 充分利用你的深度推理能力分析文档结构
- 准确理解中文业务术语的含义
- 生成符合PostgreSQL最佳实践的高质量SQL

## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能
- 添加主键约束
- 根据字段特征添加检查约束
- 为经常查询的字段创建索引
- 考虑数据的实际使用场景

## 输出格式

### 第一部分：文档分析
\`\`\`markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
\`\`\`

### 第二部分：完整SQL设计
\`\`\`sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件
    {根据文档内容生成合适的CHECK约束}
);

-- 自动生成合适的索引
{根据字段特征和预期查询模式生成索引};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
\`\`\`

### 第三部分：JSON字段定义
\`\`\`json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
\`\`\`

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行`;

// AI对话相关状态
const showAIChat = ref(false);
const chatMessages = ref([]);
const chatInput = ref('');
const chatLoading = ref(false);

// 量表属性保存状态
const savingProperties = ref(false);

// 上传配置
const uploadUrl = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL}/api/docling/convert-with-info`;
});

// 超时配置（根据文件大小动态调整）
const getTimeoutByFileSize = (fileSize) => {
  const sizeMB = fileSize / (1024 * 1024);
  if (sizeMB < 1) return 60000; // 1分钟 for <1MB
  if (sizeMB < 5) return 180000; // 3分钟 for 1-5MB
  if (sizeMB < 20) return 300000; // 5分钟 for 5-20MB
  return 600000; // 10分钟 for >20MB
};

const uploadData = computed(() => ({
  preview: previewMode.value,
  output_format: outputFormat.value,
}));

// 支持的文件类型
const acceptedFileTypes = computed(() => {
  return '.pdf,.docx,.xlsx,.html,.htm,.png,.jpg,.jpeg,.gif,.bmp,.tiff,.md,.txt';
});

// 支持的文件格式配置
const supportedFormats = {
  input: [
    { ext: '.pdf', mime: 'application/pdf', name: 'PDF文档', icon: '📄' },
    { ext: '.docx', mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', name: 'Word文档', icon: '📝' },
    { ext: '.xlsx', mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', name: 'Excel表格', icon: '📊' },
    { ext: '.html', mime: 'text/html', name: 'HTML页面', icon: '🌐' },
    { ext: '.htm', mime: 'text/html', name: 'HTM页面', icon: '🌐' },
    { ext: '.md', mime: 'text/markdown', name: 'Markdown文档', icon: '📋' },
    { ext: '.txt', mime: 'text/plain', name: '纯文本文档', icon: '📄' },
    { ext: '.png', mime: 'image/png', name: 'PNG图片', icon: '🖼️' },
    { ext: '.jpg', mime: 'image/jpeg', name: 'JPG图片', icon: '🖼️' },
    { ext: '.jpeg', mime: 'image/jpeg', name: 'JPEG图片', icon: '🖼️' },
    { ext: '.gif', mime: 'image/gif', name: 'GIF图片', icon: '🖼️' },
    { ext: '.bmp', mime: 'image/bmp', name: 'BMP图片', icon: '🖼️' },
    { ext: '.tiff', mime: 'image/tiff', name: 'TIFF图片', icon: '🖼️' }
  ],
  output: [
    { value: 'markdown', name: 'Markdown', icon: '📝', desc: '轻量级标记语言，易于编辑和阅读' },
    { value: 'html', name: 'HTML', icon: '🌐', desc: '网页格式，可直接在浏览器查看' },
    { value: 'json', name: 'JSON', icon: '📋', desc: '结构化数据格式，便于程序处理' }
  ]
};

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token');
  return token
    ? {
        Authorization: `Bearer ${token}`,
      }
    : {};
});

// 组件引用
const uploadRef = ref();

// Docling服务状态计算属性
const doclingStatusTitle = computed(() => {
  if (checkingDocling.value) return 'Docling服务检查中...';
  return doclingAvailable.value ? 'Docling AI引擎已就绪' : 'Docling AI引擎未启动';
});

const doclingStatusType = computed(() => {
  if (checkingDocling.value) return 'info';
  return doclingAvailable.value ? 'success' : 'warning';
});

const doclingStatusMessage = computed(() => {
  if (checkingDocling.value) return '正在检查Docling PDF转换服务的可用性...';
  if (doclingAvailable.value) {
    return '✅ Docling AI引擎运行正常，可以进行高质量PDF解析。系统已优化处理复杂布局的评估量表文档。';
  }
  return '⚠️ Docling AI引擎当前不可用。请确保Docker服务已启动并且Docling容器正在运行。没有Docling支持，PDF解析功能将受到限制。';
});

// Markdown渲染相关计算属性
const renderedMarkdown = computed(() => {
  // 简单的Markdown渲染，可以后续替换为更复杂的渲染器
  if (!editableMarkdown.value) return '';
  
  return editableMarkdown.value
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/\n/gim, '<br>');
});

// 获取输出格式显示名称
const getOutputFormatName = (format) => {
  const outputFormat = supportedFormats.output.find(f => f.value === format);
  return outputFormat ? `${outputFormat.icon} ${outputFormat.name}` : format;
};

// 获取有效的时间线类型
const getValidTimelineType = (type) => {
  // Element Plus timeline 支持的类型: 'primary', 'success', 'warning', 'danger', 'info'
  const validTypes = ['primary', 'success', 'warning', 'danger', 'info'];
  if (validTypes.includes(type)) {
    return type;
  }
  // 映射其他类型到有效类型
  const typeMap = {
    'error': 'danger',
    'exception': 'danger'
  };
  return typeMap[type] || 'primary';
};

// 获取文件格式信息
const getFileFormatInfo = (file) => {
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  const format = supportedFormats.input.find(f => f.ext === extension);
  return format || { ext: extension, name: '未知格式', icon: '📄' };
};

// 检查是否为支持的文件格式
const isSupportedFileFormat = (file) => {
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  const mimeType = file.type;
  
  return supportedFormats.input.some(format => 
    format.ext === extension || format.mime === mimeType
  );
};

// 上传前验证
const beforeUpload = file => {
  const isSupported = isSupportedFileFormat(file);
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isSupported) {
    const fileInfo = getFileFormatInfo(file);
    ElMessage.error(`不支持的文件格式：${fileInfo.name}！\n支持格式：PDF, DOCX, XLSX, HTML, 图片文件`);
    return false;
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB!');
    return false;
  }

  // 检查Docling状态
  if (!doclingAvailable.value) {
    ElMessage.warning('Docling AI引擎不可用，将使用基础文档解析功能，解析效果可能受限。');
  }

  const fileInfo = getFileFormatInfo(file);
  const outputInfo = getOutputFormatName(outputFormat.value);

  uploading.value = true;
  uploadProgress.value = 0;
  parseResult.value = null;
  processingSteps.value = [];
  progressMessage.value = `正在上传${fileInfo.name}文件...`;
  progressStatus.value = '';

  // 添加处理步骤
  addProcessingStep('📤', `开始上传${fileInfo.icon} ${fileInfo.name}文件`, 'primary', 
    `输出格式：${outputInfo}`);

  return true;
};

// 上传进度
const onUploadProgress = event => {
  const percentage = Math.round((event.loaded / event.total) * 100);
  uploadProgress.value = percentage;
  
  if (percentage === 100) {
    progressMessage.value = '文件上传完成，开始Docling AI解析...';
    addProcessingStep('🤖', '文件上传完成，开始AI解析', 'success');
    processingDocling.value = true;
    
    // 启动解析进度模拟
    startParseProgress();
  } else {
    progressMessage.value = `正在上传PDF文件... ${percentage}%`;
  }
};

// 上传成功
const onUploadSuccess = response => {
  uploading.value = false;
  
  // 停止解析进度模拟
  stopParseProgress();
  
  if (response.success) {
    // 完成解析进度
    parseProgress.value = 100;
    parseStage.value = '✅ 解析完成！';
    processingDocling.value = false;
    
    parseResult.value = response.data;
    
    // 初始化新属性
    if (parseResult.value) {
      // 确保有name属性
      if (!parseResult.value.name && parseResult.value.fileName) {
        parseResult.value.name = parseResult.value.fileName.replace(/\.[^/.]+$/, '');
      }
      
      // 初始化高级配置属性
      if (!parseResult.value.assessmentModes) {
        parseResult.value.assessmentModes = ['MOBILE', 'WEB'];
      }
      
      if (!parseResult.value.complianceStandard) {
        parseResult.value.complianceStandard = 'GB_T';
      }
      
      if (parseResult.value.isActive === undefined) {
        parseResult.value.isActive = true;
      }
    }
    
    // 存储原始Markdown和可编辑内容
    if (response.data && response.data.markdownContent) {
      rawMarkdown.value = response.data.markdownContent;
      editableMarkdown.value = response.data.markdownContent;
    }
    
    progressMessage.value = 'PDF解析完成！';
    progressStatus.value = 'success';
    
    // 添加最终成功步骤
    addProcessingStep('✅', '解析完成', 'success', '量表结构已成功识别，可以开始编辑');
    
    ElMessage.success(response.message || 'PDF上传解析成功!');

    // 如果不是预览模式，直接刷新列表
    if (!previewMode.value) {
      refreshRecentScales();
    }
  } else {
    parseProgress.value = 0;
    parseStage.value = '❌ 解析失败';
    processingDocling.value = false;
    
    progressMessage.value = 'PDF解析失败';
    progressStatus.value = 'exception';
    addProcessingStep('❌', '解析失败', 'danger', response.message);
    ElMessage.error(response.message || 'PDF上传失败!');
  }

  // 清空文件列表
  fileList.value = [];
  uploadRef.value?.clearFiles();
};

// 上传失败
const onUploadError = error => {
  uploading.value = false;
  processingDocling.value = false;
  uploadProgress.value = 0;
  parseProgress.value = 0;
  
  // 停止解析进度模拟
  stopParseProgress();
  
  progressMessage.value = '上传失败';
  progressStatus.value = 'exception';
  parseStage.value = '❌ 上传失败';
  
  addProcessingStep('❌', '文件上传失败', 'danger', '请检查网络连接或文件格式');
  console.error('Upload error:', error);
  ElMessage.error('PDF上传失败，请重试!');
};

// 确认保存
const confirmSave = async () => {
  if (!parseResult.value) {
    ElMessage.warning('没有可保存的解析结果');
    return;
  }

  try {
    // 人工确认对话框
    await ElMessageBox.confirm(
      `请确认以下信息是否正确：
      
📝 量表名称：${parseResult.value.name || '未设置'}
🔢 量表代码：${parseResult.value.code || '未设置'}  
📊 量表类型：${getTypeDisplayName(parseResult.value.type) || '未设置'}
⏱️ 预估时长：${parseResult.value.estimatedDuration || 0}分钟
📄 识别字段：${parseResult.value.fieldCount || 0}个

✏️ 编辑内容：${editableMarkdown.value ? '已编辑' : '未编辑'}

确认保存到数据库？保存后将可以用于创建评估任务。`,
      '💾 确认保存量表',
      {
        confirmButtonText: '✅ 确认保存',
        cancelButtonText: '❌ 取消',
        type: 'info',
        customClass: 'confirm-save-dialog',
        dangerouslyUseHTMLString: true
      }
    );

    saving.value = true;

    // 构建保存数据，包含用户编辑的内容
    const saveData = {
      ...parseResult.value,
      editedMarkdown: editableMarkdown.value,
      originalMarkdown: rawMarkdown.value,
      userConfirmed: true,
      confirmedAt: new Date().toISOString()
    };

    // 调用保存API（这里需要实际的保存接口）
    const response = await request({
      url: '/api/assessment-scales',
      method: 'POST',
      data: saveData
    });

    if (response.success) {
      ElMessage.success('✅ 量表保存成功！已加入量表库，可以开始使用。');
      refreshRecentScales();
      
      // 可选：保存后清除当前编辑
      // clearPreview();
    } else {
      ElMessage.error(response.message || '保存失败，请重试！');
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('Save error:', error);
      ElMessage.error('保存过程中发生错误，请重试！');
    }
  } finally {
    saving.value = false;
  }
};

// 清除预览
const clearPreview = () => {
  parseResult.value = null;
  fileList.value = [];
  processingSteps.value = [];
  processingDocling.value = false;
  editableMarkdown.value = '';
  rawMarkdown.value = '';
  editMode.value = 'preview';
  uploadRef.value?.clearFiles();
  ElMessage.info('已清除当前解析结果');
};

// 刷新最近量表
const refreshRecentScales = async () => {
  try {
    loadingRecent.value = true;

    const response = await request({
      url: '/api/assessment-scales',
      method: 'GET',
    });

    if (response.success) {
      recentScales.value = response.data || [];
      total.value = response.data ? response.data.length : 0;
    } else {
      recentScales.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('Refresh error:', error);
    // 暂时不显示错误消息，因为可能是认证问题
    recentScales.value = [];
    total.value = 0;
  } finally {
    loadingRecent.value = false;
  }
};

// 分页处理
const onPageSizeChange = newPageSize => {
  pageSize.value = newPageSize;
  refreshRecentScales();
};

const onCurrentPageChange = newPage => {
  currentPage.value = newPage;
  refreshRecentScales();
};

// 操作方法
const viewScale = scale => {
  ElMessage.info(`查看量表: ${scale.name}`);
  // 这里可以跳转到量表详情页
};

const editScale = scale => {
  ElMessage.info(`编辑量表: ${scale.name}`);
  // 这里可以跳转到量表编辑页
};

// 打开字段映射确认页面
const openFieldMapping = scale => {
  // 跳转到字段映射确认页面
  window.open(`/assessment/field-mapping/${scale.id}`, '_blank');
};

// 确认入库
const approveScale = async scale => {
  try {
    await ElMessageBox.confirm(
      `确认将量表 "${scale.name}" 正式入库吗？入库后将可以用于评估。`,
      '确认入库',
      {
        confirmButtonText: '确认入库',
        cancelButtonText: '取消',
        type: 'success',
      }
    );

    const result = await request({
      url: `/api/assessment-scales/${scale.id}/approve`,
      method: 'POST',
    });

    if (result.success) {
      ElMessage.success('量表已成功入库!');
      refreshRecentScales();
    } else {
      ElMessage.error(result.message || '入库失败!');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Approve error:', error);
      ElMessage.error('入库操作失败!');
    }
  }
};

// 切换启用状态
const toggleScale = async scale => {
  try {
    const action = scale.isActive ? '停用' : '启用';
    await ElMessageBox.confirm(
      `确定要${action}量表 "${scale.name}" 吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const result = await request({
      url: `/api/assessment-scales/${scale.id}/toggle`,
      method: 'POST',
    });

    if (result.success) {
      ElMessage.success(`量表${action}成功!`);
      refreshRecentScales();
    } else {
      ElMessage.error(result.message || `${action}失败!`);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Toggle error:', error);
      ElMessage.error('状态切换失败!');
    }
  }
};

const deleteScale = async scale => {
  try {
    await ElMessageBox.confirm(
      `确定要删除量表 "${scale.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const result = await request({
      url: `/api/assessment-scales/${scale.id}`,
      method: 'DELETE',
    });

    if (result.success) {
      ElMessage.success('删除成功!');
      refreshRecentScales();
    } else {
      ElMessage.error(result.message || '删除失败!');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete error:', error);
      ElMessage.error('删除失败!');
    }
  }
};

// 工具方法
const getTypeTagType = type => {
  const typeMap = {
    ELDERLY_ABILITY: 'primary',
    EMOTIONAL_QUICK: 'success',
    INTER_RAI: 'warning',
    LONG_CARE_INSURANCE: 'info',
    CUSTOM: 'info',
  };
  return typeMap[type] || 'info';
};

const getTypeDisplayName = type => {
  const typeMap = {
    ELDERLY_ABILITY: '老年人能力评估',
    EMOTIONAL_QUICK: '情绪快评',
    INTER_RAI: 'interRAI评估',
    LONG_CARE_INSURANCE: '长护险评估',
    CUSTOM: '自定义量表',
  };
  return typeMap[type] || type;
};

const getStatusTagType = status => {
  const statusMap = {
    SUCCESS: 'success',
    PENDING: 'warning',
    PARSING: 'primary',
    FAILED: 'danger',
    REVIEWING: 'info',
    APPROVED: 'success',
  };
  return statusMap[status] || 'info';
};

const getStatusDisplayName = status => {
  const statusMap = {
    SUCCESS: '解析成功',
    PENDING: '待解析',
    PARSING: '解析中',
    FAILED: '解析失败',
    REVIEWING: '人工审核中',
    APPROVED: '已审核通过',
  };
  return statusMap[status] || status;
};

const formatDateTime = dateString => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

// Docling服务状态检查
const checkDoclingStatus = async () => {
  try {
    checkingDocling.value = true;
    
    const response = await request({
      url: '/api/docling/health',
      method: 'GET',
    });

    if (response.success) {
      doclingAvailable.value = true;
      ElMessage.success('Docling AI引擎运行正常');
    } else {
      doclingAvailable.value = false;
      ElMessage.warning('Docling AI引擎不可用');
    }
  } catch (error) {
    console.error('Docling health check error:', error);
    doclingAvailable.value = false;
    ElMessage.error('无法连接到Docling服务');
  } finally {
    checkingDocling.value = false;
  }
};

// AI服务状态检查
const checkAIStatus = async () => {
  try {
    checkingAI.value = true;
    
    // 检查LM Studio AI服务状态
    const response = await request({
      url: '/api/ai/status',
      method: 'GET',
    });

    if (response.success) {
      aiServiceAvailable.value = true;
      ElMessage.success('🤖 LM Studio AI服务运行正常');
    } else {
      aiServiceAvailable.value = false;
      ElMessage.warning('🤖 LM Studio AI服务不可用');
    }
  } catch (error) {
    console.error('AI service check error:', error);
    aiServiceAvailable.value = false;
    ElMessage.error(`无法连接到LM Studio AI服务 (${modelInfo.value?.url || 'Unknown'})`);
  } finally {
    checkingAI.value = false;
  }
};

// 测试Docling转换功能
const testDoclingConversion = () => {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = acceptedFileTypes.value; // 支持所有格式
  fileInput.onchange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // 验证文件格式
    if (!isSupportedFileFormat(file)) {
      const fileInfo = getFileFormatInfo(file);
      ElMessage.error(`不支持的文件格式：${fileInfo.name}！`);
      return;
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在进行Docling转换测试，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('output_format', outputFormat.value);

      const fileInfo = getFileFormatInfo(file);
      ElMessage.info(`开始测试 ${fileInfo.icon} ${fileInfo.name} 文件转换...`);

      const dynamicTimeout = getTimeoutByFileSize(file.size);
      
      // 更新loading文本显示文件大小和预估时间
      loading.text = `正在进行Docling转换测试...\n文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB\n预估时间: ${Math.ceil(dynamicTimeout / 60000)}分钟`;
      
      const response = await request({
        url: '/api/docling/convert-with-info',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: dynamicTimeout, // 动态超时时间
        onUploadProgress: (progressEvent) => {
          const percentage = Math.round((progressEvent.loaded / progressEvent.total) * 100);
          console.log(`测试文件上传进度: ${percentage}%`);
        }
      });

      loading.close();

      if (response.success) {
        const result = response.data;
        console.log('Docling转换结果:', result);
        
        // 创建转换结果对话框
        let message = `📄 文件名: ${result.fileName || 'N/A'}\n`;
        message += `📊 文件大小: ${(result.fileSize / 1024).toFixed(2)} KB\n`;
        message += `⏱️ 处理时间: ${(result.processingTimeMs / 1000).toFixed(2)} 秒\n`;
        
        if (result.parseStatus) {
          message += `📑 页数: ${result.parseStatus.totalPages || 'N/A'}\n`;
          message += `📝 内容长度: ${result.parseStatus.contentLength || 'N/A'} 字符\n`;
        }
        
        message += `📤 输出格式: ${getOutputFormatName(outputFormat.value)}\n`;
        
        if (result.markdownContent) {
          message += `\n🎉 转换成功！\n`;
          message += `📋 前200字符预览:\n${result.markdownContent.substring(0, 200)}...`;
        } else if (result.errorMessage) {
          message += `\n❌ 转换失败\n`;
          message += `📝 错误: ${result.errorMessage}`;
        }

        ElMessageBox.alert(message, 'Docling转换测试结果', {
          confirmButtonText: '确定',
          type: result.markdownContent ? 'success' : 'warning',
          customClass: 'docling-test-dialog'
        });
      } else {
        ElMessage.error(`转换测试失败: ${response.message}`);
      }
    } catch (error) {
      loading.close();
      console.error('Docling转换测试错误:', error);
      
      if (error.code === 'ECONNABORTED') {
        const timeoutMinutes = Math.ceil(dynamicTimeout / 60000);
        ElMessageBox.alert(
          `文档转换超时（超过${timeoutMinutes}分钟）！\n\n可能的原因：\n1. 文档内容复杂，包含大量表格或图片\n2. 文档需要OCR处理，耗时较长\n3. Docling服务负载较高\n\n建议：\n• 尝试使用较小的文档测试\n• 确保文档格式规范，避免扫描版PDF\n• 稍后再试或联系技术支持`,
          'Docling转换超时',
          {
            confirmButtonText: '我知道了',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
      } else if (error.response?.status === 413) {
        ElMessage.error('文件太大！请选择小于50MB的文件。');
      } else if (error.response?.status === 415) {
        ElMessage.error('不支持的文件格式！请检查文件类型。');
      } else {
        ElMessage.error('Docling转换测试失败，请检查服务状态后重试');
      }
    }
  };
  
  fileInput.click();
};

// 显示Docling启动指南
const showDoclingHelp = () => {
  const helpContent = `
🚀 Docling AI引擎启动指南

请按以下步骤启动Docling服务：

1️⃣ 打开终端，进入项目目录
   cd /Volumes/acasis/Assessment

2️⃣ 进入docker目录
   cd docker

3️⃣ 启动Docling服务（持久化模型版本）
   ./start-docling-persistent.sh

4️⃣ 或者启动API服务模式
   ./docling-api-persistent.sh

5️⃣ 验证服务状态
   访问 http://localhost:8088/health

💡 提示：
- 首次启动需要下载模型文件，请耐心等待
- 确保Docker已安装并运行
- 端口8088需要保持空闲
- 建议使用持久化版本以避免重复下载模型

🔧 故障排除：
- 检查Docker是否运行：docker --version
- 检查端口占用：lsof -i :8088
- 查看容器日志：docker logs docling-api
`;

  ElMessageBox.alert(helpContent, 'Docling启动指南', {
    confirmButtonText: '我知道了',
    type: 'info',
    customClass: 'docling-help-dialog'
  });
};

// 添加处理步骤 - 性能优化版本
const addProcessingStep = (icon, message, type = 'primary', detail = '') => {
  // 使用性能更好的时间格式化
  const timestamp = new Date().toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  
  const newStep = {
    timestamp,
    message,
    type,
    icon,
    detail,
    status: type === 'primary' ? 'pending' : 'completed'
  };
  
  // 使用批量更新减少响应式触发
  const steps = [...processingSteps.value];
  steps.push(newStep);
  
  // 限制步骤数量，避免界面过长
  if (steps.length > 8) { // 减少到8个，提高性能
    steps.splice(0, steps.length - 8);
  }
  
  processingSteps.value = steps;
};

// 测试调试API（已废弃，改用 checkDoclingStatus）
// const testDebugAPI = async () => {
//   try {
//     const response = await request({
//       url: '/api/pdf-import/test',
//       method: 'GET',
//     });

//     if (response.success) {
//       ElMessage.success(`API测试成功: ${response.data}`);
//     } else {
//       ElMessage.error(`API测试失败: ${response.message}`);
//     }
//   } catch (error) {
//     console.error('API测试错误:', error);
//     ElMessage.error('API连接失败，请检查后端服务是否正常运行');
//   }
// };

// 调试PDF文件（已废弃，使用 testDoclingConversion 代替）
// const debugPDFFile = () => {
//   const fileInput = document.createElement('input');
//   fileInput.type = 'file';
//   fileInput.accept = '.pdf';
//   fileInput.onchange = async (e) => {
//     const file = e.target.files[0];
//     if (!file) return;

//     try {
//       const formData = new FormData();
//       formData.append('file', file);

//       ElMessage.info('开始调试PDF文件...');

//       const response = await request({
//         url: '/api/pdf-import/debug-parse',
//         method: 'POST',
//         data: formData,
//         headers: {
//           'Content-Type': 'multipart/form-data',
//         },
//       });

//       if (response.success) {
//         const debugInfo = response.data;
//         console.log('PDF调试信息:', debugInfo);
        
//         // 创建调试信息对话框
//         let message = `📄 文件名: ${debugInfo.fileName || 'N/A'}\n`;
//         message += `📊 大小: ${debugInfo.fileSize || 'N/A'} bytes\n`;
//         message += `🏷️ 类型: ${debugInfo.contentType || 'N/A'}\n`;
//         message += `✅ 验证: ${debugInfo.validationSuccess ? '通过' : '失败'}\n`;
        
//         if (debugInfo.validationError) {
//           message += `❌ 验证错误: ${debugInfo.validationError}\n`;
//         }
        
//         if (debugInfo.fileHeader) {
//           message += `📋 文件头: ${debugInfo.fileHeader}\n`;
//         }
        
//         if (debugInfo.parseSuccess) {
//           message += `🎉 解析: 成功\n`;
//           message += `📋 量表名: ${debugInfo.scaleName || 'N/A'}\n`;
//           message += `🏷️ 量表类型: ${debugInfo.scaleType || 'N/A'}`;
//         } else {
//           message += `❌ 解析: 失败\n`;
//           message += `📝 错误: ${debugInfo.parseError || 'N/A'}\n`;
//           message += `🔍 根本原因: ${debugInfo.rootCause || 'N/A'}`;
//         }

//         ElMessageBox.alert(message, 'PDF调试信息', {
//           confirmButtonText: '确定',
//           type: debugInfo.parseSuccess ? 'success' : 'warning',
//         });
//       } else {
//         ElMessage.error(`调试失败: ${response.message}`);
//       }
//     } catch (error) {
//       console.error('PDF调试错误:', error);
//       ElMessage.error('PDF调试失败，请重试');
//     }
//   };
  
//   fileInput.click();
// };

// 下载文件（支持多种格式）
const downloadFile = (format = null) => {
  if (!editableMarkdown.value) {
    ElMessage.warning('没有可下载的内容');
    return;
  }

  const downloadFormat = format || outputFormat.value || 'markdown';
  const filename = parseResult.value?.fileName || '文档解析结果';
  
  let content = editableMarkdown.value;
  let mimeType = 'text/plain;charset=utf-8';
  let extension = '.txt';
  
  switch (downloadFormat) {
    case 'markdown':
      mimeType = 'text/markdown;charset=utf-8';
      extension = '.md';
      break;
    case 'html':
      // 如果是HTML格式，转换Markdown为HTML
      content = convertMarkdownToHtml(editableMarkdown.value);
      mimeType = 'text/html;charset=utf-8';
      extension = '.html';
      break;
    case 'json':
      // 如果是JSON格式，构建JSON结构
      content = JSON.stringify({
        filename: filename,
        originalFormat: parseResult.value?.originalFormat || 'unknown',
        outputFormat: downloadFormat,
        content: editableMarkdown.value,
        metadata: {
          parseTime: parseResult.value?.processingTime || 0,
          fileSize: parseResult.value?.fileSize || 0,
          pages: parseResult.value?.parseStatus?.totalPages || 0
        },
        timestamp: new Date().toISOString()
      }, null, 2);
      mimeType = 'application/json;charset=utf-8';
      extension = '.json';
      break;
  }

  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}${extension}`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
  
  const formatInfo = getOutputFormatName(downloadFormat);
  ElMessage.success(`${formatInfo} 文件下载成功`);
};

// 简单的Markdown转HTML
const convertMarkdownToHtml = (markdown) => {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档解析结果</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin: 16px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background-color: #f4f4f4; padding: 16px; border-radius: 6px; overflow-x: auto; }
    </style>
</head>
<body>
${markdown
  .replace(/^# (.*$)/gim, '<h1>$1</h1>')
  .replace(/^## (.*$)/gim, '<h2>$1</h2>')
  .replace(/^### (.*$)/gim, '<h3>$1</h3>')
  .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
  .replace(/\*(.*)\*/gim, '<em>$1</em>')
  .replace(/\n/gim, '<br>')}
</body>
</html>`;
};

// 兼容性方法 - 保持原有的下载Markdown功能
const downloadMarkdown = () => downloadFile('markdown');

// 加载最近的量表
const loadRecentScale = async (scale) => {
  try {
    // 从服务器获取详细信息
    const response = await request({
      url: `/api/assessment-scales/${scale.id}`,
      method: 'GET',
    });

    if (response.success && response.data) {
      parseResult.value = response.data;
      
      // 确保有必要的属性
      if (!parseResult.value.name) {
        parseResult.value.name = scale.name;
      }
      
      // 初始化高级配置属性
      if (!parseResult.value.assessmentModes) {
        parseResult.value.assessmentModes = ['MOBILE', 'WEB'];
      }
      
      if (!parseResult.value.complianceStandard) {
        parseResult.value.complianceStandard = 'GB_T';
      }
      
      if (parseResult.value.isActive === undefined) {
        parseResult.value.isActive = true;
      }
      
      if (response.data.formSchema) {
        // 如果有表单结构，显示在编辑器中
        editableMarkdown.value = JSON.stringify(response.data.formSchema, null, 2);
        rawMarkdown.value = editableMarkdown.value;
      }
      ElMessage.success(`已载入量表: ${scale.name}`);
    } else {
      ElMessage.error('载入量表失败');
    }
  } catch (error) {
    console.error('Load scale error:', error);
    ElMessage.error('载入量表失败');
  }
};

// 删除最近文件
const deleteRecentScale = async (scale, index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除量表 "${scale.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const response = await request({
      url: `/api/assessment-scales/${scale.id}`,
      method: 'DELETE',
    });

    if (response.success) {
      // 从本地列表中移除
      recentScales.value.splice(index, 1);
      ElMessage.success('删除成功');
      
      // 如果删除的是当前加载的量表，清空编辑器
      if (parseResult.value && parseResult.value.id === scale.id) {
        clearPreview();
      }
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete scale error:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 生成量表代码
const generateCode = () => {
  if (!parseResult.value) return;
  
  const type = parseResult.value.type || 'CUSTOM';
  const timestamp = new Date().getTime().toString().slice(-6);
  const version = parseResult.value.version || '1.0';
  
  parseResult.value.code = `${type}_${timestamp}_V${version.replace(/\./g, '')}`;
  ElMessage.success('已生成量表代码');
};

// 量表类型变更处理
const onTypeChange = (value) => {
  if (!parseResult.value) return;
  
  // 根据类型设置默认的预估时长
  const defaultDurations = {
    'ELDERLY_ABILITY': 30,
    'EMOTIONAL_QUICK': 10,
    'INTER_RAI': 45,
    'LONG_CARE_INSURANCE': 25,
    'CUSTOM': 20
  };
  
  if (!parseResult.value.estimatedDuration) {
    parseResult.value.estimatedDuration = defaultDurations[value] || 20;
  }
  
  // 设置默认的合规标准
  const defaultStandards = {
    'ELDERLY_ABILITY': 'GB_T',
    'EMOTIONAL_QUICK': 'MZ_T',
    'INTER_RAI': 'GB_T',
    'LONG_CARE_INSURANCE': 'MZ_T',
    'CUSTOM': 'INTERNAL'
  };
  
  if (!parseResult.value.complianceStandard) {
    parseResult.value.complianceStandard = defaultStandards[value] || 'INTERNAL';
  }
};

// 保存量表属性
const saveScaleProperties = async () => {
  if (!parseResult.value) return;
  
  try {
    savingProperties.value = true;
    
    // 验证必填字段
    if (!parseResult.value.name) {
      ElMessage.warning('请输入量表名称');
      return;
    }
    
    if (!parseResult.value.type) {
      ElMessage.warning('请选择量表类型');
      return;
    }
    
    // 确保有默认值
    if (!parseResult.value.code) {
      generateCode();
    }
    
    if (!parseResult.value.version) {
      parseResult.value.version = '1.0.0';
    }
    
    if (!parseResult.value.assessmentModes) {
      parseResult.value.assessmentModes = ['MOBILE', 'WEB'];
    }
    
    if (parseResult.value.isActive === undefined) {
      parseResult.value.isActive = true;
    }
    
    const scaleData = {
      id: parseResult.value.id,
      name: parseResult.value.name,
      code: parseResult.value.code,
      type: parseResult.value.type,
      version: parseResult.value.version,
      estimatedDuration: parseResult.value.estimatedDuration,
      description: parseResult.value.description,
      assessmentModes: parseResult.value.assessmentModes,
      complianceStandard: parseResult.value.complianceStandard,
      isActive: parseResult.value.isActive,
      formSchema: parseResult.value.formSchema,
      scoringRules: parseResult.value.scoringRules
    };
    
    const response = await request({
      url: parseResult.value.id 
        ? `/api/assessment-scales/${parseResult.value.id}`
        : '/api/assessment-scales',
      method: parseResult.value.id ? 'PUT' : 'POST',
      data: scaleData
    });
    
    if (response.success) {
      parseResult.value = response.data;
      ElMessage.success('量表属性保存成功');
      refreshRecentScales();
    } else {
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('Save properties error:', error);
    ElMessage.error('保存失败');
  } finally {
    savingProperties.value = false;
  }
};

// 开始解析进度模拟
const startParseProgress = () => {
  parseProgress.value = 0;
  parseStartTime.value = Date.now();
  parseElapsedTime.value = 0;
  
  // 解析阶段配置
  const stages = [
    { progress: 10, stage: '📖 读取PDF文档结构...', step: '正在分析PDF元数据和页面布局' },
    { progress: 25, stage: '🔍 识别文档布局...', step: '检测表格、文本区域和图像元素' },
    { progress: 40, stage: '📊 提取表格数据...', step: '解析评估量表结构和字段信息' },
    { progress: 60, stage: '📝 转换为Markdown...', step: '生成结构化文本和格式标记' },
    { progress: 80, stage: '✨ 优化格式...', step: '应用智能格式化和内容校正' },
    { progress: 95, stage: '🔧 最终处理...', step: '完成数据验证和质量检查' }
  ];
  
  let currentStageIndex = 0;
  
  // 清理之前的定时器
  if (parseTimer) {
    clearInterval(parseTimer);
  }
  
  // 启动进度更新定时器 - 高性能优化版本
  parseTimer = setInterval(() => {
    // 性能监控开始
    const perfStart = performance.now();
    
    // 使用requestAnimationFrame优化DOM更新
    requestAnimationFrame(() => {
      try {
        const now = Date.now();
        parseElapsedTime.value = Math.floor((now - parseStartTime.value) / 1000);
        
        // 避免频繁DOM操作，只在必要时更新
        if (currentStageIndex < stages.length) {
          const currentStage = stages[currentStageIndex];
          
          // 缓存当前阶段，避免重复计算
          if (parseStage.value !== currentStage.stage) {
            parseStage.value = currentStage.stage;
          }
          
          // 优化进度计算
          if (parseProgress.value < currentStage.progress) {
            const increment = Math.random() * 1.5 + 0.3; // 进一步减少随机范围
            const newProgress = Math.min(parseProgress.value + increment, currentStage.progress);
            parseProgress.value = newProgress;
            
            // 减少处理步骤添加频率，只在阶段完成时添加
            if (newProgress >= currentStage.progress) {
              // 使用微任务延迟执行，优化性能
              Promise.resolve().then(() => {
                addProcessingStep('🤖', currentStage.stage.replace('...', ''), 'success', currentStage.step);
              });
              currentStageIndex++;
            }
          }
        }
        
        // 最大不超过95%，等待真实API响应
        if (parseProgress.value >= 95) {
          parseProgress.value = 95;
          if (parseStage.value !== '⏳ 等待服务器响应...') {
            parseStage.value = '⏳ 等待服务器响应...';
          }
        }
        
        // 性能监控（仅在开发环境）
        const perfEnd = performance.now();
        if (perfEnd - perfStart > 50 && process.env.NODE_ENV === 'development') {
          console.warn(`Progress update took ${(perfEnd - perfStart).toFixed(2)}ms`);
        }
      } catch (error) {
        console.error('Progress update error:', error);
      }
    });
  }, 1000); // 进一步增加间隔到1秒，减少执行频率
};

// 停止解析进度模拟
const stopParseProgress = () => {
  if (parseTimer) {
    clearInterval(parseTimer);
    parseTimer = null;
  }
};

// 页面可见性变化处理，优化性能
const handleVisibilityChange = () => {
  if (document.hidden && parseTimer) {
    // 页面不可见时暂停定时器
    clearInterval(parseTimer);
    parseTimer = null;
  } else if (!document.hidden && processingDocling.value && !parseTimer) {
    // 页面重新可见且正在处理时恢复定时器
    startParseProgress();
  }
};

// 组件挂载时添加页面可见性监听
onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

// 组件卸载时清理
onBeforeUnmount(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  stopParseProgress();
});

// ==================== 提示词编辑相关方法 ====================

// 切换提示词编辑器显示状态
const togglePromptEditor = () => {
  showPromptEditor.value = !showPromptEditor.value;
  if (showPromptEditor.value && !customPrompt.value) {
    // 首次打开时加载最佳提示词
    resetToOptimalPrompt();
  }
};

// 重置为最佳提示词
const resetToOptimalPrompt = () => {
  const currentDate = new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit', 
    day: '2-digit'
  });
  customPrompt.value = OPTIMAL_PROMPT.replace('{当前日期}', currentDate);
  ElMessage.success('已加载最佳提示词（136/136分验证版本）');
};

// 预览当前提示词
const previewCurrentPrompt = () => {
  if (!customPrompt.value.trim()) {
    ElMessage.warning('提示词内容为空');
    return;
  }
  
  ElMessageBox.alert(customPrompt.value, '当前提示词预览', {
    confirmButtonText: '确定',
    customStyle: {
      'white-space': 'pre-wrap',
      'word-break': 'break-word',
      'max-height': '60vh',
      'overflow-y': 'auto'
    }
  });
};

// 保存自定义提示词
const saveCustomPrompt = () => {
  if (!customPrompt.value.trim()) {
    ElMessage.warning('提示词内容不能为空');
    return;
  }
  
  // 保存到localStorage
  localStorage.setItem('ai-custom-prompt', customPrompt.value);
  ElMessage.success('自定义提示词已保存到本地');
};

// 处理AI内容块 - 实现缓冲区累积和定期输出（桌面浏览器优化）
const handleAIContentChunk = (content) => {
  // 新系统：使用流式解析器处理内容（结构化显示）
  if (streamParser.value) {
    streamParser.value.processChunk(content);
  }
  
  // 兼容性：同时更新原始AI内容（用于后备显示）
  aiContentBuffer.value += content;
  
  const now = Date.now();
  const timeSinceLastFlush = now - lastContentFlushTime.value;
  
  const sizeThreshold = 100;
  const timeThreshold = 200;
  
  const shouldFlush = 
    aiContentBuffer.value.length >= sizeThreshold ||
    timeSinceLastFlush >= timeThreshold ||
    /[。？！\n]/.test(content) ||
    (aiContentBuffer.value.length > 50 && /[，、；：]/.test(content));
  
  if (shouldFlush) {
    flushAIContentBuffer();
  }
};

// 刷新AI内容缓冲区到显示区域
const flushAIContentBuffer = async () => {
  if (aiContentBuffer.value) {
    aiGeneratedContent.value += aiContentBuffer.value;
    aiContentBuffer.value = '';
    lastContentFlushTime.value = Date.now();
    
    // 自动滚动到最新内容
    await nextTick(() => {
      const aiContentEl = document.querySelector('.ai-generated-content');
      if (aiContentEl) {
        aiContentEl.scrollTop = aiContentEl.scrollHeight;
      }
    });
  }
};

// 确保所有缓冲内容都被显示（流式分析结束时调用）
const finalFlushAIContent = async () => {
  // 完成流式解析器
  if (streamParser.value) {
    streamParser.value.flush();
  }
  
  // 兼容性：保留原始缓冲区刷新
  await flushAIContentBuffer();
};

// 加载自定义提示词
const loadCustomPrompt = () => {
  const saved = localStorage.getItem('ai-custom-prompt');
  if (saved) {
    customPrompt.value = saved;
    ElMessage.success('已加载本地保存的自定义提示词');
  } else {
    resetToOptimalPrompt();
  }
};

// ==================== AI内容块显示相关方法 ====================

// 计算属性：是否有代码块
const hasCodeBlocks = computed(() => {
  return contentBlocks.value.some(block => block.type === 'code');
});

// 复制所有内容
const copyAllContent = async () => {
  try {
    const allContent = contentBlocks.value
      .map(block => block.content)
      .join('\n\n');
    await navigator.clipboard.writeText(allContent);
    ElMessage.success('📋 所有内容已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 复制所有代码块
const copyCodeBlocks = async () => {
  try {
    const codeContent = contentBlocks.value
      .filter(block => block.type === 'code')
      .map(block => {
        const langLabel = block.language ? `\n// ${block.language?.toUpperCase()}\n` : '\n';
        return langLabel + block.content;
      })
      .join('\n\n---\n\n');
    
    await navigator.clipboard.writeText(codeContent);
    ElMessage.success('📝 所有代码已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 解析AI结果（从块中提取JSON）
const parseAIResult = () => {
  try {
    // 查找JSON代码块
    const jsonBlock = contentBlocks.value.find(
      block => block.type === 'code' && block.language === 'json'
    );
    
    if (jsonBlock) {
      const jsonData = JSON.parse(jsonBlock.content);
      
      if (jsonData.tableName && jsonData.fields) {
        // 更新数据库结构
        databaseStructure.value.tableName = jsonData.tableName;
        databaseStructure.value.fields = jsonData.fields;
        
        // 生成SQL
        generateSQL();
        
        ElMessage.success('✅ JSON结果解析成功，已更新数据库结构');
      } else {
        ElMessage.warning('JSON格式不符合要求，缺少必要字段');
      }
    } else {
      ElMessage.warning('未找到JSON格式的分析结果');
    }
  } catch (error) {
    console.error('解析JSON失败:', error);
    ElMessage.error('JSON解析失败，请检查格式');
  }
};

// 初始化流式解析器
const initStreamParser = () => {
  streamParser.value = new StreamParser((blocks) => {
    contentBlocks.value = blocks;
  });
};

// 重置内容块
const resetContentBlocks = () => {
  contentBlocks.value = [];
  if (streamParser.value) {
    streamParser.value.reset();
  }
};

// ==================== 数据库结构分析相关方法 ====================

// AI智能分析文档结构
const analyzeWithAI = async () => {
  if (!editableMarkdown.value) {
    ElMessage.warning('请先上传并解析文档');
    return;
  }

  // 确保有可用的提示词
  if (!customPrompt.value.trim()) {
    ElMessage.info('正在加载最佳提示词...');
    resetToOptimalPrompt();
  }

  aiAnalyzing.value = true;
  
  // 创建取消控制器
  abortController.value = new AbortController();
  
  // 初始化新的块状解析器
  initStreamParser();
  resetContentBlocks();
  
  // 初始化流式输出，显示提示词
  streamOutput.value = `🤖 AI分析开始时间: ${new Date().toLocaleString()}\n\n`;
  aiGeneratedContent.value = ''; // 清空AI生成内容
  
  // 重置AI内容缓冲区状态
  aiContentBuffer.value = '';
  lastContentFlushTime.value = Date.now();
  appendStreamOutput(`📝 使用提示词:\n${customPrompt.value.trim()}\n\n`);
  appendStreamOutput(`📄 分析文档: ${parseResult.value?.fileName || '未知文档'}\n`);
  appendStreamOutput(`📊 文档长度: ${editableMarkdown.value.length} 字符\n\n`);
  appendStreamOutput(`🚀 正在启动AI推理引擎...\n`);
  
  // 显示流式分析进度
  const startTime = Date.now();
  const progressNotification = ElMessage({
    message: '🚀 启动AI深度推理分析引擎...',
    type: 'info',
    duration: 0, // 不自动关闭
    showClose: false
  });

  // 模拟流式输出的进度更新
  const progressSteps = [
    { time: 1000, message: '🧠 DeepSeek推理引擎已启动，开始深度分析' },
    { time: 2000, message: '📊 正在解析文档结构和数据字段' },
    { time: 4000, message: '🔍 智能识别业务实体和关系模型' },
    { time: 6000, message: '🏗️ 生成PostgreSQL数据库设计方案' },
    { time: 8000, message: '✨ 优化字段类型和索引策略' },
    { time: 10000, message: '📝 正在生成最终分析报告' }
  ];

  progressSteps.forEach(step => {
    setTimeout(() => {
      if (aiAnalyzing.value) {
        // 更新流式输出
        appendStreamOutput(`${step.message}\n`);
        
        // 显示进度通知
        progressNotification.close();
        ElMessage({
          message: step.message,
          type: 'info',
          duration: 1500
        });
      }
    }, step.time);
  });
  
  try {
    // 发送分析请求数据（使用fetch进行流式请求）
    // console.log('开始发送流式分析请求...');
    const streamResponse = await fetch('http://localhost:8181/api/ai/analyze-document-structure-stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      },
      signal: abortController.value.signal,
      body: JSON.stringify({
        markdownContent: editableMarkdown.value,
        fileName: parseResult.value?.fileName || '未知文档',
        customPrompt: customPrompt.value,
        useStream: true
      })
    });

    // console.log('流式响应状态:', streamResponse.status, streamResponse.statusText);

    if (!streamResponse.ok) {
      const errorText = await streamResponse.text();
      console.error('错误响应内容:', errorText);
      throw new Error(`HTTP错误: ${streamResponse.status} - ${errorText}`);
    }

    // 桌面浏览器优化的更新策略
    let updateBuffer = '';
    let lastUpdateTime = 0;
    const updateInterval = 100; // 桌面浏览器高频更新
    
    // 批量更新UI的函数
    const flushUIUpdates = () => {
      if (updateBuffer) {
        appendStreamOutput(updateBuffer);
        updateBuffer = '';
        lastUpdateTime = Date.now();
      }
    };
    
    // 使用requestAnimationFrame确保UI更新流畅
    const scheduleUIUpdate = (content) => {
      // 在最早阶段拦截并清理被外部hook工具篡改的内容
      let cleanContent = content;
      
      // 检测并移除"响应行 X:"前缀（来自外部hook工具）
      const responseLinePattern = /^响应行\s*\d+:\s*/;
      if (responseLinePattern.test(content)) {
        cleanContent = content.replace(responseLinePattern, '');
        console.warn('在scheduleUIUpdate中检测到hook工具注入，已清理:', content.substring(0, 30) + '...');
      }
      
      // 如果清理后的内容是原始SSE数据或空白，跳过
      if (cleanContent.startsWith('data: ') || cleanContent.trim() === '') {
        return;
      }
      
      // 桌面端：高频更新策略
      updateBuffer += cleanContent;
      const now = Date.now();
      if (now - lastUpdateTime >= updateInterval) {
        requestAnimationFrame(flushUIUpdates);
      }
    };

    // 处理流式响应
    const reader = streamResponse.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }
    
    const decoder = new TextDecoder();
    let finalResult = null;
    let buffer = ''; // 用于处理不完整的行

    while (true) {
      const { value, done } = await reader.read();
      if (done) {
        // 处理剩余的buffer内容
        if (buffer.trim()) {
          const remainingLines = buffer.split('\n').filter(line => line.trim());
          for (const line of remainingLines) {
            await processSSELine(line, scheduleUIUpdate);
          }
        }
        // 确保所有UI更新都被刷新
        if (updateBuffer) {
          flushUIUpdates();
        }
        // 确保AI内容缓冲区被完全清空
        await finalFlushAIContent();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;

      // 按行处理，保留不完整的行在buffer中
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 保存最后一个可能不完整的行

      for (const line of lines) {
        if (line.trim()) {
          const result = await processSSELine(line, scheduleUIUpdate);
          if (result) {
            finalResult = result;
          }
        }
      }
    }
    

    // SSE行处理函数 - 正确处理事件类型
    async function processSSELine(line, updateCallback) {
      // 直接在函数内部创建保护包装器
      const protectedCallback = (content) => {
        // 多层过滤确保完全清理
        let cleanContent = content;
        
        // 移除"响应行 X:"前缀
        cleanContent = cleanContent.replace(/^响应行\s*\d+:\s*/, '');
        
        // 跳过原始SSE数据
        if (cleanContent.startsWith('data: ') || cleanContent.trim() === '') {
          console.log('跳过原始SSE数据或空内容:', cleanContent.substring(0, 50) + '...');
          return;
        }
        
        // 调用原始回调
        updateCallback(cleanContent);
      };
      // 使用静态变量来跟踪事件类型
      if (!processSSELine.currentEventType) {
        processSSELine.currentEventType = null;
      }
      // console.log('前端收到SSE行:', line);
      
      // 处理SSE格式
      if (line.startsWith('event:')) {
        // 记录事件类型
        processSSELine.currentEventType = line.substring(6).trim();
        return null;
      } else if (line.startsWith('data:')) {
        const data = line.substring(5).trim();
        // console.log('SSE数据 (事件类型: {}): {}', processSSELine.currentEventType, data);
        
        if (data === '[DONE]') {
          protectedCallback('✅ 流式分析完成\n');
          return null;
        }
        
        // 根据事件类型处理数据
        if (processSSELine.currentEventType === 'ai_content') {
          // AI生成的内容 - 直接发送到AI内容区域
          // console.log('处理AI内容:', data);
          handleAIContentChunk(data);
        } else if (processSSELine.currentEventType === 'progress') {
          // 进度消息 - 过滤掉不需要的消息
          // console.log('进度消息:', data, '包含"...": ', data.includes('...'));
          if (!data.includes('💬') && !data.includes('接收到AI内容') && !data.includes('...') && data.trim() !== '') {
            protectedCallback(data + '\n');
          }
        } else if (processSSELine.currentEventType === 'result') {
          // 最终结果
          try {
            const jsonData = JSON.parse(data);
            // console.log('解析到最终结果:', jsonData);
            protectedCallback('📊 接收到完整分析结果\n');
            return jsonData;
          } catch (e) {
            console.error('解析最终结果失败:', e);
          }
        } else {
          // 其他类型或无类型 - 尝试JSON解析
          try {
            const jsonData = JSON.parse(data);
            if (jsonData.scaleType) {
              // console.log('解析到最终结果:', jsonData);
              protectedCallback('📊 接收到完整分析结果\n');
              return jsonData;
            }
          } catch (e) {
            // 不是JSON，作为普通文本处理 - 过滤掉不需要的消息
            // console.log('无类型文本数据:', data, '包含"...": ', data.includes('...'));
            if (!data.includes('💬') && !data.includes('接收到AI内容') && !data.includes('...') && data.trim() !== '') {
              // 再次过滤，防止hook工具在data层面注入
              const filteredData = data.replace(/^响应行\s*\d+:\s*/, '');
              protectedCallback(data + '\n');
            }
          }
        }
        
        // 重置事件类型
        processSSELine.currentEventType = null;
      }
      return null;
    }

    // 处理响应结果
    let response;
    if (finalResult) {
      response = { success: true, data: finalResult };
    } else {
      response = { success: true, data: { tableName: 'streaming_analysis_complete', fields: [] } };
    }

    // 关闭进度提示
    progressNotification.close();

    if (response.success) {
      aiAnalysisResult.value = response.data;
      
      // 应用AI分析结果到数据库结构
      databaseStructure.value.tableName = response.data.tableName || '';
      databaseStructure.value.tableComment = response.data.tableComment || '';
      databaseStructure.value.fields = response.data.fields || [];
      
      // 生成SQL
      generateSQL();
      
      const processingTime = Date.now() - startTime;
      
      // 更新流式输出 - 显示完成信息
      appendStreamOutput(`\n✅ AI分析成功完成！\n`);
      appendStreamOutput(`📊 分析结果:\n`);
      appendStreamOutput(`   • 表名: ${response.data.tableName}\n`);
      appendStreamOutput(`   • 字段数量: ${response.data.fields?.length || 0}\n`);
      appendStreamOutput(`   • 分析时间: ${response.data.analysisTimeMs || processingTime}ms\n`);
      appendStreamOutput(`   • 完成时间: ${new Date().toLocaleString()}\n\n`);
      
      ElMessage.success({
        message: `🎉 AI分析完成！使用已验证最佳提示词，处理时间: ${response.data.analysisTimeMs || processingTime}ms`,
        duration: 3000
      });
    } else {
      // 更新流式输出 - 显示错误信息
      appendStreamOutput(`\n❌ AI分析失败: ${response.message || '未知错误'}\n`);
      appendStreamOutput(`   • 失败时间: ${new Date().toLocaleString()}\n\n`);
      
      ElMessage.error(response.message || 'AI分析失败');
    }
  } catch (error) {
    // 关闭进度提示
    progressNotification.close();
    
    // 检查是否是用户取消
    if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
      appendStreamOutput(`\n⚠️ AI分析已被用户取消\n`);
      appendStreamOutput(`   • 取消时间: ${new Date().toLocaleString()}\n\n`);
      
      ElMessage.warning('⚠️ AI分析已取消');
      console.log('AI分析被用户取消');
    } else {
      appendStreamOutput(`\n💥 AI分析发生错误: ${error.message || '未知错误'}\n`);
      appendStreamOutput(`   • 错误时间: ${new Date().toLocaleString()}\n`);
      appendStreamOutput(`   • 错误类型: ${error.name || 'Unknown'}\n\n`);
      
      console.error('AI分析错误:', error);
      ElMessage.error('AI分析失败：' + (error.message || '未知错误'));
    }
  } finally {
    aiAnalyzing.value = false;
    abortController.value = null;
  }
};

// 取消AI分析
const cancelAIAnalysis = () => {
  if (abortController.value) {
    abortController.value.abort();
    
    // 如果有EventSource，也要关闭它
    if (abortController.value.eventSource) {
      abortController.value.eventSource.close();
    }
    
    appendStreamOutput(`\n⚠️ AI分析已被用户取消\n`);
    appendStreamOutput(`   • 取消时间: ${new Date().toLocaleString()}\n\n`);
    
    ElMessage.warning('AI分析已取消');
  }
  
  aiAnalyzing.value = false;
};

// 清空流式输出
const clearStreamOutput = () => {
  streamOutput.value = '';
  ElMessage.success('✅ 输出已清空');
};

// 复制流式输出
const copyStreamOutput = async () => {
  if (!streamOutput.value.trim()) {
    ElMessage.warning('没有可复制的内容');
    return;
  }
  await copyTextToClipboard(streamOutput.value, '📋 流式输出已复制到剪贴板');
};

// 保护函数免受外部hook工具干扰
const createProtectedAppendFunction = () => {
  const originalAppend = (text) => {
    // 智能过滤并修复被hook工具篡改的内容
    let cleanText = text;
    
    // 检测并移除"响应行 X:"前缀（来自外部hook工具）
    const responseLinePattern = /^响应行\s*\d+:\s*/;
    if (responseLinePattern.test(text)) {
      cleanText = text.replace(responseLinePattern, '');
      console.warn('检测到外部hook工具注入的前缀，已自动移除:', text.substring(0, 20) + '...');
    }
    
    // 如果清理后的内容是原始SSE数据，跳过显示
    if (cleanText.startsWith('data: ') || cleanText.trim() === '') {
      return;
    }
    
    streamOutput.value += cleanText;
    // 自动滚动到底部
    nextTick(() => {
      const streamElement = document.querySelector('.stream-output');
      if (streamElement) {
        streamElement.scrollTop = streamElement.scrollHeight;
      }
    });
  };
  
  // 返回受保护的函数
  return originalAppend;
};

// 添加流式输出内容
const appendStreamOutput = createProtectedAppendFunction();


// 通用复制函数 - 处理现代浏览器API和兼容性
const copyTextToClipboard = async (text, successMessage) => {
  try {
    // 优先使用现代Clipboard API
    await navigator.clipboard.writeText(text);
    ElMessage.success(successMessage);
  } catch (error) {
    // 降级方案 - 显示文本供用户手动复制
    ElMessageBox.alert(
      `复制功能不可用，请手动复制以下内容：\n\n${text}`,
      '手动复制',
      {
        confirmButtonText: '已复制',
        type: 'info',
        customStyle: {
          'white-space': 'pre-wrap',
          'word-break': 'break-all'
        }
      }
    ).then(() => {
      ElMessage.success(successMessage);
    }).catch(() => {
      // 用户取消
    });
  }
};

// 复制AI生成的SQL语句到剪贴板
const copyAISQL = async () => {
  if (!aiAnalysisResult.value?.sqlStatements) {
    ElMessage.warning('没有可复制的SQL语句');
    return;
  }
  await copyTextToClipboard(aiAnalysisResult.value.sqlStatements, 'AI生成的SQL语句已复制到剪贴板');
};

// 将AI生成的字段应用到字段列表
const applySQLToFields = () => {
  if (!aiAnalysisResult.value || !aiAnalysisResult.value.fields) {
    ElMessage.warning('没有可应用的字段信息');
    return;
  }
  
  // 确认操作
  ElMessageBox.confirm(
    '这将覆盖当前的字段配置，确定要应用AI生成的字段结构吗？',
    '确认应用',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 应用字段
    databaseStructure.value.fields = [...aiAnalysisResult.value.fields];
    generateSQL();
    ElMessage.success('已应用AI生成的字段结构');
  }).catch(() => {
    // 用户取消
  });
};

// 获取模型信息
const fetchModelInfo = async () => {
  try {
    const response = await request({
      url: '/api/ai/model-info',
      method: 'GET'
    });
    
    if (response.success && response.data) {
      modelInfo.value = response.data;
    }
  } catch (error) {
    console.error('获取模型信息失败:', error);
    // 设置一个默认值以避免一直显示加载状态
    modelInfo.value = null;
  }
};

// 从URL中提取主机信息
const getHostFromUrl = (url) => {
  if (!url) return 'Unknown';
  try {
    const urlObj = new URL(url);
    return `${urlObj.hostname}:${urlObj.port}`;
  } catch {
    return url;
  }
};

// 打开AI对话
const openAIChat = () => {
  if (!editableMarkdown.value) {
    ElMessage.warning('请先上传并解析文档');
    return;
  }
  
  showAIChat.value = true;
  
  // 初始化对话，添加欢迎消息
  if (chatMessages.value.length === 0) {
    chatMessages.value.push({
      role: 'assistant',
      content: '您好！我是数据库设计助手。我已经理解了您上传的评估量表文档。请问有什么关于数据库设计、字段定义或表结构的问题需要我帮助您解答吗？',
      timestamp: new Date()
    });
  }
};

// 发送聊天消息
const sendChatMessage = async () => {
  if (!chatInput.value.trim()) return;
  
  const userMessage = chatInput.value.trim();
  chatInput.value = '';
  
  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content: userMessage,
    timestamp: new Date()
  });
  
  chatLoading.value = true;
  
  try {
    const response = await request({
      url: '/api/ai/chat',
      method: 'POST',
      timeout: 600000, // 10分钟超时
      data: {
        message: userMessage,
        context: editableMarkdown.value
      }
    });
    
    if (response.success) {
      // 添加AI回复
      chatMessages.value.push({
        role: 'assistant',
        content: response.data,
        timestamp: new Date()
      });
    } else {
      ElMessage.error('AI回复失败: ' + response.message);
    }
  } catch (error) {
    console.error('AI对话错误:', error);
    ElMessage.error('AI对话失败，请稍后再试');
  } finally {
    chatLoading.value = false;
  }
};

// 清空对话记录
const clearChat = () => {
  ElMessageBox.confirm('确定要清空所有对话记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    chatMessages.value = [];
    chatInput.value = '';
    ElMessage.success('对话记录已清空');
  }).catch(() => {});
};

// 格式化消息内容（简单的Markdown转HTML）
const formatMessageContent = (content) => {
  if (!content) return '';
  
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    .replace(/\n/g, '<br>');
};

// 自动检测字段
const autoDetectFields = async () => {
  if (!editableMarkdown.value) {
    ElMessage.warning('请先上传并解析文档');
    return;
  }

  detectingFields.value = true;
  
  try {
    // 简单的字段检测逻辑（可以替换为更复杂的AI分析）
    const lines = editableMarkdown.value.split('\n');
    const detectedFields = [];
    
    // 检测表格行、表单字段等
    lines.forEach(line => {
      // 检测表格头部
      if (line.includes('|') && !line.startsWith('|-')) {
        const columns = line.split('|').map(col => col.trim()).filter(col => col);
        columns.forEach(col => {
          if (col && !detectedFields.find(f => f.name === col.toLowerCase().replace(/\s+/g, '_'))) {
            detectedFields.push({
              name: col.toLowerCase().replace(/\s+/g, '_'),
              type: 'VARCHAR',
              length: '255',
              nullable: true,
              comment: col,
              defaultValue: ''
            });
          }
        });
      }
      
      // 检测关键词段落
      const fieldPatterns = [
        /(\d+)\.\s*(.+?)[:：]/g,  // 编号字段
        /【(.+?)】/g,              // 中文方括号
        /\*\*(.+?)\*\*/g          // 粗体字段
      ];
      
      fieldPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const fieldName = match[1] || match[2];
          if (fieldName && fieldName.length < 50 && !detectedFields.find(f => f.name === fieldName.toLowerCase().replace(/\s+/g, '_'))) {
            detectedFields.push({
              name: fieldName.toLowerCase().replace(/\s+/g, '_'),
              type: 'VARCHAR',
              length: '255',
              nullable: true,
              comment: fieldName,
              defaultValue: ''
            });
          }
        }
      });
    });
    
    // 添加基础字段
    const baseFields = [
      { name: 'id', type: 'INT', length: '', nullable: false, comment: '主键ID', defaultValue: 'AUTO_INCREMENT' },
      { name: 'assessment_id', type: 'VARCHAR', length: '50', nullable: false, comment: '评估记录ID', defaultValue: '' },
      { name: 'created_at', type: 'DATETIME', length: '', nullable: false, comment: '创建时间', defaultValue: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'DATETIME', length: '', nullable: false, comment: '更新时间', defaultValue: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
    ];
    
    databaseStructure.value.fields = [...baseFields, ...detectedFields.slice(0, 20)]; // 限制字段数量
    
    // 自动生成表名
    if (!databaseStructure.value.tableName) {
      const fileName = parseResult.value?.fileName || '量表';
      databaseStructure.value.tableName = `assessment_${fileName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}`;
    }
    
    generateSQL();
    ElMessage.success(`检测到 ${detectedFields.length} 个字段`);
    
  } catch (error) {
    console.error('字段检测错误:', error);
    ElMessage.error('字段检测失败');
  } finally {
    detectingFields.value = false;
  }
};

// 添加字段
const addField = () => {
  databaseStructure.value.fields.push({
    name: '',
    type: 'VARCHAR',
    length: '255',
    nullable: true,
    comment: '',
    defaultValue: ''
  });
  generateSQL();
};

// 删除字段
const removeField = (index) => {
  databaseStructure.value.fields.splice(index, 1);
  generateSQL();
};

// 生成SQL语句
const generateSQL = () => {
  if (!databaseStructure.value.tableName || !databaseStructure.value.fields.length) {
    generatedSQL.value = '';
    return;
  }
  
  let sql = `CREATE TABLE \`${databaseStructure.value.tableName}\` (\n`;
  
  const fieldSqls = databaseStructure.value.fields.map(field => {
    if (!field.name) return '';
    
    let fieldSql = `  \`${field.name}\` ${field.type}`;
    
    // 添加长度
    if (field.length && ['VARCHAR', 'DECIMAL'].includes(field.type)) {
      fieldSql += `(${field.length})`;
    }
    
    // 添加是否允许空
    fieldSql += field.nullable ? ' NULL' : ' NOT NULL';
    
    // 添加默认值
    if (field.defaultValue) {
      if (field.defaultValue.includes('AUTO_INCREMENT') || field.defaultValue.includes('CURRENT_TIMESTAMP')) {
        fieldSql += ` DEFAULT ${field.defaultValue}`;
      } else {
        fieldSql += ` DEFAULT '${field.defaultValue}'`;
      }
    }
    
    // 添加注释
    if (field.comment) {
      fieldSql += ` COMMENT '${field.comment}'`;
    }
    
    return fieldSql;
  }).filter(sql => sql);
  
  sql += fieldSqls.join(',\n');
  
  // 添加主键
  const primaryKey = databaseStructure.value.fields.find(f => f.name === 'id');
  if (primaryKey) {
    sql += ',\n  PRIMARY KEY (`id`)';
  }
  
  sql += '\n)';
  
  // 添加表注释
  if (databaseStructure.value.tableComment) {
    sql += ` COMMENT='${databaseStructure.value.tableComment}'`;
  }
  
  sql += ' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';
  
  generatedSQL.value = sql;
};

// 复制SQL
const copySQL = async () => {
  if (!generatedSQL.value) {
    ElMessage.warning('没有可复制的SQL语句');
    return;
  }
  await copyTextToClipboard(generatedSQL.value, 'SQL语句已复制到剪贴板');
};

// 执行建表SQL
const executeSQL = async () => {
  if (!generatedSQL.value) {
    ElMessage.warning('没有可执行的SQL语句');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确认执行以下SQL语句创建数据库表吗？\n\n表名: ${databaseStructure.value.tableName}\n字段数: ${databaseStructure.value.fields.length}`,
      '确认建表',
      {
        confirmButtonText: '确认执行',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'sql-confirm-dialog'
      }
    );

    const response = await request({
      url: '/api/database/execute-ddl',
      method: 'POST',
      data: {
        sql: generatedSQL.value,
        tableName: databaseStructure.value.tableName,
        tableComment: databaseStructure.value.tableComment
      }
    });

    if (response.success) {
      ElMessage.success('数据库表创建成功！');
      
      // 可以在这里添加后续操作，比如保存量表配置等
      await saveScaleConfiguration();
      
    } else {
      ElMessage.error(response.message || '建表失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('建表错误:', error);
      ElMessage.error('建表失败：' + (error.message || '未知错误'));
    }
  }
};

// 确认数据库结构
const confirmDatabaseStructure = async () => {
  if (!databaseStructure.value.fields.length) {
    ElMessage.warning('请先分析或添加字段信息');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      '确认当前的数据库结构配置吗？确认后将保存量表配置并可用于评估。',
      '确认数据库结构',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success'
      }
    );

    await saveScaleConfiguration();
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认结构错误:', error);
    }
  }
};

// 保存量表配置
const saveScaleConfiguration = async () => {
  try {
    const scaleConfig = {
      name: parseResult.value?.fileName || '未命名量表',
      markdownContent: editableMarkdown.value,
      databaseStructure: databaseStructure.value,
      sql: generatedSQL.value,
      aiAnalysis: aiAnalysisResult.value
    };

    const response = await request({
      url: '/api/assessment-scales/save-configuration',
      method: 'POST',
      data: scaleConfig
    });

    if (response.success) {
      ElMessage.success('量表配置保存成功！');
      refreshRecentScales();
    } else {
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存配置错误:', error);
    ElMessage.error('保存失败：' + (error.message || '未知错误'));
  }
};

// 监听字段变化，自动生成SQL
watchEffect(() => {
  if (databaseStructure.value.fields.length > 0) {
    generateSQL();
  }
});

// 组件挂载
onMounted(() => {
  refreshRecentScales();
  // 自动检查服务状态
  checkDoclingStatus();
  checkAIStatus();
  // 获取模型信息
  fetchModelInfo();
  // 初始化提示词
  loadCustomPrompt();
});
</script>

<style scoped>
.pdf-upload-container {
  min-height: calc(100vh - 60px);
  padding: 16px;
  overflow: auto;
}

.upload-header-card {
  margin-bottom: 16px;
}

.page-title h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.subtitle {
  color: #909399;
  font-size: 13px;
}

.compact-upload {
  width: 100%;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.status-actions {
  text-align: center;
}

.status-tag {
  margin-bottom: 4px;
  font-weight: 500;
  display: inline-block;
  margin-right: 4px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
}

.ai-service-info {
  text-align: center;
  font-size: 11px !important;
  line-height: 1.2;
  margin-top: 6px !important;
}

.upload-progress-compact {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 13px;
  color: #606266;
  margin: 0;
}

/* 主工作区域 */
.main-workspace {
  height: calc(100vh - 140px);
}

/* AI生成内容区域样式 */
.ai-generated-content {
  max-height: 400px;
  overflow-y: auto;
  scroll-behavior: smooth;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-top: 8px;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.ai-generated-content::-webkit-scrollbar {
  width: 6px;
}

.ai-generated-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-generated-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ai-generated-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.editor-card {
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-title {
  font-weight: 600;
  color: #303133;
}

.editor-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.editor-content {
  height: calc(100% - 80px);
  overflow: hidden;
}

/* Markdown编辑器样式 */
.markdown-editor {
  height: 100% !important;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.markdown-editor :deep(.el-textarea__inner) {
  height: 100% !important;
  resize: none !important;
  border: none !important;
  box-shadow: none !important;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Markdown预览样式 */
.markdown-preview {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
}

.markdown-preview h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.markdown-preview h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 24px 0 12px 0;
  color: #606266;
}

.markdown-preview h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 16px 0 8px 0;
  color: #909399;
}

.markdown-preview p {
  margin: 0 0 12px 0;
  color: #303133;
}

.markdown-preview strong {
  font-weight: 600;
  color: #303133;
}

.markdown-preview em {
  font-style: italic;
  color: #606266;
}

/* 提示词编辑器样式 */
.prompt-editor-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafbfc;
}

.prompt-info p {
  margin: 4px 0;
  font-size: 13px;
}

.prompt-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prompt-textarea {
  border-radius: 4px;
}

.prompt-textarea :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  line-height: 1.5;
  font-size: 13px;
}

.prompt-stats {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f0f2f5;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 分屏模式 */
.split-mode {
  display: flex;
  gap: 16px;
  height: 100%;
}

.split-left, .split-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.split-header {
  padding: 8px 12px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  font-size: 13px;
  font-weight: 500;
  color: #606266;
}

.split-left .markdown-editor,
.split-right .markdown-preview {
  flex: 1;
  border-radius: 0 0 4px 4px;
}

/* 右侧属性面板 */
.properties-card {
  height: 100%;
}

.properties-content {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.parse-stats .el-descriptions {
  margin-bottom: 16px;
}

.quick-actions {
  margin-bottom: 16px;
}

.recent-files {
  height: 200px;
}

.recent-file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.2s;
}

.recent-file-item:hover {
  background: #f5f7fa;
  border-color: #409eff;
}

.file-content {
  flex: 1;
  cursor: pointer;
}

.file-actions {
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.recent-file-item:hover .file-actions {
  opacity: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 13px;
}

.file-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.file-time {
  color: #909399;
}

/* AI生成内容样式 */
.ai-generated-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-top: 8px;
}

.ai-generated-text {
  color: #303133;
  line-height: 1.6;
  font-size: 13px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ai-generated-text strong {
  color: #1976d2;
}

.ai-generated-text .sql-keyword {
  color: #0066cc;
  font-weight: bold;
}

.ai-generated-text .sql-type {
  color: #cc6600;
}

.ai-generated-text .sql-string {
  color: #009900;
}

/* 流式输出区域样式 */
.stream-output {
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.stream-text {
  margin: 0;
  white-space: pre-wrap;
  color: #495057;
}

.empty-recent {
  text-align: center;
  padding: 20px;
}

.status-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.quick-start-guide {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.quick-start-guide .el-divider {
  margin: 0 0 12px 0;
}

.quick-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #606266;
}

.step-number {
  flex-shrink: 0;
}

.step-text {
  line-height: 1.4;
}

/* 量表配置区域样式 */
.scale-config-section {
  margin-top: 16px;
}

.scale-config-section .el-form-item {
  margin-bottom: 12px;
}

.scale-config-section .el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scale-config-section .el-checkbox {
  margin-right: 0;
}

.docling-status-alert {
  margin-bottom: 15px;
}

/* 确认保存对话框样式 */
:deep(.confirm-save-dialog) {
  width: 500px;
}

:deep(.confirm-save-dialog .el-message-box__message) {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-line;
  color: #303133;
}

/* 滚动条样式 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-scrollbar__wrap)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

:deep(.el-scrollbar__wrap)::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-scrollbar__wrap)::-webkit-scrollbar-track {
  background: transparent;
}

.docling-status-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.docling-status-content p {
  margin: 0;
  flex: 1;
}

.docling-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.format-alert {
  margin-top: 15px;
}

.feature-list {
  margin: 10px 0;
  padding-left: 20px;
}

.feature-list li {
  margin: 8px 0;
  color: #606266;
  line-height: 1.6;
}

.format-list {
  margin: 10px 0;
  padding-left: 20px;
}

.format-list li {
  margin: 5px 0;
  color: #606266;
}

/* 文件格式信息样式 */
.format-info-section {
  margin-bottom: 16px;
}

.format-list .format-category {
  margin-bottom: 12px;
}

.format-list .format-category:last-child {
  margin-bottom: 0;
}

.format-items {
  margin-top: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.format-items .el-tag {
  margin: 0;
}

/* 上传部分样式优化 */
.upload-section {
  width: 100%;
}

.format-selector .el-select {
  width: 100%;
}

.upload-tip {
  text-align: center;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.upload-tip > div {
  margin-bottom: 2px;
}

.upload-tip > div:last-child {
  margin-bottom: 0;
}

.format-selector-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.format-selector-inline span {
  flex-shrink: 0;
}

/* 下载操作样式 */
.download-actions {
  margin-bottom: 16px;
}

.download-format-info {
  text-align: center;
}

.upload-card,
.preview-card,
.recent-scales-card {
  min-height: 400px;
}

.recent-scales-card {
  margin-top: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mode-switch {
  margin-left: 10px;
}

.pdf-upload {
  margin: 20px 0;
}

.upload-progress {
  margin: 20px 0;
  text-align: center;
}

.progress-details {
  margin-top: 15px;
}

.progress-text {
  margin: 10px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.processing-steps {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.processing-steps :deep(.el-timeline) {
  padding-left: 0;
}

.processing-steps :deep(.el-timeline-item__content) {
  font-size: 13px;
  color: #606266;
}

.timeline-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  background-color: #f0f2f5;
  border-radius: 50%;
}

.empty-preview {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content {
  padding: 10px 0;
}

.preview-actions {
  margin-top: 20px;
  text-align: center;
}

.preview-actions .el-button {
  margin: 0 5px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

:deep(.el-upload-dragger) {
  min-height: 200px !important;
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
}

:deep(.el-icon--upload) {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}

:deep(.el-upload__text) {
  color: #606266 !important;
  font-size: 14px !important;
  text-align: center !important;
  white-space: normal !important;
  word-break: break-all !important;
  line-height: 1.6 !important;
  padding: 0 8px !important;
  display: block !important;
  overflow: visible !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
}

/* Docling帮助对话框样式 */
:deep(.docling-help-dialog) {
  width: 600px;
}

:deep(.docling-help-dialog .el-message-box__content) {
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
}

/* 数据库结构分析面板样式 */
.database-structure-card {
  border: 1px solid #e4e7ed;
  margin-top: 16px;
}

.database-content {
  min-height: 200px;
  max-height: 800px;
  overflow-y: auto;
}

.ai-analysis-section {
  margin-bottom: 16px;
}

.ai-suggestions p {
  margin: 4px 0;
  font-size: 13px;
}

/* AI功能介绍样式 */
.ai-intro {
  line-height: 1.6;
}

.feature-highlight {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9ff;
  border: 1px solid #e1e6ff;
  border-radius: 8px;
}

.feature-highlight h4 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.feature-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.capability-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.capability-icon {
  font-size: 18px;
  margin-top: 2px;
}

.capability-item strong {
  display: block;
  color: #303133;
  font-size: 14px;
  margin-bottom: 4px;
}

.capability-item p {
  margin: 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.usage-steps {
  margin: 20px 0;
  padding: 16px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
}

.usage-steps h5 {
  margin: 0 0 8px 0;
  color: #e6a23c;
  font-size: 14px;
  font-weight: 600;
}

.usage-steps ol {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.usage-steps li {
  margin-bottom: 6px;
  color: #606266;
  font-size: 13px;
}

.tech-info {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
}

.tech-info p {
  margin: 0;
  color: #1e40af;
  font-size: 13px;
}

/* AI生成的SQL语句样式 */
.ai-sql-section {
  margin-top: 16px;
}

.sql-display {
  max-height: 400px;
  overflow-y: auto;
  background: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
}

.sql-display pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.sql-display code {
  color: #d4d4d4;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.6;
  background: none;
  padding: 0;
}

.fields-section {
  margin: 16px 0;
}

.fields-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.field-count {
  font-size: 12px;
  color: #909399;
}

.sql-preview {
  margin: 16px 0;
}

.sql-actions {
  display: flex;
  gap: 8px;
}

/* SQL确认对话框样式 */
:deep(.sql-confirm-dialog) {
  width: 500px;
}

:deep(.sql-confirm-dialog .el-message-box__content) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

/* AI对话窗口样式 */
.ai-chat-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.message {
  margin-bottom: 16px;
}

.message.user .message-content {
  margin-left: 50px;
  background: #e3f2fd;
  border-radius: 12px 12px 4px 12px;
}

.message.assistant .message-content {
  margin-right: 50px;
  background: #f5f5f5;
  border-radius: 12px 12px 12px 4px;
}

.message-content {
  padding: 12px 16px;
  position: relative;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.message-role {
  font-weight: 600;
  color: #409eff;
}

.message-time {
  color: #909399;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-text code {
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Monaco, Consolas, monospace;
}

.message-text pre {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.message.loading .message-text {
  color: #909399;
  font-style: italic;
}

.chat-input-area {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.chat-input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.input-tip {
  font-size: 12px;
  color: #909399;
}

.chat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-info {
  font-size: 12px;
  color: #606266;
}

/* AI流式输出样式 */
.ai-stream-section {
  margin: 20px 0;
}

.stream-card {
  border: 2px solid #e1f5fe;
  background: linear-gradient(135deg, #f8fffe 0%, #f0fdf4 100%);
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stream-title {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
}

.stream-output {
  background: #1e1e1e;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.stream-text {
  color: #e0e0e0;
  background: transparent;
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.stream-actions {
  border-top: 1px solid #eee;
  padding-top: 12px;
}

/* 滚动条样式 */
.stream-output::-webkit-scrollbar {
  width: 8px;
}

.stream-output::-webkit-scrollbar-track {
  background: #333;
  border-radius: 4px;
}

.stream-output::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 4px;
}

.stream-output::-webkit-scrollbar-thumb:hover {
  background: #888;
}

/* AI生成内容样式 */
.ai-generated-section {
  margin-top: 16px;
}

.ai-generated-content {
  background: #001529;
  border: 1px solid #1890ff;
  border-radius: 8px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.ai-generated-text {
  color: #52c41a;
  background: transparent;
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* AI生成内容滚动条样式 */
.ai-generated-content::-webkit-scrollbar {
  width: 8px;
}

.ai-generated-content::-webkit-scrollbar-track {
  background: #001529;
  border-radius: 4px;
}

.ai-generated-content::-webkit-scrollbar-thumb {
  background: #1890ff;
  border-radius: 4px;
}

.ai-generated-content::-webkit-scrollbar-thumb:hover {
  background: #40a9ff;
}
</style>

