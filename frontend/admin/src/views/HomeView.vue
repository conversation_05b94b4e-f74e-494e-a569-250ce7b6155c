<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 欢迎区域 -->
    <div class="px-6 py-4 mb-5">
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center">
            <StarIcon class="h-5 w-5 text-primary-700 mr-2" />
            <span class="text-lg text-primary-700 font-medium">欢迎使用评估管理系统</span>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <span class="text-sm text-primary-700 font-medium"
            >欢迎，{{ userInfo?.realName || '系统管理员' }}</span
          >
          <el-button @click="handleLogout" size="small" type="primary" class="logout-btn"
            >退出登录</el-button
          >
        </div>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <el-card class="mx-5 mb-5">
      <template #header>
        <div class="flex flex-col items-start">
          <div class="flex items-center">
            <ChartBarIcon class="h-5 w-5 text-primary-700 mr-2" />
            <span class="text-xl font-bold text-primary-700">系统状态</span>
          </div>
        </div>
      </template>

      <el-row :gutter="160" justify="center">
        <el-col :span="5">
          <div class="text-center p-5">
            <div class="flex items-center justify-center text-sm text-primary-700 font-medium mb-2">
              <BoltIcon class="h-4 w-4 mr-1" />
              <span>后端API状态</span>
            </div>
            <div class="flex items-center justify-center gap-2">
              <div 
                class="w-3 h-3 rounded-full"
                :class="apiStatus === '正常' ? 'bg-green-500' : 'bg-red-500'"
              ></div>
              <div class="text-2xl font-bold" :class="apiStatusClass">
                {{ apiStatus }}
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="text-center p-5">
            <div class="flex items-center justify-center text-sm text-primary-700 font-medium mb-2">
              <TagIcon class="h-4 w-4 mr-1" />
              <span>版本</span>
            </div>
            <div class="text-2xl font-bold text-green-500">1.0.0</div>
          </div>
        </el-col>
      </el-row>

      <div class="mt-5">
        <el-button type="primary" @click="checkApiStatus"
          >检查API状态</el-button
        >
        <el-button type="primary" @click="goToApi">访问API文档</el-button>
      </div>
    </el-card>

    <!-- 快速导航卡片 -->
    <el-card class="mx-5 mb-5">
      <template #header>
        <div class="flex flex-col items-start">
          <div class="flex items-center">
            <RocketLaunchIcon class="h-5 w-5 text-primary-700 mr-2" />
            <span class="text-xl font-bold text-primary-700">快速导航</span>
          </div>
        </div>
      </template>

      <el-row :gutter="24">
        <el-col :span="8">
          <el-card
            class="cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105 inner-card"
            :body-style="{ padding: '20px' }"
            shadow="hover"
            @click="goToUpload"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <DocumentIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-lg font-bold text-primary-700 mb-1">
                  量表上传并数字化
                </div>
                <div class="text-sm text-gray-600">
                  智能解析评估量表并生成数据库表结构
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card
            class="cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105 inner-card"
            :body-style="{ padding: '20px' }"
            shadow="hover"
            @click="goToScales"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <ClipboardDocumentListIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-lg font-bold text-primary-700 mb-1">
                  量表管理
                </div>
                <div class="text-sm text-gray-600">
                  管理已上传的评估量表
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card
            class="cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105 inner-card"
            :body-style="{ padding: '20px' }"
            shadow="hover"
            @click="goToRecords"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <ChartBarIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-lg font-bold text-primary-700 mb-1">
                  评估记录
                </div>
                <div class="text-sm text-gray-600">查看和管理评估记录</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 系统概览 -->
    <el-card class="mx-5 mb-5">
      <template #header>
        <div class="flex flex-col items-start">
          <div class="flex items-center">
            <PresentationChartLineIcon class="h-5 w-5 text-primary-700 mr-2" />
            <span class="text-xl font-bold text-primary-700">系统概览</span>
          </div>
        </div>
      </template>

      <el-row :gutter="24">
        <el-col :span="6">
          <el-card
            class="hover:shadow-md transition-shadow inner-card"
            :body-style="{ padding: '24px' }"
            shadow="hover"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <ClipboardDocumentListIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-3xl font-extrabold text-primary-700">
                  {{ stats.totalScales }}
                </div>
                <div class="text-sm text-primary-700">量表总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card
            class="hover:shadow-md transition-shadow inner-card"
            :body-style="{ padding: '24px' }"
            shadow="hover"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <CheckCircleIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-3xl font-extrabold text-primary-700">
                  {{ stats.totalRecords }}
                </div>
                <div class="text-sm text-primary-700">评估记录</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card
            class="hover:shadow-md transition-shadow inner-card"
            :body-style="{ padding: '24px' }"
            shadow="hover"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <ClockIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-3xl font-extrabold text-primary-700">
                  {{ stats.pendingRecords }}
                </div>
                <div class="text-sm text-primary-700">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card
            class="hover:shadow-md transition-shadow inner-card"
            :body-style="{ padding: '24px' }"
            shadow="hover"
          >
            <div class="flex items-center space-x-4">
              <div class="text-primary-700">
                <ChartPieIcon class="h-10 w-10" />
              </div>
              <div class="flex-1">
                <div class="text-3xl font-extrabold text-primary-700">
                  {{ stats.todayRecords }}
                </div>
                <div class="text-sm text-primary-700">今日新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  StarIcon,
  ChartBarIcon,
  BoltIcon,
  TagIcon,
  RocketLaunchIcon,
  DocumentIcon,
  ClipboardDocumentListIcon,
  PresentationChartLineIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartPieIcon,
} from '@heroicons/vue/24/outline';

const router = useRouter();
const apiStatus = ref('未检查');

// 定义用户信息类型
interface UserInfo {
  realName?: string;
  username?: string;
  role?: string;
  [key: string]: any;
}

const userInfo = ref<UserInfo | null>(null);

const stats = reactive({
  totalScales: 3,
  totalRecords: 1254,
  pendingRecords: 267,
  todayRecords: 23,
});

const apiStatusClass = computed(() => {
  switch (apiStatus.value) {
    case '正常':
      return 'text-primary-500';
    case '异常':
      return 'text-accent-600';
    case '连接失败':
      return 'text-accent-600';
    default:
      return 'text-primary-500';
  }
});

const checkApiStatus = async () => {
  try {
    const response = await fetch('http://localhost:8181/api/health');
    if (response.ok) {
      apiStatus.value = '正常';
      ElMessage.success('API连接正常');
    } else {
      apiStatus.value = '异常';
      ElMessage.error('API连接异常');
    }
  } catch (error) {
    apiStatus.value = '连接失败';
    ElMessage.error('无法连接到API');
  }
};

const goToApi = () => {
  window.open('http://localhost:8181/swagger-ui/index.html', '_blank');
};

// 导航方法
const goToUpload = () => {
  router.push('/assessment/pdf-upload');
};

const goToScales = () => {
  router.push('/assessment/scales');
};

const goToRecords = () => {
  router.push('/assessment/records');
};

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    // 清理认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('refreshToken');
    sessionStorage.clear();

    ElMessage.success('已退出登录');

    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    // 用户取消
  }
};

// 组件挂载时检查API状态和获取用户信息
onMounted(() => {
  checkApiStatus();

  // 获取用户信息
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      userInfo.value = JSON.parse(userStr);
    } catch (error) {
      console.error('解析用户信息失败:', error);
    }
  }
});
</script>
