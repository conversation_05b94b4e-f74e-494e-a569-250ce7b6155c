<template>
  <div class="layout-container">
    <div class="layout-header">
      <h2 class="flex items-center">
        <BuildingOffice2Icon class="h-6 w-6 mr-2 text-primary-700" />
        智慧养老评估平台
      </h2>
      <div class="user-info">
        <span class="user-text">{{ userInfo?.realName || '系统管理员' }}</span>
        <el-button @click="handleLogout" size="small" type="primary" class="logout-btn"
          >退出登录</el-button
        >
      </div>
    </div>
    <div class="layout-content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { BuildingOffice2Icon } from '@heroicons/vue/24/outline';

const router = useRouter();
const userInfo = ref(null);

onMounted(() => {
  // 获取用户信息
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      userInfo.value = JSON.parse(userStr);
    } catch (error) {
      console.error('解析用户信息失败:', error);
    }
  }
});

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    // 清理认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('refreshToken');
    sessionStorage.clear();

    ElMessage.success('已退出登录');

    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    // 用户取消
  }
};
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.layout-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info span {
  color: #606266;
  font-size: 14px;
}

.user-text {
  color: #5357A0;
  font-weight: 500;
}

.logout-btn {
  background: #5357A0;
  border-color: #5357A0;
  color: white;
}

.logout-btn:hover {
  background: #434683;
  border-color: #434683;
}

.layout-content {
  flex: 1;
  background: #f5f7fa;
}
</style>
