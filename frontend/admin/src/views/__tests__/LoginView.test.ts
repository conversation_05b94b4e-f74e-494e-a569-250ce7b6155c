import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../LoginView.vue'
import { ElForm, ElFormItem, ElInput, ElButton } from 'element-plus'

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/login', component: LoginView }
  ]
})

// Mock axios
vi.mock('axios', () => ({
  default: {
    post: vi.fn(() => Promise.resolve({ data: { token: 'mock-token' } }))
  }
}))

describe('LoginView', () => {
  let wrapper: ReturnType<typeof mount>
  
  beforeEach(() => {
    wrapper = mount(LoginView, {
      global: {
        plugins: [router],
        components: {
          ElForm,
          ElFormItem,
          ElInput,
          ElButton
        }
      }
    })
  })

  it('应该渲染登录表单', () => {
    expect(wrapper.find('form').exists()).toBe(true)
    expect(wrapper.find('input[type="text"]').exists()).toBe(true)
    expect(wrapper.find('input[type="password"]').exists()).toBe(true)
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
  })

  it('应该显示系统标题', () => {
    expect(wrapper.text()).toContain('智能评估平台')
  })

  it('应该处理表单提交', async () => {
    const form = wrapper.find('form')
    const usernameInput = wrapper.find('input[type="text"]')
    const passwordInput = wrapper.find('input[type="password"]')
    
    await usernameInput.setValue('admin')
    await passwordInput.setValue('password')
    
    expect(wrapper.vm.loginForm.username).toBe('admin')
    expect(wrapper.vm.loginForm.password).toBe('password')
  })

  it('应该验证表单字段', async () => {
    const submitButton = wrapper.find('button[type="submit"]')
    await submitButton.trigger('click')
    
    // 应该显示验证错误信息
    expect(wrapper.text()).toContain('请输入')
  })

  it('应该处理登录成功', async () => {
    const usernameInput = wrapper.find('input[type="text"]')
    const passwordInput = wrapper.find('input[type="password"]')
    const form = wrapper.find('form')
    
    await usernameInput.setValue('admin')
    await passwordInput.setValue('password123')
    await form.trigger('submit')
    
    // 模拟成功后的跳转
    expect(wrapper.vm.loading).toBeDefined()
  })
})