/* Element Plus 品牌主题配置 - 长春蓝 + 佛手黄 */
:root {
  /* 主色调 - 长春花蓝 */
  --el-color-primary: #5357A0;
  --el-color-primary-light-3: #7e82bb;
  --el-color-primary-light-5: #9ea1d0;
  --el-color-primary-light-7: #bec1e5;
  --el-color-primary-light-8: #d6d8f2;
  --el-color-primary-light-9: #eaebf8;
  --el-color-primary-dark-2: #434683;
  
  /* 成功色 - 恢复Element Plus默认值 */
  /* --el-color-success: #22c55e;
  --el-color-success-light-3: #4ade80;
  --el-color-success-light-5: #86efac;
  --el-color-success-light-7: #bbf7d0;
  --el-color-success-light-8: #dcfce7;
  --el-color-success-light-9: #f0fdf4;
  --el-color-success-dark-2: #16a34a; */
  
  /* 警告色 - 佛手黄（品牌配色） */
  --el-color-warning: #fed81f;
  --el-color-warning-light-3: #ffe985;
  --el-color-warning-light-5: #ffdc52;
  --el-color-warning-light-7: #fff3c4;
  --el-color-warning-light-8: #fffae0;
  --el-color-warning-light-9: #fffdf0;
  --el-color-warning-dark-2: #efbc1a;
  
  /* 危险/错误色 - 红色（最佳实践） */
  --el-color-danger: #ef4444;
  --el-color-danger-light-3: #f87171;
  --el-color-danger-light-5: #fca5a5;
  --el-color-danger-light-7: #fecaca;
  --el-color-danger-light-8: #fee2e2;
  --el-color-danger-light-9: #fef2f2;
  --el-color-danger-dark-2: #dc2626;
  
  /* 信息色 - 蓝色（最佳实践） */
  --el-color-info: #3b82f6;
  --el-color-info-light-3: #60a5fa;
  --el-color-info-light-5: #93c5fd;
  --el-color-info-light-7: #bfdbfe;
  --el-color-info-light-8: #dbeafe;
  --el-color-info-light-9: #eff6ff;
  --el-color-info-dark-2: #2563eb;
  
  /* 按钮文字颜色优化 */
  --el-button-text-color: #ffffff;
  --el-button-active-color: #ffffff;
  --el-tag-text-color: #ffffff;
  
  /* 全局文字颜色 - 使用长春花蓝 */
  --el-text-color-primary: #5357A0;
  --el-text-color-regular: #5357A0;
  --el-text-color-secondary: #7e82bb;
  --el-text-color-placeholder: #9ea1d0;
}

/* 品牌样式覆盖 - 外层卡片保留佛手黄边框 */
.el-card {
  border: 2px solid var(--el-color-warning) !important;
  box-shadow: 0 2px 8px rgba(254, 216, 31, 0.1) !important;
  transition: all 0.3s ease !important;
}

.el-card:hover {
  border-color: var(--el-color-primary) !important;
  box-shadow: 0 4px 16px rgba(83, 87, 160, 0.15) !important;
  transform: translateY(-1px) !important;
}

/* 内部卡片使用简洁边框 */
.el-card.inner-card {
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  background: white !important;
}

.el-card.inner-card:hover {
  border-color: var(--el-color-primary) !important;
  box-shadow: 0 4px 16px rgba(83, 87, 160, 0.1) !important;
}

/* 卡片标题样式统一 */
.el-card__header .text-primary-700 {
  color: var(--el-color-primary-dark-2) !important;
}

.el-card__header .text-xl {
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
}

.el-card__header .font-bold {
  font-weight: 700 !important;
}

/* 确保主题颜色全局应用 */
.text-primary-700 {
  color: var(--el-color-primary-dark-2) !important;
}

/* 按钮样式优化 */
.el-button--primary {
  background: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: white;
}

.el-button--primary:hover {
  background: var(--el-color-primary-dark-2);
  border-color: var(--el-color-primary-dark-2);
  color: white;
}

.el-button--warning {
  background: var(--el-color-warning) !important;
  border-color: var(--el-color-warning) !important;
  color: var(--el-color-primary-dark-2) !important;
}

.el-button--warning:hover {
  background: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
  color: white !important;
}


/* 进度条样式 */
.el-progress-bar__inner {
  background: var(--el-color-primary) !important;
}

/* 输入框焦点样式 */
.el-input__wrapper:focus,
.el-input__wrapper:focus-within {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
}

.el-select .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
}

/* 上传组件样式 */
.el-upload-dragger {
  border: 2px dashed var(--el-color-warning) !important;
  background: var(--el-color-primary-light-9) !important;
}

.el-upload-dragger:hover {
  border-color: var(--el-color-primary) !important;
  background: #fefefe !important;
}

/* 长春花蓝为主的文字配色最佳实践 */

/* 主要标题使用长春花蓝 */
h1, h2, h3, h4, h5, h6 {
  color: var(--el-color-primary) !important;
}

/* 卡片标题 */
.el-card__header h1,
.el-card__header h2, 
.el-card__header h3,
.el-card__header h4,
.el-card__header h5,
.el-card__header h6,
.el-card__header .card-title {
  color: var(--el-color-primary) !important;
}

/* 表单标签 */
.el-form-item__label {
  color: var(--el-color-primary) !important;
  font-weight: 500;
}

/* 菜单项 */
.el-menu-item,
.el-submenu__title {
  color: var(--el-color-primary) !important;
}

/* 面包屑导航 */
.el-breadcrumb__item .el-breadcrumb__inner {
  color: var(--el-color-primary) !important;
}

/* 标签页 */
.el-tabs__item {
  color: var(--el-color-primary) !important;
}

/* 链接 */
a, .el-link {
  color: var(--el-color-primary) !important;
}

a:hover, .el-link:hover {
  color: var(--el-color-primary-dark-2) !important;
}

/* 表格标题 */
.el-table th.el-table__cell {
  color: var(--el-color-primary-dark-2) !important;
  font-weight: 600;
}

/* 重要文字内容 */
.text-important,
.important-text {
  color: var(--el-color-primary) !important;
}


/* 按钮文字颜色 */
.el-button:not(.el-button--primary) {
  color: inherit !important;
}

/* 单选按钮组文字颜色修复 */
.el-radio-button__inner {
  color: var(--el-color-primary) !important;
}

.el-radio-button__inner span {
  color: inherit !important;
}

.el-radio-button__inner .flex {
  color: inherit !important;
}

.el-radio-button__original-radio:checked + .el-radio-button__inner {
  color: white !important;
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

.el-radio-button__original-radio:checked + .el-radio-button__inner span,
.el-radio-button__original-radio:checked + .el-radio-button__inner .flex {
  color: white !important;
}

.el-radio-button__inner:hover {
  color: var(--el-color-primary) !important;
}

/* 消息提示保持原有颜色 */
.el-message,
.el-notification,
.el-alert {
  color: inherit !important;
}

/* 输入框文字 */
.el-input__inner {
  color: var(--el-color-primary) !important;
}

.el-textarea__inner {
  color: var(--el-color-primary) !important;
}

/* 占位符文字 */
.el-input__inner::placeholder,
.el-textarea__inner::placeholder {
  color: var(--el-color-primary-light-5) !important;
}


/* 模型信息区域 */
.model-info {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  color: #5357A0 !important;
}

.model-info h5 {
  color: #434683 !important;
  font-weight: 600;
}

.model-info .text-gray-700 {
  color: #5357A0 !important;
}

.model-info .text-gray-500 {
  color: #718096 !important;
}