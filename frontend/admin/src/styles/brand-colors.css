/* 智慧养老评估平台 - 品牌配色系统 */

:root {
  /* 主要品牌色彩 */
  --changchun-blue: #5357A0;      /* 长春花蓝 - 主色调，用于主要按钮、链接、激活状态 */
  --foshou-yellow: #fed81f;       /* 佛手黄 - 配色，用于边框、装饰、辅助元素 */
  
  /* 扩展色彩 */
  --light-blue: #eaebf8;          /* 浅蓝 - 背景色、容器背景 */
  --dark-blue: #434683;           /* 深蓝 - 标题、重要文字 */
  --warm-white: #FEFEFE;          /* 温白 - 卡片背景、主要内容区 */
  --soft-gray: #F8F9FA;           /* 柔灰 - 页面背景 */
  
  /* 文字色彩 */
  --text-primary: #5357A0;        /* 主要文字 - 长春花蓝，标题、重要内容 */
  --text-secondary: #718096;      /* 次要文字 - 中性灰，描述、辅助信息 */
  --text-light: #9ea1d0;          /* 浅色文字 - 浅长春花蓝，占位符、禁用状态 */
  
  /* 状态色彩 (保持常规最佳实践) */
  --success: #48BB78;             /* 成功状态 - 绿色 */
  --warning: #ED8936;             /* 警告状态 - 橙色 */
  --error: #F56565;               /* 错误状态 - 红色 */
  --info: #4299E1;                /* 信息状态 - 蓝色 */
  
  /* 品牌色彩变体 (用于hover、focus等状态) */
  --changchun-blue-light: rgba(83, 87, 160, 0.1);
  --changchun-blue-medium: rgba(83, 87, 160, 0.2);
  --foshou-yellow-light: rgba(254, 216, 31, 0.1);
  --foshou-yellow-medium: rgba(254, 216, 31, 0.2);
}

/* 全局卡片样式 */
.brand-card {
  border: 2px solid var(--foshou-yellow);
  background: var(--warm-white);
  box-shadow: 0 2px 8px var(--foshou-yellow-light);
  transition: all 0.3s ease;
}

.brand-card:hover {
  border-color: var(--changchun-blue);
  box-shadow: 0 4px 16px var(--changchun-blue-light);
  transform: translateY(-1px);
}

/* 主要按钮样式 */
.brand-button-primary {
  background: var(--changchun-blue);
  border-color: var(--changchun-blue);
  color: white;
}

.brand-button-primary:hover {
  background: var(--dark-blue);
  border-color: var(--dark-blue);
}

/* 次要按钮样式 */
.brand-button-secondary {
  background: var(--foshou-yellow);
  border-color: var(--foshou-yellow);
  color: var(--dark-blue);
}

.brand-button-secondary:hover {
  background: var(--changchun-blue);
  border-color: var(--changchun-blue);
  color: white;
}

/* 输入框样式 */
.brand-input:focus {
  border-color: var(--changchun-blue);
  box-shadow: 0 0 0 3px var(--changchun-blue-light);
}

/* 标签样式 */
.brand-tag-primary {
  background: var(--changchun-blue);
  color: white;
}

.brand-tag-secondary {
  background: var(--foshou-yellow);
  color: var(--dark-blue);
}

/* 进度条样式 */
.brand-progress-bar {
  background: var(--changchun-blue);
}

/* 文字样式 */
.text-brand-primary {
  color: var(--changchun-blue);
}

.text-brand-secondary {
  color: var(--foshou-yellow);
}

.text-brand-dark {
  color: var(--dark-blue);
}

/* 背景样式 */
.bg-brand-light {
  background: var(--light-blue);
}

.bg-brand-warm {
  background: var(--warm-white);
}

.bg-brand-soft {
  background: var(--soft-gray);
}

/* Element Plus组件样式覆盖 */
.el-card.brand-style {
  border: 2px solid var(--foshou-yellow);
  box-shadow: 0 2px 8px var(--foshou-yellow-light);
}

.el-button--primary.brand-style {
  background: var(--changchun-blue);
  border-color: var(--changchun-blue);
}

.el-button--primary.brand-style:hover {
  background: var(--dark-blue);
  border-color: var(--dark-blue);
}

.el-tag.brand-style {
  background: var(--foshou-yellow);
  color: var(--dark-blue);
  border: none;
}

.el-progress-bar__inner.brand-style {
  background: var(--changchun-blue);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .brand-card {
    border-width: 1px;
    box-shadow: 0 1px 4px var(--foshou-yellow-light);
  }
}

/* 动画效果 */
@keyframes brand-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.brand-pulse {
  animation: brand-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 阴影效果 */
.brand-shadow-sm {
  box-shadow: 0 1px 3px var(--foshou-yellow-light);
}

.brand-shadow-md {
  box-shadow: 0 4px 8px var(--changchun-blue-light);
}

.brand-shadow-lg {
  box-shadow: 0 8px 16px var(--changchun-blue-medium);
}