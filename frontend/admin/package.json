{"name": "elderly-assessment-admin", "version": "1.0.0", "description": "智慧养老评估平台管理后台", "private": true, "scripts": {"dev": "vite --port 5274", "build": "vue-tsc --noEmit && vite build", "build:skip-check": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue,.ts,.tsx src", "lint:fix": "eslint --ext .js,.vue,.ts,.tsx src --fix", "format": "prettier --write \"src/**/*.{js,ts,vue,json,css,scss}\"", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^10.9.0", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "element-plus": "^2.6.3", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "marked": "^15.0.12", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.25", "vue-router": "^4.3.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.0", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/marked": "^5.0.2", "@types/node": "^20.11.19", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-vue": "^5.2.4", "@vue/compiler-sfc": "^3.4.21", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "happy-dom": "^18.0.1", "postcss": "^8.5.0", "prettier": "^3.2.5", "sass": "^1.71.0", "tailwindcss": "^4.0.0", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.5", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.19", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^1.6.1", "vue-tsc": "^2.2.10"}, "overrides": {"esbuild": "^0.25.5", "braces": "^3.0.3", "micromatch": "^4.0.8", "postcss": "^8.5.0"}}