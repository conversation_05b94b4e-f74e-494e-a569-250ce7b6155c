// 强制登出脚本 - 在浏览器Console中执行

console.log('=== 强制清理认证状态 ===');

// 清理所有可能的认证数据
localStorage.clear();
sessionStorage.clear();

// 特别清理认证相关的项目
const authKeys = ['token', 'user', 'refreshToken', 'userInfo', 'permissions'];
authKeys.forEach(key => {
  localStorage.removeItem(key);
  sessionStorage.removeItem(key);
  console.log(`已清理: ${key}`);
});

// 清理cookies（如果有的话）
document.cookie.split(";").forEach(function(c) { 
  document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
});

console.log('✅ 认证状态已完全清理');
console.log('📱 即将重新加载页面...');

// 重新加载页面
setTimeout(() => {
  window.location.href = '/login';
}, 1000);