#!/usr/bin/env python3
"""
测试与LM Studio的连接和AI分析功能
"""

import requests
import json

def test_lm_studio_connection():
    """测试LM Studio连接"""
    print("🔌 测试LM Studio连接...")
    
    try:
        # 检查LM Studio模型列表
        response = requests.get("http://192.168.1.231:1234/v1/models")
        if response.status_code == 200:
            models = response.json()
            print(f"✅ LM Studio连接成功，发现 {len(models['data'])} 个模型")
            for model in models['data'][:3]:  # 只显示前3个模型
                print(f"   - {model['id']}")
        else:
            print(f"❌ LM Studio连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ LM Studio连接异常: {e}")
        return False
    
    return True

def test_simplified_ai_analysis():
    """测试简化的AI分析（直接调用LM Studio）"""
    print("\n🤖 测试简化的AI分析...")
    
    # 准备测试数据
    test_markdown = """
# 老年人能力评估量表

## 基本信息
- 姓名：_______
- 年龄：_______
- 评估日期：_______

## 日常生活能力评估

### 1. 进食能力
- A. 完全独立 (3分)
- B. 需要部分帮助 (2分)  
- C. 需要全面帮助 (1分)
- D. 完全依赖 (0分)

### 2. 洗浴能力
- A. 完全独立 (3分)
- B. 需要部分帮助 (2分)
- C. 需要全面帮助 (1分)  
- D. 完全依赖 (0分)

## 评分标准
- 总分：0-6分
- 3-6分：轻度依赖
- 1-2分：中度依赖
- 0分：重度依赖
"""
    
    # 构建简化的API请求（只发送模型、消息和stream参数）
    messages = [
        {
            "role": "system",
            "content": "你是一个专业的数据库设计师，专门负责将Markdown文档转换为PostgreSQL数据库设计。"
        },
        {
            "role": "user", 
            "content": f"""请分析以下老年人能力评估量表的Markdown文档，并生成PostgreSQL数据库设计。

文档内容：
{test_markdown}

请按照以下要求生成精准的分析结果：

1. 识别量表类型和评估领域
2. 设计合适的数据库表结构
3. 生成完整的PostgreSQL建表语句
4. 提供JSON格式的字段定义

请确保生成的SQL语句包含：
- 主键ID字段
- 评估记录ID字段
- 每个评估项目的字段
- 总分字段
- 创建和更新时间字段

最后，请以JSON格式返回字段列表，包含字段名、类型、长度、是否必填等信息。"""
        }
    ]
    
    # 获取当前可用的模型
    try:
        models_response = requests.get("http://192.168.1.231:1234/v1/models")
        if models_response.status_code == 200:
            models_data = models_response.json()
            # 选择第一个非嵌入模型
            selected_model = None
            for model in models_data['data']:
                if 'embedding' not in model['id'].lower():
                    selected_model = model['id']
                    break
            
            if not selected_model:
                print("❌ 未找到合适的对话模型")
                return False
                
            print(f"📝 使用模型: {selected_model}")
        else:
            print("❌ 无法获取模型列表")
            return False
    except Exception as e:
        print(f"❌ 获取模型列表异常: {e}")
        return False
    
    # 发送简化的请求（只包含必要参数）
    request_body = {
        "model": selected_model,
        "messages": messages,
        "stream": False
    }
    
    print("🚀 发送简化请求到LM Studio（无temperature等参数）...")
    print(f"📊 请求体大小: {len(json.dumps(request_body))} 字符")
    
    try:
        response = requests.post(
            "http://192.168.1.231:1234/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=60  # 60秒超时
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                print("✅ AI分析成功!")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 显示响应的前500字符
                if len(ai_response) > 500:
                    print(f"📄 响应预览:\n{ai_response[:500]}...\n")
                else:
                    print(f"📄 完整响应:\n{ai_response}\n")
                
                # 检查是否包含SQL和JSON
                has_sql = 'CREATE TABLE' in ai_response.upper()
                has_json = '{' in ai_response and '}' in ai_response
                
                print(f"🔍 分析质量检查:")
                print(f"   - 包含SQL语句: {'✅' if has_sql else '❌'}")
                print(f"   - 包含JSON结果: {'✅' if has_json else '❌'}")
                
                return True
            else:
                print("❌ AI响应格式异常")
                return False
        else:
            print(f"❌ AI分析请求失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ AI分析请求超时")
        return False
    except Exception as e:
        print(f"❌ AI分析异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试LM Studio连接和AI分析...")
    print("=" * 50)
    
    # 测试LM Studio连接
    if not test_lm_studio_connection():
        print("\n❌ LM Studio连接测试失败，停止后续测试")
        return
    
    # 测试简化的AI分析
    if test_simplified_ai_analysis():
        print("\n✅ 所有测试通过！")
        print("🎉 LM Studio连接正常，简化API调用成功")
        print("📋 项目已成功移除temperature等参数，只发送必要数据")
    else:
        print("\n❌ AI分析测试失败")

if __name__ == "__main__":
    main()