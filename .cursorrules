# Cursor Rules - 智慧养老评估平台 (Elderly Assessment Platform)
请用中文交流
## Project Context
This is an enterprise-grade elderly assessment platform with Spring Boot backend and Vue.js/uni-app frontends, optimized for Apple M4 architecture.

## Core Architecture
- **Backend**: Spring Boot 3.5 + Java 21 + PostgreSQL + Redis + MinIO
- **Frontend**: Vue 3 + Element Plus (Admin) + uni-app (Mobile)
- **Build**: <PERSON>ven (Backend) + Vite (Frontend)
- **Deployment**: Docker with ARM64 optimization

## Code Style & Standards

### Java Backend Rules
- Use Java 21 LTS features and modern syntax
- Follow Spring Boot 3.5 conventions and best practices
- Package structure: `com.assessment.*` with layered architecture
- Use Lombok annotations for DTOs and entities
- Implement Builder pattern for complex objects
- Use `@Slf4j` for logging, never `System.out.println`
- Line length maximum: 120 characters
- Method length maximum: 50 lines
- Parameter count maximum: 7 parameters

### Spring Boot Patterns
```java
// Controller pattern
@RestController
@RequestMapping("/api/resource")
@Tag(name = "Resource Management", description = "Resource CRUD operations")
@Slf4j
@Validated
public class ResourceController {
    @Autowired
    private ResourceService resourceService;
}

// Service pattern with transaction management
@Service
@Transactional
@Slf4j
public class ResourceService {
    // Implementation
}

// Entity pattern with JPA
@Entity
@Table(name = "resource")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Resource {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
}
```

### Vue.js Frontend Rules
- Use Vue 3 Composition API with `<script setup>` syntax
- TypeScript strict mode enabled - always use proper typing
- Component names must be PascalCase
- Use Pinia for state management
- Follow Element Plus design patterns for admin interface
- Use uni-app conventions for mobile components

### Vue Component Pattern
```vue
<template>
  <div class="component-name">
    <!-- Template content -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// Props with TypeScript
interface Props {
  title: string;
  data?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
});

// Reactive state
const loading = ref(false);

// Computed properties
const computedValue = computed(() => {
  // computation
});

// Lifecycle hooks
onMounted(() => {
  // initialization
});
</script>

<style scoped lang="scss">
.component-name {
  // Styles
}
</style>
```

## API Design Standards

### RESTful Endpoints
- Use consistent resource-based URLs
- HTTP methods: GET (read), POST (create), PUT (update), DELETE (remove)
- Response format must be consistent across all endpoints
- Always include proper error handling and status codes
- Use OpenAPI 3 annotations for documentation

### Error Handling Pattern
```java
@ExceptionHandler(ResourceNotFoundException.class)
public ResponseEntity<ErrorResponse> handleResourceNotFound(ResourceNotFoundException ex) {
    ErrorResponse error = ErrorResponse.builder()
        .message(ex.getMessage())
        .timestamp(LocalDateTime.now())
        .build();
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
}
```

## Database & Data Layer

### JPA Entity Guidelines
- Use `@Entity` with explicit `@Table` names
- Primary keys should be `@Id @GeneratedValue(strategy = GenerationType.IDENTITY)`
- Use appropriate JPA relationships (`@OneToMany`, `@ManyToOne`, etc.)
- Include audit fields (createdAt, updatedAt) where appropriate
- Use indexes for frequently queried columns

### Repository Pattern
```java
@Repository
public interface ResourceRepository extends JpaRepository<Resource, Long> {
    @Query("SELECT r FROM Resource r WHERE r.status = :status")
    List<Resource> findByStatus(@Param("status") String status);
    
    Page<Resource> findByNameContainingIgnoreCase(String name, Pageable pageable);
}
```

## Security Implementation

### Authentication & Authorization
- Use Spring Security with JWT tokens
- Implement role-based access control (RBAC)
- Secure all API endpoints with appropriate permissions
- Never log sensitive information (passwords, tokens, personal data)
- Use HTTPS in production environments

### Data Protection
- Encrypt sensitive data at rest using AES-256
- Implement proper input validation and sanitization
- Use parameterized queries to prevent SQL injection
- Follow OWASP security guidelines

## Performance & Optimization

### Backend Performance
- Use `@Cacheable` for frequently accessed data
- Implement pagination for large datasets
- Use Redis for session storage and caching
- Optimize database queries with proper indexing
- Use connection pooling for database connections

### Frontend Performance  
- Implement lazy loading for routes and components
- Use `v-memo` for expensive list rendering
- Optimize bundle size with tree shaking
- Implement proper error boundaries
- Use CDN for static assets in production

## Testing Requirements

### Backend Testing
- Unit tests with JUnit 5 and Mockito
- Integration tests with `@SpringBootTest`
- Minimum code coverage: 85%
- Test all service layer methods
- Mock external dependencies

### Frontend Testing
- Component tests with Vitest and Vue Test Utils
- Unit tests for utility functions
- E2E tests for critical user flows
- Test async operations and error states

## Docker & Deployment

### Containerization
- Use multi-stage Docker builds for optimization
- ARM64 native images for Apple M4 compatibility  
- Include health check endpoints
- Use non-root users in containers
- Optimize layer caching

### Environment Configuration
- Use profiles for different environments (dev, test, prod)
- Environment variables for sensitive configuration
- Proper logging levels per environment
- Database migrations with Flyway

## Assessment Domain Specific Rules

### Assessment Scale Management
- PDF parsing must validate file integrity
- Support multiple assessment types (老年人能力评估, interRAI, etc.)
- Implement proper field mapping for different scales
- Generate standardized reports in PDF/Excel format

### Data Processing
- Validate assessment data before saving
- Implement audit trails for all assessment modifications
- Support offline assessment capabilities on mobile
- Ensure data synchronization between devices

## File Organization

### Backend Structure
```
backend/src/main/java/com/assessment/
├── controller/     # REST controllers
├── service/        # Business logic
├── repository/     # Data access
├── entity/         # JPA entities
├── dto/           # Data transfer objects
├── config/        # Configuration classes
├── security/      # Security implementation
└── exception/     # Custom exceptions
```

### Frontend Structure
```
frontend/
├── admin/src/
│   ├── views/         # Page components
│   ├── components/    # Reusable components
│   ├── api/          # API service layer
│   ├── stores/       # Pinia state management
│   └── utils/        # Utility functions
└── uni-app/
    ├── pages/        # uni-app pages
    ├── components/   # Cross-platform components
    └── store/        # State management
```

## Code Quality Gates

### Pre-commit Requirements
- ESLint and TypeScript checks pass
- Prettier formatting applied
- No console.log statements in production code
- All imports properly organized
- No unused variables or imports

### Build Requirements
- All tests must pass
- Code coverage thresholds met
- No security vulnerabilities in dependencies
- Docker images build successfully
- API documentation generated

## Development Workflow

### Branch Strategy
- Main branch for production-ready code
- Feature branches for new development
- Pull request reviews required
- Automated CI/CD pipeline validation

### Commit Messages
- Use conventional commit format
- Include ticket/issue references
- Clear, descriptive commit messages
- Separate code changes from configuration changes

## Apple M4 Optimization

### Performance Considerations
- Use ARM64 native Docker images
- Optimize JVM parameters for Apple Silicon
- Monitor memory usage and adjust heap sizes
- Use native compilation where beneficial

### Development Environment
- Use conda environment: `Assessment`
- Start services with `./scripts/dev-start-m4.sh`
- Monitor performance with provided scripts
- Test cross-platform compatibility

## Error Prevention

### Common Pitfalls to Avoid
- Don't use blocking I/O operations without proper thread management
- Avoid N+1 query problems with JPA relationships
- Don't expose sensitive data in API responses
- Never commit secrets or API keys to version control
- Don't use `any` type in TypeScript - always provide proper types

### Best Practices
- Always validate input data
- Use proper exception handling
- Implement graceful degradation
- Log errors with appropriate context
- Use meaningful variable and method names

## Documentation Requirements

### Code Documentation
- JavaDoc for all public methods and classes
- JSDoc for complex TypeScript functions
- README files for each major component
- API documentation with OpenAPI 3
- Database schema documentation

### Architecture Documentation
- System architecture diagrams
- API flow documentation
- Database relationship diagrams
- Deployment guides
- Security implementation details

Remember: This is a production-grade healthcare platform. Prioritize security, reliability, and maintainability in all code decisions.