#!/bin/bash

echo "=== 停止智慧养老评估平台开发环境 ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 停止后端服务
if [ -f logs/backend.pid ]; then
    PID=$(cat logs/backend.pid)
    if kill -0 $PID 2>/dev/null; then
        echo -e "${YELLOW}停止后端服务...${NC}"
        kill $PID
        rm logs/backend.pid
        echo -e "${GREEN}✓${NC} 后端服务已停止"
    fi
else
    echo "后端服务未在运行"
fi

# 停止uni-app服务
if [ -f logs/uni-app.pid ]; then
    PID=$(cat logs/uni-app.pid)
    if kill -0 $PID 2>/dev/null; then
        echo -e "${YELLOW}停止uni-app服务...${NC}"
        kill $PID
        rm logs/uni-app.pid
        echo -e "${GREEN}✓${NC} uni-app服务已停止"
    fi
else
    echo "uni-app服务未在运行"
fi

# 停止管理后台服务
if [ -f logs/admin.pid ]; then
    PID=$(cat logs/admin.pid)
    if kill -0 $PID 2>/dev/null; then
        echo -e "${YELLOW}停止管理后台服务...${NC}"
        kill $PID
        rm logs/admin.pid
        echo -e "${GREEN}✓${NC} 管理后台服务已停止"
    fi
else
    echo "管理后台服务未在运行"
fi

# 可选：停止Docker服务
echo
read -p "是否停止Docker数据库服务? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}停止Docker服务...${NC}"
    docker-compose down
    echo -e "${GREEN}✓${NC} Docker服务已停止"
fi

echo -e "\n${GREEN}开发环境已停止${NC}"