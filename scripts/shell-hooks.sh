#!/bin/bash

# 智慧养老评估平台 - Shell钩子配置
# 用于在shell配置文件中添加自动激活功能

# 配置项
PROJECT_ROOT="/Volumes/acasis/Assessment"
SCRIPT_DIR="$PROJECT_ROOT/scripts"

# 支持的shell类型
declare -A SHELL_CONFIGS=(
    ["bash"]="$HOME/.bashrc"
    ["zsh"]="$HOME/.zshrc"
    ["fish"]="$HOME/.config/fish/config.fish"
)

# 获取当前shell类型
get_current_shell() {
    basename "$SHELL"
}

# 检查配置是否已存在
check_config_exists() {
    local config_file="$1"
    local marker="# Assessment Project Auto-Activation"
    
    if [ -f "$config_file" ]; then
        grep -q "$marker" "$config_file"
        return $?
    fi
    return 1
}

# 为bash/zsh添加配置
add_bash_zsh_config() {
    local config_file="$1"
    local shell_type="$2"
    
    cat >> "$config_file" << 'EOF'

# Assessment Project Auto-Activation
# 智慧养老评估平台自动环境激活
assessment_auto_activate() {
    local current_dir="$(pwd)"
    local project_root="/Volumes/acasis/Assessment"
    
    # 检查是否在项目目录中
    case "$current_dir" in
        "$project_root"*)
            # 检查是否已经激活了正确的环境
            if [ "$CONDA_DEFAULT_ENV" != "Assessment" ]; then
                if [ -f "$project_root/scripts/auto-conda.sh" ]; then
                    source "$project_root/scripts/auto-conda.sh"
                    auto_activate_conda
                fi
            fi
            ;;
    esac
}

# 钩子函数：在每次cd后检查
assessment_cd_hook() {
    assessment_auto_activate
}

# 添加cd钩子（bash/zsh通用）
if [ -n "$BASH_VERSION" ]; then
    # Bash
    PROMPT_COMMAND="${PROMPT_COMMAND:+$PROMPT_COMMAND; }assessment_cd_hook"
elif [ -n "$ZSH_VERSION" ]; then
    # Zsh
    autoload -U add-zsh-hook
    add-zsh-hook chpwd assessment_cd_hook
fi

# 立即检查当前目录
assessment_auto_activate
EOF
    
    echo "✅ Added configuration to $config_file"
}

# 为fish添加配置
add_fish_config() {
    local config_file="$1"
    
    # 确保配置目录存在
    mkdir -p "$(dirname "$config_file")"
    
    cat >> "$config_file" << 'EOF'

# Assessment Project Auto-Activation
# 智慧养老评估平台自动环境激活
function assessment_auto_activate
    set current_dir (pwd)
    set project_root "/Volumes/acasis/Assessment"
    
    # 检查是否在项目目录中
    if string match -q "$project_root*" "$current_dir"
        # 检查是否已经激活了正确的环境
        if test "$CONDA_DEFAULT_ENV" != "Assessment"
            if test -f "$project_root/scripts/auto-conda.sh"
                source "$project_root/scripts/auto-conda.sh"
                auto_activate_conda
            end
        end
    end
end

# 添加cd钩子
function assessment_cd_hook --on-variable PWD
    assessment_auto_activate
end

# 立即检查当前目录
assessment_auto_activate
EOF
    
    echo "✅ Added configuration to $config_file"
}

# 主安装函数
install_shell_hooks() {
    local current_shell=$(get_current_shell)
    
    echo "🔧 Installing shell hooks for $current_shell..."
    
    # 检查当前shell是否支持
    if [ -z "${SHELL_CONFIGS[$current_shell]}" ]; then
        echo "❌ Unsupported shell: $current_shell"
        echo "Supported shells: ${!SHELL_CONFIGS[@]}"
        return 1
    fi
    
    local config_file="${SHELL_CONFIGS[$current_shell]}"
    
    # 检查配置是否已存在
    if check_config_exists "$config_file"; then
        echo "⚠️  Configuration already exists in $config_file"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Installation cancelled"
            return 1
        fi
        
        # 移除旧配置
        sed -i.bak '/# Assessment Project Auto-Activation/,/^$/d' "$config_file"
    fi
    
    # 添加新配置
    case "$current_shell" in
        "bash"|"zsh")
            add_bash_zsh_config "$config_file" "$current_shell"
            ;;
        "fish")
            add_fish_config "$config_file"
            ;;
    esac
    
    echo ""
    echo "🎉 Installation complete!"
    echo "💡 Please restart your terminal or run:"
    echo "   source $config_file"
    echo ""
    echo "📚 Usage:"
    echo "   - Navigate to any directory in $PROJECT_ROOT"
    echo "   - The Assessment conda environment will be automatically activated"
    echo "   - Use 'conda deactivate' to exit the environment"
}

# 卸载函数
uninstall_shell_hooks() {
    local current_shell=$(get_current_shell)
    local config_file="${SHELL_CONFIGS[$current_shell]}"
    
    if [ -z "$config_file" ]; then
        echo "❌ Unsupported shell: $current_shell"
        return 1
    fi
    
    if check_config_exists "$config_file"; then
        echo "🗑️  Removing configuration from $config_file..."
        sed -i.bak '/# Assessment Project Auto-Activation/,/^$/d' "$config_file"
        echo "✅ Configuration removed"
    else
        echo "ℹ️  No configuration found in $config_file"
    fi
}

# 显示帮助
show_help() {
    cat << EOF
智慧养老评估平台 - Shell钩子配置工具

Usage: $0 [OPTIONS]

Options:
    install     安装shell钩子配置
    uninstall   卸载shell钩子配置
    status      检查配置状态
    help        显示此帮助信息

Examples:
    $0 install      # 安装自动激活功能
    $0 uninstall    # 卸载自动激活功能
    $0 status       # 检查当前配置状态

Supported Shells:
    - bash
    - zsh
    - fish

EOF
}

# 检查状态
check_status() {
    local current_shell=$(get_current_shell)
    local config_file="${SHELL_CONFIGS[$current_shell]}"
    
    echo "📊 Configuration Status:"
    echo "   Current shell: $current_shell"
    echo "   Config file: $config_file"
    
    if check_config_exists "$config_file"; then
        echo "   Status: ✅ Installed"
    else
        echo "   Status: ❌ Not installed"
    fi
}

# 主程序
main() {
    case "${1:-help}" in
        "install")
            install_shell_hooks
            ;;
        "uninstall")
            uninstall_shell_hooks
            ;;
        "status")
            check_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo "❌ Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi