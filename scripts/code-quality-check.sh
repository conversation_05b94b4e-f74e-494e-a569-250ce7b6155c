#!/bin/bash

# Assessment项目代码质量检查脚本
# 使用方法: ./scripts/code-quality-check.sh [选项]
# 选项:
#   --backend-only    仅检查后端代码
#   --frontend-only   仅检查前端代码
#   --fix            自动修复可修复的问题
#   --report         生成详细报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORT_DIR="$PROJECT_ROOT/quality-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建报告目录
mkdir -p "$REPORT_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查工具是否安装
check_tool() {
    local tool=$1
    local install_cmd=$2
    
    if ! command -v "$tool" &> /dev/null; then
        log_warning "$tool 未安装"
        if [ -n "$install_cmd" ]; then
            log_info "建议安装命令: $install_cmd"
        fi
        return 1
    fi
    return 0
}

# 后端代码质量检查
check_backend() {
    log_info "开始检查后端Java代码质量..."
    
    cd "$PROJECT_ROOT/backend"
    
    # 1. Maven编译检查
    log_info "1. 编译检查"
    
    # 检查Maven包装器
    local MAVEN_CMD="mvn"
    if [ -f "mvnw" ]; then
        MAVEN_CMD="./mvnw"
        log_info "使用Maven包装器"
    fi
    
    if $MAVEN_CMD clean compile -q; then
        log_success "编译通过"
    else
        log_error "编译失败"
        return 1
    fi
    
    # 2. 单元测试
    log_info "2. 单元测试"
    if $MAVEN_CMD test -q; then
        log_success "单元测试通过"
    else
        log_warning "单元测试失败或无测试"
    fi
    
    # 3. 代码覆盖率（如果有JaCoCo）
    log_info "3. 代码覆盖率检查"
    if $MAVEN_CMD jacoco:report -q 2>/dev/null; then
        if [ -f "target/site/jacoco/index.html" ]; then
            log_success "代码覆盖率报告已生成: backend/target/site/jacoco/index.html"
        fi
    else
        log_warning "JaCoCo插件未配置，跳过覆盖率检查"
    fi
    
    # 4. SpotBugs静态分析（如果有）
    log_info "4. SpotBugs静态分析"
    if $MAVEN_CMD spotbugs:check -q 2>/dev/null; then
        log_success "SpotBugs检查通过"
    else
        log_warning "SpotBugs插件未配置或发现问题"
    fi
    
    # 5. 代码风格检查
    log_info "5. 代码风格检查"
    
    # 检查常见问题
    local issues=0
    
    # 检查TODO/FIXME
    local todo_count=$(find src -name "*.java" -exec grep -l "TODO\|FIXME" {} \; | wc -l)
    if [ "$todo_count" -gt 0 ]; then
        log_warning "发现 $todo_count 个文件包含TODO/FIXME注释"
        if [ "$GENERATE_REPORT" = "true" ]; then
            find src -name "*.java" -exec grep -Hn "TODO\|FIXME" {} \; > "$REPORT_DIR/backend_todos_$TIMESTAMP.txt"
        fi
        ((issues++))
    fi
    
    # 检查System.out.println
    local println_count=$(find src -name "*.java" -exec grep -l "System\.out\.println" {} \; | wc -l)
    if [ "$println_count" -gt 0 ]; then
        log_warning "发现 $println_count 个文件使用System.out.println，建议使用日志框架"
        if [ "$GENERATE_REPORT" = "true" ]; then
            find src -name "*.java" -exec grep -Hn "System\.out\.println" {} \; > "$REPORT_DIR/backend_println_$TIMESTAMP.txt"
        fi
        ((issues++))
    fi
    
    # 检查长方法（超过50行）
    log_info "检查长方法..."
    find src -name "*.java" -exec awk '
        /^[[:space:]]*public|^[[:space:]]*private|^[[:space:]]*protected/ && /\(.*\)[[:space:]]*\{/ {
            method_start = NR
            method_name = $0
            brace_count = 1
            next
        }
        brace_count > 0 {
            if (/\{/) brace_count++
            if (/\}/) brace_count--
            if (brace_count == 0) {
                length = NR - method_start
                if (length > 50) {
                    print FILENAME ":" method_start ":长方法(" length "行): " method_name
                }
            }
        }
    ' {} \; > /tmp/long_methods.txt
    
    if [ -s /tmp/long_methods.txt ]; then
        local long_method_count=$(wc -l < /tmp/long_methods.txt)
        log_warning "发现 $long_method_count 个长方法（>50行）"
        if [ "$GENERATE_REPORT" = "true" ]; then
            cp /tmp/long_methods.txt "$REPORT_DIR/backend_long_methods_$TIMESTAMP.txt"
        fi
        ((issues++))
    fi
    
    if [ "$issues" -eq 0 ]; then
        log_success "后端代码质量检查通过"
    else
        log_warning "后端代码发现 $issues 类问题，建议优化"
    fi
    
    cd "$PROJECT_ROOT"
}

# 前端代码质量检查
check_frontend() {
    log_info "开始检查前端代码质量..."
    
    # 检查uni-app
    if [ -d "$PROJECT_ROOT/frontend/uni-app" ]; then
        log_info "检查uni-app项目..."
        cd "$PROJECT_ROOT/frontend/uni-app"
        
        # 检查package.json
        if [ ! -f "package.json" ]; then
            log_error "未找到package.json文件"
            return 1
        fi
        
        # 检查依赖
        if [ ! -d "node_modules" ]; then
            log_warning "node_modules不存在，请运行 npm install"
        fi
        
        # ESLint检查（如果配置了）
        if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ] || [ -f "eslint.config.js" ]; then
            log_info "运行ESLint检查..."
            if npx eslint src --ext .js,.ts,.vue --format compact --ignore-path .gitignore > "$REPORT_DIR/frontend_eslint_$TIMESTAMP.txt" 2>&1; then
                log_success "ESLint检查通过"
            else
                log_warning "ESLint发现问题，详见报告文件"
            fi
        else
            log_warning "未配置ESLint"
        fi
        
        # TypeScript检查（如果是TS项目）
        if [ -f "tsconfig.json" ]; then
            log_info "运行TypeScript类型检查..."
            if npx tsc --noEmit > "$REPORT_DIR/frontend_typescript_$TIMESTAMP.txt" 2>&1; then
                log_success "TypeScript检查通过"
            else
                log_warning "TypeScript类型检查发现问题"
            fi
        fi
        
        # 检查常见问题
        local issues=0
        
        # 检查console.log
        local console_count=$(find src -name "*.js" -o -name "*.ts" -o -name "*.vue" | xargs grep -l "console\.log" | wc -l)
        if [ "$console_count" -gt 0 ]; then
            log_warning "发现 $console_count 个文件使用console.log"
            if [ "$GENERATE_REPORT" = "true" ]; then
                find src -name "*.js" -o -name "*.ts" -o -name "*.vue" | xargs grep -Hn "console\.log" > "$REPORT_DIR/frontend_console_$TIMESTAMP.txt"
            fi
            ((issues++))
        else
            log_success "未发现console.log语句"
        fi
        
        # 检查TODO/FIXME
        local todo_count=$(find src -name "*.js" -o -name "*.ts" -o -name "*.vue" | xargs grep -l "TODO\|FIXME" | wc -l)
        if [ "$todo_count" -gt 0 ]; then
            log_warning "发现 $todo_count 个文件包含TODO/FIXME注释"
            if [ "$GENERATE_REPORT" = "true" ]; then
                find src -name "*.js" -o -name "*.ts" -o -name "*.vue" | xargs grep -Hn "TODO\|FIXME" > "$REPORT_DIR/frontend_todos_$TIMESTAMP.txt"
            fi
            ((issues++))
        fi
        
        if [ "$issues" -eq 0 ]; then
            log_success "uni-app代码质量检查通过"
        else
            log_warning "uni-app代码发现 $issues 类问题"
        fi
    fi
    
    # 检查admin后台
    if [ -d "$PROJECT_ROOT/frontend/admin" ]; then
        log_info "检查admin后台项目..."
        cd "$PROJECT_ROOT/frontend/admin"
        
        # 类似的检查逻辑...
        if [ -f "package.json" ]; then
            log_success "admin项目结构正常"
        else
            log_warning "admin项目package.json缺失"
        fi
    fi
    
    cd "$PROJECT_ROOT"
}

# 通用代码质量检查
check_general() {
    log_info "开始通用代码质量检查..."
    
    # Git状态检查已移除 - 专注于代码质量检查
    
    # 2. 文件编码检查
    log_info "2. 文件编码检查"
    local non_utf8_files=$(find . -name "*.java" -o -name "*.js" -o -name "*.ts" -o -name "*.vue" -o -name "*.md" -not -path "*/node_modules/*" -not -path "*/target/*" -not -path "*/dist/*" -not -path "*/.git/*" -print0 | xargs -0 file 2>/dev/null | grep -v "UTF-8" | cut -d: -f1)
    if [ -n "$non_utf8_files" ]; then
        log_warning "发现非UTF-8编码文件:"
        echo "$non_utf8_files"
    else
        log_success "所有文本文件均为UTF-8编码"
    fi
    
    # 3. 行尾符检查
    log_info "3. 行尾符检查"
    local crlf_files=$(find . -name "*.java" -o -name "*.js" -o -name "*.ts" -o -name "*.vue" -not -path "*/node_modules/*" -not -path "*/target/*" -not -path "*/dist/*" -not -path "*/.git/*" -print0 | xargs -0 file 2>/dev/null | grep "CRLF" | cut -d: -f1)
    if [ -n "$crlf_files" ]; then
        log_warning "发现Windows行尾符(CRLF)文件:"
        echo "$crlf_files"
        if [ "$AUTO_FIX" = "true" ]; then
            log_info "自动修复行尾符..."
            echo "$crlf_files" | xargs dos2unix
            log_success "行尾符已修复"
        fi
    else
        log_success "行尾符检查通过"
    fi
    
    # 4. 空白字符检查
    log_info "4. 空白字符检查"
    local trailing_spaces=$(find . -name "*.java" -o -name "*.js" -o -name "*.ts" -o -name "*.vue" -not -path "*/node_modules/*" -not -path "*/target/*" -not -path "*/dist/*" -not -path "*/.git/*" -print0 | xargs -0 grep -l "[[:space:]]$" 2>/dev/null | wc -l)
    if [ "$trailing_spaces" -gt 0 ]; then
        log_warning "发现 $trailing_spaces 个文件包含行尾空白字符"
        if [ "$AUTO_FIX" = "true" ]; then
            log_info "自动移除行尾空白字符..."
            find . -name "*.java" -o -name "*.js" -o -name "*.ts" -o -name "*.vue" -not -path "*/node_modules/*" -not -path "*/target/*" -not -path "*/dist/*" -not -path "*/.git/*" -print0 | xargs -0 sed -i '' 's/[[:space:]]*$//'
            log_success "行尾空白字符已移除"
        fi
    else
        log_success "无行尾空白字符"
    fi
}

# 生成质量报告
generate_report() {
    if [ "$GENERATE_REPORT" = "true" ]; then
        log_info "生成质量报告..."
        
        local report_file="$REPORT_DIR/quality_report_$TIMESTAMP.md"
        
        cat > "$report_file" << EOF
# Assessment项目代码质量报告

生成时间: $(date)

## 检查概要

### 后端Java代码
- 编译状态: $(cd backend && ([ -f "mvnw" ] && ./mvnw compile -q || mvn compile -q) && echo "✅ 通过" || echo "❌ 失败")
- 测试状态: $(cd backend && ([ -f "mvnw" ] && ./mvnw test -q || mvn test -q) && echo "✅ 通过" || echo "⚠️ 失败")

### 前端代码
- uni-app: $([ -d "frontend/uni-app" ] && echo "✅ 存在" || echo "❌ 缺失")
- admin: $([ -d "frontend/admin" ] && echo "✅ 存在" || echo "❌ 缺失")

### 通用检查
- 文件编码: UTF-8

## 详细报告文件

EOF
        
        # 列出所有生成的报告文件
        find "$REPORT_DIR" -name "*_$TIMESTAMP.*" -exec basename {} \; | while read file; do
            echo "- $file" >> "$report_file"
        done
        
        log_success "质量报告已生成: $report_file"
    fi
}

# 主函数
main() {
    local check_backend=true
    local check_frontend=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                check_frontend=false
                shift
                ;;
            --frontend-only)
                check_backend=false
                shift
                ;;
            --fix)
                AUTO_FIX=true
                shift
                ;;
            --report)
                GENERATE_REPORT=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --backend-only    仅检查后端代码"
                echo "  --frontend-only   仅检查前端代码"
                echo "  --fix            自动修复可修复的问题"
                echo "  --report         生成详细报告"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "Assessment项目代码质量检查开始..."
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 检查必要工具
    local tools_ok=true
    if [ "$check_backend" = "true" ]; then
        # 检查Maven (优先使用包装器)
        if [ -f "$PROJECT_ROOT/backend/mvnw" ]; then
            log_info "发现Maven包装器，将使用 ./mvnw"
        elif command -v mvn &> /dev/null; then
            log_info "使用系统Maven"
        else
            log_warning "Maven未安装且无包装器"
            log_info "建议安装命令: 请安装Maven"
            tools_ok=false
        fi
        check_tool "java" "请安装Java 17+" || tools_ok=false
    fi
    
    if [ "$check_frontend" = "true" ]; then
        check_tool "node" "请安装Node.js" || tools_ok=false
        check_tool "npm" "请安装npm" || tools_ok=false
    fi
    
    if [ "$tools_ok" = "false" ]; then
        log_error "缺少必要工具，请安装后重试"
        exit 1
    fi
    
    # 执行检查
    if [ "$check_backend" = "true" ]; then
        check_backend
    fi
    
    if [ "$check_frontend" = "true" ]; then
        check_frontend
    fi
    
    check_general
    
    # 生成报告
    generate_report
    
    log_success "代码质量检查完成！"
    
    if [ "$GENERATE_REPORT" = "true" ]; then
        log_info "报告文件位置: $REPORT_DIR"
    fi
}

# 执行主函数
main "$@"