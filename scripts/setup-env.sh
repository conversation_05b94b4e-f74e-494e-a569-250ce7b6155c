#!/bin/bash

set -e

echo "=== 智慧养老评估平台环境设置 ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} $1 已安装"
        return 0
    else
        echo -e "${RED}✗${NC} $1 未安装"
        return 1
    fi
}

# 1. Apple M4兼容性检查
echo -e "\n${YELLOW}1. Apple M4兼容性检查${NC}"
if [ "$(uname -m)" = "arm64" ]; then
    echo -e "${GREEN}✓${NC} 检测到Apple Silicon (ARM64)架构"
    
    # 检查Rosetta 2
    if ! /usr/bin/pgrep oahd >/dev/null 2>&1; then
        echo -e "${YELLOW}!${NC} 建议安装Rosetta 2以兼容x86应用"
        echo "安装命令: softwareupdate --install-rosetta --agree-to-license"
    fi
else
    echo -e "${YELLOW}!${NC} 非ARM64架构，某些优化可能不适用"
fi

# 2. 检查系统依赖
echo -e "\n${YELLOW}2. 检查系统依赖${NC}"
check_command conda || {
    echo "请先安装 Miniconda/Anaconda (Apple Silicon版本)"
    echo "下载地址: https://docs.conda.io/en/latest/miniconda.html"
    echo "确保下载ARM64版本以获得最佳性能"
    exit 1
}

check_command docker || {
    echo "请先安装 Docker Desktop for Mac (Apple Silicon版本)"
    echo "下载地址: https://www.docker.com/products/docker-desktop/"
    echo "确保选择Apple Silicon版本以获得最佳性能"
    exit 1
}

check_command git || {
    echo "请先安装 Git"
    exit 1
}

# 3. 创建Conda环境
echo -e "\n${YELLOW}3. 创建 Conda 环境${NC}"
if conda env list | grep -q "Assessment"; then
    echo "Assessment 环境已存在，是否重新创建? (y/n)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        conda env remove -n Assessment
        echo "使用兼容性更好的环境配置..."
        conda env create -f environment-minimal.yml
    fi
else
    echo "使用兼容性更好的环境配置..."
    conda env create -f environment-minimal.yml
fi

# 4. 激活环境并安装依赖
echo -e "\n${YELLOW}4. 激活环境并安装依赖${NC}"

# 激活conda环境
eval "$(conda shell.bash hook)"
conda activate Assessment

# 检查Java版本
if java -version 2>&1 | grep -q "17"; then
    echo -e "${GREEN}✓${NC} Java 17 已安装"
else
    echo -e "${YELLOW}!${NC} 正在检查Java安装..."
    java -version || echo "Java可能需要重新安装"
fi

# 5. 安装前端依赖
echo -e "\n${YELLOW}5. 安装前端依赖${NC}"

# uni-app依赖
echo "安装 uni-app 依赖..."
cd frontend/uni-app
npm install

# 管理后台依赖
echo "安装管理后台依赖..."
cd ../admin
npm install

# 回到项目根目录
cd ../..

# 6. 构建后端项目
echo -e "\n${YELLOW}6. 构建后端项目${NC}"
cd backend
if [ -f ./mvnw ]; then
    echo "使用项目自带的 Maven Wrapper"
    chmod +x ./mvnw
    ./mvnw clean compile -DskipTests
else
    echo "使用系统 Maven"
    mvn clean compile -DskipTests
fi
cd ..

# 7. 设置环境变量
echo -e "\n${YELLOW}7. 设置环境变量${NC}"
if [ ! -f .env ]; then
    cp .env.example .env
    echo "已创建 .env 文件，请根据需要修改配置"
else
    echo ".env 文件已存在"
fi

# 8. 初始化Git钩子
echo -e "\n${YELLOW}8. 初始化Git钩子${NC}"
if [ -d .git ]; then
    if command -v pre-commit >/dev/null 2>&1; then
        pre-commit install
        echo "Git pre-commit 钩子已安装"
    else
        echo "pre-commit未安装，跳过钩子设置"
    fi
else
    echo "这不是一个Git仓库，跳过钩子安装"
fi

# 9. 创建必要的目录
echo -e "\n${YELLOW}9. 创建必要的目录${NC}"
mkdir -p {data,logs,backup}/{postgres,redis,minio,nginx}
mkdir -p ssl

echo -e "\n${GREEN}=== 环境设置完成 ===${NC}"
echo
echo "下一步操作："
echo "1. 激活环境: conda activate Assessment"
echo "2. 启动数据库: docker-compose -f docker-compose.dev.yml up -d"
echo "3. 运行后端: cd backend && ./mvnw spring-boot:run"
echo "4. 运行前端: cd frontend/uni-app && npm run dev:h5"
echo "5. 运行管理后台: cd frontend/admin && npm run dev"
echo
echo "Apple M4优化版本："
echo "- 启动优化环境: ./scripts/dev-start-m4.sh"
echo "- 性能监控: ./scripts/monitor-m4.sh"
echo
echo "访问地址："
echo "- 后端API: http://localhost:8080"
echo "- Swagger文档: http://localhost:8080/swagger-ui.html"
echo "- 前端H5: http://localhost:5273"
echo "- 管理后台: http://localhost:5274"
echo
echo "数据库连接信息已在 .env 文件中配置"