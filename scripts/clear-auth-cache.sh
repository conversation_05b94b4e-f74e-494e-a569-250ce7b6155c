#!/bin/bash

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧹 清理浏览器认证缓存脚本${NC}"

# 清理Chrome localStorage 和 sessionStorage
clear_chrome_storage() {
    echo -e "\n${BLUE}🔧 清理Chrome浏览器存储...${NC}"
    
    # 关闭Chrome中的localhost:5274标签页
    osascript << 'EOF' 2>/dev/null || true
    tell application "Google Chrome"
        if it is running then
            set windowList to every window
            repeat with currentWindow in windowList
                set tabList to every tab of currentWindow
                repeat with currentTab in tabList
                    if URL of currentTab contains "localhost:5274" then
                        close currentTab
                    end if
                end repeat
            end repeat
        end if
    end tell
EOF

    # 清理Chrome的应用数据
    CHROME_USER_DATA="$HOME/Library/Application Support/Google/Chrome/Default"
    
    if [ -d "$CHROME_USER_DATA" ]; then
        echo -e "${BLUE}📂 清理Chrome用户数据目录...${NC}"
        
        # 清理localStorage
        rm -rf "$CHROME_USER_DATA/Local Storage/leveldb/localhost_5274*" 2>/dev/null || true
        rm -rf "$CHROME_USER_DATA/Local Storage/leveldb/*localhost*5274*" 2>/dev/null || true
        
        # 清理sessionStorage
        rm -rf "$CHROME_USER_DATA/Session Storage/"*localhost*5274* 2>/dev/null || true
        
        # 清理IndexedDB
        rm -rf "$CHROME_USER_DATA/IndexedDB/"*localhost*5274* 2>/dev/null || true
        
        # 清理WebSQL
        rm -rf "$CHROME_USER_DATA/databases/"*localhost*5274* 2>/dev/null || true
        
        echo -e "${GREEN}✅ Chrome存储数据清理完成${NC}"
    else
        echo -e "${YELLOW}⚠️ Chrome用户数据目录未找到${NC}"
    fi
}

# 清理Safari localStorage
clear_safari_storage() {
    echo -e "\n${BLUE}🔧 清理Safari浏览器存储...${NC}"
    
    # Safari LocalStorage
    rm -rf ~/Library/Safari/LocalStorage/*localhost*5274* 2>/dev/null || true
    rm -rf ~/Library/Safari/Databases/*localhost*5274* 2>/dev/null || true
    rm -rf ~/Library/Safari/WebSQL/*localhost*5274* 2>/dev/null || true
    
    echo -e "${GREEN}✅ Safari存储数据清理完成${NC}"
}

# 清理其他浏览器
clear_other_browsers() {
    echo -e "\n${BLUE}🔧 清理其他浏览器存储...${NC}"
    
    # Firefox
    FIREFOX_PROFILES="$HOME/Library/Application Support/Firefox/Profiles"
    if [ -d "$FIREFOX_PROFILES" ]; then
        find "$FIREFOX_PROFILES" -name "*localhost*5274*" -delete 2>/dev/null || true
    fi
    
    # Edge
    EDGE_USER_DATA="$HOME/Library/Application Support/Microsoft Edge/Default"
    if [ -d "$EDGE_USER_DATA" ]; then
        rm -rf "$EDGE_USER_DATA/Local Storage/leveldb/*localhost*5274*" 2>/dev/null || true
        rm -rf "$EDGE_USER_DATA/Session Storage/"*localhost*5274* 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ 其他浏览器存储数据清理完成${NC}"
}

# 创建清理标记文件
create_clear_marker() {
    echo -e "\n${BLUE}📝 创建清理标记文件...${NC}"
    
    # 在项目根目录创建标记文件
    PROJECT_ROOT="/Volumes/acasis/Assessment"
    echo "$(date)" > "$PROJECT_ROOT/.auth_cleared"
    echo "dev_startup_clear=true" >> "$PROJECT_ROOT/.auth_cleared"
    
    echo -e "${GREEN}✅ 清理标记文件已创建${NC}"
}

# 主函数
main() {
    echo -e "\n${BLUE}🚀 开始清理浏览器认证缓存${NC}"
    
    clear_chrome_storage
    clear_safari_storage
    clear_other_browsers
    create_clear_marker
    
    echo -e "\n${GREEN}🎉 浏览器认证缓存清理完成${NC}"
    echo -e "${YELLOW}💡 现在启动管理后台将显示登录界面${NC}"
}

# 运行主函数
main "$@"