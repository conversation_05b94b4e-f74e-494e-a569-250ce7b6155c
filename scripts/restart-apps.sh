#!/bin/bash

set -e

echo "=== 快速重启应用服务 (保持数据库运行) ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 信号处理函数：优雅关闭服务
cleanup() {
    echo -e "\n${YELLOW}🛑 接收到退出信号，正在优雅关闭应用服务...${NC}"
    
    # 清理后端进程
    if [ -f "logs/backend-m4.pid" ]; then
        BACKEND_PID=$(cat logs/backend-m4.pid)
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止后端服务 (PID: $BACKEND_PID)...${NC}"
            kill -TERM $BACKEND_PID 2>/dev/null || kill -9 $BACKEND_PID 2>/dev/null
        fi
        rm -f logs/backend-m4.pid
    fi
    
    # 清理前端进程
    if [ -f "logs/uni-app-m4.pid" ]; then
        FRONTEND_PID=$(cat logs/uni-app-m4.pid)
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止uni-app服务 (PID: $FRONTEND_PID)...${NC}"
            kill -TERM $FRONTEND_PID 2>/dev/null || kill -9 $FRONTEND_PID 2>/dev/null
        fi
        rm -f logs/uni-app-m4.pid
    fi
    
    if [ -f "logs/admin-m4.pid" ]; then
        ADMIN_PID=$(cat logs/admin-m4.pid)
        if ps -p $ADMIN_PID > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止管理后台服务 (PID: $ADMIN_PID)...${NC}"
            kill -TERM $ADMIN_PID 2>/dev/null || kill -9 $ADMIN_PID 2>/dev/null
        fi
        rm -f logs/admin-m4.pid
    fi
    
    # 清理端口占用
    echo -e "${BLUE}🔧 清理应用端口占用...${NC}"
    lsof -ti :8181 | xargs kill -9 2>/dev/null || true
    lsof -ti :5273 | xargs kill -9 2>/dev/null || true
    lsof -ti :5274 | xargs kill -9 2>/dev/null || true
    
    echo -e "${GREEN}✅ 应用服务已优雅关闭${NC}"
    exit 0
}

# 注册信号处理
trap cleanup SIGINT SIGTERM

# 检查数据库是否运行
check_database() {
    echo -e "\n${BLUE}🗄️ 检查数据库服务状态${NC}"
    
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        echo -e "${GREEN}✅ 数据库服务正在运行${NC}"
        
        # 检查PostgreSQL连接
        if docker exec assessment-postgres-dev pg_isready -U assessment_user -d elderly_assessment >/dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL连接正常${NC}"
        else
            echo -e "${RED}❌ PostgreSQL连接失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ 数据库服务未启动，请先运行 ./scripts/dev-start-m4.sh${NC}"
        exit 1
    fi
}

# 停止现有应用服务
stop_existing_services() {
    echo -e "\n${YELLOW}🛑 停止现有应用服务${NC}"
    
    # 停止后端服务
    if [ -f "logs/backend-m4.pid" ]; then
        BACKEND_PID=$(cat logs/backend-m4.pid)
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止后端服务...${NC}"
            kill -TERM $BACKEND_PID 2>/dev/null || kill -9 $BACKEND_PID 2>/dev/null
            sleep 2
        fi
        rm -f logs/backend-m4.pid
    fi
    
    # 停止前端服务
    if [ -f "logs/uni-app-m4.pid" ]; then
        FRONTEND_PID=$(cat logs/uni-app-m4.pid)
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止uni-app服务...${NC}"
            kill -TERM $FRONTEND_PID 2>/dev/null || kill -9 $FRONTEND_PID 2>/dev/null
            sleep 1
        fi
        rm -f logs/uni-app-m4.pid
    fi
    
    if [ -f "logs/admin-m4.pid" ]; then
        ADMIN_PID=$(cat logs/admin-m4.pid)
        if ps -p $ADMIN_PID > /dev/null 2>&1; then
            echo -e "${BLUE}🔧 停止管理后台服务...${NC}"
            kill -TERM $ADMIN_PID 2>/dev/null || kill -9 $ADMIN_PID 2>/dev/null
            sleep 1
        fi
        rm -f logs/admin-m4.pid
    fi
    
    # 清理端口占用
    lsof -ti :8181 | xargs kill -9 2>/dev/null || true
    lsof -ti :5273 | xargs kill -9 2>/dev/null || true
    lsof -ti :5274 | xargs kill -9 2>/dev/null || true
    
    echo -e "${GREEN}✅ 现有服务已停止${NC}"
}

# 设置环境变量
setup_environment() {
    echo -e "\n${BLUE}⚙️ 设置运行环境${NC}"
    
    # 检查内存并设置配置
    TOTAL_MEM=$(sysctl hw.memsize | awk '{print $2/1024/1024/1024}')
    
    if (( $(echo "$TOTAL_MEM >= 16" | bc -l) )); then
        export MEMORY_PROFILE="high"
        export JAVA_OPTS="-Xmx6g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m"
    elif (( $(echo "$TOTAL_MEM >= 8" | bc -l) )); then
        export MEMORY_PROFILE="standard"
        export JAVA_OPTS="-Xmx4g -Xms1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m"
    else
        export MEMORY_PROFILE="low"
        export JAVA_OPTS="-Xmx2g -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=300"
    fi
    
    export NODE_OPTIONS="--max-old-space-size=4096"
    echo -e "${GREEN}✅ 环境配置完成 ($MEMORY_PROFILE profile)${NC}"
}

# 启动后端服务
start_backend() {
    echo -e "\n${BLUE}☕ 启动后端服务${NC}"
    
    cd backend
    
    echo -e "${BLUE}🔨 启动Spring Boot应用...${NC}"
    
    # 检查jar文件是否存在
    if [ ! -f "target/elderly-assessment-platform-1.0.0-SNAPSHOT.jar" ]; then
        echo -e "${BLUE}🔨 编译应用...${NC}"
        ./mvnw clean package -DskipTests
    fi
    
    # 启动应用
    nohup java $JAVA_OPTS -jar target/elderly-assessment-platform-1.0.0-SNAPSHOT.jar --spring.profiles.active=local > ../logs/backend-m4.log 2>&1 &
    echo $! > ../logs/backend-m4.pid
    
    cd ..
    
    # 等待服务启动
    echo -e "${BLUE}⏳ 等待后端服务启动...${NC}"
    max_attempts=60
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8181/actuator/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 后端服务启动成功${NC}"
            return
        fi
        echo -e "${YELLOW}⏳ 等待启动 $attempt/$max_attempts...${NC}"
        sleep 3
        ((attempt++))
    done
    
    echo -e "${RED}❌ 后端服务启动失败，请查看日志: logs/backend-m4.log${NC}"
    tail -10 logs/backend-m4.log
    exit 1
}

# 启动前端服务
start_frontend() {
    echo -e "\n${BLUE}🎨 启动前端服务${NC}"
    
    # uni-app H5
    echo -e "${BLUE}📱 启动uni-app H5开发服务器...${NC}"
    cd frontend/uni-app
    
    export UV_USE_IO_URING=0
    nohup npm run dev:h5 > ../../logs/uni-app-m4.log 2>&1 &
    echo $! > ../../logs/uni-app-m4.pid
    echo -e "${GREEN}✅ uni-app H5服务已启动${NC}"
    
    cd ../..
    
    # 管理后台
    echo -e "${BLUE}💼 启动管理后台开发服务器...${NC}"
    cd frontend/admin
    
    nohup npm run dev > ../../logs/admin-m4.log 2>&1 &
    echo $! > ../../logs/admin-m4.pid
    echo -e "${GREEN}✅ 管理后台服务已启动${NC}"
    
    cd ../..
    
    echo -e "${BLUE}⏳ 等待前端服务完全启动...${NC}"
    sleep 5
}

# 显示访问信息
show_access_info() {
    echo -e "\n${GREEN}🎉 应用服务重启完成${NC}"
    echo
    echo -e "${PURPLE}🔗 访问地址:${NC}"
    echo "   🖥️  后端API:          http://localhost:8181"
    echo "   📚 API文档:          http://localhost:8181/swagger-ui/index.html"
    echo "   📱 前端H5:           http://localhost:5273"
    echo "   💼 管理后台:         http://localhost:5274"
    echo
    echo -e "${PURPLE}📄 日志文件:${NC}"
    echo "   ☕ 后端日志:         logs/backend-m4.log"
    echo "   📱 前端日志:         logs/uni-app-m4.log, logs/admin-m4.log"
    echo
    echo -e "${YELLOW}🛑 停止服务:${NC} Ctrl+C"
    echo -e "${BLUE}📊 查看日志:${NC} tail -f logs/backend-m4.log"
}

# 主函数
main() {
    # 确保在项目根目录
    if [ ! -f "docker-compose.dev.yml" ]; then
        echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 执行重启流程
    check_database
    stop_existing_services
    setup_environment
    start_backend
    start_frontend
    show_access_info
    
    # 等待用户信号
    echo -e "\n${BLUE}🔄 应用服务正在运行中... 按 Ctrl+C 停止应用服务${NC}"
    while true; do
        sleep 1
    done
}

# 运行主函数
main "$@"