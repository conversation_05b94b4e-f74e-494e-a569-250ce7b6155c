#!/bin/bash

# 端口配置检查脚本
# 用于验证所有端口配置是否正确修改

echo "🔍 检查端口配置..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查计数器
total_checks=0
passed_checks=0

# 检查函数
check_port_config() {
    local file=$1
    local expected_port=$2
    local description=$3
    
    total_checks=$((total_checks + 1))
    
    if [ -f "$file" ]; then
        if grep -q "$expected_port" "$file"; then
            echo -e "${GREEN}✅ $description${NC}"
            passed_checks=$((passed_checks + 1))
        else
            echo -e "${RED}❌ $description - 端口配置不正确${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ $description - 文件不存在${NC}"
    fi
}

echo -e "${BLUE}📋 检查后端配置 (端口 8181)${NC}"
check_port_config "backend/src/main/resources/application.yml" "8181" "Spring Boot主配置文件"
check_port_config "backend/src/main/resources/application-local.yml" "8181" "Spring Boot本地配置文件"

echo -e "\n${BLUE}📋 检查环境变量配置${NC}"
check_port_config ".env" "8181" "环境变量文件"
check_port_config ".env.example" "8181" "环境变量示例文件"

echo -e "\n${BLUE}📋 检查前端配置${NC}"
check_port_config "frontend/uni-app/vite.config.js" "5273" "uni-app Vite配置"
check_port_config "frontend/uni-app/manifest.json" "5273" "uni-app manifest配置"
check_port_config "frontend/uni-app/utils/request.js" "8181" "uni-app API请求配置"
check_port_config "frontend/uni-app/store/modules/config.js" "8181" "uni-app状态管理配置"

check_port_config "frontend/admin/vite.config.ts" "5274" "管理后台Vite配置"
check_port_config "frontend/admin/package.json" "5274" "管理后台package.json"
check_port_config "frontend/admin/src/views/HomeView.vue" "8181" "管理后台API调用"

echo -e "\n${BLUE}📋 检查启动脚本${NC}"
check_port_config "scripts/dev-start-m4.sh" "8181" "Apple M4启动脚本后端端口"
check_port_config "scripts/dev-start-m4.sh" "5273" "Apple M4启动脚本前端端口"
check_port_config "scripts/dev-start-m4.sh" "5274" "Apple M4启动脚本管理后台端口"

echo -e "\n${BLUE}📋 检查文档配置${NC}"
check_port_config "CLAUDE.md" "8181" "Claude开发指南"
check_port_config "CLAUDE.md" "5273" "Claude开发指南前端端口"
check_port_config "CLAUDE.md" "5274" "Claude开发指南管理后台端口"

# 检查是否有遗留的旧端口配置
echo -e "\n${BLUE}🔍 检查是否有遗留的旧端口配置${NC}"
old_ports_found=0

echo "检查旧端口8080..."
if grep -r "8080" --exclude-dir=node_modules --exclude-dir=.git --exclude="*.log" --exclude-dir=target . | grep -v "check-ports.sh" | head -5; then
    old_ports_found=$((old_ports_found + 1))
fi

echo "检查旧端口5173..."
if grep -r "5173" --exclude-dir=node_modules --exclude-dir=.git --exclude="*.log" . | grep -v "check-ports.sh" | head -3; then
    old_ports_found=$((old_ports_found + 1))
fi

echo "检查旧端口5174..."
if grep -r "5174" --exclude-dir=node_modules --exclude-dir=.git --exclude="*.log" . | grep -v "check-ports.sh" | head -3; then
    old_ports_found=$((old_ports_found + 1))
fi

# 生成报告
echo -e "\n${BLUE}📊 检查结果总结${NC}"
echo "=============================="
echo "总检查项: $total_checks"
echo "通过检查: $passed_checks"
echo "失败检查: $((total_checks - passed_checks))"

if [ $old_ports_found -gt 0 ]; then
    echo -e "${YELLOW}⚠️ 发现 $old_ports_found 处旧端口配置需要手动检查${NC}"
fi

if [ $passed_checks -eq $total_checks ] && [ $old_ports_found -eq 0 ]; then
    echo -e "${GREEN}🎉 所有端口配置检查通过！${NC}"
    exit 0
else
    echo -e "${RED}❌ 部分端口配置需要修正${NC}"
    exit 1
fi