#!/bin/bash
# 智慧养老评估平台 - 快速修复脚本

set -e

echo "🔧 开始执行快速修复..."

# 1. 修复Docker Compose端口配置
echo "📦 修复Docker Compose配置..."
if grep -q "5433:5433" docker-compose.yml; then
    sed -i.bak 's/5433:5433/5433:5432/' docker-compose.yml
    echo "✅ PostgreSQL端口配置已修复"
fi

# 2. 统一Redis端口配置
echo "🔧 检查Redis端口配置..."
if grep -q "port: 6380" backend/src/main/resources/application.yml; then
    sed -i.bak 's/port: 6380/port: 6379/' backend/src/main/resources/application.yml
    echo "✅ Redis端口配置已统一"
fi

# 3. 更新README中的Java版本
echo "📝 更新README版本信息..."
if grep -q "Java 17" README.md; then
    sed -i.bak 's/Java 17/Java 21/' README.md
    echo "✅ README中Java版本已更新为21"
fi

# 4. 创建安全配置修复建议
echo "🔒 生成安全配置修复建议..."
cat > security-fixes-needed.md << 'EOF'
# 安全配置修复建议

## 1. 跨域配置修复
需要手动修复以下文件中的@CrossOrigin注解：

- `backend/src/main/java/com/assessment/controller/PasswordHashController.java:8`
- `backend/src/main/java/com/assessment/controller/TestController.java:7`
- `backend/src/main/java/com/assessment/controller/AuthController.java:32`
- `backend/src/main/java/com/assessment/controller/AIAnalysisController.java:23`

### 修复方式：
```java
// 修复前
@CrossOrigin(origins = "*")

// 修复后
@CrossOrigin(origins = {
    "http://localhost:5273", 
    "http://localhost:5274",
    "${app.frontend.urls:http://localhost:3000}"
})
```

## 2. 添加方法级安全注解
在Controller方法上添加适当的权限控制：

```java
@PreAuthorize("hasRole('ADMIN')")
@GetMapping("/users")
public ResponseEntity<List<User>> getAllUsers() {
    // ...
}

@PreAuthorize("hasRole('ASSESSOR') or hasRole('ADMIN')")
@PostMapping("/assessments")
public ResponseEntity<?> createAssessment() {
    // ...
}
```
EOF

echo "✅ 安全修复建议已生成到 security-fixes-needed.md"

# 5. 运行代码格式化（如果Maven可用）
if command -v mvn &> /dev/null; then
    echo "🎨 运行代码格式化..."
    cd backend
    if mvn spotless:check &> /dev/null; then
        mvn spotless:apply
        echo "✅ 代码格式化完成"
    else
        echo "⚠️  部分代码格式化问题需要手动修复"
    fi
    cd ..
else
    echo "⚠️  Maven未安装，跳过代码格式化"
fi

# 6. 创建前端清理脚本
echo "🧹 创建前端console.log清理脚本..."
cat > scripts/clean-console-logs.sh << 'EOF'
#!/bin/bash
# 清理前端console.log语句

echo "🔍 查找console.log使用情况..."

# 查找所有console.log
find frontend -name "*.js" -o -name "*.ts" -o -name "*.vue" | \
  xargs grep -n "console\.log" | \
  grep -v "node_modules" | \
  head -20

echo ""
echo "⚠️  请手动审查并移除不必要的console.log语句"
echo "💡 建议：在生产环境中使用适当的日志框架替代console.log"
EOF

chmod +x scripts/clean-console-logs.sh
echo "✅ 前端清理脚本已创建"

# 7. 创建安全检查脚本
cat > scripts/security-check.sh << 'EOF'
#!/bin/bash
# 简单的安全检查脚本

echo "🔒 执行安全检查..."

# 检查CrossOrigin配置
echo "1. 检查跨域配置..."
grep -rn "@CrossOrigin.*\*" backend/src/ || echo "✅ 未发现通配符跨域配置"

# 检查硬编码密码
echo "2. 检查硬编码凭证..."
grep -rn -i "password.*=" backend/src/ | grep -v ".class" | head -5

# 检查JWT密钥
echo "3. 检查JWT配置..."
grep -rn "jwt.secret" backend/src/main/resources/ || echo "✅ JWT密钥未硬编码"

echo "🔒 安全检查完成"
EOF

chmod +x scripts/security-check.sh
echo "✅ 安全检查脚本已创建"

echo ""
echo "🎉 快速修复完成！"
echo ""
echo "📋 修复总结："
echo "  ✅ Docker端口配置已修复"
echo "  ✅ Redis端口配置已统一"  
echo "  ✅ README版本信息已更新"
echo "  ✅ 安全修复建议已生成"
echo "  ✅ 代码格式化已运行"
echo "  ✅ 辅助脚本已创建"
echo ""
echo "🔄 下一步操作："
echo "  1. 查看 security-fixes-needed.md 并手动修复安全问题"
echo "  2. 运行 ./scripts/clean-console-logs.sh 检查前端代码"
echo "  3. 运行 ./scripts/security-check.sh 执行安全检查"
echo "  4. 重新启动服务测试修复效果"