#!/bin/bash

echo "=== Apple M4 兼容性检查 ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查CPU架构
echo -e "\n${BLUE}1. CPU架构检查${NC}"
ARCH=$(uname -m)
if [ "$ARCH" = "arm64" ]; then
    echo -e "${GREEN}✓${NC} 检测到 ARM64 架构 (Apple Silicon)"
    
    # 检查具体芯片
    CHIP=$(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "未知")
    echo "芯片信息: $CHIP"
else
    echo -e "${RED}✗${NC} 当前架构: $ARCH (非Apple Silicon)"
    echo "注意：某些优化可能不适用"
fi

# 检查macOS版本
echo -e "\n${BLUE}2. macOS版本检查${NC}"
MACOS_VERSION=$(sw_vers -productVersion)
echo "macOS版本: $MACOS_VERSION"

# 检查Rosetta 2状态
echo -e "\n${BLUE}3. Rosetta 2状态${NC}"
if /usr/bin/pgrep oahd >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} Rosetta 2 已安装并运行"
else
    echo -e "${YELLOW}!${NC} Rosetta 2 未检测到运行"
    echo "如需运行x86程序，请安装: softwareupdate --install-rosetta"
fi

# 检查Homebrew
echo -e "\n${BLUE}4. Homebrew检查${NC}"
if command -v brew >/dev/null 2>&1; then
    BREW_PREFIX=$(brew --prefix)
    echo -e "${GREEN}✓${NC} Homebrew已安装"
    echo "Homebrew路径: $BREW_PREFIX"
    
    if [ "$BREW_PREFIX" = "/opt/homebrew" ]; then
        echo -e "${GREEN}✓${NC} 使用ARM64原生Homebrew"
    else
        echo -e "${YELLOW}!${NC} 使用Intel Homebrew (可能通过Rosetta运行)"
    fi
else
    echo -e "${RED}✗${NC} Homebrew未安装"
    echo "安装命令: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
fi

# 检查Docker
echo -e "\n${BLUE}5. Docker检查${NC}"
if command -v docker >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} Docker已安装"
    
    # 检查Docker版本和架构支持
    DOCKER_VERSION=$(docker --version)
    echo "Docker版本: $DOCKER_VERSION"
    
    # 检查buildx插件（多架构支持）
    if docker buildx version >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} Docker Buildx支持（多架构构建）"
    else
        echo -e "${YELLOW}!${NC} Docker Buildx不可用"
    fi
    
    # 检查Docker Desktop是否使用Apple虚拟化框架
    if docker info 2>/dev/null | grep -q "virtualization"; then
        echo -e "${GREEN}✓${NC} Docker使用Apple虚拟化框架"
    fi
else
    echo -e "${RED}✗${NC} Docker未安装"
    echo "推荐安装Docker Desktop for Mac (Apple Silicon版本)"
fi

# 检查Node.js
echo -e "\n${BLUE}6. Node.js检查${NC}"
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    echo "Node.js版本: $NODE_VERSION"
    
    # 检查npm全局包位置
    NPM_PREFIX=$(npm config get prefix 2>/dev/null || echo "未知")
    echo "npm全局路径: $NPM_PREFIX"
    
    # 检查是否为ARM64原生版本
    NODE_ARCH=$(node -p "process.arch")
    if [ "$NODE_ARCH" = "arm64" ]; then
        echo -e "${GREEN}✓${NC} Node.js ARM64原生版本"
    else
        echo -e "${YELLOW}!${NC} Node.js架构: $NODE_ARCH (可能通过Rosetta运行)"
    fi
else
    echo -e "${RED}✗${NC} Node.js未安装"
fi

# 检查Java
echo -e "\n${BLUE}7. Java检查${NC}"
if command -v java >/dev/null 2>&1; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    echo "Java版本: $JAVA_VERSION"
    
    # 检查Java架构
    if java -version 2>&1 | grep -q "aarch64"; then
        echo -e "${GREEN}✓${NC} Java ARM64原生版本"
    else
        echo -e "${YELLOW}!${NC} Java可能不是ARM64原生版本"
    fi
else
    echo -e "${RED}✗${NC} Java未安装"
fi

# 检查Python
echo -e "\n${BLUE}8. Python检查${NC}"
if command -v python3 >/dev/null 2>&1; then
    PYTHON_VERSION=$(python3 --version)
    echo "Python版本: $PYTHON_VERSION"
    
    # 检查Python架构
    PYTHON_ARCH=$(python3 -c "import platform; print(platform.machine())")
    if [ "$PYTHON_ARCH" = "arm64" ]; then
        echo -e "${GREEN}✓${NC} Python ARM64原生版本"
    else
        echo -e "${YELLOW}!${NC} Python架构: $PYTHON_ARCH"
    fi
else
    echo -e "${RED}✗${NC} Python3未安装"
fi

# 检查Conda
echo -e "\n${BLUE}9. Conda检查${NC}"
if command -v conda >/dev/null 2>&1; then
    CONDA_VERSION=$(conda --version 2>/dev/null || echo "未知版本")
    echo "Conda版本: $CONDA_VERSION"
    
    # 检查conda-forge是否配置
    if conda config --show channels | grep -q conda-forge; then
        echo -e "${GREEN}✓${NC} conda-forge频道已配置"
    else
        echo -e "${YELLOW}!${NC} 建议添加conda-forge频道: conda config --add channels conda-forge"
    fi
else
    echo -e "${RED}✗${NC} Conda未安装"
fi

# 性能建议
echo -e "\n${BLUE}10. M4性能优化建议${NC}"
echo "📈 Apple M4针对性优化:"
echo "  • 使用ARM64原生镜像和包"
echo "  • 启用Docker虚拟化框架"
echo "  • 配置JVM使用Apple Silicon优化"
echo "  • 使用原生ARM64编译的Node.js和npm包"
echo "  • 避免不必要的Rosetta转换"

# 内存建议
echo -e "\n${BLUE}11. 内存配置建议${NC}"
TOTAL_MEM=$(sysctl hw.memsize | awk '{print $2/1024/1024/1024}')
echo "总内存: ${TOTAL_MEM}GB"

if (( $(echo "$TOTAL_MEM >= 16" | bc -l) )); then
    echo -e "${GREEN}✓${NC} 内存充足，可以运行完整开发环境"
    echo "建议配置:"
    echo "  • Docker内存限制: 8GB"
    echo "  • JVM最大堆内存: 4GB"
    echo "  • Node.js内存限制: 4GB"
else
    echo -e "${YELLOW}!${NC} 内存有限，建议优化配置"
    echo "建议配置:"
    echo "  • Docker内存限制: 4GB"
    echo "  • JVM最大堆内存: 2GB"
    echo "  • 关闭不必要的服务"
fi

echo -e "\n${GREEN}=== 兼容性检查完成 ===${NC}"