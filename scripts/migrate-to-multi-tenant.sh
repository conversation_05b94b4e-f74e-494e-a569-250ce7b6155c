#!/bin/bash

# ========================================
# 智慧养老评估平台 - 多租户架构迁移脚本
# migrate-to-multi-tenant.sh
# 创建日期: 2025-06-21
# 功能: 执行数据库结构迁移到多租户架构
# ========================================

set -e

# 配置参数
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-assessment}"
DB_USER="${DB_USER:-postgres}"
LOG_FILE="/tmp/migration_$(date +%Y%m%d_%H%M%S).log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    log "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    log "${RED}[ERROR]${NC} $1"
}

log_success() {
    log "${GREEN}[SUCCESS]${NC} $1"
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查PostgreSQL客户端
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL客户端未安装"
        exit 1
    fi
    
    # 检查数据库连接
    if ! psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -c "SELECT 1;" &> /dev/null; then
        log_error "无法连接到数据库 $DB_HOST:$DB_PORT/$DB_NAME"
        exit 1
    fi
    
    # 检查迁移文件
    MIGRATION_FILE="../backend/src/main/resources/db/migration/V2__Create_multi_tenant_architecture.sql"
    if [ ! -f "$MIGRATION_FILE" ]; then
        log_error "迁移文件不存在: $MIGRATION_FILE"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 备份现有数据
backup_existing_data() {
    log_info "备份现有数据..."
    
    BACKUP_DIR="/tmp/assessment_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # 备份整个数据库
    pg_dump -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER \
        --clean --create --verbose \
        --file="$BACKUP_DIR/full_backup.sql" 2>> $LOG_FILE
    
    # 备份关键表数据
    TABLES=("institutions" "users" "elderly_persons" "assessment_scales" "assessment_records")
    
    for table in "${TABLES[@]}"; do
        if psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER \
           -c "SELECT 1 FROM information_schema.tables WHERE table_name='$table';" | grep -q "1 row"; then
            
            log_info "备份表: $table"
            pg_dump -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER \
                --table=$table --data-only --verbose \
                --file="$BACKUP_DIR/${table}_data.sql" 2>> $LOG_FILE
        fi
    done
    
    log_success "数据备份完成: $BACKUP_DIR"
    echo $BACKUP_DIR > /tmp/last_backup_dir.txt
}

# 执行数据库迁移
execute_migration() {
    log_info "执行数据库结构迁移..."
    
    MIGRATION_FILE="../backend/src/main/resources/db/migration/V2__Create_multi_tenant_architecture.sql"
    
    # 执行迁移脚本
    if psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER \
       -f "$MIGRATION_FILE" -v ON_ERROR_STOP=1 2>> $LOG_FILE; then
        log_success "数据库结构迁移成功"
    else
        log_error "数据库结构迁移失败，请检查日志: $LOG_FILE"
        exit 1
    fi
}

# 数据迁移
migrate_data() {
    log_info "开始数据迁移..."
    
    # 检查是否有备份数据需要迁移
    if [ ! -f "/tmp/last_backup_dir.txt" ]; then
        log_warn "没有找到备份数据，跳过数据迁移"
        return
    fi
    
    BACKUP_DIR=$(cat /tmp/last_backup_dir.txt)
    
    # 迁移机构数据到租户表
    if [ -f "$BACKUP_DIR/institutions_data.sql" ]; then
        log_info "迁移机构数据到租户表..."
        
        psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
        -- 从备份的institutions表迁移数据到tenants表
        INSERT INTO tenants (
            code, name, industry, contact_person, contact_email, contact_phone, address,
            subscription_plan, max_users, max_monthly_assessments, max_custom_scales,
            status, created_at, updated_at
        )
        SELECT 
            code,
            name,
            CASE 
                WHEN type = 'HOSPITAL' THEN 'healthcare'
                WHEN type = 'NURSING_HOME' THEN 'nursing'
                WHEN type = 'COMMUNITY_CENTER' THEN 'community'
                ELSE 'other'
            END,
            contact_person,
            contact_email,
            contact_phone,
            address,
            'standard', -- 默认订阅计划
            100, -- 默认最大用户数
            2000, -- 默认月度评估限制
            20, -- 默认自定义量表限制
            CASE WHEN is_active THEN 'active' ELSE 'inactive' END,
            created_at,
            updated_at
        FROM institutions_backup_20250621
        WHERE NOT EXISTS (
            SELECT 1 FROM tenants WHERE tenants.code = institutions_backup_20250621.code
        );
EOF
        
        if [ $? -eq 0 ]; then
            log_success "机构数据迁移成功"
        else
            log_error "机构数据迁移失败"
        fi
    fi
    
    # 迁移用户数据
    log_info "迁移用户数据..."
    
    psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
    -- 迁移用户到platform_users
    INSERT INTO platform_users (
        username, email, password_hash, first_name, last_name, phone,
        is_active, last_login_at, created_at, updated_at
    )
    SELECT 
        username,
        COALESCE(email, username || '@example.com'),
        password_hash,
        split_part(real_name, ' ', 1),
        CASE WHEN position(' ' in real_name) > 0 
             THEN substring(real_name from position(' ' in real_name) + 1)
             ELSE '' END,
        phone,
        is_active,
        last_login_at,
        created_at,
        updated_at
    FROM users_backup_20250621
    WHERE NOT EXISTS (
        SELECT 1 FROM platform_users WHERE platform_users.username = users_backup_20250621.username
    );
    
    -- 创建租户用户关联
    INSERT INTO tenant_user_memberships (
        tenant_id, user_id, tenant_role, display_name, professional_title, license_number,
        status, joined_at
    )
    SELECT 
        t.id,
        pu.id,
        u.role,
        u.real_name,
        u.professional_title,
        u.license_number,
        CASE WHEN u.is_active THEN 'active' ELSE 'inactive' END,
        u.created_at
    FROM users_backup_20250621 u
    JOIN institutions_backup_20250621 i ON u.institution_id = i.id
    JOIN tenants t ON t.code = i.code
    JOIN platform_users pu ON pu.username = u.username
    WHERE NOT EXISTS (
        SELECT 1 FROM tenant_user_memberships 
        WHERE tenant_id = t.id AND user_id = pu.id
    );
EOF
    
    if [ $? -eq 0 ]; then
        log_success "用户数据迁移成功"
    else
        log_error "用户数据迁移失败"
    fi
    
    # 迁移被评估人数据
    log_info "迁移评估对象数据..."
    
    psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
    -- 迁移被评估人到assessment_subjects
    INSERT INTO assessment_subjects (
        tenant_id, name, id_number, gender, birth_date, phone, address,
        emergency_contact_name, emergency_contact_phone, medical_insurance_number,
        current_care_level, is_active, created_at, updated_at, created_by
    )
    SELECT 
        t.id,
        ep.name,
        ep.id_number,
        ep.gender,
        ep.birth_date,
        ep.phone,
        ep.address,
        ep.emergency_contact_name,
        ep.emergency_contact_phone,
        ep.medical_insurance_number,
        ep.care_level,
        ep.is_active,
        ep.created_at,
        ep.updated_at,
        tum.user_id
    FROM elderly_persons_backup_20250621 ep
    JOIN institutions_backup_20250621 i ON ep.institution_id = i.id
    JOIN tenants t ON t.code = i.code
    LEFT JOIN users_backup_20250621 u ON u.username = ep.created_by
    LEFT JOIN platform_users pu ON pu.username = u.username
    LEFT JOIN tenant_user_memberships tum ON tum.tenant_id = t.id AND tum.user_id = pu.id;
EOF
    
    if [ $? -eq 0 ]; then
        log_success "评估对象数据迁移成功"
    else
        log_error "评估对象数据迁移失败"
    fi
    
    # 迁移量表数据
    log_info "迁移量表数据..."
    
    psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
    -- 迁移官方量表到全局注册中心
    INSERT INTO global_scale_registry (
        code, name, version, category, industry_tags, form_schema, scoring_rules,
        publisher_type, is_official, is_verified, status, created_at, updated_at
    )
    SELECT 
        code,
        name,
        version,
        CASE 
            WHEN type = 'ELDERLY_ABILITY' THEN 'elderly_ability'
            WHEN type = 'EMOTIONAL_QUICK' THEN 'emotional'
            WHEN type = 'INTER_RAI' THEN 'interrai'
            WHEN type = 'LONG_CARE_INSURANCE' THEN 'care_insurance'
            ELSE 'other'
        END,
        CASE 
            WHEN applicable_scope LIKE '%医院%' THEN '{"healthcare"}'
            WHEN applicable_scope LIKE '%养老%' THEN '{"nursing"}'
            WHEN applicable_scope LIKE '%社区%' THEN '{"community"}'
            ELSE '{"general"}'
        END,
        form_schema,
        scoring_rules,
        'platform',
        is_official,
        true,
        CASE WHEN is_active THEN 'active' ELSE 'inactive' END,
        created_at,
        updated_at
    FROM assessment_scales_backup_20250621
    WHERE is_official = true
    AND NOT EXISTS (
        SELECT 1 FROM global_scale_registry WHERE global_scale_registry.code = assessment_scales_backup_20250621.code
    );
    
    -- 迁移自定义量表
    INSERT INTO tenant_custom_scales (
        tenant_id, code, name, description, version, form_schema, scoring_rules,
        status, created_at, updated_at
    )
    SELECT 
        t.id,
        'custom_' || scales.code,
        scales.name,
        scales.description,
        scales.version,
        scales.form_schema,
        scales.scoring_rules,
        CASE WHEN scales.is_active THEN 'active' ELSE 'archived' END,
        scales.created_at,
        scales.updated_at
    FROM assessment_scales_backup_20250621 scales
    JOIN institutions_backup_20250621 i ON scales.created_by IS NOT NULL
    JOIN tenants t ON t.code = i.code
    WHERE scales.is_official = false
    AND NOT EXISTS (
        SELECT 1 FROM tenant_custom_scales 
        WHERE tenant_id = t.id AND code = 'custom_' || scales.code
    );
EOF
    
    if [ $? -eq 0 ]; then
        log_success "量表数据迁移成功"
    else
        log_error "量表数据迁移失败"
    fi
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    # 检查关键表是否创建成功
    TABLES=("tenants" "global_scale_registry" "platform_users" "tenant_user_memberships" "assessment_subjects" "tenant_assessment_records")
    
    for table in "${TABLES[@]}"; do
        COUNT=$(psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -t -c "SELECT COUNT(*) FROM $table;")
        log_info "表 $table 记录数: $COUNT"
    done
    
    # 检查分区表
    PARTITION_COUNT=$(psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -t -c "
        SELECT COUNT(*) FROM pg_class 
        WHERE relname LIKE '%_p[0-7]' AND relkind = 'r';
    ")
    log_info "分区表数量: $PARTITION_COUNT"
    
    # 检查索引
    INDEX_COUNT=$(psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -t -c "
        SELECT COUNT(*) FROM pg_indexes 
        WHERE indexname LIKE 'idx_%';
    ")
    log_info "索引数量: $INDEX_COUNT"
    
    # 检查行级安全策略
    RLS_COUNT=$(psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -t -c "
        SELECT COUNT(*) FROM pg_policies;
    ")
    log_info "行级安全策略数量: $RLS_COUNT"
    
    log_success "迁移验证完成"
}

# 更新应用配置
update_application_config() {
    log_info "更新应用配置..."
    
    # 创建新的应用配置文件
    CONFIG_FILE="../backend/src/main/resources/application-multitenant.yml"
    
    cat > $CONFIG_FILE << 'EOF'
# 多租户架构配置
spring:
  datasource:
    hikari:
      # 连接池优化配置
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        # 多租户配置
        dialect: org.hibernate.dialect.PostgreSQLDialect
        # 批量操作优化
        jdbc.batch_size: 50
        order_inserts: true
        order_updates: true
        # 查询优化
        default_batch_fetch_size: 16
        max_fetch_depth: 3

# 多租户配置
multitenant:
  # 租户识别策略
  identification:
    strategy: header # header, subdomain, jwt
    header-name: X-Tenant-Code
    
  # 数据源配置
  datasource:
    default:
      enabled: true
      
  # 缓存配置  
  cache:
    tenant-config-ttl: 300s
    scale-schema-ttl: 600s
    
  # 安全配置
  security:
    encryption:
      enabled: true
      algorithm: AES-256
    audit:
      enabled: true
      retention-days: 365
      
# 业务配置
assessment:
  # 默认配额
  default-quotas:
    max-users: 50
    max-monthly-assessments: 1000
    max-custom-scales: 10
    max-storage-mb: 1024
    
  # 评估配置
  auto-save-interval: 30s
  max-assessment-duration: 120m
  
  # 报告配置
  report:
    max-export-records: 10000
    supported-formats: [pdf, excel, csv]

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      application: assessment-platform
      version: 2.0.0
EOF

    if [ $? -eq 0 ]; then
        log_success "应用配置更新完成: $CONFIG_FILE"
    else
        log_error "应用配置更新失败"
    fi
}

# 生成迁移报告
generate_migration_report() {
    log_info "生成迁移报告..."
    
    REPORT_FILE="/tmp/migration_report_$(date +%Y%m%d_%H%M%S).html"
    
    cat > $REPORT_FILE << EOF
<!DOCTYPE html>
<html>
<head>
    <title>数据库迁移报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .success { color: #52c41a; }
        .warning { color: #faad14; }
        .error { color: #ff4d4f; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>智慧养老评估平台 - 多租户架构迁移报告</h1>
        <p>迁移时间: $(date)</p>
        <p>数据库: $DB_HOST:$DB_PORT/$DB_NAME</p>
    </div>
    
    <div class="section">
        <h2>迁移概要</h2>
        <ul>
            <li class="success">✓ 数据库结构迁移完成</li>
            <li class="success">✓ 分区表创建完成</li>
            <li class="success">✓ 索引优化完成</li>
            <li class="success">✓ 行级安全策略配置完成</li>
            <li class="success">✓ 数据迁移完成</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>新架构特性</h2>
        <ul>
            <li>🏢 SaaS多租户架构</li>
            <li>🔒 数据完全隔离（分区+RLS）</li>
            <li>📊 租户自定义量表支持</li>
            <li>🛡️ 敏感数据加密</li>
            <li>📈 性能优化（分区裁剪）</li>
            <li>📝 完整审计日志</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>数据统计</h2>
        <table>
            <tr><th>表名</th><th>记录数</th><th>状态</th></tr>
EOF

    # 添加表统计信息
    TABLES=("tenants" "global_scale_registry" "platform_users" "tenant_user_memberships" "assessment_subjects")
    for table in "${TABLES[@]}"; do
        COUNT=$(psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null || echo "0")
        echo "            <tr><td>$table</td><td>$COUNT</td><td class=\"success\">✓</td></tr>" >> $REPORT_FILE
    done
    
    cat >> $REPORT_FILE << EOF
        </table>
    </div>
    
    <div class="section">
        <h2>下一步操作</h2>
        <ol>
            <li>更新应用程序代码以适配新的实体结构</li>
            <li>配置租户识别中间件</li>
            <li>测试多租户数据隔离</li>
            <li>配置监控和告警</li>
            <li>进行性能测试</li>
        </ol>
    </div>
    
    <div class="section">
        <h2>重要提醒</h2>
        <div class="warning">
            <p>⚠️ 旧表已重命名为 *_backup_20250621，可在确认新架构稳定后删除</p>
            <p>⚠️ 请更新应用程序代码以适配新的数据库结构</p>
            <p>⚠️ 建议在生产环境部署前进行充分测试</p>
        </div>
    </div>
</body>
</html>
EOF

    log_success "迁移报告生成: $REPORT_FILE"
    
    # 在macOS上自动打开报告
    if command -v open &> /dev/null; then
        open $REPORT_FILE
    fi
}

# 主函数
main() {
    log_info "开始多租户架构迁移..."
    
    check_prerequisites
    backup_existing_data
    execute_migration
    migrate_data
    verify_migration
    update_application_config
    generate_migration_report
    
    log_success "多租户架构迁移完成！"
    log_info "日志文件: $LOG_FILE"
}

# 错误处理
trap 'log_error "迁移过程中发生错误，请检查日志: $LOG_FILE"; exit 1' ERR

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF