#!/bin/bash

echo "=== Apple M4 性能监控面板 ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 清屏函数
clear_screen() {
    clear
    echo -e "${CYAN}================================================${NC}"
    echo -e "${CYAN}    Apple M4 智慧养老评估平台性能监控${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo
}

# 系统信息
show_system_info() {
    echo -e "${BLUE}🍎 Apple M4 系统信息${NC}"
    echo -e "${YELLOW}芯片:${NC} $(sysctl -n machdep.cpu.brand_string)"
    echo -e "${YELLOW}架构:${NC} $(uname -m)"
    echo -e "${YELLOW}macOS:${NC} $(sw_vers -productVersion)"
    echo -e "${YELLOW}内核:${NC} $(uname -v | cut -d' ' -f1-4)"
    echo
}

# CPU监控
show_cpu_info() {
    echo -e "${BLUE}⚡ CPU信息${NC}"
    
    # CPU基本信息
    TOTAL_CORES=$(sysctl -n hw.ncpu)
    P_CORES=$(sysctl -n hw.perflevel0.physicalcpu)
    E_CORES=$(sysctl -n hw.perflevel1.physicalcpu 2>/dev/null || echo "0")
    
    echo -e "${YELLOW}总核心数:${NC} $TOTAL_CORES"
    echo -e "${YELLOW}性能核心:${NC} $P_CORES"
    echo -e "${YELLOW}效率核心:${NC} $E_CORES"
    
    # CPU使用率
    CPU_USAGE=$(top -l 1 -s 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    echo -e "${YELLOW}CPU使用率:${NC} ${CPU_USAGE}%"
    
    # 温度 (如果可获取)
    if command -v powermetrics >/dev/null 2>&1; then
        TEMP=$(sudo powermetrics -n 1 -i 1000 --samplers smc -a --hide-cpu-duty-cycle 2>/dev/null | grep "CPU die temperature" | awk '{print $4}' | head -1)
        if [ -n "$TEMP" ]; then
            echo -e "${YELLOW}CPU温度:${NC} ${TEMP}°C"
        fi
    fi
    echo
}

# 内存监控
show_memory_info() {
    echo -e "${BLUE}💾 内存信息${NC}"
    
    # 总内存
    TOTAL_MEM=$(sysctl hw.memsize | awk '{print $2/1024/1024/1024}')
    echo -e "${YELLOW}总内存:${NC} ${TOTAL_MEM}GB"
    
    # 内存使用情况
    MEM_INFO=$(vm_stat | perl -ne '/page size of (\d+)/ and $size=$1; /Pages\s+([^:]+):\s+(\d+)/ and printf("%-16s % 16.2f MB\n", "$1:", $2 * $size / 1048576);')
    echo "$MEM_INFO"
    echo
}

# Docker容器监控
show_docker_info() {
    echo -e "${BLUE}🐳 Docker容器状态${NC}"
    
    if ! command -v docker >/dev/null 2>&1; then
        echo -e "${RED}Docker未安装${NC}"
        return
    fi
    
    if ! docker info >/dev/null 2>&1; then
        echo -e "${RED}Docker未运行${NC}"
        return
    fi
    
    echo -e "${YELLOW}运行中的容器:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(assessment|postgres|redis|minio)"
    
    echo
    echo -e "${YELLOW}资源使用情况:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}" | head -10
    echo
}

# Java进程监控
show_java_info() {
    echo -e "${BLUE}☕ Java进程监控${NC}"
    
    JAVA_PIDS=$(pgrep -f "spring-boot")
    if [ -z "$JAVA_PIDS" ]; then
        echo -e "${YELLOW}未发现Java进程${NC}"
        echo
        return
    fi
    
    for PID in $JAVA_PIDS; do
        if ps -p $PID >/dev/null 2>&1; then
            JAVA_CMD=$(ps -p $PID -o comm= 2>/dev/null || echo "java")
            MEM_USAGE=$(ps -p $PID -o rss= 2>/dev/null || echo "0")
            MEM_MB=$((MEM_USAGE / 1024))
            CPU_USAGE=$(ps -p $PID -o pcpu= 2>/dev/null || echo "0")
            
            echo -e "${YELLOW}PID:${NC} $PID"
            echo -e "${YELLOW}进程:${NC} $JAVA_CMD"
            echo -e "${YELLOW}内存:${NC} ${MEM_MB}MB"
            echo -e "${YELLOW}CPU:${NC} ${CPU_USAGE}%"
            echo
            
            # JVM信息 (如果可获取)
            if command -v jps >/dev/null 2>&1; then
                JPS_INFO=$(jps -l | grep $PID)
                if [ -n "$JPS_INFO" ]; then
                    echo -e "${YELLOW}应用:${NC} $(echo $JPS_INFO | cut -d' ' -f2-)"
                fi
            fi
        fi
    done
    echo
}

# Node.js进程监控
show_node_info() {
    echo -e "${BLUE}🟢 Node.js进程监控${NC}"
    
    NODE_PIDS=$(pgrep -f "node")
    if [ -z "$NODE_PIDS" ]; then
        echo -e "${YELLOW}未发现Node.js进程${NC}"
        echo
        return
    fi
    
    for PID in $NODE_PIDS; do
        if ps -p $PID >/dev/null 2>&1; then
            NODE_CMD=$(ps -p $PID -o args= | head -c 50)
            MEM_USAGE=$(ps -p $PID -o rss= 2>/dev/null || echo "0")
            MEM_MB=$((MEM_USAGE / 1024))
            CPU_USAGE=$(ps -p $PID -o pcpu= 2>/dev/null || echo "0")
            
            echo -e "${YELLOW}PID:${NC} $PID"
            echo -e "${YELLOW}命令:${NC} $NODE_CMD..."
            echo -e "${YELLOW}内存:${NC} ${MEM_MB}MB"
            echo -e "${YELLOW}CPU:${NC} ${CPU_USAGE}%"
            echo
        fi
    done
}

# 网络监控
show_network_info() {
    echo -e "${BLUE}🌐 网络监控${NC}"
    
    echo -e "${YELLOW}端口监听状态:${NC}"
    netstat -an | grep LISTEN | grep -E "(5273|5274|8181|5433|6380|9000|9001)" | while read line; do
        port=$(echo $line | awk '{print $4}' | cut -d'.' -f2)
        case $port in
            "8181") service="后端API" ;;
            "5273") service="uni-app H5" ;;
            "5274") service="管理后台" ;;
            "5433") service="PostgreSQL" ;;
            "6380") service="Redis" ;;
            "9000") service="MinIO API" ;;
            "9001") service="MinIO Console" ;;
            *) service="未知服务" ;;
        esac
        echo -e "  ${GREEN}✓${NC} $service ($port)"
    done
    echo
}

# 磁盘空间监控
show_disk_info() {
    echo -e "${BLUE}💿 磁盘空间${NC}"
    
    df -h / | tail -1 | while read filesystem size used avail percent mount; do
        echo -e "${YELLOW}根分区:${NC} $used / $size (${percent} 已使用)"
    done
    
    # Docker卷空间
    if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        DOCKER_ROOT=$(docker info --format '{{.DockerRootDir}}' 2>/dev/null || echo "/var/lib/docker")
        if [ -d "$DOCKER_ROOT" ]; then
            DOCKER_SIZE=$(du -sh "$DOCKER_ROOT" 2>/dev/null | cut -f1 || echo "未知")
            echo -e "${YELLOW}Docker数据:${NC} $DOCKER_SIZE"
        fi
    fi
    
    # 项目目录大小
    PROJECT_SIZE=$(du -sh . 2>/dev/null | cut -f1 || echo "未知")
    echo -e "${YELLOW}项目大小:${NC} $PROJECT_SIZE"
    echo
}

# 应用健康检查
show_health_check() {
    echo -e "${BLUE}❤️ 应用健康检查${NC}"
    
    # 后端健康检查
    if curl -f -s http://localhost:8181/actuator/health >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} 后端API服务正常"
        
        # 获取详细健康信息
        HEALTH_INFO=$(curl -s http://localhost:8181/actuator/health 2>/dev/null)
        if echo "$HEALTH_INFO" | grep -q '"status":"UP"'; then
            echo -e "  状态: ${GREEN}UP${NC}"
        fi
    else
        echo -e "${RED}✗${NC} 后端API服务异常"
    fi
    
    # 数据库连接检查
    if docker exec assessment-postgres-dev pg_isready -U assessment_user >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} PostgreSQL连接正常"
    else
        echo -e "${RED}✗${NC} PostgreSQL连接异常"
    fi
    
    # Redis连接检查
    if docker exec assessment-redis-dev redis-cli -a redis123 ping >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} Redis连接正常"
    else
        echo -e "${RED}✗${NC} Redis连接异常"
    fi
    
    # MinIO健康检查
    if curl -f -s http://localhost:9000/minio/health/live >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} MinIO服务正常"
    else
        echo -e "${RED}✗${NC} MinIO服务异常"
    fi
    
    echo
}

# 实时监控模式
realtime_monitor() {
    while true; do
        clear_screen
        show_system_info
        show_cpu_info
        show_memory_info
        show_docker_info
        show_health_check
        
        echo -e "${CYAN}更新时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
        echo -e "${YELLOW}按 Ctrl+C 退出实时监控${NC}"
        
        sleep 5
    done
}

# 主函数
main() {
    case "${1:-summary}" in
        "realtime"|"-r"|"--realtime")
            echo "启动实时监控模式..."
            realtime_monitor
            ;;
        "system"|"-s"|"--system")
            clear_screen
            show_system_info
            show_cpu_info
            show_memory_info
            show_disk_info
            ;;
        "docker"|"-d"|"--docker")
            clear_screen
            show_docker_info
            ;;
        "java"|"-j"|"--java")
            clear_screen
            show_java_info
            show_node_info
            ;;
        "network"|"-n"|"--network")
            clear_screen
            show_network_info
            ;;
        "health"|"-h"|"--health")
            clear_screen
            show_health_check
            ;;
        "help"|"--help")
            echo "Apple M4 性能监控工具"
            echo
            echo "用法: $0 [选项]"
            echo
            echo "选项:"
            echo "  summary, (默认)    显示综合信息"
            echo "  realtime, -r       实时监控模式"
            echo "  system, -s         系统信息"
            echo "  docker, -d         Docker容器信息"
            echo "  java, -j           Java/Node.js进程信息"
            echo "  network, -n        网络监控"
            echo "  health, -h         健康检查"
            echo "  help, --help       显示帮助"
            ;;
        *)
            clear_screen
            show_system_info
            show_cpu_info
            show_memory_info
            show_docker_info
            show_java_info
            show_health_check
            show_network_info
            echo -e "${CYAN}提示: 使用 '$0 realtime' 进入实时监控模式${NC}"
            ;;
    esac
}

# 运行主函数
main "$@"