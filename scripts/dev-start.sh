#!/bin/bash

set -e

echo "=== 启动智慧养老评估平台开发环境 ==="

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查conda环境
check_conda_env() {
    if conda env list | grep -q "Assessment.*\*"; then
        echo -e "${GREEN}✓${NC} Assessment 环境已激活"
    else
        echo -e "${YELLOW}!${NC} 正在激活 Assessment 环境..."
        eval "$(conda shell.bash hook)"
        conda activate Assessment
    fi
}

# 启动数据库服务
start_database() {
    echo -e "\n${YELLOW}1. 启动数据库服务${NC}"
    
    if docker-compose ps | grep -q "Up"; then
        echo "Docker服务已在运行"
    else
        echo "启动 PostgreSQL, Redis, MinIO..."
        docker-compose up -d postgres redis minio
        
        echo "等待服务启动..."
        sleep 10
        
        # 检查服务状态
        if docker-compose ps | grep -q "postgres.*Up"; then
            echo -e "${GREEN}✓${NC} PostgreSQL 已启动"
        else
            echo -e "${RED}✗${NC} PostgreSQL 启动失败"
            exit 1
        fi
        
        if docker-compose ps | grep -q "redis.*Up"; then
            echo -e "${GREEN}✓${NC} Redis 已启动"
        else
            echo -e "${RED}✗${NC} Redis 启动失败"
            exit 1
        fi
        
        if docker-compose ps | grep -q "minio.*Up"; then
            echo -e "${GREEN}✓${NC} MinIO 已启动"
        else
            echo -e "${RED}✗${NC} MinIO 启动失败"
            exit 1
        fi
    fi
}

# 初始化数据库
init_database() {
    echo -e "\n${YELLOW}2. 初始化数据库${NC}"
    
    # 等待PostgreSQL完全启动
    echo "等待 PostgreSQL 完全启动..."
    sleep 5
    
    # 检查数据库是否已初始化
    if docker exec assessment-postgres psql -U assessment_user -d elderly_assessment -c "SELECT 1 FROM users LIMIT 1;" >/dev/null 2>&1; then
        echo "数据库已初始化"
    else
        echo "正在初始化数据库..."
        docker exec -i assessment-postgres psql -U assessment_user -d elderly_assessment < scripts/init-db.sql
        echo -e "${GREEN}✓${NC} 数据库初始化完成"
    fi
}

# 启动后端服务
start_backend() {
    echo -e "\n${YELLOW}3. 启动后端服务${NC}"
    
    cd backend
    
    # 检查是否已经在运行
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}!${NC} 端口 8080 已被占用，可能后端已在运行"
    else
        echo "编译并启动 Spring Boot 应用..."
        nohup ./mvnw spring-boot:run > ../logs/backend.log 2>&1 &
        echo $! > ../logs/backend.pid
        
        echo "等待后端服务启动..."
        sleep 15
        
        # 检查服务是否启动成功
        if curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} 后端服务启动成功"
        else
            echo -e "${RED}✗${NC} 后端服务启动失败，请查看日志: logs/backend.log"
        fi
    fi
    
    cd ..
}

# 启动前端服务
start_frontend() {
    echo -e "\n${YELLOW}4. 启动前端服务${NC}"
    
    # uni-app H5
    cd frontend/uni-app
    if lsof -Pi :5273 -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}!${NC} 端口 5273 已被占用，可能 uni-app 已在运行"
    else
        echo "启动 uni-app H5 开发服务器..."
        nohup npm run dev:h5 > ../../logs/uni-app.log 2>&1 &
        echo $! > ../../logs/uni-app.pid
    fi
    cd ../..
    
    # 管理后台
    cd frontend/admin
    if lsof -Pi :5274 -sTCP:LISTEN -t >/dev/null; then
        echo -e "${YELLOW}!${NC} 端口 5274 已被占用，可能管理后台已在运行"
    else
        echo "启动管理后台开发服务器..."
        nohup npm run dev > ../../logs/admin.log 2>&1 &
        echo $! > ../../logs/admin.pid
    fi
    cd ../..
    
    echo "等待前端服务启动..."
    sleep 10
}

# 显示访问信息
show_access_info() {
    echo -e "\n${GREEN}=== 开发环境启动完成 ===${NC}"
    echo
    echo -e "${BLUE}访问地址:${NC}"
    echo "🔗 后端API:          http://localhost:8080"
    echo "🔗 API文档:          http://localhost:8080/swagger-ui.html"
    echo "🔗 前端H5:           http://localhost:5273"
    echo "🔗 管理后台:         http://localhost:5274"
    echo "🔗 MinIO控制台:      http://localhost:9001"
    echo
    echo -e "${BLUE}数据库连接:${NC}"
    echo "📊 PostgreSQL:       localhost:5433/elderly_assessment"
    echo "📊 Redis:            localhost:6380"
    echo
    echo -e "${BLUE}默认账号:${NC}"
    echo "👤 管理员:           admin / admin123"
    echo "👤 MinIO:            minioadmin / minioadmin"
    echo
    echo -e "${BLUE}日志文件:${NC}"
    echo "📄 后端日志:         logs/backend.log"
    echo "📄 前端日志:         logs/uni-app.log, logs/admin.log"
    echo
    echo -e "${YELLOW}停止服务:${NC} ./scripts/dev-stop.sh"
}

# 主函数
main() {
    # 确保在项目根目录
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    check_conda_env
    start_database
    init_database
    start_backend
    start_frontend
    show_access_info
}

# 运行主函数
main "$@"