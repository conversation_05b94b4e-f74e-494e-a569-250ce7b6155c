-- 创建数据库（如果不存在）
-- CREATE DATABASE elderly_assessment;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 创建枚举类型
CREATE TYPE user_role AS ENUM ('SUPER_ADMIN', 'ADMIN', 'ASSESSOR', 'VIEWER');
CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'LOCKED');
CREATE TYPE assessment_status AS ENUM ('DRAFT', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');
CREATE TYPE scale_type AS ENUM ('ELDERLY_CAPACITY', 'EMOTION_QUICK', 'INTERRAI', 'LONG_TERM_CARE', 'CUSTOM');

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    phone VARCHAR(20),
    email VARCHAR(100),
    role user_role NOT NULL DEFAULT 'ASSESSOR',
    status user_status NOT NULL DEFAULT 'ACTIVE',
    organization_id UUID,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 机构表
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type VARCHAR(50),
    address VARCHAR(200),
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    license_number VARCHAR(100),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 老年人信息表
CREATE TABLE IF NOT EXISTS elderly (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    id_card VARCHAR(18) UNIQUE,
    gender VARCHAR(10),
    birth_date DATE,
    phone VARCHAR(20),
    address VARCHAR(200),
    emergency_contact VARCHAR(50),
    emergency_phone VARCHAR(20),
    organization_id UUID REFERENCES organizations(id),
    admission_date DATE,
    room_number VARCHAR(20),
    bed_number VARCHAR(20),
    care_level VARCHAR(20),
    medical_history TEXT,
    allergies TEXT,
    medications TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估量表配置表
CREATE TABLE IF NOT EXISTS assessment_scales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type scale_type NOT NULL,
    version VARCHAR(20),
    description TEXT,
    form_schema JSONB NOT NULL,
    scoring_rules JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估记录表
CREATE TABLE IF NOT EXISTS assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    elderly_id UUID NOT NULL REFERENCES elderly(id),
    scale_id UUID NOT NULL REFERENCES assessment_scales(id),
    assessor_id UUID NOT NULL REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id),
    status assessment_status NOT NULL DEFAULT 'DRAFT',
    form_data JSONB NOT NULL,
    score_detail JSONB,
    total_score DECIMAL(10,2),
    risk_level VARCHAR(20),
    suggestions TEXT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER, -- 评估时长（分钟）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估报告表
CREATE TABLE IF NOT EXISTS assessment_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    assessment_id UUID NOT NULL REFERENCES assessments(id),
    report_number VARCHAR(50) UNIQUE NOT NULL,
    report_type VARCHAR(20),
    content JSONB,
    file_url VARCHAR(500),
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by UUID REFERENCES users(id)
);

-- 文件上传表
CREATE TABLE IF NOT EXISTS files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    mime_type VARCHAR(100),
    bucket_name VARCHAR(100),
    object_key VARCHAR(500),
    url VARCHAR(1000),
    uploaded_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    username VARCHAR(50),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_params JSONB,
    response_status INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(50) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, key)
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_elderly_organization ON elderly(organization_id);
CREATE INDEX idx_elderly_id_card ON elderly(id_card);
CREATE INDEX idx_assessments_elderly ON assessments(elderly_id);
CREATE INDEX idx_assessments_assessor ON assessments(assessor_id);
CREATE INDEX idx_assessments_status ON assessments(status);
CREATE INDEX idx_assessments_created ON assessments(created_at);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created ON audit_logs(created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_elderly_updated_at BEFORE UPDATE ON elderly
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessment_scales_updated_at BEFORE UPDATE ON assessment_scales
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessments_updated_at BEFORE UPDATE ON assessments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认数据
-- 默认超级管理员（密码: admin123）
INSERT INTO users (username, password, real_name, role, status) VALUES
('admin', '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36Zf4yXrwfJpRTOgY7Xov1i', '系统管理员', 'SUPER_ADMIN', 'ACTIVE');

-- 默认评估量表
INSERT INTO assessment_scales (name, code, type, version, description, form_schema) VALUES
('老年人能力评估表', 'ELDERLY_CAPACITY_V1', 'ELDERLY_CAPACITY', '1.0', '民政部老年人能力评估标准表', '{}'),
('情绪快速评估表', 'EMOTION_QUICK_V1', 'EMOTION_QUICK', '1.0', '老年人情绪状态快速评估', '{}'),
('interRAI评估表', 'INTERRAI_V1', 'INTERRAI', '1.0', '国际居民评估工具', '{}'),
('长期护理保险评估表', 'LONG_TERM_CARE_V1', 'LONG_TERM_CARE', '1.0', '长护险失能等级评估', '{}');

-- 系统配置
INSERT INTO system_configs (category, key, value, description) VALUES
('system', 'version', '1.0.0', '系统版本'),
('assessment', 'auto_save_interval', '30', '自动保存间隔（秒）'),
('assessment', 'max_duration', '120', '最大评估时长（分钟）'),
('security', 'password_min_length', '8', '密码最小长度'),
('security', 'max_login_attempts', '5', '最大登录尝试次数'),
('security', 'lock_duration', '1800', '账户锁定时长（秒）');