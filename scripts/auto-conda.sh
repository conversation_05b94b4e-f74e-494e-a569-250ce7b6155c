#!/bin/bash

# 智慧养老评估平台 - Conda环境自动激活脚本
# 作者: Claude Code
# 版本: 1.0.0
# 功能: 自动检测项目目录并激活Assessment conda环境

# 配置项
PROJECT_NAME="Assessment"
CONDA_ENV_NAME="Assessment"
PROJECT_ROOT="/Volumes/acasis/Assessment"
CONDA_PATH="/Volumes/acasis/miniconda3/miniconda3"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查conda是否可用
check_conda() {
    if [ ! -f "$CONDA_PATH/bin/conda" ]; then
        log_error "Conda not found at $CONDA_PATH"
        return 1
    fi
    
    # 初始化conda（如果还未初始化）
    if ! command -v conda &> /dev/null; then
        log_info "Initializing conda..."
        source "$CONDA_PATH/etc/profile.d/conda.sh"
    fi
    
    return 0
}

# 检查环境是否存在
check_env_exists() {
    conda env list | grep -q "^$CONDA_ENV_NAME"
    return $?
}

# 获取当前激活的环境
get_current_env() {
    if [ -n "$CONDA_DEFAULT_ENV" ]; then
        echo "$CONDA_DEFAULT_ENV"
    else
        echo "base"
    fi
}

# 检查是否在项目目录内
is_in_project_directory() {
    local current_dir=$(pwd)
    case "$current_dir" in
        "$PROJECT_ROOT"*) return 0 ;;
        *) return 1 ;;
    esac
}

# 主函数：自动激活环境
auto_activate_conda() {
    # 检查conda可用性
    if ! check_conda; then
        return 1
    fi
    
    # 检查是否在项目目录
    if ! is_in_project_directory; then
        return 0  # 不在项目目录，什么都不做
    fi
    
    # 获取当前环境
    local current_env=$(get_current_env)
    
    # 如果已经在目标环境中，不需要切换
    if [ "$current_env" = "$CONDA_ENV_NAME" ]; then
        return 0
    fi
    
    # 检查目标环境是否存在
    if ! check_env_exists; then
        log_warn "Conda environment '$CONDA_ENV_NAME' not found!"
        log_info "Creating environment from environment.yml..."
        
        if [ -f "$PROJECT_ROOT/environment.yml" ]; then
            conda env create -f "$PROJECT_ROOT/environment.yml"
            if [ $? -eq 0 ]; then
                log_success "Environment '$CONDA_ENV_NAME' created successfully"
            else
                log_error "Failed to create environment '$CONDA_ENV_NAME'"
                return 1
            fi
        else
            log_error "environment.yml not found in project root"
            return 1
        fi
    fi
    
    # 激活环境
    log_info "Activating conda environment: $CONDA_ENV_NAME"
    conda activate "$CONDA_ENV_NAME"
    
    if [ $? -eq 0 ]; then
        log_success "Conda environment '$CONDA_ENV_NAME' activated"
        log_info "Current environment: $(get_current_env)"
        
        # 显示环境信息
        echo ""
        echo -e "${BLUE}=== Environment Information ===${NC}"
        echo -e "Project: ${GREEN}$PROJECT_NAME${NC}"
        echo -e "Environment: ${GREEN}$CONDA_ENV_NAME${NC}"
        echo -e "Python: ${GREEN}$(python --version 2>/dev/null || echo 'Not available')${NC}"
        echo -e "Node.js: ${GREEN}$(node --version 2>/dev/null || echo 'Not available')${NC}"
        echo -e "Java: ${GREEN}$(java -version 2>&1 | head -n1 | cut -d'"' -f2 2>/dev/null || echo 'Not available')${NC}"
        echo ""
    else
        log_error "Failed to activate conda environment '$CONDA_ENV_NAME'"
        return 1
    fi
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    auto_activate_conda
fi