# 项目结构详解

## 项目根目录结构
```
Assessment/
├── backend/                 # 后端Java项目
├── frontend/               # 前端项目集合
│   ├── admin/             # Vue3管理后台
│   └── uni-app/           # uni-app移动端
├── docker/                # Docker相关文件
├── config/                # 配置文件
├── scripts/               # 脚本文件
├── docs/                  # 文档
├── data/                  # 数据目录
├── logs/                  # 日志目录
└── docker-compose.yml     # Docker编排文件
```

## 后端项目结构 (backend/)
```
backend/
├── src/
│   ├── main/
│   │   ├── java/com/assessment/
│   │   │   ├── AssessmentApplication.java    # 主启动类
│   │   │   ├── config/                       # 配置类
│   │   │   │   ├── SecurityConfig.java      # 安全配置
│   │   │   │   ├── OpenApiConfig.java       # API文档配置
│   │   │   │   ├── CorsConfig.java          # 跨域配置
│   │   │   │   └── *Properties.java         # 配置属性类
│   │   │   ├── controller/                  # 控制器层
│   │   │   │   ├── AIAnalysisController.java
│   │   │   │   ├── MultiTenantAuthController.java
│   │   │   │   ├── SystemScaleController.java
│   │   │   │   └── ...
│   │   │   ├── service/                     # 服务层
│   │   │   │   ├── AssessmentService.java
│   │   │   │   ├── AIAnalysisService.java
│   │   │   │   ├── MultiTenantAuthService.java
│   │   │   │   └── scoring/                 # 评分策略
│   │   │   ├── repository/                  # 数据访问层
│   │   │   │   └── multitenant/            # 多租户仓储
│   │   │   ├── entity/                      # 实体类
│   │   │   │   ├── BaseEntity.java         # 基础实体
│   │   │   │   └── multitenant/            # 多租户实体
│   │   │   ├── dto/                         # 数据传输对象
│   │   │   ├── security/                    # 安全相关
│   │   │   │   ├── JwtTokenProvider.java
│   │   │   │   ├── JwtAuthenticationFilter.java
│   │   │   │   └── ...
│   │   │   ├── pdf/                         # PDF处理
│   │   │   │   ├── PDFParserService.java
│   │   │   │   ├── extractor/              # 表格提取
│   │   │   │   └── generator/              # 报告生成
│   │   │   ├── exception/                   # 异常处理
│   │   │   │   ├── GlobalExceptionHandler.java
│   │   │   │   └── *Exception.java
│   │   │   ├── constants/                   # 常量定义
│   │   │   └── utils/                       # 工具类
│   │   └── resources/
│   │       ├── application.yml              # 主配置文件
│   │       ├── application-local.yml        # 本地环境配置
│   │       ├── db/migration/               # 数据库迁移脚本
│   │       ├── scripts/                    # Python脚本
│   │       └── static/                     # 静态资源
│   └── test/                               # 测试代码
│       └── java/com/assessment/
├── target/                                 # 构建输出
├── pom.xml                                # Maven配置
└── .mvn/                                  # Maven包装器
```

## 前端管理后台结构 (frontend/admin/)
```
admin/
├── src/
│   ├── views/                             # 页面组件
│   │   ├── assessment/                    # 评估相关页面
│   │   │   ├── PdfUpload.vue
│   │   │   ├── FieldMapping.vue
│   │   │   └── components/               # 评估组件
│   │   │       ├── AIAnalysisSection.vue
│   │   │       ├── AIChatDialog.vue
│   │   │       └── stages/              # 评估阶段组件
│   │   └── system/                       # 系统管理页面
│   │       ├── AssessmentManagement.vue
│   │       ├── ScaleManagement.vue
│   │       ├── TenantManagement.vue
│   │       └── components/              # 系统管理组件
│   ├── components/                        # 公共组件
│   │   ├── ContentBlock.vue
│   │   ├── FieldEditor.vue
│   │   └── HelloWorld.test.ts
│   ├── api/                              # API接口
│   │   ├── assessment.js
│   │   └── multiTenantAdapter.js
│   ├── router/                           # 路由配置
│   │   └── index.ts
│   ├── store/                            # 状态管理
│   ├── utils/                            # 工具函数
│   │   ├── request.ts                   # HTTP请求封装
│   │   ├── auth-clear.ts               # 认证清理
│   │   └── index.test.ts
│   ├── styles/                           # 样式文件
│   │   ├── brand-colors.css
│   │   └── element-theme.css
│   ├── types/                            # TypeScript类型定义
│   ├── App.vue                           # 根组件
│   └── main.ts                           # 入口文件
├── public/                               # 静态资源
├── package.json                          # 依赖配置
├── vite.config.ts                        # Vite配置
├── tsconfig.json                         # TypeScript配置
└── .eslintrc.js                          # ESLint配置
```

## uni-app移动端结构 (frontend/uni-app/)
```
uni-app/
├── src/
│   ├── pages/                            # 页面
│   │   ├── index/                       # 首页
│   │   ├── assessment/                  # 评估相关
│   │   │   ├── conduct/                # 执行评估
│   │   │   ├── create/                 # 创建评估
│   │   │   └── report/                 # 评估报告
│   │   ├── elderly/                     # 老人管理
│   │   ├── scale/                       # 量表管理
│   │   └── user/                        # 用户管理
│   ├── components/                       # 组件
│   │   ├── Common/                      # 通用组件
│   │   │   ├── Button.vue
│   │   │   ├── Card.vue
│   │   │   └── ...
│   │   ├── Form/                        # 表单组件
│   │   ├── Layout/                      # 布局组件
│   │   └── Scale/                       # 量表组件
│   ├── api/                             # API接口
│   │   ├── assessment.js
│   │   ├── auth.js
│   │   └── elderly.js
│   ├── store/                           # 状态管理
│   │   ├── index.js
│   │   └── modules/
│   │       ├── assessment.js
│   │       ├── elderly.js
│   │       └── user.js
│   ├── utils/                           # 工具函数
│   │   ├── index.js
│   │   ├── request.js
│   │   └── eventOptimizer.js
│   ├── common/styles/                   # 公共样式
│   │   ├── index.scss
│   │   ├── variables.scss
│   │   └── mixins.scss
│   ├── App.vue                          # 根组件
│   ├── main.js                          # 入口文件
│   └── manifest.json                    # uni-app配置
├── package.json                         # 依赖配置
└── .eslintrc.js                         # ESLint配置
```

## 配置和脚本目录

### config/ - 配置文件
```
config/
├── nginx/                               # Nginx配置
│   ├── nginx.conf
│   └── conf.d/
├── postgres/                            # PostgreSQL配置
├── redis/                               # Redis配置
└── app/                                 # 应用配置
```

### scripts/ - 脚本文件
```
scripts/
├── dev-start-m4.sh                     # M4优化启动脚本
├── dev-start.sh                         # 标准启动脚本
├── dev-stop.sh                          # 停止脚本
├── check-m4-compatibility.sh           # M4兼容性检查
├── check-ports.sh                       # 端口检查
├── code-quality-check.sh               # 代码质量检查
├── security-check.sh                    # 安全检查
├── monitor-m4.sh                        # M4性能监控
├── init-db.sql                          # 数据库初始化
├── backup/                              # 备份脚本
├── deploy/                              # 部署脚本
└── monitor/                             # 监控脚本
```

### docs/ - 文档目录
```
docs/
├── plan/                                # 规划文档
│   ├── PRD.md                          # 产品需求文档
│   ├── Local_Deployment_Architecture.md
│   └── Flutter_vs_UniApp_Comparison.md
├── api/                                 # API文档
└── deployment/                          # 部署文档
```

## 数据和日志目录

### data/ - 数据持久化
```
data/
├── postgres/                            # PostgreSQL数据
├── redis/                               # Redis数据
└── minio/                               # MinIO对象存储
```

### logs/ - 日志文件
```
logs/
├── backend-m4.log                       # 后端日志
├── uni-app-m4.log                       # uni-app日志
├── admin-m4.log                         # 管理后台日志
└── nginx/                               # Nginx日志
```

## 关键文件说明

### 配置文件
- `docker-compose.yml`: 生产环境Docker编排
- `docker-compose.dev.yml`: 开发环境Docker编排
- `environment.yml`: Conda环境配置
- `.env.example`: 环境变量模板

### 构建文件
- `backend/pom.xml`: Maven构建配置
- `frontend/admin/package.json`: 管理后台依赖
- `frontend/uni-app/package.json`: uni-app依赖

### 文档文件
- `README.md`: 项目主文档
- `QUICK_START.md`: 快速开始指南
- `README_M4.md`: Apple M4优化说明