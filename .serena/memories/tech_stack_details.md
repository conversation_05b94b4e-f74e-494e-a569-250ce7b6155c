# 技术栈详细说明

## 后端技术栈详情

### 核心框架
- **Spring Boot 3.x**: 主框架，配置了多环境支持 (local, dev, prod)
- **Java 21 LTS**: 使用最新长期支持版本，支持现代Java特性
- **Maven**: 构建工具，配置了Apple Silicon优化

### 数据层
- **PostgreSQL 15**: 主数据库，端口5433，支持多租户架构
- **Redis 7**: 缓存和会话存储，端口6379
- **MinIO**: 对象存储服务，端口9000/9001
- **JPA/Hibernate**: ORM框架，配置了PostgreSQL方言

### 安全认证
- **Spring Security**: 安全框架
- **JWT**: 无状态认证，24小时过期，7天刷新
- **密码策略**: 最少8位，需要特殊字符
- **多租户认证**: 支持跨租户用户管理

### API文档
- **OpenAPI 3**: API规范
- **Swagger UI**: 交互式文档，访问地址 `/swagger-ui/index.html`

### 监控运维
- **Actuator**: 健康检查和指标监控
- **Prometheus**: 指标导出
- **日志**: 分级日志，支持文件和控制台输出

## 前端技术栈详情

### uni-app移动端
- **框架版本**: uni-app 3.x
- **开发语言**: JavaScript + Vue 3
- **UI组件**: uni-ui + 自定义组件
- **状态管理**: Vuex 4
- **支持平台**: H5、微信小程序、App
- **开发端口**: 5273

### 管理后台
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus 2.x
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **开发端口**: 5274

### 开发工具链
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript strict模式
- **测试**: Vitest + Vue Test Utils
- **样式**: SCSS + Tailwind CSS

## 部署架构

### 容器化
- **Docker**: 多阶段构建，ARM64原生镜像
- **Docker Compose**: 开发和生产环境编排
- **健康检查**: 所有服务都配置了健康检查

### 网络配置
- **端口映射**:
  - 后端API: 8181
  - PostgreSQL: 5433
  - Redis: 6379
  - MinIO: 9000/9001
  - uni-app: 5273
  - 管理后台: 5274
- **网络**: 自定义桥接网络 `assessment-net`

### Apple M4优化
- **ARM64镜像**: 所有服务使用ARM64原生镜像
- **内存优化**: 根据系统内存动态调整JVM参数
- **性能监控**: 专门的M4性能监控脚本