# 建议的开发命令

## 环境管理

### Conda环境
```bash
# 激活开发环境
conda activate Assessment

# 检查环境状态
conda env list
```

### 系统兼容性检查
```bash
# 检查Apple M4兼容性
./scripts/check-m4-compatibility.sh

# 检查端口占用
./scripts/check-ports.sh
```

## 开发服务启动

### 一键启动 (推荐)
```bash
# Apple M4优化版启动脚本
./scripts/dev-start-m4.sh

# 标准启动脚本
./scripts/dev-start.sh

# 停止所有服务
./scripts/dev-stop.sh
```

### 分步启动

#### 基础服务 (数据库、缓存等)
```bash
# 启动基础设施服务
docker-compose -f docker-compose.dev.yml up -d postgres redis minio

# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f postgres
```

#### 后端服务
```bash
cd backend

# 编译项目
./mvnw clean package -DskipTests

# 启动开发服务器
./mvnw spring-boot:run

# 或者运行jar文件
java -jar target/assessment-platform-1.0.0-SNAPSHOT.jar
```

#### 前端服务
```bash
# uni-app H5开发
cd frontend/uni-app
npm run dev:h5

# 管理后台开发
cd frontend/admin
npm run dev

# 同时启动两个前端
npm run dev:all  # 如果配置了这个脚本
```

## 代码质量检查

### 后端代码检查
```bash
cd backend

# 运行测试
./mvnw test

# 代码覆盖率测试
./mvnw test jacoco:report

# 代码风格检查 (如果配置了checkstyle)
./mvnw checkstyle:check

# 静态代码分析 (如果配置了SpotBugs)
./mvnw spotbugs:check
```

### 前端代码检查
```bash
# 管理后台
cd frontend/admin
npm run lint          # ESLint检查
npm run lint:fix       # 自动修复
npm run format         # Prettier格式化
npm run type-check     # TypeScript检查
npm run test           # 运行测试
npm run test:coverage  # 测试覆盖率

# uni-app
cd frontend/uni-app
npm run lint
npm run lint:fix
npm run format
npm run type-check
```

### 全项目质量检查
```bash
# 运行项目质量检查脚本
./scripts/code-quality-check.sh

# 安全检查
./scripts/security-check.sh
```

## 构建和部署

### 本地构建
```bash
# 后端构建
cd backend
./mvnw clean package

# 前端构建
cd frontend/admin
npm run build

cd ../uni-app
npm run build:h5
```

### Docker构建
```bash
# 构建所有服务
docker-compose build

# 构建特定服务
docker-compose build backend

# 生产环境部署
docker-compose -f docker-compose.yml up -d
```

## 数据库管理

### 数据库连接
```bash
# 连接PostgreSQL
docker exec -it assessment-postgres-dev psql -U assessment_user -d assessment_multitenant

# 连接Redis
docker exec -it assessment-redis-dev redis-cli -a redis123
```

### 数据库迁移
```bash
# 运行数据库迁移脚本
./scripts/migrate-to-multi-tenant.sh

# 测试多租户数据库
./scripts/test-multi-tenant-db.sh
```

## 监控和调试

### 性能监控
```bash
# Apple M4性能监控
./scripts/monitor-m4.sh

# 一般性能监控
./scripts/monitor/system-monitor.sh
```

### 日志查看
```bash
# 查看后端日志
tail -f logs/backend-m4.log

# 查看前端日志
tail -f logs/uni-app-m4.log
tail -f logs/admin-m4.log

# 查看Docker服务日志
docker-compose logs -f backend
```

### 调试工具
```bash
# 清理认证缓存
./scripts/clear-auth-cache.sh

# 清理控制台日志
./scripts/clean-console-logs.sh

# 重启应用服务
./scripts/restart-apps.sh
```

## 常用开发地址

### 本地访问地址
- 后端API: http://localhost:8181
- API文档: http://localhost:8181/swagger-ui/index.html
- 前端H5: http://localhost:5273
- 管理后台: http://localhost:5274
- MinIO控制台: http://localhost:9001

### 健康检查
```bash
# 后端健康检查
curl http://localhost:8181/actuator/health

# 前端可用性检查
curl http://localhost:5273
curl http://localhost:5274
```

## 故障排除

### 常见问题解决
```bash
# 端口被占用
lsof -ti :8181 | xargs kill -9

# 清理Docker资源
docker system prune -f

# 重置开发环境
./scripts/dev-stop.sh
docker-compose down -v
./scripts/dev-start-m4.sh
```

### 快速修复脚本
```bash
# 运行快速修复
./scripts/quick-fix.sh
```

## macOS特定命令

### 系统信息
```bash
# 检查架构
uname -m

# 检查内存
sysctl hw.memsize | awk '{print $2/1024/1024/1024}'

# 检查CPU信息
sysctl -n machdep.cpu.brand_string
```

### 进程管理
```bash
# 查看Java进程
jps -v

# 查看Node.js进程
ps aux | grep node
```