# 代码风格和约定

## 后端Java代码规范

### 基本规范
- **Java版本**: Java 21 LTS，使用现代语法特性
- **包结构**: `com.assessment.*` 分层架构
- **编码规范**: 遵循阿里巴巴Java开发规范
- **行长度**: 最大120字符
- **方法长度**: 最大50行
- **参数数量**: 最大7个参数

### 注解使用
- **Lombok**: 广泛使用 `@Data`, `@Builder`, `@NoArgsConstructor`, `@AllArgsConstructor`
- **日志**: 统一使用 `@Slf4j`，禁止 `System.out.println`
- **Spring**: 使用 `@RestController`, `@Service`, `@Repository`, `@Entity`
- **验证**: 使用 `@Validated` 和 Bean Validation注解

### 架构模式
```java
// Controller层模式
@RestController
@RequestMapping("/api/resource")
@Tag(name = "Resource Management")
@Slf4j
@Validated
public class ResourceController {
    @Autowired
    private ResourceService resourceService;
}

// Service层模式
@Service
@Transactional
@Slf4j
public class ResourceService {
    // 业务逻辑实现
}

// Entity层模式
@Entity
@Table(name = "resource")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Resource {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
}
```

### 异常处理
- 统一异常处理器：`GlobalExceptionHandler`
- 自定义异常：继承RuntimeException，包含错误码和消息
- 响应格式：统一使用 `ApiResponse<T>` 包装

## 前端代码规范

### Vue.js规范
- **语法**: Vue 3 Composition API + `<script setup>`
- **类型**: TypeScript strict模式，避免使用 `any`
- **组件命名**: PascalCase
- **文件命名**: kebab-case

### 组件结构模式
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// Props定义
interface Props {
  title: string;
  data?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => []
});

// 响应式状态
const loading = ref(false);

// 计算属性
const computedValue = computed(() => {
  // 计算逻辑
});

// 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped lang="scss">
.component-name {
  // 样式定义
}
</style>
```

### 代码质量工具
- **ESLint**: 代码检查，配置了TypeScript和Vue规则
- **Prettier**: 代码格式化，统一代码风格
- **TypeScript**: 严格类型检查
- **Vitest**: 单元测试框架

## 数据库设计规范

### 命名约定
- **表名**: 小写下划线分隔，如 `platform_users`
- **字段名**: 小写下划线分隔，如 `created_at`
- **主键**: 统一使用 `id` 自增主键
- **外键**: 使用 `_id` 后缀，如 `tenant_id`

### JPA实体规范
- 使用 `@Entity` 和明确的 `@Table(name = "table_name")`
- 主键策略：`@GeneratedValue(strategy = GenerationType.IDENTITY)`
- 审计字段：包含 `createdAt`, `updatedAt`
- 关系映射：正确使用 `@OneToMany`, `@ManyToOne` 等

## API设计规范

### RESTful设计
- **HTTP方法**: GET(查询), POST(创建), PUT(更新), DELETE(删除)
- **URL设计**: 资源导向，如 `/api/assessments/{id}`
- **响应格式**: 统一JSON格式，包含状态码和消息
- **错误处理**: 标准HTTP状态码 + 详细错误信息

### 文档要求
- **OpenAPI注解**: 所有API都需要完整的文档注解
- **参数验证**: 使用Bean Validation注解
- **示例数据**: 提供请求和响应示例

## 安全编码规范

### 数据保护
- **敏感信息**: 不在日志中记录密码、token等敏感信息
- **输入验证**: 所有用户输入都需要验证和清理
- **SQL注入**: 使用参数化查询
- **XSS防护**: 前端输出转义

### 认证授权
- **JWT**: 无状态认证，合理设置过期时间
- **权限控制**: 基于角色的访问控制(RBAC)
- **接口保护**: 所有API端点都需要适当的权限检查