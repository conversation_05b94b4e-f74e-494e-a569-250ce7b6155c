# 任务完成检查清单

## 开发任务完成后的标准流程

### 1. 代码质量检查
#### 后端代码检查
- [ ] 运行所有单元测试: `./mvnw test`
- [ ] 检查测试覆盖率: `./mvnw test jacoco:report`
- [ ] 代码风格检查: `./mvnw checkstyle:check` (如果配置)
- [ ] 静态代码分析: `./mvnw spotbugs:check` (如果配置)
- [ ] 确保无编译警告: `./mvnw compile`

#### 前端代码检查
- [ ] ESLint检查: `npm run lint`
- [ ] TypeScript类型检查: `npm run type-check`
- [ ] 运行单元测试: `npm run test`
- [ ] 代码格式化: `npm run format`
- [ ] 构建检查: `npm run build`

### 2. 功能验证
#### 本地环境验证
- [ ] 启动完整开发环境: `./scripts/dev-start-m4.sh`
- [ ] 验证后端API正常: 访问 http://localhost:8181/actuator/health
- [ ] 验证前端页面正常: 访问 http://localhost:5274
- [ ] 验证移动端正常: 访问 http://localhost:5273
- [ ] 测试新功能的完整流程
- [ ] 验证现有功能未受影响

#### API文档更新
- [ ] 更新OpenAPI注解
- [ ] 验证Swagger文档: http://localhost:8181/swagger-ui/index.html
- [ ] 确保API示例正确
- [ ] 更新相关接口文档

### 3. 安全检查
#### 代码安全审查
- [ ] 检查是否有硬编码密码或密钥
- [ ] 验证输入验证和清理
- [ ] 检查SQL注入防护
- [ ] 验证认证和授权逻辑
- [ ] 检查敏感信息是否正确处理

#### 依赖安全检查
- [ ] 后端依赖检查: `./mvnw dependency-check:check` (如果配置)
- [ ] 前端依赖检查: `npm audit`
- [ ] 修复已知安全漏洞

### 4. 性能检查
#### 后端性能
- [ ] 检查数据库查询效率
- [ ] 验证缓存策略正确
- [ ] 检查内存使用情况
- [ ] 验证并发处理能力

#### 前端性能
- [ ] 检查包大小: `npm run build` 后查看dist大小
- [ ] 验证页面加载速度
- [ ] 检查内存泄漏
- [ ] 验证移动端性能

### 5. 数据库相关
#### 数据库变更
- [ ] 编写数据库迁移脚本 (如有schema变更)
- [ ] 测试迁移脚本的正确性
- [ ] 验证数据完整性
- [ ] 备份重要数据

#### 多租户兼容性
- [ ] 验证多租户数据隔离
- [ ] 测试跨租户操作限制
- [ ] 验证租户权限控制

### 6. 文档更新
#### 代码文档
- [ ] 更新JavaDoc (后端)
- [ ] 更新JSDoc (前端)
- [ ] 更新README.md (如有重大变更)
- [ ] 更新API文档

#### 用户文档
- [ ] 更新用户手册 (如有界面变更)
- [ ] 更新部署文档 (如有部署变更)
- [ ] 更新配置说明 (如有新配置项)

### 7. 版本控制
#### Git提交
- [ ] 提交信息遵循约定格式
- [ ] 包含必要的变更说明
- [ ] 关联相关issue或任务
- [ ] 确保提交粒度合适

#### 分支管理
- [ ] 确保在正确的分支上开发
- [ ] 合并前进行代码审查
- [ ] 解决合并冲突
- [ ] 删除已合并的特性分支

### 8. 环境兼容性
#### Apple M4优化
- [ ] 验证ARM64兼容性
- [ ] 测试Docker镜像构建
- [ ] 验证性能优化效果
- [ ] 检查内存使用情况

#### 跨平台兼容性
- [ ] 验证不同操作系统兼容性
- [ ] 测试不同浏览器兼容性
- [ ] 验证移动端兼容性

### 9. 部署准备
#### 构建验证
- [ ] 本地构建成功: `./mvnw clean package`
- [ ] Docker构建成功: `docker-compose build`
- [ ] 生产环境配置检查
- [ ] 环境变量配置验证

#### 回滚准备
- [ ] 准备回滚方案
- [ ] 备份当前版本
- [ ] 确认回滚步骤
- [ ] 准备应急联系方式

### 10. 最终验证
#### 完整流程测试
- [ ] 用户注册/登录流程
- [ ] 评估创建和执行流程
- [ ] 数据导入导出功能
- [ ] 报告生成功能
- [ ] 权限控制验证

#### 压力测试 (重要功能)
- [ ] 并发用户访问测试
- [ ] 大文件上传测试
- [ ] 大量数据处理测试
- [ ] 长时间运行稳定性测试

## 自动化检查脚本

### 使用项目提供的检查脚本
```bash
# 代码质量检查
./scripts/code-quality-check.sh

# 安全检查
./scripts/security-check.sh

# 性能监控
./scripts/monitor-m4.sh

# 完整环境检查
./scripts/check-m4-compatibility.sh
./scripts/check-ports.sh
```

### 快速检查命令组合
```bash
# 后端快速检查
cd backend && ./mvnw clean test && cd ..

# 前端快速检查
cd frontend/admin && npm run lint && npm run test && npm run build && cd ../..

# 环境启动验证
./scripts/dev-start-m4.sh
```

## 检查清单模板

创建任务时可以复制以下模板到任务描述中：

```markdown
## 完成检查清单
- [ ] 代码质量检查通过
- [ ] 所有测试通过
- [ ] 功能验证完成
- [ ] 安全检查通过
- [ ] 性能检查正常
- [ ] 文档已更新
- [ ] 环境兼容性验证
- [ ] 部署准备就绪
```

## 质量门禁标准

任务只有在满足以下条件时才能被标记为完成：
1. 所有自动化测试通过
2. 代码覆盖率达到85%以上
3. 无高危安全漏洞
4. 代码审查通过
5. 功能验证完成
6. 文档更新完成