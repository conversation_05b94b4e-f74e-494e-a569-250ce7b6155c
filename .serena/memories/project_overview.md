# 智慧养老评估平台项目概览

## 项目目的
智慧养老评估平台是一个面向养老机构、社区服务中心、医疗机构的专业老年人综合能力评估数字化平台。通过移动端应用和Web管理后台，实现老年人能力的标准化评估和智能化分析。

## 技术栈
### 后端技术栈
- **框架**: Spring Boot 3.x + Java 21 LTS
- **数据库**: PostgreSQL 15 (主数据库) + Redis 7 (缓存)
- **存储**: MinIO (对象存储)
- **构建工具**: Maven
- **安全**: Spring Security + JWT认证
- **文档**: OpenAPI 3 (Swagger)
- **监控**: Spring Boot Actuator + Micrometer

### 前端技术栈
- **移动端**: uni-app (支持H5、微信小程序、App)
- **管理后台**: Vue 3 + TypeScript + Element Plus
- **状态管理**: Pinia (管理后台) + Vuex (uni-app)
- **构建工具**: Vite
- **样式**: SCSS + Tailwind CSS

### 部署技术栈
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **平台优化**: ARM64原生支持 (Apple M4优化)

## 核心功能特性
1. **多量表支持**: 老年人能力评估、情绪快评、interRAI评估、长护险评估
2. **移动端优化**: 离线评估支持、语音输入、大字体适老化设计
3. **智能分析**: AI评估建议、数据趋势分析、风险预警
4. **多租户架构**: 支持多机构独立管理
5. **数据安全**: 端到端加密、角色权限控制、审计日志

## 项目架构
- **多层架构**: Controller -> Service -> Repository -> Entity
- **多租户设计**: 全局用户管理 + 租户数据隔离
- **前后端分离**: RESTful API + 独立前端应用
- **微服务友好**: 模块化设计，易于拆分

## 开发环境特点
- **Apple M4优化**: ARM64原生容器、内存优化配置
- **开发便利性**: 一键启动脚本、自动端口管理、健康检查
- **代码质量**: ESLint、Prettier、单元测试、集成测试