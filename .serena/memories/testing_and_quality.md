# 测试和质量保证

## 测试框架和工具

### 后端测试
- **测试框架**: JUnit 5 + Mockito
- **集成测试**: Spring Boot Test
- **测试配置**: `application-test.yml`
- **测试覆盖率**: Ja<PERSON>o<PERSON>o (目标85%+)

### 前端测试
- **管理后台**: Vitest + Vue Test Utils
- **uni-app**: 支持单元测试
- **E2E测试**: 支持关键用户流程测试
- **类型检查**: TypeScript strict模式

## 现有测试用例

### 后端测试用例
1. **AssessmentApplicationTests.java**
   - 应用程序上下文加载测试
   - 基本功能测试

2. **SecurityConstantsTest.java**
   - 安全常量验证测试
   - JWT配置验证
   - 密码策略验证
   - 文件上传限制验证

3. **DefaultScoringStrategyTest.java**
   - 评分策略核心逻辑测试
   - 权重计算测试
   - 等级判定测试

4. **LMStudioConfigurationTest.java**
   - LM Studio配置测试
   - API配置移除验证

5. **LMStudioDynamicModelTest.java**
   - 动态模型选择测试
   - 模型优先级匹配测试
   - 能力推断测试

### 前端测试用例
1. **HelloWorld.test.ts** (管理后台)
   - 组件基础测试
2. **index.test.ts** (工具函数测试)
3. **index.test.js** (uni-app工具测试)

## 代码质量工具

### 后端质量检查
- **Checkstyle**: 代码风格检查
  - 配置文件: `checkstyle.xml`
  - 抑制规则: `checkstyle-suppressions.xml`
- **SpotBugs**: 静态代码分析 (推荐配置)
- **JaCoCo**: 代码覆盖率报告

### 前端质量检查
- **ESLint**: 代码检查和风格统一
  - TypeScript支持
  - Vue.js特定规则
  - Prettier集成
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查
- **Vitest**: 测试运行器

## 测试命令

### 后端测试命令
```bash
cd backend

# 运行所有测试
./mvnw test

# 运行特定测试类
./mvnw test -Dtest=SecurityConstantsTest

# 运行测试并生成覆盖率报告
./mvnw test jacoco:report

# 查看覆盖率报告
open target/site/jacoco/index.html
```

### 前端测试命令
```bash
# 管理后台测试
cd frontend/admin
npm run test                # 运行测试
npm run test:coverage       # 生成覆盖率报告
npm run lint               # ESLint检查
npm run lint:fix           # 自动修复
npm run format             # Prettier格式化
npm run type-check         # TypeScript检查

# uni-app测试
cd frontend/uni-app
npm run lint
npm run lint:fix
npm run format
npm run type-check
```

### 全项目质量检查
```bash
# 运行项目质量检查脚本
./scripts/code-quality-check.sh

# 安全检查
./scripts/security-check.sh
```

## 质量标准

### 代码覆盖率要求
- **后端**: 最低85%代码覆盖率
- **前端**: 推荐80%+覆盖率
- **关键业务逻辑**: 100%覆盖率

### 代码质量指标
- **复杂度**: 圈复杂度 < 10
- **方法长度**: < 50行
- **类长度**: < 500行
- **参数数量**: < 7个

### 安全检查项目
- **依赖漏洞**: 定期检查第三方依赖
- **密码策略**: 强密码要求
- **输入验证**: 所有用户输入验证
- **SQL注入**: 参数化查询
- **XSS防护**: 输出转义

## 持续集成建议

### 预提交检查
```bash
# Git hooks建议
# pre-commit hook
#!/bin/sh
# 运行代码格式化
npm run format
# 运行基础测试
npm run test:unit
# 检查TypeScript类型
npm run type-check
```

### 构建流水线
1. **代码检查阶段**
   - ESLint/Checkstyle检查
   - TypeScript类型检查
   - 安全漏洞扫描

2. **测试阶段**
   - 单元测试
   - 集成测试
   - 覆盖率检查

3. **构建阶段**
   - 后端Maven构建
   - 前端Vite构建
   - Docker镜像构建

4. **部署阶段**
   - 环境部署
   - 健康检查
   - 回滚准备

## 测试数据管理

### 测试数据库
- 使用H2内存数据库进行单元测试
- 集成测试使用TestContainers
- 测试数据自动清理

### 测试数据
- Mock数据生成
- 测试夹具 (Fixtures)
- 数据工厂模式

## 性能测试

### 后端性能测试
- **负载测试**: JMeter或Gatling
- **压力测试**: 评估系统极限
- **数据库性能**: 查询优化测试

### 前端性能测试
- **页面加载速度**: Lighthouse
- **内存泄漏**: Chrome DevTools
- **Bundle大小**: Webpack Bundle Analyzer

## 质量报告

### 自动化报告
- 每日质量报告生成
- 覆盖率趋势分析
- 技术债务跟踪

### 质量门禁
- 代码覆盖率不低于85%
- 所有测试必须通过
- 无高危安全漏洞
- ESLint/Checkstyle无错误