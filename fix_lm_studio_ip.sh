#!/bin/bash
# 修复LM Studio IP地址问题

echo "🔧 修复LM Studio IP地址配置..."

# 1. 清理可能的缓存
echo "1️⃣ 清理Spring Boot缓存..."
rm -rf ~/.m2/repository/com/assessment/
rm -rf target/

# 2. 确保配置文件正确
echo "2️⃣ 检查配置文件..."
grep -n "192.168" backend/src/main/resources/lm-studio-config.yml

# 3. 重新编译
echo "3️⃣ 重新编译项目..."
cd backend
./mvnw clean compile -DskipTests

# 4. 设置环境变量强制使用正确的IP
echo "4️⃣ 设置环境变量..."
export LM_STUDIO_PRIMARY_URL="http://*************:1234"

# 5. 重启服务
echo "5️⃣ 重启后端服务..."
pkill -f "spring-boot:run"
sleep 3

# 使用环境变量启动
nohup ./mvnw spring-boot:run \
  -Dlm-studio.server.primary-url="http://*************:1234" \
  > backend.log 2>&1 &

echo "✅ 修复完成，等待服务启动..."
sleep 15

# 验证配置
echo "6️⃣ 验证配置..."
curl -s "http://localhost:8181/api/ai/model-info" | jq '.data.url'

echo "🎉 完成！"