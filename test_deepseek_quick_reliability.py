#!/usr/bin/env python3
"""
deepseek模型快速可靠性测试
使用较短的提示词进行快速测试，评估基本可靠性
"""

import requests
import json
import time
from datetime import datetime
import os

def quick_reliability_test():
    """快速可靠性测试"""
    print("🚀 deepseek模型快速可靠性测试")
    print("=" * 70)
    
    # 基准数据
    baseline = {
        "model": "deepseek-r1-0528-qwen3-8b-mlx@8bit",
        "url": "http://192.168.1.231:1234",
        "baseline_time": 172.2,
        "baseline_score": 125
    }
    
    # 简化的测试提示词
    quick_prompt = """你是PostgreSQL数据库设计师。分析以下评估表设计数据库：

## 老年人日常生活能力评估
1. 进食能力：独立(1分)/需帮助(2分)/完全依赖(3分)
2. 洗澡能力：独立(1分)/需帮助(2分)/完全依赖(3分)  
3. 穿衣能力：独立(1分)/需帮助(2分)/完全依赖(3分)

要求输出：
1. 表名和用途
2. 完整CREATE TABLE语句
3. 字段说明JSON

必须包含：主键、created_at、updated_at字段。"""
    
    print(f"📊 测试配置:")
    print(f"   🎯 模型: {baseline['model']}")
    print(f"   🌐 地址: {baseline['url']}")
    print(f"   📝 提示词长度: {len(quick_prompt)} 字符（简化版）")
    print(f"   📊 基准处理时间: {baseline['baseline_time']}秒")
    
    results = []
    
    # 运行3次测试
    for test_round in range(1, 4):
        print(f"\n🔄 第{test_round}轮测试:")
        print("-" * 50)
        
        try:
            # 获取模型
            models_response = requests.get(f"{baseline['url']}/v1/models")
            if models_response.status_code != 200:
                print(f"❌ 无法连接LM Studio")
                continue
                
            models_data = models_response.json()
            target_model = None
            
            for model in models_data['data']:
                if 'deepseek' in model['id'].lower() and 'mlx' in model['id'].lower():
                    target_model = model['id']
                    break
            
            if not target_model:
                print(f"❌ 未找到目标模型")
                continue
            
            print(f"✅ 使用模型: {target_model}")
            
            # 发送请求
            request_body = {
                "model": target_model,
                "messages": [{"role": "user", "content": quick_prompt}],
                "stream": False
            }
            
            print("⏳ 发送请求...")
            start_time = time.time()
            
            response = requests.post(
                f"{baseline['url']}/v1/chat/completions",
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer lm-studio"
                },
                timeout=120  # 2分钟超时
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    ai_response = result['choices'][0]['message']['content']
                    
                    print(f"✅ 测试成功")
                    print(f"⏱️ 处理时间: {processing_time:.1f}秒")
                    print(f"📝 响应长度: {len(ai_response)} 字符")
                    
                    # 快速质量检查
                    has_create_table = 'CREATE TABLE' in ai_response.upper()
                    has_primary_key = 'PRIMARY KEY' in ai_response.upper()
                    has_timestamps = 'created_at' in ai_response.lower() and 'updated_at' in ai_response.lower()
                    has_json = '{' in ai_response and '}' in ai_response
                    has_assessment_terms = any(term in ai_response.lower() for term in ['eating', 'bathing', 'dressing', '进食', '洗澡', '穿衣'])
                    
                    quality_checks = [has_create_table, has_primary_key, has_timestamps, has_json, has_assessment_terms]
                    quality_score = sum(quality_checks) * 20  # 满分100
                    
                    print(f"📊 质量检查:")
                    print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌失败'}")
                    print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌失败'}")
                    print(f"   ✅ 时间戳字段: {'通过' if has_timestamps else '❌失败'}")
                    print(f"   ✅ JSON格式: {'通过' if has_json else '❌失败'}")
                    print(f"   ✅ 业务理解: {'通过' if has_assessment_terms else '❌失败'}")
                    print(f"🏆 质量评分: {quality_score}/100分")
                    
                    # 预览响应
                    preview_length = min(200, len(ai_response))
                    print(f"📄 响应预览:")
                    print(f"   {ai_response[:preview_length]}{'...' if len(ai_response) > preview_length else ''}")
                    
                    results.append({
                        "round": test_round,
                        "success": True,
                        "processing_time": processing_time,
                        "quality_score": quality_score,
                        "response_length": len(ai_response),
                        "response": ai_response
                    })
                    
                else:
                    print("❌ 响应格式异常")
                    results.append({"round": test_round, "success": False, "error": "响应格式异常"})
                    
            else:
                print(f"❌ API错误: {response.status_code}")
                results.append({"round": test_round, "success": False, "error": f"HTTP {response.status_code}"})
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
            results.append({"round": test_round, "success": False, "error": "超时"})
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append({"round": test_round, "success": False, "error": str(e)})
        
        time.sleep(2)  # 间隔2秒
    
    return analyze_quick_results(results, baseline)

def analyze_quick_results(results, baseline):
    """分析快速测试结果"""
    print(f"\n📊 快速可靠性分析")
    print("=" * 70)
    
    successful_tests = [r for r in results if r.get('success', False)]
    success_rate = len(successful_tests) / len(results) * 100
    
    print(f"📈 基础可靠性:")
    print(f"   🎯 成功率: {len(successful_tests)}/{len(results)} ({success_rate:.1f}%)")
    
    if not successful_tests:
        print("❌ 所有测试均失败，模型不可靠")
        return {"reliability": "不可靠", "grade": "F"}
    
    # 时间一致性分析
    times = [r['processing_time'] for r in successful_tests]
    avg_time = sum(times) / len(times)
    time_variance = max(times) - min(times)
    time_cv = (time_variance / avg_time * 100) if avg_time > 0 else 100
    
    print(f"\n⏱️ 时间一致性:")
    print(f"   📊 平均时间: {avg_time:.1f}秒")
    print(f"   📈 时间范围: {min(times):.1f}-{max(times):.1f}秒")
    print(f"   📉 时间变异: {time_variance:.1f}秒 ({time_cv:.1f}%)")
    
    # 质量一致性分析
    scores = [r['quality_score'] for r in successful_tests]
    avg_score = sum(scores) / len(scores)
    score_variance = max(scores) - min(scores)
    score_cv = (score_variance / avg_score * 100) if avg_score > 0 else 100
    
    print(f"\n🎯 质量一致性:")
    print(f"   📊 平均评分: {avg_score:.1f}/100分")
    print(f"   📈 评分范围: {min(scores)}-{max(scores)}分")
    print(f"   📉 评分变异: {score_variance}分 ({score_cv:.1f}%)")
    
    # 长度一致性分析
    lengths = [r['response_length'] for r in successful_tests]
    avg_length = sum(lengths) / len(lengths)
    length_variance = max(lengths) - min(lengths)
    length_cv = (length_variance / avg_length * 100) if avg_length > 0 else 100
    
    print(f"\n📝 长度一致性:")
    print(f"   📊 平均长度: {avg_length:.0f}字符")
    print(f"   📈 长度范围: {min(lengths)}-{max(lengths)}字符")
    print(f"   📉 长度变异: {length_variance} ({length_cv:.1f}%)")
    
    # 综合可靠性评估
    reliability_factors = []
    
    # 成功率评分
    if success_rate >= 100:
        reliability_factors.append(("成功率", "优秀", 4))
    elif success_rate >= 80:
        reliability_factors.append(("成功率", "良好", 3))
    elif success_rate >= 60:
        reliability_factors.append(("成功率", "一般", 2))
    else:
        reliability_factors.append(("成功率", "较差", 1))
    
    # 时间稳定性评分
    if time_cv <= 15:
        reliability_factors.append(("时间稳定性", "优秀", 4))
    elif time_cv <= 25:
        reliability_factors.append(("时间稳定性", "良好", 3))
    elif time_cv <= 40:
        reliability_factors.append(("时间稳定性", "一般", 2))
    else:
        reliability_factors.append(("时间稳定性", "较差", 1))
    
    # 质量稳定性评分
    if score_cv <= 10:
        reliability_factors.append(("质量稳定性", "优秀", 4))
    elif score_cv <= 20:
        reliability_factors.append(("质量稳定性", "良好", 3))
    elif score_cv <= 30:
        reliability_factors.append(("质量稳定性", "一般", 2))
    else:
        reliability_factors.append(("质量稳定性", "较差", 1))
    
    print(f"\n🎖️ 可靠性因子评估:")
    total_score = 0
    for factor, grade, score in reliability_factors:
        print(f"   {factor}: {grade} ({score}/4)")
        total_score += score
    
    overall_percentage = (total_score / (len(reliability_factors) * 4)) * 100
    
    # 最终等级
    if overall_percentage >= 90:
        final_grade = "A级 - 高可靠性"
        recommendation = "推荐生产使用"
    elif overall_percentage >= 75:
        final_grade = "B级 - 良好可靠性"
        recommendation = "适合生产使用"
    elif overall_percentage >= 60:
        final_grade = "C级 - 基本可靠性"
        recommendation = "谨慎使用，加强监控"
    elif overall_percentage >= 40:
        final_grade = "D级 - 较低可靠性"
        recommendation = "不推荐生产使用"
    else:
        final_grade = "F级 - 不可靠"
        recommendation = "需要调查问题"
    
    print(f"\n🏆 最终可靠性评估:")
    print(f"   📊 综合得分: {total_score}/{len(reliability_factors)*4} ({overall_percentage:.1f}%)")
    print(f"   🎯 可靠性等级: {final_grade}")
    print(f"   💡 使用建议: {recommendation}")
    
    # 与完整测试基准的对比
    if successful_tests:
        print(f"\n📈 与完整测试基准对比:")
        print(f"   ⏱️ 简化测试平均时间: {avg_time:.1f}秒")
        print(f"   ⏱️ 完整测试基准时间: {baseline['baseline_time']}秒")
        if avg_time < baseline['baseline_time']:
            speed_ratio = baseline['baseline_time'] / avg_time
            print(f"   🚀 简化测试快{speed_ratio:.1f}倍（符合预期）")
        
        print(f"   🎯 简化测试平均质量: {avg_score:.1f}/100分")
        print(f"   🎯 完整测试基准质量: {baseline['baseline_score']}/125分 (100%)")
        
        # 预期完整测试表现
        if overall_percentage >= 75:
            print(f"   📊 预期完整测试表现: 优秀")
            print(f"   ✅ 建议: 可以进行完整的可靠性测试验证")
        elif overall_percentage >= 60:
            print(f"   📊 预期完整测试表现: 良好")
            print(f"   ⚠️ 建议: 建议进行完整测试以确认")
        else:
            print(f"   📊 预期完整测试表现: 可能不稳定")
            print(f"   ❌ 建议: 检查模型和环境配置")
    
    # 保存结果
    save_quick_report(results, reliability_factors, final_grade, overall_percentage)
    
    return {
        "success_rate": success_rate,
        "avg_time": avg_time,
        "avg_score": avg_score,
        "time_cv": time_cv,
        "score_cv": score_cv,
        "final_grade": final_grade,
        "overall_percentage": overall_percentage,
        "recommendation": recommendation
    }

def save_quick_report(results, factors, grade, percentage):
    """保存快速测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"/Volumes/acasis/Assessment/test_results/deepseek_quick_reliability_{timestamp}.md"
    
    os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# deepseek模型快速可靠性测试报告\n\n")
        f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**测试方式**: 3轮简化测试\n")
        f.write(f"**最终等级**: {grade}\n")
        f.write(f"**综合得分**: {percentage:.1f}%\n\n")
        
        f.write("## 测试结果详情\n\n")
        for i, result in enumerate(results, 1):
            f.write(f"### 第{i}轮测试\n")
            if result.get('success'):
                f.write(f"- 状态: ✅ 成功\n")
                f.write(f"- 处理时间: {result['processing_time']:.1f}秒\n")
                f.write(f"- 质量评分: {result['quality_score']}/100分\n")
                f.write(f"- 响应长度: {result['response_length']}字符\n\n")
            else:
                f.write(f"- 状态: ❌ 失败\n")
                f.write(f"- 错误: {result.get('error', '未知')}\n\n")
        
        f.write("## 可靠性因子评估\n\n")
        for factor, grade_level, score in factors:
            f.write(f"- {factor}: {grade_level} ({score}/4)\n")
        
        f.write(f"\n## 结论\n\n")
        if percentage >= 75:
            f.write("✅ **高可靠性**: deepseek模型表现稳定，推荐使用\n")
        elif percentage >= 60:
            f.write("⚠️ **基本可靠性**: 模型基本稳定，建议监控\n")
        else:
            f.write("❌ **低可靠性**: 模型存在不稳定因素\n")
    
    print(f"\n📄 快速测试报告已保存: {report_file}")

if __name__ == "__main__":
    print("🎯 deepseek模型快速可靠性评估")
    print("=" * 70)
    print("📝 说明: 使用简化提示词进行3轮测试")
    print("⚡ 优势: 快速评估基本可靠性")
    print("🎯 目标: 为完整测试提供参考")
    print("=" * 70)
    
    # 运行测试
    analysis = quick_reliability_test()
    
    if analysis:
        print(f"\n🎉 快速可靠性测试完成!")
        print(f"🏆 可靠性等级: {analysis['final_grade']}")
        print(f"📊 综合得分: {analysis['overall_percentage']:.1f}%")
        print(f"💡 建议: {analysis['recommendation']}")
        
        if analysis['overall_percentage'] >= 75:
            print(f"\n✅ 推荐: 可以进行完整的3分钟可靠性测试")
            print(f"🎯 预期: 完整测试应该表现优秀")
        elif analysis['overall_percentage'] >= 60:
            print(f"\n⚠️ 建议: 可以尝试完整测试，但需要监控")
            print(f"🔍 关注: 注意完整测试中的稳定性表现")
        else:
            print(f"\n❌ 警告: 模型可靠性较低")
            print(f"🔧 建议: 检查环境配置或考虑其他模型")
    else:
        print(f"\n❌ 快速测试失败，请检查连接")