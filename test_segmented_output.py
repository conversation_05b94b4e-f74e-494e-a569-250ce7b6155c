#!/usr/bin/env python3
"""
分段输出测试 - 解决token限制问题
将数据库设计分成三个阶段：文档分析 → SQL设计 → JSON定义
"""

import requests
import json
import time
from datetime import datetime

def call_lm_studio(url, model, prompt, timeout=600):
    """调用LM Studio的通用函数"""
    request_body = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=timeout
        )
        
        print(f"   📡 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"   ✅ 响应长度: {len(content)} 字符")
                return content
            else:
                print(f"   ❌ 响应格式异常: {result}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            print(f"   📄 错误信息: {response.text[:500]}")
            
    except requests.exceptions.Timeout:
        print(f"   ⏰ 请求超时（{timeout}秒）")
    except Exception as e:
        print(f"   ❌ 调用异常: {e}")
        
    return None

def stage1_document_analysis(lm_studio_url, model, document):
    """第一阶段：文档分析"""
    prompt = f"""你是一个经验丰富的PostgreSQL数据库设计师。

## 任务
请分析以下文档，识别其类型和关键信息：

## 输出要求
请按以下格式输出文档分析结果：

```markdown
## 文档分析结果
- **文档类型**: {{评估量表/调查问卷/数据记录表/其他}}
- **主要内容**: {{文档核心内容概述}}
- **数据项目**: {{识别出的数据项目数量和类型}}
- **结构特征**: {{评分方式/记录格式/数据特征等}}
- **建议表名**: {{根据文档内容建议的主表名}}
- **核心字段**: {{列出5-10个最重要的字段}}
```

## 文档内容
{document}"""
    
    print("🔍 第一阶段：文档分析...")
    return call_lm_studio(lm_studio_url, model, prompt)

def stage2_sql_design(lm_studio_url, model, document_analysis, document):
    """第二阶段：SQL设计"""
    prompt = f"""你是一个经验丰富的PostgreSQL数据库设计师。

## 背景信息
基于之前的文档分析结果：
{document_analysis}

## 任务
请为文档设计完整的PostgreSQL数据库结构。

## 输出要求
请生成完整的SQL脚本，包含：

```sql
-- ==========================================
-- {{文档标题}} PostgreSQL数据库设计
-- ==========================================

-- 1. 创建主表
CREATE TABLE {{表名}} (
    id BIGSERIAL PRIMARY KEY,
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容生成的完整字段列表
    {{所有业务字段}},
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建索引
{{所有必要的索引}}

-- 3. 添加约束
{{所有CHECK约束和其他约束}}

-- 4. 创建触发器
{{自动更新时间戳的触发器}}

-- 5. 添加注释
{{表和字段的完整注释}}
```

## 设计原则
- SQL语法完全正确，可直接执行
- 字段类型选择合理
- 包含完整的约束条件
- 为经常查询的字段创建索引
- 符合PostgreSQL最佳实践

## 参考文档
{document[:2000]}...  # 只包含文档的前2000字符作为参考"""
    
    print("🗄️ 第二阶段：SQL设计...")
    return call_lm_studio(lm_studio_url, model, prompt, timeout=900)

def stage3_json_definition(lm_studio_url, model, document_analysis, sql_design):
    """第三阶段：JSON字段定义"""
    prompt = f"""你是一个经验丰富的PostgreSQL数据库设计师。

## 背景信息
文档分析结果：
{document_analysis[:500]}...

SQL设计结果：
{sql_design[:1000]}...

## 任务
请为数据库设计生成详细的JSON字段定义。

## 输出要求
```json
{{
  "database_design": {{
    "document_type": "{{文档类型}}",
    "table_name": "{{表名}}",
    "description": "{{表的用途说明}}",
    "total_fields": {{字段总数}},
    "fields": [
      {{
        "name": "{{字段名}}",
        "type": "{{PostgreSQL数据类型}}",
        "length": "{{长度(如适用)}}",
        "nullable": true/false,
        "default_value": "{{默认值}}",
        "comment": "{{字段说明}}",
        "constraints": ["{{约束说明}}"],
        "source": "{{来源于文档的哪个部分}}"
      }}
    ],
    "indexes": [
      {{
        "name": "{{索引名}}",
        "columns": ["{{字段列表}}"],
        "type": "btree/gin/gist",
        "purpose": "{{索引用途说明}}"
      }}
    ],
    "usage_recommendations": [
      "{{使用建议1}}",
      "{{使用建议2}}"
    ]
  }}
}}
```

## 要求
- JSON格式完全正确
- 包含所有字段的详细信息
- 提供实用的使用建议"""
    
    print("📋 第三阶段：JSON定义...")
    return call_lm_studio(lm_studio_url, model, prompt)

def test_segmented_approach():
    """测试分段输出方法"""
    print("🚀 分段输出测试 - 解决token限制")
    print("=" * 80)
    
    # 配置
    lm_studio_url = "http://192.168.1.223:1234"
    target_model = "qwen3-32b"  # 改用qwen3-32b模型
    
    # 读取文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        document = f.read()
    
    try:
        # 获取模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
        
        models_data = models_response.json()
        selected_model = None
        
        for model in models_data['data']:
            if target_model.lower() in model['id'].lower():
                selected_model = model['id']
                break
        
        if not selected_model:
            print(f"❌ 未找到{target_model}模型")
            return False
        
        print(f"✅ 选择模型: {selected_model}")
        print(f"📊 文档长度: {len(document)} 字符")
        print(f"🎯 分段策略: 三阶段输出")
        
        start_time = time.time()
        
        # 第一阶段：文档分析
        stage1_result = stage1_document_analysis(lm_studio_url, selected_model, document)
        if not stage1_result:
            print("❌ 第一阶段失败")
            return False
        
        stage1_time = time.time()
        print(f"✅ 第一阶段完成，耗时: {stage1_time - start_time:.1f}秒")
        
        # 第二阶段：SQL设计
        stage2_result = stage2_sql_design(lm_studio_url, selected_model, stage1_result, document)
        if not stage2_result:
            print("❌ 第二阶段失败")
            return False
        
        stage2_time = time.time()
        print(f"✅ 第二阶段完成，耗时: {stage2_time - stage1_time:.1f}秒")
        
        # 第三阶段：JSON定义
        stage3_result = stage3_json_definition(lm_studio_url, selected_model, stage1_result, stage2_result)
        if not stage3_result:
            print("❌ 第三阶段失败")
            return False
        
        end_time = time.time()
        print(f"✅ 第三阶段完成，耗时: {end_time - stage2_time:.1f}秒")
        
        total_time = end_time - start_time
        print(f"\n🎉 分段输出测试完成！")
        print(f"⏱️ 总处理时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
        
        # 合并结果
        combined_result = f"""# 分段输出完整结果

## 第一部分：文档分析
{stage1_result}

## 第二部分：SQL设计
{stage2_result}

## 第三部分：JSON定义
{stage3_result}
"""
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"/Volumes/acasis/Assessment/test_results/segmented_output_{timestamp}.md"
        
        import os
        os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
        
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write(f"# 分段输出测试结果\n\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**LM Studio地址**: {lm_studio_url}\n")
            f.write(f"**模型名称**: {selected_model}\n")
            f.write(f"**方法**: 分段输出（三阶段）\n")
            f.write(f"**总处理时间**: {total_time:.1f}秒\n")
            f.write(f"**文档长度**: {len(document)} 字符\n\n")
            f.write("---\n\n")
            f.write(combined_result)
        
        print(f"📄 完整结果已保存到: {result_file}")
        
        # 质量检查
        has_document_analysis = '文档类型' in stage1_result
        has_sql = 'CREATE TABLE' in stage2_result.upper()
        has_json = '"database_design"' in stage3_result
        
        print(f"\n📊 分段输出质量检查:")
        print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
        print(f"   ✅ SQL设计: {'通过' if has_sql else '❌未通过'}")
        print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
        
        # 时间分析
        print(f"\n⏱️ 分段时间分析:")
        print(f"   🔍 文档分析: {stage1_time - start_time:.1f}秒")
        print(f"   🗄️ SQL设计: {stage2_time - stage1_time:.1f}秒")
        print(f"   📋 JSON定义: {end_time - stage2_time:.1f}秒")
        
        # 显示预览
        print(f"\n📄 各阶段结果预览:")
        print("=" * 50)
        print("第一阶段预览:")
        print(stage1_result[:300] + "..." if len(stage1_result) > 300 else stage1_result)
        print("\n" + "=" * 50)
        print("第二阶段预览:")
        print(stage2_result[:300] + "..." if len(stage2_result) > 300 else stage2_result)
        print("\n" + "=" * 50)
        print("第三阶段预览:")
        print(stage3_result[:300] + "..." if len(stage3_result) > 300 else stage3_result)
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 分段输出方法测试")
    print("=" * 80)
    print("💡 解决方案: 将长文档的数据库设计分成三个阶段")
    print("📍 目标: 解决token限制，保持内容完整性")
    print("🎪 测试模型: qwq-32b")
    print("=" * 80)
    
    if test_segmented_approach():
        print(f"\n✅ 分段输出测试成功！")
        print(f"💡 这种方法可以处理任意长度的文档")
        print(f"🎯 每个阶段专注一个任务，质量更好")
    else:
        print(f"\n❌ 分段输出测试失败")