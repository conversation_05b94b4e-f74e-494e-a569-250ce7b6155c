#!/usr/bin/env python3
"""
使用简洁版提示词测试192.168.1.223:1234中的qwq-32b模型
对比不同模型在相同提示词下的表现
"""

import requests
import json
import time
from datetime import datetime

def test_simple_version_qwq32b():
    """使用简洁版提示词测试qwq-32b模型"""
    print("🚀 测试简洁版通用数据库设计提示词（qwq-32b模型）")
    print("=" * 80)
    
    # LM Studio配置
    lm_studio_url = "http://192.168.1.223:1234"
    target_model = "qwq-32b"
    
    # 简洁版提示词（已验证100分版本）
    simple_prompt = """你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为高质量的数据库设计。

## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能
- 添加主键约束
- 根据字段特征添加检查约束
- 为经常查询的字段创建索引
- 考虑数据的实际使用场景

## 输出格式

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
```

### 第二部分：完整SQL设计
```sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件
    {根据文档内容生成合适的CHECK约束}
);

-- 自动生成合适的索引
{根据字段特征和预期查询模式生成索引};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
```

### 第三部分：JSON字段定义
```json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
```

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行"""
    
    # 读取国标评估文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        national_standard_doc = f.read()
    
    # 完整提示词
    full_prompt = simple_prompt + "\n\n" + national_standard_doc

    try:
        print(f"🔍 连接到LM Studio: {lm_studio_url}")
        print(f"🎯 查找qwq-32b模型...")
        
        # 获取可用模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print(f"❌ 无法获取模型列表: {models_response.status_code}")
            return False
            
        models_data = models_response.json()
        selected_model = None
        
        print("📋 可用模型列表:")
        for model in models_data['data']:
            model_id = model['id']
            print(f"   - {model_id}")
            # 查找qwq-32b模型
            if target_model.lower() in model_id.lower():
                selected_model = model_id
                break
        
        if not selected_model:
            print(f"❌ 未找到qwq-32b模型")
            print("💡 请确保qwq-32b模型已在LM Studio中加载")
            return False
            
        print(f"\n✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送简洁版提示词到qwq-32b模型...")
        print(f"🤖 模型: {selected_model}")
        print(f"📊 提示词长度: {len(full_prompt)} 字符")
        print(f"🏆 提示词版本: 简洁版（已验证100分基准）")
        print(f"📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
        print(f"🔄 对比目标: 与deepseek-r1-0528-qwen3-8b-mlx@8bit结果对比")
        print("⏳ 等待qwq-32b模型生成结果...")
        
        start_time = time.time()
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=1800  # 30分钟超时
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 简洁版qwq-32b模型测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 使用与deepseek测试相同的检查标准
                has_document_analysis = '文档分析' in ai_response or '文档类型' in ai_response
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_json = '"database_design"' in ai_response and '"fields"' in ai_response
                has_constraints = 'CHECK' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                has_triggers = 'CREATE TRIGGER' in ai_response.upper()
                
                # 检查输出结构（三部分）
                has_three_parts = len([part for part in ['## 文档分析', 'CREATE TABLE', '"database_design"'] 
                                     if part in ai_response]) >= 2
                
                # 检查业务理解（国标特定）
                has_national_standard_fields = any(field in ai_response.lower() for field in 
                    ['assessment', 'elderly', 'ability', '评估', '老年人', '能力'])
                
                # 检查SQL质量
                has_primary_key = 'BIGSERIAL PRIMARY KEY' in ai_response.upper()
                has_audit_fields = all(field in ai_response for field in ['created_at', 'updated_at'])
                
                print(f"\n📊 简洁版qwq-32b模型质量检查:")
                print(f"🤖 测试模型: {selected_model}")
                print(f"📦 提示词版本: 简洁版（100分基准）")
                print(f"🖥️  LM Studio: {lm_studio_url}")
                
                print(f"\n1️⃣ 基础功能检查:")
                print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
                print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
                print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
                print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
                print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
                print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
                print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
                
                print(f"\n2️⃣ 结构完整性:")
                print(f"   📋 输出结构: {'通过' if has_three_parts else '❌未通过'}")
                print(f"   🏥 业务理解: {'通过' if has_national_standard_fields else '❌未通过'}")
                
                print(f"\n3️⃣ SQL质量:")
                print(f"   🔑 主键设计: {'通过' if has_primary_key else '❌未通过'}")
                print(f"   🕒 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
                
                # 保存结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/simple_version_qwq32b_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 简洁版提示词qwq-32b模型测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**LM Studio地址**: {lm_studio_url}\n")
                    f.write(f"**模型名称**: {selected_model}\n")
                    f.write(f"**提示词版本**: 简洁版（已验证100分基准）\n")
                    f.write(f"**处理时间**: {analysis_time:.1f}秒\n")
                    f.write(f"**测试文档**: 老年人能力评估报告（GB/T42195-2022）\n")
                    f.write(f"**对比基准**: deepseek-r1-0528-qwen3-8b-mlx@8bit (125/125分)\n\n")
                    f.write("---\n\n")
                    f.write("## qwq-32b模型生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: {result_file}")
                
                # 计算质量评分（与deepseek基准对比）
                basic_score = 0
                if has_document_analysis: basic_score += 15
                if has_create_table: basic_score += 20
                if has_json: basic_score += 20
                if has_constraints: basic_score += 15
                if has_indexes: basic_score += 10
                if has_comments: basic_score += 10
                if has_triggers: basic_score += 10
                
                structure_score = 0
                if has_three_parts: structure_score += 15
                if has_national_standard_fields: structure_score += 10
                
                sql_quality_score = 0
                if has_primary_key: sql_quality_score += 10
                if has_audit_fields: sql_quality_score += 10
                
                total_score = basic_score + structure_score + sql_quality_score
                
                print(f"\n🏆 qwq-32b模型评分:")
                print(f"   📋 基础功能: {basic_score}/100分")
                print(f"   📊 结构完整性: {structure_score}/25分")
                print(f"   🗄️ SQL质量: {sql_quality_score}/20分")
                print(f"   📈 总分: {total_score}/145分 ({total_score/145*100:.1f}%)")
                
                # 与deepseek基准对比（125/125 = 100%）
                deepseek_baseline = 125
                performance_ratio = (total_score / 145) * 100
                vs_deepseek = (total_score / 145) / (deepseek_baseline / 125) * 100
                
                if performance_ratio >= 90:
                    grade = "A级 - 优秀表现"
                elif performance_ratio >= 80:
                    grade = "B级 - 良好表现"
                elif performance_ratio >= 70:
                    grade = "C级 - 基础通过"
                elif performance_ratio >= 60:
                    grade = "D级 - 需要改进"
                else:
                    grade = "F级 - 表现不佳"
                
                print(f"\n🎯 最终评级: {grade}")
                print(f"📊 绝对性能: {performance_ratio:.1f}%")
                print(f"🆚 对比deepseek基准: {vs_deepseek:.1f}%")
                print(f"🔄 模型对比: qwq-32b vs deepseek-r1-mlx@8bit")
                
                # 性能对比总结
                deepseek_time = 172.2  # deepseek测试的处理时间
                speed_comparison = analysis_time / deepseek_time
                
                print(f"\n📈 性能对比总结:")
                print(f"   ⏱️ 处理时间: {analysis_time:.1f}s vs {deepseek_time}s (qwq-32b {'慢' if speed_comparison > 1 else '快'}{abs(speed_comparison-1)*100:.1f}%)")
                print(f"   📝 响应长度: {len(ai_response)} vs 3331字符")
                print(f"   📊 质量得分: {total_score}/145 vs 125/125")
                
                # 显示部分响应预览
                print(f"\n📄 响应预览（前800字符）:")
                print("=" * 80)
                print(ai_response[:800] + "..." if len(ai_response) > 800 else ai_response)
                print("=" * 80)
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ AI分析超时（超过30分钟）")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到LM Studio: {lm_studio_url}")
        print("💡 请检查:")
        print("   1. LM Studio是否在192.168.1.223上运行")
        print("   2. 端口1234是否正确")
        print("   3. 防火墙设置是否允许连接")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🏆 简洁版提示词qwq-32b模型测试")
    print("=" * 80)
    print("📍 LM Studio地址: http://192.168.1.223:1234")
    print("🎯 目标模型: qwq-32b (32B参数大模型)")
    print("📦 提示词版本: 简洁版（已验证100分）")
    print("📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
    print("🔍 测试目标: 对比qwq-32b与deepseek模型的表现差异")
    print("=" * 80)
    
    if test_simple_version_qwq32b():
        print(f"\n✅ 简洁版qwq-32b模型测试成功完成!")
        print(f"📊 请查看详细测试结果文件")
        print(f"🔄 可与deepseek基准结果对比分析")
    else:
        print(f"\n❌ 测试失败，请检查LM Studio连接和模型加载")

if __name__ == "__main__":
    main()