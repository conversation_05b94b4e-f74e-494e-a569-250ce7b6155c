#!/usr/bin/env python3
"""
使用终极实用版提示词测试qwq-32b模型
专注于SQL质量和可执行性，移除不必要的JSON输出要求
"""

import requests
import json
import time
from datetime import datetime

def test_ultimate_practical_version():
    """测试终极实用版提示词"""
    print("🚀 测试终极实用版通用数据库设计提示词")
    print("=" * 80)
    
    # LM Studio配置
    lm_studio_url = "http://192.168.1.223:1234"
    target_model = "qwq-32b"
    
    # 终极实用版提示词（移除JSON要求）
    ultimate_practical_prompt = """你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为可直接执行的数据库SQL脚本。

## 核心任务
分析以下文档内容，生成完整的、可直接在PostgreSQL中执行的数据库创建脚本。

## 设计要求

### 1. 文档理解
- 准确识别文档类型和业务用途
- 提取所有数据项并映射为数据库字段
- 理解数据之间的关系和业务规则

### 2. SQL设计规范
- 所有标识符使用snake_case命名（表名、字段名、约束名、索引名）
- 选择最合适的PostgreSQL数据类型
- 添加完整的约束条件保证数据完整性
- 设计高效的索引策略

### 3. 企业级必需元素
- 主键：id BIGSERIAL PRIMARY KEY
- 业务标识：合适的业务唯一标识字段
- 审计字段：created_at, updated_at, created_by, updated_by
- 版本控制：version INTEGER DEFAULT 1
- 软删除：is_deleted BOOLEAN DEFAULT FALSE, deleted_at TIMESTAMP
- 数据完整性：NOT NULL, CHECK, UNIQUE等约束
- 性能优化：单列索引和复合索引
- 自动化：触发器和函数

## 输出要求

### 第一部分：简要分析（2-3句话）
- 识别的文档类型
- 主要数据结构
- 设计思路

### 第二部分：完整可执行的SQL脚本（重点）
```sql
-- ==========================================
-- 数据库设计：{文档标题}
-- 生成时间：{当前时间}
-- 设计原则：企业级、可扩展、高性能
-- ==========================================

-- 1. 创建主表
CREATE TABLE {表名} (
    -- 系统字段
    id BIGSERIAL PRIMARY KEY,
    record_id VARCHAR(50) UNIQUE NOT NULL DEFAULT 'REC_' || nextval('record_seq'),
    
    -- 业务字段（完整映射文档中的所有数据项）
    {完整的业务字段定义，确保覆盖文档中的每个数据项},
    
    -- 企业级审计字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER,
    version INTEGER DEFAULT 1,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP,
    
    -- 数据质量字段
    data_quality_score INTEGER CHECK (data_quality_score BETWEEN 0 AND 100),
    validation_errors JSONB
);

-- 2. 创建序列（如需要）
CREATE SEQUENCE IF NOT EXISTS record_seq START 1;

-- 3. 创建索引策略
-- 单列索引（高频查询字段）
CREATE INDEX idx_{表名}_record_id ON {表名}(record_id);
CREATE INDEX idx_{表名}_created_at ON {表名}(created_at DESC);
CREATE INDEX idx_{表名}_status ON {表名}(status) WHERE is_deleted = FALSE;

-- 复合索引（多字段组合查询）
CREATE INDEX idx_{表名}_active_records ON {表名}(is_deleted, status, created_at DESC);
{其他基于业务场景的复合索引}

-- 部分索引（优化特定查询）
CREATE INDEX idx_{表名}_deleted ON {表名}(deleted_at) WHERE is_deleted = TRUE;

-- 4. 添加约束
{基于文档内容的具体业务约束}

-- 5. 创建触发器
CREATE OR REPLACE FUNCTION update_audit_fields()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_{表名}_audit
BEFORE UPDATE ON {表名}
FOR EACH ROW
EXECUTE FUNCTION update_audit_fields();

-- 软删除触发器
CREATE OR REPLACE FUNCTION soft_delete_record()
RETURNS TRIGGER AS $$
BEGIN
    NEW.is_deleted = TRUE;
    NEW.deleted_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. 添加完整注释
COMMENT ON TABLE {表名} IS '{详细的表用途说明，包括业务背景}';
{为每个重要字段添加注释，说明其业务含义和数据来源}

-- 7. 创建相关表（如果文档涉及多实体）
{如果需要，创建关联表和外键关系}

-- 8. 创建视图（便于查询）
CREATE VIEW v_{表名}_active AS
SELECT * FROM {表名} WHERE is_deleted = FALSE;

-- 9. 示例查询（验证设计）
-- SELECT * FROM {表名} WHERE status = 'active' ORDER BY created_at DESC LIMIT 10;
```

### 第三部分：使用说明（简洁实用）
- 表的主要用途和核心功能
- 重要的业务规则和约束
- 查询优化建议和索引使用
- 数据维护和清理策略

## 质量要求
✅ SQL语法100%正确，可直接复制执行
✅ 完整覆盖文档中的所有数据项
✅ 数据类型选择合理且高效
✅ 包含企业级特性（审计、版本、软删除）
✅ 索引设计全面（单列、复合、部分索引）
✅ 代码格式清晰，注释完整
✅ 可扩展性好，支持未来业务增长

## 重要原则
- 实用性优先：生成的SQL必须可以直接使用
- 企业级标准：包含审计、版本控制、软删除等特性
- 性能考虑：合理的索引策略和查询优化
- 可维护性：清晰的注释和规范的命名"""
    
    # 读取国标评估文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        national_standard_doc = f.read()
    
    # 完整提示词
    full_prompt = ultimate_practical_prompt + "\n\n" + national_standard_doc

    try:
        print(f"🔍 连接到LM Studio: {lm_studio_url}")
        print(f"🎯 查找{target_model}模型...")
        
        # 获取可用模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print(f"❌ 无法获取模型列表: {models_response.status_code}")
            return False
            
        models_data = models_response.json()
        selected_model = None
        
        for model in models_data['data']:
            if target_model.lower() in model['id'].lower():
                selected_model = model['id']
                break
        
        if not selected_model:
            print(f"❌ 未找到{target_model}模型")
            return False
            
        print(f"✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送终极实用版提示词...")
        print(f"🤖 模型: {selected_model}")
        print(f"📊 提示词长度: {len(full_prompt)} 字符")
        print(f"🎯 核心目标: 生成可直接执行的企业级SQL脚本")
        print(f"💡 关键改进: 移除JSON要求，专注SQL质量")
        print("⏳ 等待AI生成企业级SQL脚本...")
        
        start_time = time.time()
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=1800
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 终极实用版测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 企业级SQL质量检查
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_primary_key = 'BIGSERIAL PRIMARY KEY' in ai_response.upper()
                has_snake_case = all(pattern in ai_response for pattern in ['_id', '_at', '_by'])
                has_constraints = 'CHECK' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_triggers = 'CREATE TRIGGER' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                
                # 企业级特性检查
                has_audit_fields = all(field in ai_response for field in ['created_at', 'updated_at', 'created_by', 'updated_by'])
                has_version_control = 'version' in ai_response.lower()
                has_soft_delete = all(field in ai_response for field in ['is_deleted', 'deleted_at'])
                has_data_quality = 'data_quality' in ai_response.lower() or 'validation_errors' in ai_response.lower()
                
                # 高级特性检查
                has_composite_indexes = 'CREATE INDEX' in ai_response.upper() and ',' in ai_response
                has_partial_indexes = 'WHERE' in ai_response and 'CREATE INDEX' in ai_response.upper()
                has_sequences = 'CREATE SEQUENCE' in ai_response.upper()
                has_views = 'CREATE VIEW' in ai_response.upper()
                
                # 检查是否移除了JSON（应该没有）
                has_json_output = '"database_design"' in ai_response or '"fields"' in ai_response
                
                print(f"\n📊 企业级SQL质量检查:")
                print(f"🔧 模型名称: {selected_model}")
                print(f"📦 提示词版本: 终极实用版（无JSON）")
                
                print(f"\n1️⃣ 基础SQL质量:")
                print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
                print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌未通过'}")
                print(f"   ✅ snake_case命名: {'通过' if has_snake_case else '❌未通过'}")
                print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
                print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
                print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
                print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
                
                print(f"\n2️⃣ 企业级特性:")
                print(f"   🏢 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
                print(f"   🏢 版本控制: {'通过' if has_version_control else '❌未通过'}")
                print(f"   🏢 软删除: {'通过' if has_soft_delete else '❌未通过'}")
                print(f"   🏢 数据质量: {'通过' if has_data_quality else '❌未通过'}")
                
                print(f"\n3️⃣ 高级特性:")
                print(f"   🚀 复合索引: {'通过' if has_composite_indexes else '❌未通过'}")
                print(f"   🚀 部分索引: {'通过' if has_partial_indexes else '❌未通过'}")
                print(f"   🚀 序列设计: {'通过' if has_sequences else '❌未通过'}")
                print(f"   🚀 视图创建: {'通过' if has_views else '❌未通过'}")
                
                print(f"\n4️⃣ 格式优化:")
                print(f"   ✅ 移除JSON输出: {'成功' if not has_json_output else '❌仍有JSON'}")
                
                # 保存结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/ultimate_practical_test_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 终极实用版提示词测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**LM Studio地址**: {lm_studio_url}\n")
                    f.write(f"**模型名称**: {selected_model}\n")
                    f.write(f"**提示词版本**: 终极实用版（专注SQL，移除JSON）\n")
                    f.write(f"**核心改进**: 去除冗余JSON要求，专注企业级SQL质量\n")
                    f.write(f"**处理时间**: {analysis_time:.1f}秒\n\n")
                    f.write("---\n\n")
                    f.write("## AI生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: {result_file}")
                
                # 计算综合评分
                basic_score = sum([has_create_table, has_primary_key, has_snake_case, has_constraints, 
                                 has_indexes, has_triggers, has_comments]) * 10
                enterprise_score = sum([has_audit_fields, has_version_control, has_soft_delete, 
                                      has_data_quality]) * 7.5
                advanced_score = sum([has_composite_indexes, has_partial_indexes, has_sequences, 
                                    has_views]) * 5
                
                total_score = basic_score + enterprise_score + advanced_score
                
                print(f"\n🏆 终极实用版评分:")
                print(f"   📋 基础SQL质量: {basic_score}/70分")
                print(f"   🏢 企业级特性: {enterprise_score}/30分")
                print(f"   🚀 高级特性: {advanced_score}/20分")
                print(f"   📊 总分: {total_score}/120分 ({total_score/120*100:.1f}%)")
                
                if total_score >= 108:
                    grade = "S级 - 企业级完美"
                elif total_score >= 96:
                    grade = "A级 - 生产就绪"
                elif total_score >= 84:
                    grade = "B级 - 优秀"
                elif total_score >= 72:
                    grade = "C级 - 良好"
                else:
                    grade = "D级 - 需改进"
                
                print(f"\n🎯 最终评级: {grade}")
                print(f"💡 核心改进: {'✅ 成功移除JSON冗余' if not has_json_output else '⚠️ 仍有JSON输出'}")
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🎯 终极实用版提示词测试")
    print("=" * 80)
    print("💡 核心理念: 实用至上，专注SQL质量")
    print("🔄 主要改进: 移除JSON要求，增强企业级特性")
    print("🎯 测试目标: 验证可执行SQL的企业级质量")
    print("=" * 80)
    
    if test_ultimate_practical_version():
        print(f"\n✅ 终极实用版测试完成!")
        print(f"🎯 专注生成高质量、可执行的企业级SQL脚本")
    else:
        print(f"\n❌ 测试失败")