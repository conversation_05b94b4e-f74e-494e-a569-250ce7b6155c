# 智慧养老评估平台 (Smart Elderly Assessment Platform)

## 项目简介

智慧养老评估平台是一个面向养老机构、社区服务中心、医疗机构的专业老年人综合能力评估数字化平台。通过移动端应用和Web管理后台，实现老年人能力的标准化评估和智能化分析。

## 技术栈

### 后端
- Java 17 + Spring Boot 3.x
- PostgreSQL 15 + Redis 7
- MinIO (文件存储)
- Maven (构建工具)

### 前端
- uni-app (移动端/小程序)
- Vue 3 + TypeScript (管理后台)
- Element Plus (UI组件库)

### 部署
- Docker + Docker Compose
- Nginx (反向代理)

## 项目结构

```
Assessment/
├── backend/                 # 后端Java项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/       # Java源代码
│   │   │   └── resources/  # 配置文件
│   │   └── test/           # 测试代码
│   └── pom.xml
├── frontend/               # 前端项目
│   ├── uni-app/           # 移动端/小程序
│   └── admin/             # 管理后台
├── docker/                # Docker相关文件
├── config/                # 配置文件
├── scripts/               # 脚本文件
├── docs/                  # 文档
└── docker-compose.yml     # Docker编排文件
```

## 环境准备

### 1. 创建Conda环境

```bash
# 创建环境
conda env create -f environment.yml

# 激活环境
conda activate Assessment
```

### 2. 安装依赖

#### 后端依赖
```bash
cd backend
mvn clean install
```

#### 前端依赖
```bash
# uni-app
cd frontend/uni-app
npm install

# 管理后台
cd ../admin
npm install
```

### 3. 启动基础服务

```bash
# 复制环境变量文件
cp .env.example .env

# 启动Docker服务
docker-compose up -d

# 检查服务状态
docker-compose ps
```

## 开发指南

### 后端开发

```bash
# 进入后端目录
cd backend

# 运行开发服务器
mvn spring-boot:run
```

访问地址：
- API: http://localhost:8080
- Swagger文档: http://localhost:8080/swagger-ui.html

### 前端开发

#### uni-app开发
```bash
cd frontend/uni-app

# H5开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# App开发
npm run dev:app
```

#### 管理后台开发
```bash
cd frontend/admin

# 启动开发服务器
npm run dev
```

访问地址: http://localhost:5273

## 部署说明

### 本地部署

1. 构建后端
```bash
cd backend
mvn clean package
```

2. 构建前端
```bash
# uni-app
cd frontend/uni-app
npm run build:h5

# 管理后台
cd ../admin
npm run build
```

3. 使用Docker Compose部署
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 开发规范

### 代码规范
- 后端：遵循阿里巴巴Java开发规范
- 前端：ESLint + Prettier

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式化
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 核心功能

1. **多量表支持**
   - 老年人能力评估
   - 情绪快评
   - interRAI评估
   - 长护险评估

2. **移动端优化**
   - 离线评估支持
   - 语音输入
   - 大字体适老化设计

3. **智能分析**
   - AI评估建议
   - 数据趋势分析
   - 风险预警

4. **数据安全**
   - 端到端加密
   - 角色权限控制
   - 审计日志

## 相关文档

- [产品需求文档](docs/plan/PRD.md)
- [技术架构文档](docs/plan/Local_Deployment_Architecture.md)
- [API文档](http://localhost:8080/swagger-ui.html)

## 许可证

本项目采用私有许可证，未经授权不得使用。

## 联系方式

- 技术支持：<EMAIL>
- 产品咨询：<EMAIL>
