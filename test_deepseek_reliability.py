#!/usr/bin/env python3
"""
deepseek-r1-0528-qwen3-8b-mlx@8bit 可靠性测试
与之前的测试结果进行详细对比，评估模型的一致性和可靠性
"""

import requests
import json
import time
from datetime import datetime
import os

def test_deepseek_reliability():
    """测试deepseek模型的可靠性"""
    print("🎯 deepseek模型可靠性测试")
    print("=" * 80)
    
    # 基准测试数据（来自之前的测试结果）
    baseline_data = {
        "test_time": "2025年6月17日 22:46:07",
        "model": "deepseek-r1-0528-qwen3-8b-mlx@8bit",
        "processing_time": 172.2,  # 秒
        "quality_score": 125,  # 总分125
        "response_length": 3331,  # 字符
        "lm_studio_url": "http://192.168.1.231:1234"
    }
    
    print(f"📊 基准测试数据:")
    print(f"   🕒 测试时间: {baseline_data['test_time']}")
    print(f"   🤖 模型: {baseline_data['model']}")
    print(f"   ⏱️ 处理时间: {baseline_data['processing_time']}秒")
    print(f"   🏆 质量评分: {baseline_data['quality_score']}/125分")
    print(f"   📝 响应长度: {baseline_data['response_length']}字符")
    
    # 使用相同的简洁版提示词
    simple_prompt = """你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为高质量的数据库设计。

## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能
- 添加主键约束
- 根据字段特征添加检查约束
- 为经常查询的字段创建索引
- 考虑数据的实际使用场景

## 输出格式

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
```

### 第二部分：完整SQL设计
```sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件
    {根据文档内容生成合适的CHECK约束}
);

-- 自动生成合适的索引
{根据字段特征和预期查询模式生成索引};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
```

### 第三部分：JSON字段定义
```json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
```

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行"""
    
    # 读取相同的测试文档
    print(f"\n📄 读取测试文档...")
    try:
        with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
            national_standard_doc = f.read()
        print(f"✅ 文档读取成功，长度: {len(national_standard_doc)} 字符")
    except FileNotFoundError:
        print("❌ 未找到测试文档，测试终止")
        return False
    
    # 组合完整提示词
    full_prompt = simple_prompt + "\n\n" + national_standard_doc
    
    print(f"📊 当前测试配置:")
    print(f"   🌐 LM Studio: {baseline_data['lm_studio_url']}")
    print(f"   🎯 目标模型: {baseline_data['model']}")
    print(f"   📝 提示词长度: {len(full_prompt)} 字符")
    print(f"   📄 文档长度: {len(national_standard_doc)} 字符")
    
    try:
        print(f"\n🔍 连接到LM Studio...")
        
        # 获取可用模型
        models_response = requests.get(f"{baseline_data['lm_studio_url']}/v1/models")
        if models_response.status_code != 200:
            print(f"❌ 无法连接到LM Studio: {models_response.status_code}")
            return False
            
        models_data = models_response.json()
        selected_model = None
        
        print("📋 可用模型列表:")
        for model in models_data['data']:
            model_id = model['id']
            print(f"   - {model_id}")
            # 查找deepseek模型
            if 'deepseek' in model_id.lower() and 'mlx' in model_id.lower():
                selected_model = model_id
                break
        
        if not selected_model:
            print(f"❌ 未找到目标模型: {baseline_data['model']}")
            return False
            
        print(f"\n✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送测试请求...")
        print(f"⏳ 开始计时，与基准{baseline_data['processing_time']:.1f}秒对比...")
        
        start_time = time.time()
        
        response = requests.post(
            f"{baseline_data['lm_studio_url']}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=600  # 10分钟超时
        )
        
        end_time = time.time()
        current_processing_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 测试完成!")
                print(f"⏱️ 当前处理时间: {current_processing_time:.1f}秒")
                print(f"📝 当前响应长度: {len(ai_response)} 字符")
                
                # 详细的可靠性分析
                reliability_analysis = analyze_reliability(ai_response, current_processing_time, baseline_data)
                
                # 保存详细对比报告
                save_reliability_report(ai_response, current_processing_time, baseline_data, reliability_analysis)
                
                return reliability_analysis
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ 请求超时（超过10分钟）")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_reliability(current_response, current_time, baseline_data):
    """分析可靠性和一致性"""
    print(f"\n📊 可靠性分析")
    print("=" * 80)
    
    # 基础质量检查（与基准测试相同的标准）
    has_document_analysis = '文档分析' in current_response or '文档类型' in current_response
    has_create_table = 'CREATE TABLE' in current_response.upper()
    has_json = '"database_design"' in current_response and '"fields"' in current_response
    has_constraints = 'CHECK' in current_response.upper()
    has_indexes = 'CREATE INDEX' in current_response.upper()
    has_comments = 'COMMENT ON' in current_response.upper()
    has_triggers = 'CREATE TRIGGER' in current_response.upper()
    has_three_parts = len([part for part in ['## 文档分析', 'CREATE TABLE', '"database_design"'] 
                         if part in current_response]) >= 2
    has_national_standard_fields = any(field in current_response.lower() for field in 
        ['assessment', 'elderly', 'ability', '评估', '老年人', '能力'])
    has_primary_key = 'BIGSERIAL PRIMARY KEY' in current_response.upper()
    has_audit_fields = all(field in current_response for field in ['created_at', 'updated_at'])
    
    # 计算当前质量评分（使用与基准相同的评分标准）
    basic_score = 0
    if has_document_analysis: basic_score += 15
    if has_create_table: basic_score += 20
    if has_json: basic_score += 20
    if has_constraints: basic_score += 15
    if has_indexes: basic_score += 10
    if has_comments: basic_score += 10
    if has_triggers: basic_score += 10
    
    structure_score = 0
    if has_three_parts: structure_score += 15
    if has_national_standard_fields: structure_score += 10
    
    sql_quality_score = 0
    if has_primary_key: sql_quality_score += 10
    if has_audit_fields: sql_quality_score += 10
    
    current_total_score = basic_score + structure_score + sql_quality_score
    
    print(f"🔍 当前测试质量检查:")
    print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
    print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
    print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
    print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
    print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
    print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
    print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
    print(f"   ✅ 输出结构: {'通过' if has_three_parts else '❌未通过'}")
    print(f"   ✅ 业务理解: {'通过' if has_national_standard_fields else '❌未通过'}")
    print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌未通过'}")
    print(f"   ✅ 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
    
    print(f"\n🏆 当前质量评分: {current_total_score}/125分")
    
    # 可靠性对比分析
    print(f"\n📈 可靠性对比分析:")
    print("=" * 60)
    
    # 时间一致性
    time_diff = abs(current_time - baseline_data['processing_time'])
    time_variation = time_diff / baseline_data['processing_time'] * 100
    
    print(f"⏱️ 处理时间对比:")
    print(f"   基准时间: {baseline_data['processing_time']:.1f}秒")
    print(f"   当前时间: {current_time:.1f}秒")
    print(f"   时间差异: {time_diff:.1f}秒 ({time_variation:.1f}%)")
    
    if time_variation <= 10:
        time_consistency = "优秀"
    elif time_variation <= 20:
        time_consistency = "良好"
    elif time_variation <= 30:
        time_consistency = "一般"
    else:
        time_consistency = "较差"
    
    print(f"   时间一致性: {time_consistency}")
    
    # 质量一致性
    quality_diff = abs(current_total_score - baseline_data['quality_score'])
    quality_variation = quality_diff / baseline_data['quality_score'] * 100
    
    print(f"\n🎯 质量评分对比:")
    print(f"   基准评分: {baseline_data['quality_score']}/125分")
    print(f"   当前评分: {current_total_score}/125分")
    print(f"   评分差异: {quality_diff}分 ({quality_variation:.1f}%)")
    
    if quality_variation <= 5:
        quality_consistency = "优秀"
    elif quality_variation <= 10:
        quality_consistency = "良好"
    elif quality_variation <= 15:
        quality_consistency = "一般"
    else:
        quality_consistency = "较差"
    
    print(f"   质量一致性: {quality_consistency}")
    
    # 输出长度对比
    length_diff = abs(len(current_response) - baseline_data['response_length'])
    length_variation = length_diff / baseline_data['response_length'] * 100
    
    print(f"\n📝 响应长度对比:")
    print(f"   基准长度: {baseline_data['response_length']} 字符")
    print(f"   当前长度: {len(current_response)} 字符")
    print(f"   长度差异: {length_diff} 字符 ({length_variation:.1f}%)")
    
    if length_variation <= 15:
        length_consistency = "优秀"
    elif length_variation <= 25:
        length_consistency = "良好"
    elif length_variation <= 40:
        length_consistency = "一般"
    else:
        length_consistency = "较差"
    
    print(f"   长度一致性: {length_consistency}")
    
    # 综合可靠性评估
    consistency_scores = {
        "time": time_consistency,
        "quality": quality_consistency,
        "length": length_consistency
    }
    
    consistency_weights = {"优秀": 4, "良好": 3, "一般": 2, "较差": 1}
    overall_score = sum(consistency_weights[score] for score in consistency_scores.values())
    overall_max = len(consistency_scores) * 4
    
    overall_percentage = (overall_score / overall_max) * 100
    
    print(f"\n🎖️ 综合可靠性评估:")
    if overall_percentage >= 90:
        reliability_grade = "A级 - 极高可靠性"
        reliability_desc = "模型表现极其稳定，可放心投入生产使用"
    elif overall_percentage >= 80:
        reliability_grade = "B级 - 高可靠性"
        reliability_desc = "模型表现稳定，适合生产环境"
    elif overall_percentage >= 70:
        reliability_grade = "C级 - 中等可靠性"
        reliability_desc = "模型基本稳定，建议加强监控"
    elif overall_percentage >= 60:
        reliability_grade = "D级 - 较低可靠性"
        reliability_desc = "模型存在不稳定因素，需要改进"
    else:
        reliability_grade = "F级 - 不可靠"
        reliability_desc = "模型表现不稳定，不推荐生产使用"
    
    print(f"   📊 综合得分: {overall_score}/{overall_max} ({overall_percentage:.1f}%)")
    print(f"   🏆 可靠性等级: {reliability_grade}")
    print(f"   💡 建议: {reliability_desc}")
    
    # 显示响应内容预览
    print(f"\n📄 当前响应预览（前800字符）:")
    print("=" * 80)
    print(current_response[:800] + "..." if len(current_response) > 800 else current_response)
    print("=" * 80)
    
    return {
        "current_time": current_time,
        "current_score": current_total_score,
        "current_length": len(current_response),
        "time_consistency": time_consistency,
        "quality_consistency": quality_consistency,
        "length_consistency": length_consistency,
        "overall_grade": reliability_grade,
        "overall_percentage": overall_percentage,
        "response": current_response
    }

def save_reliability_report(current_response, current_time, baseline_data, analysis):
    """保存可靠性测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"/Volumes/acasis/Assessment/test_results/deepseek_reliability_test_{timestamp}.md"
    
    os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# deepseek模型可靠性测试报告\n\n")
        f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**测试目标**: 评估deepseek-r1-0528-qwen3-8b-mlx@8bit的一致性和可靠性\n")
        f.write(f"**基准测试**: {baseline_data['test_time']}\n\n")
        
        f.write("## 对比数据摘要\n\n")
        f.write("| 指标 | 基准测试 | 当前测试 | 差异 | 一致性 |\n")
        f.write("|------|----------|----------|------|--------|\n")
        f.write(f"| 处理时间 | {baseline_data['processing_time']:.1f}秒 | {current_time:.1f}秒 | {abs(current_time-baseline_data['processing_time']):.1f}秒 | {analysis['time_consistency']} |\n")
        f.write(f"| 质量评分 | {baseline_data['quality_score']}/125 | {analysis['current_score']}/125 | {abs(analysis['current_score']-baseline_data['quality_score'])} | {analysis['quality_consistency']} |\n")
        f.write(f"| 响应长度 | {baseline_data['response_length']} | {analysis['current_length']} | {abs(analysis['current_length']-baseline_data['response_length'])} | {analysis['length_consistency']} |\n\n")
        
        f.write(f"## 综合可靠性评估\n\n")
        f.write(f"**等级**: {analysis['overall_grade']}\n")
        f.write(f"**得分**: {analysis['overall_percentage']:.1f}%\n\n")
        
        f.write("## 当前测试完整响应\n\n")
        f.write("```\n")
        f.write(current_response)
        f.write("\n```\n\n")
        
        f.write("## 结论\n\n")
        if analysis['overall_percentage'] >= 80:
            f.write("✅ **高可靠性**: deepseek模型表现稳定，推荐继续使用\n")
        elif analysis['overall_percentage'] >= 60:
            f.write("⚠️ **中等可靠性**: 模型基本稳定，建议加强监控\n")
        else:
            f.write("❌ **低可靠性**: 模型存在不稳定因素，需要进一步调查\n")
    
    print(f"\n📄 详细报告已保存: {report_file}")
    return report_file

if __name__ == "__main__":
    print("🎯 deepseek模型可靠性评估")
    print("=" * 80)
    print("📝 目标: 测试模型在相同条件下的一致性表现")
    print("🔄 对比: 与2025年6月17日基准测试结果对比")
    print("📊 评估: 时间、质量、长度三个维度的稳定性")
    print("=" * 80)
    
    # 运行可靠性测试
    analysis_result = test_deepseek_reliability()
    
    if analysis_result:
        print(f"\n🎉 可靠性测试完成!")
        print(f"🏆 最终等级: {analysis_result['overall_grade']}")
        print(f"📊 综合得分: {analysis_result['overall_percentage']:.1f}%")
        
        if analysis_result['overall_percentage'] >= 80:
            print("✅ 结论: deepseek模型具有高可靠性，推荐生产使用")
            print("💡 建议: 可以放心部署，定期监控即可")
        elif analysis_result['overall_percentage'] >= 60:
            print("⚠️ 结论: deepseek模型基本可靠，需要监控")
            print("💡 建议: 加强质量检查和性能监控")
        else:
            print("❌ 结论: deepseek模型可靠性不足")
            print("💡 建议: 调查不稳定原因，考虑替代方案")
    else:
        print(f"\n❌ 可靠性测试失败")
        print("🔧 建议: 检查网络连接和LM Studio状态")