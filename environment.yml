name: Assessment
channels:
  - conda-forge
  - defaults
dependencies:
  # Python环境（最新稳定版）
  - python=3.11
  - pip
  
  # Node.js环境（使用conda-forge的最新LTS版本）
  - nodejs>=20.0
  - npm
  
  # 数据库客户端（ARM64兼容版本）
  - psycopg2>=2.9  # PostgreSQL Python驱动
  - redis-py>=5.0  # Redis Python客户端
  
  # Java开发环境（ARM64 native）
  - openjdk=17  # 使用Java 17 LTS（更好的conda兼容性）
  - maven>=3.9  # 兼容Java 17的Maven版本
  
  # 开发工具
  - git
  - curl
  - wget
  - tree
  
  # Python包（最新稳定版）
  - pip:
    # 项目管理工具
    - poetry==1.8.2
    - pre-commit==3.6.2
    
    # 测试工具
    - pytest==8.1.1
    - pytest-cov==4.0.0
    
    # 代码质量工具
    - black==24.3.0
    - ruff==0.3.2  # 替代flake8，更快更现代
    - isort==5.13.2
    
    # 文档工具
    - mkdocs==1.5.3
    - mkdocs-material==9.5.13
    
    # 部署工具
    - docker-compose==2.24.6
    
    # 监控工具
    - prometheus-client==0.20.0
    
    # 实用工具
    - requests==2.31.0
    - pyyaml==6.0.1
    - python-dotenv==1.0.1
    - typer==0.9.0  # 现代CLI工具
    - rich==13.7.1  # 美化输出