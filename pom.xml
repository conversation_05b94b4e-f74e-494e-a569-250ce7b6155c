<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.assessment</groupId>
    <artifactId>assessment-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Assessment Platform Parent</name>
    <description>父项目，用于管理智慧养老评估平台的所有模块</description>

    <modules>
        <module>backend</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud.version>2025.0.0</spring-cloud.version>
        <minio.version>8.5.17</minio.version>
        <jwt.version>0.12.6</jwt.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <springdoc.version>2.8.9</springdoc.version>

        <!-- Code Quality Plugin Versions -->
        <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
        <checkstyle.version>10.25.0</checkstyle.version>
        <spotless-maven-plugin.version>2.44.5</spotless-maven-plugin.version>
        <spotbugs-maven-plugin.version>*******</spotbugs-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.13</jacoco-maven-plugin.version>
        <maven-pmd-plugin.version>3.26.0</maven-pmd-plugin.version>
        <lombok.version>1.18.38</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless-maven-plugin.version}</version>
                    <configuration>
                        <!-- <skip>true</skip> -->
                        <java>
                            <includes>
                                <include>src/main/java/**/*.java</include>
                                <include>src/test/java/**/*.java</include>
                            </includes>
                        </java>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs-maven-plugin.version}</version>
                    <configuration>
                        <skip>true</skip>
                        <effort>Max</effort>
                        <threshold>Low</threshold>
                        <failOnError>true</failOnError>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project> 