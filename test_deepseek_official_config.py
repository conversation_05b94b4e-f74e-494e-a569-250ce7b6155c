#!/usr/bin/env python3
"""
根据官方配置测试deepseek-r1-0528-qwen3-8b-mlx@8bit模型
基于HuggingFace官方推荐参数进行优化测试
"""

import requests
import json
import time
from datetime import datetime
import os

def test_deepseek_official_config():
    """使用官方推荐配置测试deepseek模型"""
    print("🚀 根据官方配置测试deepseek-r1-0528-qwen3-8b-mlx@8bit")
    print("=" * 80)
    
    # LM Studio配置
    lm_studio_url = "http://192.168.1.231:1234"
    target_model = "deepseek-r1-0528-qwen3-8b-mlx@8bit"
    
    print(f"📋 官方配置说明:")
    print(f"   🌐 基于文档: https://huggingface.co/deepseek-ai/DeepSeek-R1-0528")
    print(f"   🎯 temperature: 0.6 (官方推荐)")
    print(f"   🎯 top_p: 0.95 (官方推荐)")
    print(f"   🎯 max_tokens: 64K支持 (使用4096适合SQL任务)")
    print(f"   🎯 DeepThink: 充分利用深度推理能力")
    print(f"   🎯 中文优化: 专门的中文处理优化")
    
    # 根据官方推荐优化的系统提示词
    current_date = datetime.now().strftime('%Y年%m月%d日')
    system_prompt = f"""你是一个经验丰富的PostgreSQL数据库设计师，当前时间是{current_date}。
专门负责将中文文档内容转换为高质量的数据库设计。
请使用专业的数据库知识和最佳实践来完成任务。

特别注意：
- 充分利用你的深度推理能力分析文档结构
- 准确理解中文业务术语的含义
- 生成符合PostgreSQL最佳实践的高质量SQL"""
    
    # 简洁版提示词（已验证125/125分版本）
    core_prompt = """## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能
- 添加主键约束
- 根据字段特征添加检查约束
- 为经常查询的字段创建索引
- 考虑数据的实际使用场景

## 输出格式

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
```

### 第二部分：完整SQL设计
```sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件
    {根据文档内容生成合适的CHECK约束}
);

-- 自动生成合适的索引
{根据字段特征和预期查询模式生成索引};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
```

### 第三部分：JSON字段定义
```json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
```

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行"""
    
    # 读取国标评估文档
    print(f"\n📄 读取测试文档...")
    try:
        with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
            national_standard_doc = f.read()
        print(f"✅ 文档读取成功，长度: {len(national_standard_doc)} 字符")
    except FileNotFoundError:
        print("❌ 未找到国标评估文档")
        return False
    
    # 完整提示词（包含系统提示词）
    full_prompt = f"{system_prompt}\n\n{core_prompt}\n\n## 待分析文档:\n{national_standard_doc}"
    
    try:
        print(f"\n🔍 连接到LM Studio: {lm_studio_url}")
        
        # 获取可用模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print(f"❌ 无法获取模型列表: {models_response.status_code}")
            return False
            
        models_data = models_response.json()
        selected_model = None
        
        print("📋 可用模型列表:")
        for model in models_data['data']:
            model_id = model['id']
            print(f"   - {model_id}")
            if model_id == target_model:
                selected_model = model_id
                break
        
        if not selected_model:
            print(f"❌ 未找到目标模型: {target_model}")
            return False
            
        print(f"\n✅ 选择模型: {selected_model}")
        
        # 根据官方推荐的请求配置
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "temperature": 0.6,    # 官方推荐值
            "top_p": 0.95,        # 官方推荐值
            "max_tokens": 4096,   # 适合SQL生成任务
            "stream": False,      # 稳定性优先
            "do_sample": True     # 启用采样
        }
        
        print(f"\n📤 使用官方推荐配置发送请求...")
        print(f"🤖 模型: {selected_model}")
        print(f"🌡️ Temperature: 0.6 (官方推荐)")
        print(f"🎯 Top-p: 0.95 (官方推荐)")
        print(f"📊 Max tokens: 4096 (适合SQL任务)")
        print(f"📝 提示词总长度: {len(full_prompt)} 字符")
        print(f"📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
        print(f"🎯 测试重点: 质量优先，充分利用深度推理")
        print("⏳ 等待模型深度推理...")
        
        start_time = time.time()
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=1800  # 30分钟超时，充分利用深度推理
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 根据官方配置的测试完成！")
                print(f"⏱️ 处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                print(f"🎯 配置效果: 官方推荐参数 + 深度推理")
                
                # 详细质量检查
                quality_results = perform_quality_check(ai_response)
                
                # 保存详细测试报告
                report_file = save_official_config_report(
                    ai_response, analysis_time, full_prompt, quality_results, 
                    selected_model, request_body, current_date
                )
                
                print(f"\n📄 详细报告已保存: {report_file}")
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ 请求超时（超过30分钟）")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def perform_quality_check(ai_response):
    """执行详细的质量检查"""
    print(f"\n📊 根据官方配置的质量检查:")
    
    # 基础功能检查
    has_document_analysis = '文档分析' in ai_response or '文档类型' in ai_response
    has_create_table = 'CREATE TABLE' in ai_response.upper()
    has_json = '"database_design"' in ai_response and '"fields"' in ai_response
    has_constraints = 'CHECK' in ai_response.upper()
    has_indexes = 'CREATE INDEX' in ai_response.upper()
    has_comments = 'COMMENT ON' in ai_response.upper()
    has_triggers = 'CREATE TRIGGER' in ai_response.upper()
    
    # 结构完整性检查
    has_three_parts = len([part for part in ['## 文档分析', 'CREATE TABLE', '"database_design"'] 
                         if part in ai_response]) >= 2
    has_national_standard_fields = any(field in ai_response.lower() for field in 
        ['assessment', 'elderly', 'ability', '评估', '老年人', '能力'])
    
    # SQL质量检查
    has_primary_key = 'BIGSERIAL PRIMARY KEY' in ai_response.upper()
    has_audit_fields = all(field in ai_response for field in ['created_at', 'updated_at'])
    
    # 中文处理质量检查
    has_chinese_understanding = any(term in ai_response for term in 
        ['老年人', '评估', '能力', '等级', '分数'])
    has_professional_terms = any(term in ai_response.lower() for term in 
        ['elderly_assessment', 'ability_score', 'assessment_date'])
    
    print(f"🎯 基础功能质量:")
    print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
    print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
    print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
    print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
    print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
    print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
    print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
    
    print(f"\n🏗️ 结构完整性:")
    print(f"   📋 三部分输出: {'通过' if has_three_parts else '❌未通过'}")
    print(f"   🏥 业务理解: {'通过' if has_national_standard_fields else '❌未通过'}")
    
    print(f"\n🗄️ SQL质量:")
    print(f"   🔑 主键设计: {'通过' if has_primary_key else '❌未通过'}")
    print(f"   🕒 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
    
    print(f"\n🇨🇳 中文处理质量 (官方优化):")
    print(f"   📖 中文理解: {'通过' if has_chinese_understanding else '❌未通过'}")
    print(f"   🔤 专业术语: {'通过' if has_professional_terms else '❌未通过'}")
    
    # 计算总分
    basic_checks = [has_document_analysis, has_create_table, has_json, has_constraints, 
                   has_indexes, has_comments, has_triggers]
    structure_checks = [has_three_parts, has_national_standard_fields]
    sql_checks = [has_primary_key, has_audit_fields]
    chinese_checks = [has_chinese_understanding, has_professional_terms]
    
    basic_score = sum(basic_checks) * 12  # 84分
    structure_score = sum(structure_checks) * 12  # 24分
    sql_score = sum(sql_checks) * 8  # 16分
    chinese_score = sum(chinese_checks) * 6  # 12分（新增）
    
    total_score = basic_score + structure_score + sql_score + chinese_score
    max_score = 136  # 更新最高分
    
    print(f"\n🏆 官方配置测试评分:")
    print(f"   📋 基础功能: {basic_score}/84分")
    print(f"   🏗️ 结构完整性: {structure_score}/24分")
    print(f"   🗄️ SQL质量: {sql_score}/16分")
    print(f"   🇨🇳 中文处理: {chinese_score}/12分")
    print(f"   📈 总分: {total_score}/{max_score}分 ({total_score/max_score*100:.1f}%)")
    
    # 与基准对比
    baseline_score = 125
    performance_vs_baseline = (total_score / max_score) / (baseline_score / 125) * 100
    
    print(f"\n📊 与基准测试对比:")
    print(f"   🎯 基准评分: 125/125分 (100%)")
    print(f"   🎯 当前评分: {total_score}/{max_score}分 ({total_score/max_score*100:.1f}%)")
    print(f"   📈 相对表现: {performance_vs_baseline:.1f}%")
    
    if performance_vs_baseline >= 100:
        grade = "A+级 - 超越基准"
    elif performance_vs_baseline >= 95:
        grade = "A级 - 优秀表现"
    elif performance_vs_baseline >= 85:
        grade = "B级 - 良好表现"
    elif performance_vs_baseline >= 75:
        grade = "C级 - 基础通过"
    else:
        grade = "D级 - 需要改进"
    
    print(f"   🏆 最终等级: {grade}")
    
    return {
        "basic_score": basic_score,
        "structure_score": structure_score,
        "sql_score": sql_score,
        "chinese_score": chinese_score,
        "total_score": total_score,
        "max_score": max_score,
        "percentage": total_score/max_score*100,
        "grade": grade,
        "checks": {
            "has_document_analysis": has_document_analysis,
            "has_create_table": has_create_table,
            "has_json": has_json,
            "has_constraints": has_constraints,
            "has_indexes": has_indexes,
            "has_comments": has_comments,
            "has_triggers": has_triggers,
            "has_three_parts": has_three_parts,
            "has_national_standard_fields": has_national_standard_fields,
            "has_primary_key": has_primary_key,
            "has_audit_fields": has_audit_fields,
            "has_chinese_understanding": has_chinese_understanding,
            "has_professional_terms": has_professional_terms
        }
    }

def save_official_config_report(ai_response, analysis_time, full_prompt, quality_results, 
                               selected_model, request_config, test_date):
    """保存根据官方配置的详细测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"/Volumes/acasis/Assessment/test_results/deepseek_official_config_{timestamp}.md"
    
    os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 根据官方配置的deepseek-r1测试报告\n\n")
        f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**配置来源**: https://huggingface.co/deepseek-ai/DeepSeek-R1-0528\n")
        f.write(f"**模型**: {selected_model}\n")
        f.write(f"**LM Studio**: http://192.168.1.231:1234\n")
        f.write(f"**处理时间**: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)\n")
        f.write(f"**测试重点**: 质量优先，充分利用深度推理能力\n\n")
        
        f.write("## 官方推荐配置\n\n")
        f.write("```json\n")
        f.write(json.dumps(request_config, indent=2, ensure_ascii=False))
        f.write("\n```\n\n")
        
        f.write("## 配置说明\n\n")
        f.write("- **Temperature 0.6**: 官方推荐值，平衡创造性和准确性\n")
        f.write("- **Top-p 0.95**: 官方推荐值，保持输出多样性\n")
        f.write("- **Max tokens 4096**: 适合SQL生成任务的长度\n")
        f.write("- **DeepThink**: 充分利用深度推理能力\n")
        f.write("- **中文优化**: 专门的中文业务术语处理\n\n")
        
        f.write("## 传递的完整提示词\n\n")
        f.write("```text\n")
        f.write(full_prompt)
        f.write("\n```\n\n")
        
        f.write("## 质量评估结果\n\n")
        f.write(f"- **基础功能**: {quality_results['basic_score']}/84分\n")
        f.write(f"- **结构完整性**: {quality_results['structure_score']}/24分\n")
        f.write(f"- **SQL质量**: {quality_results['sql_score']}/16分\n")
        f.write(f"- **中文处理**: {quality_results['chinese_score']}/12分\n")
        f.write(f"- **总分**: {quality_results['total_score']}/{quality_results['max_score']}分 ({quality_results['percentage']:.1f}%)\n")
        f.write(f"- **最终等级**: {quality_results['grade']}\n\n")
        
        f.write("## 详细检查项目\n\n")
        checks = quality_results['checks']
        f.write("### 基础功能\n")
        f.write(f"- 文档分析: {'✅ 通过' if checks['has_document_analysis'] else '❌ 未通过'}\n")
        f.write(f"- CREATE TABLE: {'✅ 通过' if checks['has_create_table'] else '❌ 未通过'}\n")
        f.write(f"- JSON定义: {'✅ 通过' if checks['has_json'] else '❌ 未通过'}\n")
        f.write(f"- 约束条件: {'✅ 通过' if checks['has_constraints'] else '❌ 未通过'}\n")
        f.write(f"- 索引设计: {'✅ 通过' if checks['has_indexes'] else '❌ 未通过'}\n")
        f.write(f"- 字段注释: {'✅ 通过' if checks['has_comments'] else '❌ 未通过'}\n")
        f.write(f"- 触发器: {'✅ 通过' if checks['has_triggers'] else '❌ 未通过'}\n\n")
        
        f.write("### 结构完整性\n")
        f.write(f"- 三部分输出: {'✅ 通过' if checks['has_three_parts'] else '❌ 未通过'}\n")
        f.write(f"- 业务理解: {'✅ 通过' if checks['has_national_standard_fields'] else '❌ 未通过'}\n\n")
        
        f.write("### SQL质量\n")
        f.write(f"- 主键设计: {'✅ 通过' if checks['has_primary_key'] else '❌ 未通过'}\n")
        f.write(f"- 审计字段: {'✅ 通过' if checks['has_audit_fields'] else '❌ 未通过'}\n\n")
        
        f.write("### 中文处理质量\n")
        f.write(f"- 中文理解: {'✅ 通过' if checks['has_chinese_understanding'] else '❌ 未通过'}\n")
        f.write(f"- 专业术语: {'✅ 通过' if checks['has_professional_terms'] else '❌ 未通过'}\n\n")
        
        f.write("## deepseek模型完整生成结果\n\n")
        f.write("```\n")
        f.write(ai_response)
        f.write("\n```\n\n")
        
        f.write("## 测试结论\n\n")
        if quality_results['percentage'] >= 90:
            f.write("✅ **优秀表现**: 官方配置显著提升了模型性能\n")
            f.write("🎯 **建议**: 推荐在生产环境中使用此配置\n")
        elif quality_results['percentage'] >= 80:
            f.write("✅ **良好表现**: 官方配置有效提升了质量\n")
            f.write("🎯 **建议**: 可以在生产环境中使用\n")
        else:
            f.write("⚠️ **需要优化**: 配置效果有待改进\n")
            f.write("🎯 **建议**: 进一步调整参数或检查环境\n")
    
    return report_file

if __name__ == "__main__":
    print("🎯 根据官方配置测试deepseek-r1-0528-qwen3-8b-mlx@8bit")
    print("=" * 80)
    print("📋 配置来源: HuggingFace官方文档")
    print("🎯 测试重点: 质量优先，深度推理")
    print("🇨🇳 中文优化: 专门的中文处理能力测试")
    print("📊 评分体系: 增强版136分评分系统")
    print("=" * 80)
    
    if test_deepseek_official_config():
        print(f"\n🎉 根据官方配置的测试成功完成!")
        print(f"📊 测试报告包含完整的提示词和配置信息")
        print(f"🎯 验证了官方推荐参数的实际效果")
    else:
        print(f"\n❌ 测试失败，请检查LM Studio连接和模型状态")