# Frontend build for Apple Silicon
FROM --platform=linux/arm64 node:20-alpine AS builder

# 安装Python和构建工具（某些npm包需要）
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 复制package.json文件
COPY frontend/admin/package*.json ./

# 设置npm配置
RUN npm config set target_arch arm64 && \
    npm config set target_platform darwin && \
    npm config set registry https://registry.npmjs.org/

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY frontend/admin ./

# 构建应用
RUN npm run build

# Production stage
FROM --platform=linux/arm64 nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY docker/nginx/frontend.conf /etc/nginx/conf.d/default.conf

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost || exit 1

# 暴露端口
EXPOSE 80

# 启动命令
CMD ["nginx", "-g", "daemon off;"]