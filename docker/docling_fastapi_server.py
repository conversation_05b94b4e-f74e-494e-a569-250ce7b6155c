#!/usr/bin/env python3
"""
Docling FastAPI服务器
提供高性能、可扩展的HTTP接口用于PDF转换
支持自动检测并使用Apple Silicon MPS加速（原生macOS环境）或CPU模式（其他环境）
"""

import os
import json
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any
from contextlib import asynccontextmanager
import platform
import subprocess

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

from docling.document_converter import DocumentConverter, PdfFormatOption, WordFormatOption, HTMLFormatOption, ImageFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions, PaginatedPipelineOptions, TableFormerMode, EasyOcrOptions
from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend
from docling.pipeline.standard_pdf_pipeline import StandardPdfPipeline
from docling.pipeline.simple_pipeline import SimplePipeline
import easyocr

# 尝试导入torch以检测MPS支持
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局转换器实例
converter = None

def init_converter():
    """初始化Docling转换器"""
    global converter
    logger.info("初始化Docling转换器...")
    try:
        # 检测运行环境并设置正确的路径
        is_docker = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER', False)
        
        if is_docker:
            # Docker环境使用容器内路径
            easyocr_model_path = '/home/<USER>/.EasyOCR'
        else:
            # 原生环境使用本地路径
            # 优先使用项目本地的models目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_models_dir = os.path.join(current_dir, 'models', 'easyocr')
            
            # 如果项目目录存在，使用项目目录
            if os.path.exists(current_dir):
                easyocr_model_path = project_models_dir
            else:
                # 否则使用用户home目录
                home_dir = os.path.expanduser("~")
                easyocr_model_path = os.path.join(home_dir, '.EasyOCR')
        
        # 设置环境变量
        os.environ['EASYOCR_MODULE_PATH'] = easyocr_model_path
        os.environ['TORCH_HOME'] = easyocr_model_path
        
        # 确保EasyOCR模型目录存在
        os.makedirs(easyocr_model_path, exist_ok=True)
        os.makedirs(os.path.join(easyocr_model_path, 'model'), exist_ok=True)
        
        logger.info(f"EasyOCR模型路径: {easyocr_model_path}")
        logger.info(f"运行环境: {'Docker' if is_docker else '原生'}")
        
        # 配置全局中英文OCR选项
        global_ocr_options = EasyOcrOptions()
        global_ocr_options.lang = ['ch_sim', 'en']  # 简体中文 + 英文
        global_ocr_options.use_gpu = False  # Docker环境通常使用CPU
        
        # 1. PDF处理选项 - 针对表格和结构化文档优化
        pdf_options = PdfPipelineOptions()
        pdf_options.do_ocr = True  # 启用OCR
        pdf_options.do_table_structure = True  # 启用表格结构识别
        pdf_options.table_structure_options.do_cell_matching = True  # 启用单元格匹配
        pdf_options.table_structure_options.mode = TableFormerMode.ACCURATE  # 使用精确模式
        pdf_options.images_scale = 2.5  # 提高图像分辨率（增强图片表格识别）
        pdf_options.generate_page_images = True  # 生成页面图像
        pdf_options.ocr_options = global_ocr_options  # 应用中英文OCR配置
        
        # 2. DOCX/Word文档处理选项
        docx_options = PaginatedPipelineOptions()
        docx_options.generate_page_images = True  # 生成页面图像
        docx_options.images_scale = 2.5  # 提高图像分辨率
        
        # 3. HTML文档处理选项
        html_options = PaginatedPipelineOptions()
        html_options.generate_page_images = True  # 支持HTML中的图像
        html_options.images_scale = 2.5  # 提高图像分辨率
        
        # 4. Excel/XLSX表格处理选项
        xlsx_options = PaginatedPipelineOptions()
        xlsx_options.generate_page_images = True  # 支持表格图像化
        xlsx_options.images_scale = 2.5  # 提高图像分辨率
        
        # 5. 图片文件处理选项 - 使用PDF选项的OCR配置
        image_options = PdfPipelineOptions()
        image_options.do_ocr = True  # 启用OCR
        image_options.images_scale = 3.0  # 图片文件使用更高分辨率
        image_options.generate_page_images = True  # 生成页面图像
        image_options.ocr_options = global_ocr_options  # 应用OCR配置
        
        # 配置多格式支持
        format_options = {
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pdf_options,
                pipeline_cls=StandardPdfPipeline,
                backend=PyPdfiumDocumentBackend  # 使用PyPdfium后端，更好的表格支持
            ),
            InputFormat.DOCX: WordFormatOption(
                pipeline_options=docx_options,
                pipeline_cls=SimplePipeline
            ),
            InputFormat.HTML: HTMLFormatOption(
                pipeline_options=html_options
            ),
            InputFormat.XLSX: WordFormatOption(  # XLSX使用Word格式选项
                pipeline_options=xlsx_options,
                pipeline_cls=SimplePipeline
            ),
            InputFormat.IMAGE: ImageFormatOption(
                pipeline_options=image_options,
                pipeline_cls=StandardPdfPipeline,
                backend=PyPdfiumDocumentBackend
            )
        }
        
        # 初始化支持多格式的转换器
        converter = DocumentConverter(
            allowed_formats=[
                InputFormat.PDF,      # PDF文档
                InputFormat.DOCX,     # Word文档
                InputFormat.HTML,     # HTML页面
                InputFormat.XLSX,     # Excel表格
                InputFormat.IMAGE,    # 图片文件 (PNG, JPG, JPEG, GIF, BMP, TIFF)
                InputFormat.MD,       # Markdown文档
                InputFormat.PPTX,     # PowerPoint文档
                InputFormat.CSV,      # CSV文件
                InputFormat.ASCIIDOC  # AsciiDoc文件
            ],
            format_options=format_options
        )
        logger.info("Docling转换器初始化完成")
    except Exception as e:
        logger.error(f"Docling转换器初始化失败: {e}")
        raise

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化转换器
    init_converter()
    yield
    # 关闭时清理资源
    logger.info("服务关闭，清理资源...")

# 创建FastAPI应用
app = FastAPI(
    title="Docling PDF转换服务",
    description="基于FastAPI的高性能PDF转换API",
    version="2.0.0",
    lifespan=lifespan
)

# 响应模型
class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

class ConversionResponse(BaseModel):
    success: bool
    filename: str
    markdown: str = None
    metadata: Dict[str, Any] = None
    error: str = None

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        service="docling-fastapi",
        version="2.0.0"
    )

@app.post("/convert", response_model=ConversionResponse)
async def convert_document(file: UploadFile = File(...)):
    """转换多格式文档为Markdown"""
    
    # 支持的文件扩展名
    supported_extensions = {
        '.pdf', '.docx', '.xlsx', '.xls', '.html', '.md', '.pptx', '.csv', '.adoc',
        '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'
    }
    
    # 验证文件类型
    file_ext = None
    if file.filename:
        file_ext = '.' + file.filename.lower().split('.')[-1]
        if file_ext not in supported_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式: {file_ext}。支持的格式: {', '.join(sorted(supported_extensions))}"
            )
    else:
        raise HTTPException(status_code=400, detail="文件名无效")
    
    if converter is None:
        raise HTTPException(status_code=503, detail="转换器未初始化")
    
    logger.info(f"开始转换文档: {file.filename} (格式: {file_ext})")
    
    # 创建临时文件
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_path = tmp_file.name
        
        try:
            # XLS格式特殊处理提示
            if file_ext == '.xls':
                raise ValueError("XLS格式不被直接支持，请转换为XLSX格式后重试")
            
            # 执行转换
            result = converter.convert(tmp_path)
            
            # 导出为Markdown
            markdown_content = result.document.export_to_markdown()
            
            # 构建元数据
            metadata = {
                "page_count": len(result.document.pages) if hasattr(result.document, 'pages') else 0,
                "content_length": len(markdown_content),
                "file_size": len(content)
            }
            
            logger.info(f"文档转换成功: {file.filename} -> {len(markdown_content)} 字符, {metadata['page_count']} 页")
            
            return ConversionResponse(
                success=True,
                filename=file.filename,
                markdown=markdown_content,
                metadata=metadata
            )
            
        finally:
            # 清理临时文件
            try:
                os.unlink(tmp_path)
            except OSError as e:
                logger.warning(f"清理临时文件失败: {e}")
                
    except Exception as e:
        logger.error(f"转换文档失败 {file.filename}: {str(e)}")
        return ConversionResponse(
            success=False,
            filename=file.filename,
            error=str(e)
        )

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "内部服务器错误",
            "detail": str(exc) if os.getenv("DEBUG") else None
        }
    )

def main():
    """启动FastAPI服务器"""
    port = int(os.getenv("PORT", 8088))
    host = os.getenv("HOST", "0.0.0.0")
    workers = int(os.getenv("WORKERS", 1))
    
    logger.info(f"启动Docling FastAPI服务器，端口: {port}, 工作进程: {workers}")
    
    uvicorn.run(
        "docling_fastapi_server:app",
        host=host,
        port=port,
        workers=workers,
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    main()