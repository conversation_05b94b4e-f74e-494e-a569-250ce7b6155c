#!/bin/bash
# Docling API服务 - 提供HTTP接口供Java调用

set -e

# 进入docker目录
cd "$(dirname "$0")"

# 创建输入输出目录
mkdir -p ./input ./output

# 检查Docker镜像
if [[ "$(docker images -q docling:latest 2> /dev/null)" == "" ]]; then
    echo "构建Docker镜像..."
    docker build -f Dockerfile.docling -t docling:latest .
fi

# 清理旧容器
docker rm -f docling-api 2>/dev/null || true

echo "🚀 启动Docling API服务..."

# 运行容器并启动简单的HTTP服务
docker run -d \
    --name docling-api \
    -p 8088:8088 \
    -v "$(pwd)/input:/app/input" \
    -v "$(pwd)/output:/app/output" \
    -v "$(pwd)/docling_api_server.py:/app/docling_api_server.py" \
    docling:latest \
    python /app/docling_api_server.py

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务状态
if curl -s http://localhost:8088/health > /dev/null; then
    echo "✅ Docling API服务已启动！"
    echo "📍 API地址: http://localhost:8088"
    echo "📍 健康检查: http://localhost:8088/health"
    echo "📍 转换接口: POST http://localhost:8088/convert"
else
    echo "❌ 服务启动失败"
    docker logs docling-api
    exit 1
fi