#!/usr/bin/env python3
"""
简单的Docling测试脚本
直接在本地运行，不使用Docker
"""

import sys
import os
sys.path.insert(0, '/opt/anaconda3/envs/Assessment/lib/python3.11/site-packages')

def test_import():
    """测试导入"""
    try:
        import docling
        print(f"✅ Docling版本: {docling.__version__ if hasattr(docling, '__version__') else 'unknown'}")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_converter_init():
    """测试转换器初始化"""
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat
        
        # 最简单的初始化
        converter = DocumentConverter(allowed_formats=[InputFormat.PDF])
        print("✅ 转换器初始化成功")
        return True, converter
    except Exception as e:
        print(f"❌ 转换器初始化失败: {e}")
        return False, None

def test_file_access():
    """测试文件访问"""
    input_dir = Path("/Volumes/acasis/Assessment/docker/input")
    pdf_files = list(input_dir.glob("*.pdf"))
    
    print(f"📁 输入目录: {input_dir}")
    print(f"📄 找到PDF文件: {len(pdf_files)}")
    
    for pdf in pdf_files:
        print(f"  - {pdf.name} ({pdf.stat().st_size} bytes)")
    
    return pdf_files

def test_simple_conversion(converter, pdf_path):
    """测试简单转换"""
    try:
        print(f"🔄 尝试转换: {pdf_path}")
        
        # 直接转换，不使用特殊配置
        result = converter.convert(str(pdf_path))
        
        print("✅ 转换成功")
        print(f"📊 页数: {len(result.document.pages) if hasattr(result.document, 'pages') else 'N/A'}")
        
        # 尝试导出
        markdown = result.document.export_to_markdown()
        text = result.document.export_to_text() if hasattr(result.document, 'export_to_text') else ""
        
        print(f"📝 Markdown长度: {len(markdown)}")
        print(f"📝 文本长度: {len(text)}")
        
        if markdown:
            print("📄 Markdown预览:")
            print(markdown[:200] + "..." if len(markdown) > 200 else markdown)
        
        return True, markdown
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False, str(e)

def main():
    print("🧪 Docling功能测试")
    print("=" * 50)
    
    # 1. 测试导入
    print("\n1. 测试模块导入")
    if not test_import():
        sys.exit(1)
    
    # 2. 测试转换器初始化
    print("\n2. 测试转换器初始化")
    success, converter = test_converter_init()
    if not success:
        sys.exit(1)
    
    # 3. 测试文件访问
    print("\n3. 检查输入文件")
    pdf_files = test_file_access()
    if not pdf_files:
        print("❌ 没有找到PDF文件")
        sys.exit(1)
    
    # 4. 测试转换最小的文件
    print("\n4. 测试PDF转换")
    
    # 选择最小的PDF文件进行测试
    smallest_pdf = min(pdf_files, key=lambda x: x.stat().st_size)
    print(f"选择最小的文件进行测试: {smallest_pdf.name}")
    
    success, result = test_simple_conversion(converter, smallest_pdf)
    
    if success:
        print("\n🎉 所有测试通过！")
        
        # 保存结果
        output_path = Path("/Volumes/acasis/Assessment/docker/output") / f"{smallest_pdf.stem}_test.md"
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(result)
        
        print(f"📁 测试结果保存到: {output_path}")
    else:
        print(f"\n❌ 测试失败: {result}")

if __name__ == "__main__":
    main()