#!/usr/bin/env python3
"""
并发测试脚本 - 测试多worker Docling服务的并发处理能力
"""

import requests
import time
import concurrent.futures
from pathlib import Path
import json

def test_single_conversion(request_id, pdf_path):
    """单个转换请求"""
    start_time = time.time()
    try:
        with open(pdf_path, 'rb') as f:
            files = {'file': (f'request_{request_id}_{pdf_path.name}', f, 'application/pdf')}
            response = requests.post(
                "http://localhost:8088/convert", 
                files=files,
                timeout=600  # 10分钟超时
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            if success:
                return {
                    'request_id': request_id,
                    'status': '✅ 成功',
                    'duration': f"{duration:.1f}s",
                    'pages': data['metadata']['page_count'],
                    'content_length': data['metadata']['content_length']
                }
            else:
                return {
                    'request_id': request_id,
                    'status': '❌ 转换失败',
                    'duration': f"{duration:.1f}s",
                    'error': data.get('error', 'Unknown error')
                }
        else:
            return {
                'request_id': request_id,
                'status': f'❌ HTTP {response.status_code}',
                'duration': f"{duration:.1f}s",
                'error': response.text[:100]
            }
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        return {
            'request_id': request_id,
            'status': '❌ 异常',
            'duration': f"{duration:.1f}s",
            'error': str(e)[:100]
        }

def test_health():
    """健康检查"""
    try:
        response = requests.get("http://localhost:8088/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    print("🚀 Docling FastAPI 并发性能测试")
    print("=" * 60)
    
    # 健康检查
    if not test_health():
        print("❌ 服务不可用，请先启动Docling FastAPI服务")
        return
    
    print("✅ 服务健康检查通过")
    
    # 找到测试PDF文件
    pdf_path = Path("/Volumes/acasis/Assessment/docker/input/国标评估报告模板.pdf")
    if not pdf_path.exists():
        print(f"❌ 测试文件不存在: {pdf_path}")
        return
    
    print(f"📄 测试文件: {pdf_path.name} ({pdf_path.stat().st_size / 1024:.1f} KB)")
    
    # 并发测试配置
    concurrent_requests = [2, 3, 5]  # 不同并发数测试
    
    for num_concurrent in concurrent_requests:
        print(f"\n🔥 并发测试: {num_concurrent} 个同时请求")
        print("-" * 40)
        
        start_time = time.time()
        
        # 执行并发请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [
                executor.submit(test_single_conversion, i+1, pdf_path) 
                for i in range(num_concurrent)
            ]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 分析结果
        successful = [r for r in results if '✅' in r['status']]
        failed = [r for r in results if '❌' in r['status']]
        
        print(f"⏱️  总耗时: {total_duration:.1f}s")
        print(f"✅ 成功: {len(successful)}/{num_concurrent}")
        print(f"❌ 失败: {len(failed)}/{num_concurrent}")
        
        if successful:
            durations = [float(r['duration'].replace('s', '')) for r in successful]
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            print(f"📊 单个请求耗时: 平均 {avg_duration:.1f}s, 最快 {min_duration:.1f}s, 最慢 {max_duration:.1f}s")
            
            # 计算并发效率
            sequential_time = sum(durations)  # 如果串行执行的时间
            concurrency_efficiency = sequential_time / total_duration
            print(f"🚀 并发效率: {concurrency_efficiency:.1f}x (理论最大: {num_concurrent}x)")
        
        # 显示详细结果
        print("\n📝 详细结果:")
        for result in sorted(results, key=lambda x: x['request_id']):
            print(f"  请求{result['request_id']}: {result['status']} ({result['duration']})")
            if result['status'] == '✅ 成功':
                print(f"    -> {result['pages']}页, {result['content_length']}字符")
            elif 'error' in result:
                print(f"    -> 错误: {result['error']}")
        
        time.sleep(2)  # 请求间隔
    
    print(f"\n🎉 并发测试完成！")
    print("\n💡 优化建议:")
    print("  - 如果并发效率低于预期，考虑增加worker数量")
    print("  - 如果内存使用率过高，考虑降低worker数量")
    print("  - 监控CPU和内存使用情况以找到最佳配置")

if __name__ == "__main__":
    main()