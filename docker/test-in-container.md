# 在Docker容器内测试Docling

容器已启动，你现在在容器内的bash shell中。以下是测试步骤：

## 1. 测试基本转换功能

首先检查Python和Docling是否正常安装：
```bash
python --version
python -c "import docling; print('Docling导入成功')"
```

## 2. 测试PDF转换

### 方法1：使用转换脚本

如果input目录中有PDF文件：
```bash
# 查看input目录
ls -la /app/input/

# 运行批量转换
python /app/docling_converter.py

# 查看输出
ls -la /app/output/
```

### 方法2：转换单个文件

如果你有特定的PDF文件：
```bash
# 转换单个文件
python /app/docling_converter.py --file /app/input/test.pdf --output /app/output

# 查看生成的Markdown
cat /app/output/test.md
```

### 方法3：测试内置示例

创建一个简单的测试脚本：
```bash
cat > /app/test_docling.py << 'EOF'
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat

# 初始化转换器
converter = DocumentConverter(allowed_formats=[InputFormat.PDF])
print("Docling转换器初始化成功！")

# 如果有PDF文件，可以测试转换
import os
if os.path.exists("/app/input/test.pdf"):
    result = converter.convert("/app/input/test.pdf")
    print("PDF转换成功！")
    print(f"页数: {len(result.document.pages) if hasattr(result.document, 'pages') else 'N/A'}")
else:
    print("请将PDF文件放入 /app/input/ 目录")
EOF

python /app/test_docling.py
```

## 3. 启动HTTP API服务（在容器内）

```bash
# 启动API服务器
python /app/docling_api_server.py &

# 等待几秒
sleep 3

# 测试健康检查
curl http://localhost:8088/health
```

## 4. 退出容器

完成测试后，输入 `exit` 退出容器。

## 5. 从宿主机使用

退出容器后，你可以：

1. 使用命令行转换：
   ```bash
   ./convert-pdf.sh /path/to/your.pdf
   ```

2. 启动API服务：
   ```bash
   ./docling-api.sh
   ```

## 注意事项

- 容器内的 `/app/input` 映射到宿主机的 `./input` 目录
- 容器内的 `/app/output` 映射到宿主机的 `./output` 目录
- 放入input目录的PDF文件可以在容器内访问