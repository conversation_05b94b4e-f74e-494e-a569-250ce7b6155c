#!/usr/bin/env python3
"""
Docling API服务器
提供简单的HTTP接口用于PDF转换
"""

import os
import json
import tempfile
import shutil
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs
import cgi
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat

# 全局转换器实例
converter = None

def init_converter():
    """初始化Docling转换器"""
    global converter
    print("初始化Docling转换器...")
    converter = DocumentConverter(
        allowed_formats=[InputFormat.PDF]
    )
    print("Docling转换器初始化完成")

class DoclingAPIHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy",
                "service": "docling-api",
                "version": "1.0.0"
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/convert':
            self.handle_convert()
        else:
            self.send_error(404, "Not Found")
    
    def handle_convert(self):
        """处理PDF转换请求"""
        try:
            # 解析multipart/form-data
            content_type = self.headers.get('Content-Type')
            if not content_type or 'multipart/form-data' not in content_type:
                self.send_error(400, "Content-Type must be multipart/form-data")
                return
            
            # 保存上传的文件
            form = cgi.FieldStorage(
                fp=self.rfile,
                headers=self.headers,
                environ={
                    'REQUEST_METHOD': 'POST',
                    'CONTENT_TYPE': self.headers['Content-Type'],
                }
            )
            
            # 获取文件
            if 'file' not in form:
                self.send_error(400, "No file uploaded")
                return
            
            file_item = form['file']
            if not file_item.filename:
                self.send_error(400, "No file selected")
                return
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                tmp_file.write(file_item.file.read())
                tmp_path = tmp_file.name
            
            try:
                # 转换PDF
                print(f"转换PDF: {file_item.filename}")
                result = converter.convert(tmp_path)
                
                # 导出为Markdown
                markdown_content = result.document.export_to_markdown()
                
                # 构建响应
                response = {
                    "success": True,
                    "filename": file_item.filename,
                    "markdown": markdown_content,
                    "metadata": {
                        "page_count": len(result.document.pages) if hasattr(result.document, 'pages') else 0,
                        "content_length": len(markdown_content)
                    }
                }
                
                # 发送响应
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                
            finally:
                # 清理临时文件
                os.unlink(tmp_path)
                
        except Exception as e:
            print(f"转换错误: {str(e)}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {
                "success": False,
                "error": str(e)
            }
            self.wfile.write(json.dumps(error_response).encode())

def main():
    """启动API服务器"""
    # 初始化转换器
    init_converter()
    
    # 启动服务器
    port = 8088
    server = HTTPServer(('0.0.0.0', port), DoclingAPIHandler)
    print(f"Docling API服务器启动在端口 {port}")
    print("按 Ctrl+C 停止服务器")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n停止服务器...")
        server.server_close()

if __name__ == "__main__":
    main()