# Multi-stage Dockerfile for Docling FastAPI Service
FROM python:3.11-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY docling_fastapi_server.py .

# 创建必要目录
RUN mkdir -p /app/input /app/output /app/models

# 创建非root用户和必要目录
RUN groupadd -r docling && useradd -r -g docling -m -d /home/<USER>
RUN chown -R docling:docling /app
RUN mkdir -p /tmp/docling && chown -R docling:docling /tmp/docling
RUN mkdir -p /home/<USER>/.cache && chown -R docling:docling /home/<USER>
RUN mkdir -p /home/<USER>/.EasyOCR && chown -R docling:docling /home/<USER>/.EasyOCR
RUN chmod 755 /home/<USER>

# 设置环境变量解决权限问题
ENV HOME=/home/<USER>
ENV TMPDIR=/tmp
ENV HF_HOME=/app/models/huggingface
ENV TRANSFORMERS_CACHE=/app/models/huggingface
ENV HF_DATASETS_CACHE=/app/models/huggingface
ENV TORCH_HOME=/home/<USER>/.EasyOCR

# 切换到非root用户
USER docling

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl --fail http://localhost:8088/health || exit 1

# 暴露端口
EXPOSE 8088

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV WORKERS=4
ENV HOST=0.0.0.0
ENV PORT=8088

# 优化PyTorch在CPU环境下的性能
ENV OMP_NUM_THREADS=4
ENV MKL_NUM_THREADS=4
ENV TORCH_NUM_THREADS=4
# 禁用pin_memory以避免警告
ENV PYTORCH_DISABLE_PIN_MEMORY=1
# 设置日志级别以减少警告
ENV PYTHONWARNINGS="ignore::UserWarning:torch.utils.data.dataloader"

# 启动命令
CMD ["uvicorn", "docling_fastapi_server:app", "--host", "0.0.0.0", "--port", "8088", "--workers", "4"]