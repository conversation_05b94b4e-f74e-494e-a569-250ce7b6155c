# Docker Compose配置 - Docling FastAPI服务

services:
  docling-fastapi:
    build:
      context: .
      dockerfile: Dockerfile.docling-fastapi
    container_name: docling-fastapi-converter
    ports:
      - "8088:8088"
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=info
      - WORKERS=4
      - HOST=0.0.0.0
      - PORT=8088
      - DEBUG=false
      - HOME=/home/<USER>
      - TMPDIR=/tmp
      - HF_HOME=/app/models/huggingface
      - TRANSFORMERS_CACHE=/app/models/huggingface
      - HF_DATASETS_CACHE=/app/models/huggingface
      - TORCH_HOME=/home/<USER>/.EasyOCR
      - EASYOCR_MODULE_PATH=/home/<USER>/.EasyOCR
      # PyTorch CPU优化
      - OMP_NUM_THREADS=4
      - MKL_NUM_THREADS=4
      - TORCH_NUM_THREADS=4
      - PYTORCH_DISABLE_PIN_MEMORY=1
      - PYTHONWARNINGS=ignore::UserWarning:torch.utils.data.dataloader
    volumes:
      # 输入目录映射
      - ./input:/app/input:ro
      # 输出目录映射
      - ./output:/app/output
      # 模型目录映射（可读写，支持EasyOCR和Hugging Face模型持久化）
      - ./models:/app/models
      # EasyOCR模型目录持久化到项目本地
      - ./models/easyocr:/home/<USER>/.EasyOCR
      # 临时文件存储 (内存文件系统提升性能)
      - type: tmpfs
        target: /tmp
        tmpfs:
          size: 1G
      # 日志存储
      - docling-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8088/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '8.0'
        reservations:
          memory: 2G
          cpus: '4.0'
    networks:
      - docling-network

  # 可选：nginx反向代理用于负载均衡
  docling-proxy:
    image: nginx:alpine
    container_name: docling-nginx-proxy
    ports:
      - "8089:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - docling-fastapi
    networks:
      - docling-network
    profiles:
      - proxy

volumes:
  docling-logs:
    driver: local

networks:
  docling-network:
    driver: bridge