#!/bin/bash

# Docling FastAPI 优化启动脚本
# 自动检测环境并选择最佳运行方式

echo "🚀 Docling FastAPI 优化启动脚本"
echo "================================"

# 检测操作系统和处理器
OS=$(uname -s)
ARCH=$(uname -m)
IS_DOCKER=false
IS_APPLE_SILICON=false
USE_NATIVE=false

# 检测是否在Docker环境中
if [ -f /.dockerenv ] || [ -n "$DOCKER_CONTAINER" ]; then
    IS_DOCKER=true
fi

# 检测是否是Apple Silicon
if [ "$OS" = "Darwin" ]; then
    CPU_BRAND=$(sysctl -n machdep.cpu.brand_string 2>/dev/null)
    if [[ "$CPU_BRAND" == *"Apple"* ]] && [[ "$CPU_BRAND" == *"M"* ]]; then
        IS_APPLE_SILICON=true
        echo "✅ 检测到 Apple Silicon: $CPU_BRAND"
    fi
fi

# 决定运行模式
if [ "$IS_APPLE_SILICON" = true ] && [ "$IS_DOCKER" = false ]; then
    echo "🎯 使用原生 macOS 模式（支持 MPS 加速）"
    USE_NATIVE=true
else
    echo "🐳 使用 Docker 容器模式（CPU 优化）"
fi

# 原生模式运行
if [ "$USE_NATIVE" = true ]; then
    echo ""
    echo "📦 检查 Python 环境..."
    
    # 检查是否有conda环境
    if command -v conda &> /dev/null; then
        echo "✅ 找到 Conda"
        # 激活Assessment环境
        if conda env list | grep -q "Assessment"; then
            echo "🔄 激活 Assessment 环境..."
            eval "$(conda shell.bash hook)"
            conda activate Assessment
        fi
    fi
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        echo "❌ 未找到 Python 3，请安装 Python 3.11+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    echo "✅ Python 版本: $PYTHON_VERSION"
    
    # 检查依赖
    echo ""
    echo "📦 检查依赖..."
    
    # 创建虚拟环境（如果需要）
    if [ ! -d "venv" ] && [ "$USE_NATIVE" = true ]; then
        echo "🔄 创建虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
    elif [ -d "venv" ]; then
        echo "🔄 激活虚拟环境..."
        source venv/bin/activate
    fi
    
    # 安装依赖
    if ! python3 -c "import docling" 2>/dev/null; then
        echo "📦 安装 Docling 和依赖..."
        pip install -r requirements.txt
    fi
    
    # 设置环境变量（Apple Silicon优化）
    export PYTORCH_ENABLE_MPS_FALLBACK=1
    export TOKENIZERS_PARALLELISM=false
    export PYTHONUNBUFFERED=1
    
    echo ""
    echo "🚀 启动 Docling FastAPI 服务（原生模式）..."
    echo "📍 服务地址: http://localhost:8088"
    echo "📊 使用 MPS 加速: 是"
    echo ""
    
    # 启动服务
    cd "$(dirname "$0")"
    python3 docling_fastapi_server.py
    
else
    # Docker模式运行
    echo ""
    echo "🐳 使用 Docker Compose 启动服务..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        echo "❌ Docker 未运行，请先启动 Docker Desktop"
        exit 1
    fi
    
    # 创建必要的目录
    mkdir -p input output models models/easyocr logs
    
    # 构建并启动容器
    echo "🔨 构建 Docker 镜像..."
    docker-compose -f docker-compose.docling-fastapi.yml build
    
    echo ""
    echo "🚀 启动 Docker 容器..."
    docker-compose -f docker-compose.docling-fastapi.yml up -d
    
    echo ""
    echo "✅ Docling FastAPI 服务已启动"
    echo "📍 服务地址: http://localhost:8088"
    echo "📊 使用加速: CPU 优化"
    echo ""
    echo "📌 常用命令:"
    echo "  查看日志: docker-compose -f docker-compose.docling-fastapi.yml logs -f"
    echo "  停止服务: docker-compose -f docker-compose.docling-fastapi.yml down"
    echo "  重启服务: docker-compose -f docker-compose.docling-fastapi.yml restart"
fi

echo ""
echo "🔍 健康检查地址: http://localhost:8088/health"
echo "📚 API 文档: http://localhost:8088/docs"