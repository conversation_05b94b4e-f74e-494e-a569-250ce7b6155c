# Multi-stage build for Apple Silicon optimization
FROM --platform=linux/arm64 eclipse-temurin:21-jdk-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY backend/pom.xml ./
COPY backend/.mvn ./.mvn
COPY backend/mvnw ./

# 下载依赖（利用Docker缓存）
RUN chmod +x ./mvnw && ./mvnw dependency:go-offline -B

# 复制源代码
COPY backend/src ./src

# 构建应用
RUN ./mvnw clean package -DskipTests -B

# Runtime stage
FROM --platform=linux/arm64 eclipse-temurin:21-jre-alpine

# 安装必要的包
RUN apk add --no-cache \
    curl \
    fontconfig \
    ttf-liberation \
    && rm -rf /var/cache/apk/*

# 创建应用用户
RUN addgroup -g 1001 -S spring && \
    adduser -S spring -u 1001 -G spring

# 设置工作目录
WORKDIR /app

# 复制应用jar包
COPY --from=builder /app/target/*.jar app.jar

# 修改文件所有者
RUN chown -R spring:spring /app

# 切换到应用用户
USER spring

# Apple Silicon JVM优化参数
ENV JAVA_OPTS="-Xmx2g -Xms512m -XX:+UseZGC -XX:+UnlockExperimentalVMOptions -Djava.awt.headless=true -Dfile.encoding=UTF-8"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]