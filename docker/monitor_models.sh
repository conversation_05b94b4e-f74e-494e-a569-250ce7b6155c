#!/bin/bash

echo "📊 EasyOCR模型持久化监控"
echo "======================================"

# 监控容器日志中的下载进度
echo "🔍 监控日志中的模型下载进度..."
docker logs -f docling-fastapi-converter 2>&1 | grep -E "(Downloading|Download complete|模型|model)" &
LOG_PID=$!

# 每30秒检查模型文件
echo "📁 每30秒检查模型文件状态..."
while true; do
    echo -e "\n⏰ $(date '+%H:%M:%S') - 检查模型文件"
    
    echo "容器内模型目录:"
    docker exec docling-fastapi-converter ls -la /home/<USER>/.EasyOCR/model/ 2>/dev/null || echo "  无法访问目录"
    
    echo "本地模型目录:"
    ls -la models/easyocr/model/ 2>/dev/null || echo "  无法访问目录"
    
    echo "tmp目录中的模型:"
    docker exec docling-fastapi-converter ls -la /tmp/.EasyOCR/model/ 2>/dev/null || echo "  无模型"
    
    sleep 30
done