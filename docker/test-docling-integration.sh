#!/bin/bash
# 测试Docling集成的完整流程

set -e

echo "🧪 测试Docling PDF转换和数据提取完整流程"

DOCLING_URL="http://localhost:8088"
BACKEND_URL="http://localhost:8181"

# 检查Docling服务是否运行
echo "1️⃣ 检查Docling服务状态..."
if ! curl -f "$DOCLING_URL/health" &>/dev/null; then
    echo "❌ Docling服务未运行，请先启动: ./start-docling.sh"
    exit 1
fi
echo "✅ Docling服务正常运行"

# 检查后端服务是否运行
echo "2️⃣ 检查后端服务状态..."
if ! curl -f "$BACKEND_URL/actuator/health" &>/dev/null; then
    echo "❌ 后端服务未运行，请先启动后端服务"
    exit 1
fi
echo "✅ 后端服务正常运行"

# 测试PDF转换
echo "3️⃣ 测试PDF转Markdown转换..."
TEST_PDF="test-assessment-sample.pdf"

if [ ! -f "$TEST_PDF" ]; then
    echo "⚠️  测试PDF文件 $TEST_PDF 不存在，跳过转换测试"
else
    echo "📄 使用测试文件: $TEST_PDF"
    
    # 调用Docling转换API
    RESPONSE=$(curl -s -X POST \
        -H "Content-Type: multipart/form-data" \
        -F "file=@$TEST_PDF" \
        "$DOCLING_URL/convert-pdf")
    
    # 检查转换结果
    SUCCESS=$(echo "$RESPONSE" | jq -r '.success // false')
    if [ "$SUCCESS" = "true" ]; then
        echo "✅ PDF转换成功"
        
        # 保存Markdown内容
        echo "$RESPONSE" | jq -r '.markdown' > test-output.md
        echo "📝 Markdown已保存到: test-output.md"
        
        # 显示转换统计
        PAGE_COUNT=$(echo "$RESPONSE" | jq -r '.metadata.page_count // 0')
        CONTENT_LENGTH=$(echo "$RESPONSE" | jq -r '.metadata.content_length // 0')
        echo "📊 转换统计: $PAGE_COUNT 页, $CONTENT_LENGTH 字符"
        
    else
        echo "❌ PDF转换失败"
        echo "$RESPONSE" | jq -r '.error // "未知错误"'
        exit 1
    fi
fi

# 测试Markdown分析
echo "4️⃣ 测试Markdown结构分析..."
if [ -f "test-output.md" ]; then
    MARKDOWN_CONTENT=$(cat test-output.md)
    
    ANALYSIS_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"markdown_content\": $(echo "$MARKDOWN_CONTENT" | jq -Rs .)}" \
        "$DOCLING_URL/analyze-markdown")
    
    ANALYSIS_SUCCESS=$(echo "$ANALYSIS_RESPONSE" | jq -r '.success // false')
    if [ "$ANALYSIS_SUCCESS" = "true" ]; then
        echo "✅ Markdown分析成功"
        
        # 显示分析结果
        TITLE=$(echo "$ANALYSIS_RESPONSE" | jq -r '.analysis.title // "未知标题"')
        SECTIONS_COUNT=$(echo "$ANALYSIS_RESPONSE" | jq -r '.analysis.sections | length // 0')
        TOTAL_LINES=$(echo "$ANALYSIS_RESPONSE" | jq -r '.analysis.total_lines // 0')
        
        echo "📋 分析结果:"
        echo "   标题: $TITLE"
        echo "   章节数: $SECTIONS_COUNT"
        echo "   总行数: $TOTAL_LINES"
        
        # 保存分析结果
        echo "$ANALYSIS_RESPONSE" | jq '.analysis' > test-analysis.json
        echo "💾 分析结果已保存到: test-analysis.json"
        
    else
        echo "❌ Markdown分析失败"
        echo "$ANALYSIS_RESPONSE" | jq -r '.error // "未知错误"'
    fi
fi

# 测试后端集成
echo "5️⃣ 测试后端PDF导入集成..."
if [ -f "$TEST_PDF" ]; then
    # 调用后端PDF导入API
    IMPORT_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: multipart/form-data" \
        -F "file=@$TEST_PDF" \
        "$BACKEND_URL/api/pdf-import/upload")
    
    IMPORT_SUCCESS=$(echo "$IMPORT_RESPONSE" | jq -r '.success // false')
    if [ "$IMPORT_SUCCESS" = "true" ]; then
        echo "✅ 后端PDF导入成功"
        
        # 显示导入结果
        SCALE_TYPE=$(echo "$IMPORT_RESPONSE" | jq -r '.data.metadata.type // "未知类型"')
        TITLE=$(echo "$IMPORT_RESPONSE" | jq -r '.data.metadata.title // "未知标题"')
        
        echo "📈 导入结果:"
        echo "   量表类型: $SCALE_TYPE"
        echo "   标题: $TITLE"
        
    else
        echo "❌ 后端PDF导入失败"
        echo "$IMPORT_RESPONSE" | jq -r '.message // "未知错误"'
    fi
fi

echo ""
echo "🎉 测试完成！检查上述结果确认集成是否正常工作"
echo ""
echo "📁 生成的文件:"
[ -f "test-output.md" ] && echo "   - test-output.md (转换的Markdown)"
[ -f "test-analysis.json" ] && echo "   - test-analysis.json (结构分析结果)"
echo ""
echo "🔗 有用的链接:"
echo "   - Docling服务健康检查: $DOCLING_URL/health"
echo "   - Docling API文档: $DOCLING_URL/docs"
echo "   - 后端API文档: $BACKEND_URL/swagger-ui.html"