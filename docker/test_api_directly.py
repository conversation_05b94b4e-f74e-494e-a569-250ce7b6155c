#!/usr/bin/env python3
"""
直接测试FastAPI服务的转换功能
无需依赖本地docling安装
"""

import requests
import json
from pathlib import Path

def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8088/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务健康: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_pdf_conversion():
    """测试PDF转换"""
    # 创建一个最小的PDF文件用于测试
    test_content = """Hello World PDF Test"""
    
    # 尝试转换现有文件
    input_dir = Path("/Volumes/acasis/Assessment/docker/input")
    pdf_files = list(input_dir.glob("*.pdf"))
    
    if pdf_files:
        # 选择最小的文件
        smallest_pdf = min(pdf_files, key=lambda x: x.stat().st_size)
        print(f"测试文件: {smallest_pdf.name} ({smallest_pdf.stat().st_size} bytes)")
        
        try:
            with open(smallest_pdf, 'rb') as f:
                files = {'file': (smallest_pdf.name, f, 'application/pdf')}
                response = requests.post(
                    "http://localhost:8088/convert", 
                    files=files,
                    timeout=600  # 增加到10分钟，等待模型下载
                )
            
            print(f"HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ PDF转换成功")
                    print(f"文件名: {data['filename']}")
                    print(f"页数: {data['metadata']['page_count']}")
                    print(f"内容长度: {data['metadata']['content_length']}")
                    
                    # 保存结果
                    output_path = Path("/Volumes/acasis/Assessment/docker/output") / f"{smallest_pdf.stem}_fastapi_test.md"
                    output_path.parent.mkdir(exist_ok=True)
                    
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(data['markdown'])
                    
                    print(f"📁 结果保存到: {output_path}")
                    print(f"内容预览: {data['markdown'][:200]}...")
                    return True
                else:
                    print(f"❌ 转换失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    else:
        print("❌ 未找到测试PDF文件")
        return False

def test_concurrent_requests():
    """测试并发请求"""
    import concurrent.futures
    import time
    
    input_dir = Path("/Volumes/acasis/Assessment/docker/input")
    pdf_files = list(input_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 无测试文件，跳过并发测试")
        return
    
    smallest_pdf = min(pdf_files, key=lambda x: x.stat().st_size)
    print(f"\n🚀 并发测试 - 发送5个并发请求...")
    
    def single_request(request_id):
        start_time = time.time()
        try:
            with open(smallest_pdf, 'rb') as f:
                files = {'file': (f'{request_id}_{smallest_pdf.name}', f, 'application/pdf')}
                response = requests.post(
                    "http://localhost:8088/convert", 
                    files=files,
                    timeout=600  # 增加到10分钟，等待模型下载
                )
            end_time = time.time()
            duration = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                success = data.get('success', False)
                return f"请求{request_id}: {'✅' if success else '❌'} {duration:.0f}ms"
            else:
                return f"请求{request_id}: ❌ HTTP {response.status_code} {duration:.0f}ms"
        except Exception as e:
            end_time = time.time()
            duration = (end_time - start_time) * 1000
            return f"请求{request_id}: ❌ {str(e)[:50]} {duration:.0f}ms"
    
    # 并发执行
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(single_request, i+1) for i in range(5)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    for result in results:
        print(result)

def main():
    print("🧪 FastAPI服务测试")
    print("=" * 50)
    
    # 1. 健康检查
    print("\n1. 健康检查")
    if not test_health():
        print("请先启动服务: ./start-docling-fastapi.sh")
        return
    
    # 2. PDF转换测试
    print("\n2. PDF转换测试")
    if test_pdf_conversion():
        print("✅ 单次转换成功")
        
        # 3. 并发测试
        print("\n3. 并发性能测试")
        test_concurrent_requests()
        
        print("\n🎉 FastAPI服务测试完成！")
    else:
        print("❌ PDF转换失败，请检查服务日志")
        print("查看日志: docker logs docling-fastapi-converter")

if __name__ == "__main__":
    main()