# Docling PDF转换服务使用指南

## 概述

本方案使用官方Docling项目的Docker配置，提供PDF到Markdown的转换服务。支持命令行和HTTP API两种使用方式。

## 快速开始

### 1. 构建Docker镜像

```bash
cd docker
./start-docling.sh
```

这将构建包含Docling的Docker镜像，并启动一个交互式容器供测试。

### 2. 使用方式

#### 方式一：命令行转换

转换单个PDF文件：
```bash
./convert-pdf.sh /path/to/your.pdf
```

批量转换（将PDF放入input目录）：
```bash
./convert-pdf.sh --batch
```

#### 方式二：HTTP API服务

启动API服务：
```bash
./docling-api.sh
```

API端点：
- 健康检查：`GET http://localhost:8088/health`
- PDF转换：`POST http://localhost:8088/convert` (multipart/form-data)

### 3. Java集成示例

```java
// 使用RestTemplate调用API
MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
body.add("file", new FileSystemResource(pdfFile));

HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.MULTIPART_FORM_DATA);

HttpEntity<MultiValueMap<String, Object>> requestEntity = 
    new HttpEntity<>(body, headers);

ResponseEntity<String> response = restTemplate.exchange(
    "http://localhost:8088/convert",
    HttpMethod.POST,
    requestEntity,
    String.class
);
```

## 文件说明

- `Dockerfile.docling` - 基于官方配置的Docker镜像定义
- `docling_converter.py` - 命令行转换工具
- `docling_api_server.py` - HTTP API服务器
- `start-docling.sh` - 构建镜像并启动交互式容器
- `convert-pdf.sh` - 便捷的PDF转换脚本
- `docling-api.sh` - 启动HTTP API服务

## 目录结构

```
docker/
├── input/          # PDF输入目录
├── output/         # Markdown输出目录
├── Dockerfile.docling
├── docling_converter.py
├── docling_api_server.py
├── start-docling.sh
├── convert-pdf.sh
└── docling-api.sh
```

## 注意事项

1. 首次构建镜像会下载模型文件，需要一些时间
2. 确保Docker已安装并运行
3. 端口8088用于HTTP API服务
4. 转换大文件可能需要较长时间

## 故障排除

### 镜像构建失败
- 检查网络连接
- 确保Docker有足够的磁盘空间

### 转换失败
- 检查PDF文件是否损坏
- 查看容器日志：`docker logs docling-api`

### API服务无响应
- 确认端口8088未被占用
- 检查防火墙设置

## 性能优化

- 使用CPU版本的PyTorch以减小镜像体积
- 设置`OMP_NUM_THREADS=4`避免线程拥塞
- 可通过调整Docker资源限制优化性能