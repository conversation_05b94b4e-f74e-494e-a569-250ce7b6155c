# Docling多格式中英文识别配置

## 🎯 全面支持的文件格式

已按照最佳实践配置Docling支持以下文件格式，全部启用中英文识别：

### 📄 文档格式
1. **PDF文档** (.pdf)
   - ✅ 专用PDF管道 (StandardPdfPipeline)
   - ✅ PyPdfium后端优化表格支持
   - ✅ TableFormer ACCURATE模式
   - ✅ 中英文OCR ['ch_sim', 'en']
   - ✅ 图像分辨率 2.5x

2. **Word文档** (.docx)
   - ✅ SimplePipeline处理
   - ✅ 页面图像生成
   - ✅ 图像分辨率 2.5x
   - ✅ 支持内嵌图片和表格

3. **Excel表格** (.xlsx)
   - ✅ SimplePipeline处理
   - ✅ 表格图像化支持
   - ✅ 图像分辨率 2.5x
   - ✅ 结构化数据提取

### 🌐 网页格式
4. **HTML页面** (.html, .htm)
   - ✅ HTMLFormatOption专用配置
   - ✅ 支持HTML中的图像
   - ✅ 图像分辨率 2.5x
   - ✅ 保持页面结构

### 🖼️ 图像格式
5. **图片文件** (.png, .jpg, .jpeg, .gif, .bmp, .tiff)
   - ✅ ImageFormatOption专用配置
   - ✅ 最高图像分辨率 3.0x
   - ✅ 中英文OCR优化
   - ✅ 支持扫描文档图片

### 📝 文本格式
6. **Markdown文档** (.md)
   - ✅ 标准文本处理
   - ✅ 保持Markdown结构

7. **纯文本文档** (.txt)
   - ✅ 基础文本提取
   - ✅ 编码自动检测

## 🔧 核心配置特性

### 全局中英文OCR配置
```python
global_ocr_options = EasyOcrOptions()
global_ocr_options.lang = ['ch_sim', 'en']  # 简体中文 + 英文
global_ocr_options.use_gpu = False  # Docker环境CPU优化
```

### 格式特定优化
- **PDF**: TableFormerMode.ACCURATE + PyPdfiumDocumentBackend
- **DOCX/XLSX**: PaginatedPipelineOptions + SimplePipeline  
- **HTML**: HTMLFormatOption + 图像支持
- **图片**: 最高分辨率3.0x + 专用图像处理

### 图像处理优化
- **PDF/DOCX/HTML/XLSX**: 2.5x分辨率增强
- **纯图片文件**: 3.0x分辨率最大化OCR效果
- **页面图像生成**: 所有格式均启用

## 📊 性能预期

### 处理时间估算
- **PDF (10MB, 19页)**: ~2-3分钟
- **DOCX文档**: ~30秒-2分钟
- **XLSX表格**: ~30秒-1分钟
- **HTML页面**: ~15-30秒
- **图片文件**: ~10-30秒

### 识别质量
- **中文识别**: 简体中文模型优化
- **英文识别**: 混合语言支持
- **表格结构**: ACCURATE模式高精度
- **图像文字**: 高分辨率增强

## 🚀 使用建议

### 最佳适用场景
1. **量表文档**: PDF格式推荐，表格识别最优
2. **评估报告**: DOCX格式，结构完整保持
3. **数据表格**: XLSX格式，数据结构化提取
4. **扫描文档**: 图片格式，OCR识别优化
5. **网页内容**: HTML格式，保持原始布局

### 工作流建议
1. **文档预处理**: 选择最合适的文件格式
2. **Docling解析**: 利用优化配置进行结构化提取
3. **AI分析**: 将解析结果送给LM Studio深度分析
4. **结构生成**: 生成数据库表结构和评估逻辑

## 📁 配置文件位置
- **后端配置**: `/docker/docling_fastapi_server.py`
- **前端支持**: `/frontend/admin/src/views/assessment/PdfUpload.vue`
- **AI服务**: `/backend/.../AIProperties.java`

## ✅ 配置验证
- ✅ Docling服务健康状态正常
- ✅ 多格式转换器初始化成功
- ✅ 中英文OCR模型加载完成
- ✅ 前后端格式支持一致
- ✅ AI服务配置*************:1234