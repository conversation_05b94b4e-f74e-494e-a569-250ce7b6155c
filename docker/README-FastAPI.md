# Docling FastAPI服务

基于FastAPI重构的高性能PDF转换服务，解决了原版本的并发限制和性能瓶颈。

## 🚀 主要改进

### 性能提升
- **并发处理**: 支持多工作进程和异步请求处理
- **内存优化**: 使用tmpfs临时文件系统，避免磁盘I/O
- **连接池**: 自动管理HTTP连接，提升吞吐量

### 开发体验
- **自动API文档**: 访问 http://localhost:8088/docs
- **结构化日志**: 标准Python logging模块
- **类型安全**: Pydantic模型验证
- **异常处理**: 全局异常捕获和格式化

### 生产就绪
- **健康检查**: 内置/health端点
- **优雅关闭**: 正确的生命周期管理
- **安全配置**: 非root用户运行
- **资源限制**: Docker资源约束

## 📋 快速开始

### 1. 启动服务
```bash
cd docker
./start-docling-fastapi.sh
```

### 2. 测试服务
```bash
./test-docling-fastapi.sh
```

### 3. 手动测试
```bash
# 健康检查
curl http://localhost:8088/health

# PDF转换
curl -X POST \
  -F "file=@test.pdf" \
  http://localhost:8088/convert
```

## 🐳 Docker配置

### 环境变量
- `WORKERS`: 工作进程数 (默认: 4)
- `HOST`: 绑定地址 (默认: 0.0.0.0)
- `PORT`: 端口号 (默认: 8088)
- `DEBUG`: 调试模式 (默认: false)

### 资源配置
- **内存限制**: 3GB (最小1GB预留)
- **CPU限制**: 2核 (最小1核预留)
- **临时存储**: 1GB tmpfs内存文件系统

## 📊 性能对比

| 指标 | 原版本 (http.server) | FastAPI版本 |
|------|---------------------|-------------|
| 并发请求 | 1 (阻塞) | 多个 (异步) |
| 工作进程 | 1 | 可配置 (默认4) |
| 响应时间 | 高延迟 | 低延迟 |
| 吞吐量 | 低 | 高 |
| 资源利用 | 低 | 高 |

## 🔧 与Java后端集成

Java服务无需修改，API接口保持兼容：

```java
// 现有代码继续工作
@Value("${docling.api.url:http://localhost:8088}")
private String doclingApiUrl;

public String convertPdfToMarkdown(MultipartFile file) {
    // 同样的API调用方式
    Request request = new Request.Builder()
        .url(doclingApiUrl + "/convert")
        .post(requestBody)
        .build();
}
```

## 🔄 服务管理

### 启动服务
```bash
docker-compose -f docker-compose.docling-fastapi.yml up -d
```

### 查看日志
```bash
docker logs -f docling-fastapi-converter
```

### 停止服务
```bash
docker-compose -f docker-compose.docling-fastapi.yml down
```

### 重启服务
```bash
docker-compose -f docker-compose.docling-fastapi.yml restart
```

## 📈 监控和调试

### 服务状态
- 健康检查: http://localhost:8088/health
- API文档: http://localhost:8088/docs
- OpenAPI规范: http://localhost:8088/openapi.json

### 日志级别
- INFO: 基本请求信息
- ERROR: 错误和异常
- DEBUG: 详细调试信息 (设置DEBUG=true)

## 🚨 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口使用
   lsof -i :8088
   
   # 修改端口
   export PORT=8089
   ```

2. **内存不足**
   ```bash
   # 减少工作进程数
   export WORKERS=2
   
   # 调整Docker内存限制
   # 修改docker-compose.yml中的memory限制
   ```

3. **文件权限问题**
   ```bash
   # 确保脚本可执行
   chmod +x *.sh
   ```

## 🔮 未来扩展

该架构为集成LM Studio多模态模型奠定了基础：

1. **多模态处理**: 添加图像理解能力
2. **智能提取**: 结构化数据提取
3. **批量处理**: 多文件并行处理
4. **缓存优化**: Redis结果缓存
5. **负载均衡**: 多实例部署

---

*Note: 该版本完全兼容现有Java后端，可以直接替换原有服务，无需修改业务代码。*