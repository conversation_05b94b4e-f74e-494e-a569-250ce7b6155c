#!/usr/bin/env python3
"""
Docling PDF转Markdown Web服务
提供HTTP API接口，供Java后端调用
"""

import os
import tempfile
import logging
from typing import Optional, Dict, Any
from pathlib import Path

from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Docling PDF转换服务",
    description="将PDF文件转换为Markdown格式，专用于评估量表解析",
    version="1.0.0"
)

# 全局变量存储转换器
converter = None

def init_docling():
    """初始化Docling转换器"""
    global converter
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        from docling.datamodel.pipeline_options import EasyOcrOptions
        
        # 配置PDF管道选项
        pdf_pipeline_options = PdfPipelineOptions(
            do_ocr=True,  # 启用OCR
            do_table_structure=True,  # 启用表格结构识别
            ocr_options=EasyOcrOptions(
                lang=["english", "chinese"],  # 支持英文和中文
                force_full_page_ocr=False,  # 不强制全页OCR
                text_score=0.5  # 文本识别阈值
            )
        )
        
        # 初始化转换器
        converter = DocumentConverter(
            allowed_formats=[InputFormat.PDF],
            pdf_pipeline_options=pdf_pipeline_options
        )
        logger.info("Docling转换器初始化成功")
        return True
        
    except ImportError as e:
        logger.error(f"Docling导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"Docling初始化失败: {e}")
        return False

def install_docling():
    """安装Docling依赖"""
    import subprocess
    import sys
    
    try:
        logger.info("开始安装Docling...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "docling", "fastapi", "uvicorn", "python-multipart"
        ])
        logger.info("Docling安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Docling安装失败: {e}")
        return False

@app.on_event("startup")
async def startup_event():
    """服务启动时初始化"""
    logger.info("启动Docling PDF转换服务...")
    
    # 尝试初始化Docling
    if not init_docling():
        logger.info("首次运行，安装Docling依赖...")
        if install_docling():
            if not init_docling():
                logger.error("Docling初始化最终失败")
                raise RuntimeError("无法初始化Docling服务")
        else:
            logger.error("Docling安装失败")
            raise RuntimeError("无法安装Docling依赖")
    
    logger.info("Docling服务启动完成，监听端口: 8088")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "docling-pdf-converter",
        "version": "1.0.0",
        "docling_available": converter is not None
    }

@app.post("/convert-pdf")
async def convert_pdf(file: UploadFile = File(...)):
    """
    转换PDF为Markdown
    
    Args:
        file: 上传的PDF文件
        
    Returns:
        JSON响应包含转换结果
    """
    if not converter:
        raise HTTPException(status_code=503, detail="Docling服务未初始化")
    
    # 验证文件类型
    if not file.filename.endswith('.pdf'):
        raise HTTPException(status_code=400, detail="只支持PDF文件")
    
    # 验证文件大小 (10MB限制)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="文件大小不能超过10MB")
    
    temp_file = None
    try:
        # 保存临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        logger.info(f"开始转换PDF: {file.filename}, 大小: {len(content)} bytes")
        
        # 使用Docling转换
        source = Path(temp_file.name)
        result = converter.convert(source)
        
        # 导出为Markdown
        markdown_content = result.document.export_to_markdown()
        
        # 如果Markdown内容为空，尝试获取纯文本
        if not markdown_content or markdown_content.strip() == "":
            markdown_content = result.document.export_to_text()
        
        # 提取元数据
        metadata = {
            "filename": file.filename,
            "page_count": len(result.document.pages) if hasattr(result.document, 'pages') else 0,
            "content_length": len(markdown_content),
            "tables_count": count_tables(result.document) if hasattr(result, 'document') else 0,
            "images_count": count_images(result.document) if hasattr(result, 'document') else 0
        }
        
        logger.info(f"转换完成: {file.filename}, Markdown长度: {len(markdown_content)}")
        
        return {
            "success": True,
            "markdown": markdown_content,
            "metadata": metadata,
            "message": "PDF转换成功"
        }
        
    except Exception as e:
        logger.error(f"PDF转换失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"PDF转换失败: {str(e)}",
                "markdown": "",
                "metadata": {}
            }
        )
    finally:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

def count_tables(document) -> int:
    """统计文档中的表格数量"""
    try:
        table_count = 0
        if hasattr(document, 'tables'):
            return len(document.tables)
        # 备用方法：遍历页面查找表格
        if hasattr(document, 'pages'):
            for page in document.pages:
                if hasattr(page, 'tables'):
                    table_count += len(page.tables)
        return table_count
    except Exception as e:
        logger.debug(f"统计表格时出错: {e}")
        return 0

def count_images(document) -> int:
    """统计文档中的图像数量"""
    try:
        image_count = 0
        if hasattr(document, 'figures'):
            return len(document.figures)
        # 备用方法：遍历页面查找图像
        if hasattr(document, 'pages'):
            for page in document.pages:
                if hasattr(page, 'figures'):
                    image_count += len(page.figures)
        return image_count
    except Exception as e:
        logger.debug(f"统计图像时出错: {e}")
        return 0

@app.post("/analyze-markdown")
async def analyze_markdown(markdown_content: str):
    """
    分析Markdown内容，提取评估量表结构
    
    Args:
        markdown_content: Markdown文本内容
        
    Returns:
        结构化的评估量表数据
    """
    try:
        # 简单的Markdown分析
        lines = markdown_content.split('\n')
        
        analysis = {
            "title": "",
            "sections": [],
            "questions": [],
            "score_range": {},
            "total_lines": len(lines)
        }
        
        current_section = None
        current_question = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 提取标题
            if line.startswith('# ') and not analysis["title"]:
                analysis["title"] = line[2:].strip()
            
            # 章节
            elif line.startswith('### '):
                if current_section:
                    analysis["sections"].append(current_section)
                current_section = {
                    "title": line[4:].strip(),
                    "questions": []
                }
            
            # 问题
            elif line.startswith('#### '):
                if current_question and current_section:
                    current_section["questions"].append(current_question)
                current_question = {
                    "title": line[5:].strip(),
                    "options": []
                }
            
            # 选项
            elif line.startswith(('A. ', 'B. ', 'C. ', 'D. ')) and current_question:
                option_text = line[3:].strip()
                score = extract_score(option_text)
                current_question["options"].append({
                    "text": option_text,
                    "score": score
                })
            
            # 分数范围
            elif "总分范围" in line:
                import re
                match = re.search(r'(\d+)-(\d+)分', line)
                if match:
                    analysis["score_range"] = {
                        "min": int(match.group(1)),
                        "max": int(match.group(2))
                    }
        
        # 添加最后的章节和问题
        if current_question and current_section:
            current_section["questions"].append(current_question)
        if current_section:
            analysis["sections"].append(current_section)
        
        return {
            "success": True,
            "analysis": analysis,
            "message": "Markdown分析完成"
        }
        
    except Exception as e:
        logger.error(f"Markdown分析失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "analysis": {}
        }

def extract_score(text: str) -> int:
    """从文本中提取分数"""
    import re
    match = re.search(r'\((\d+)分\)', text)
    return int(match.group(1)) if match else 0

if __name__ == "__main__":
    # 启动服务
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8088,
        log_level="info"
    )