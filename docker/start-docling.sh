#!/bin/bash
# 启动Docling PDF转换服务Docker容器

set -e

echo "🐳 启动Docling PDF转换服务..."

# 进入docker目录
cd "$(dirname "$0")"

# 检查是否已经在运行
if [ "$(docker ps -q -f name=docling-pdf-converter)" ]; then
    echo "⚠️  Docling服务已在运行，先停止现有容器..."
    docker stop docling-pdf-converter
    docker rm docling-pdf-converter
fi

# 构建Docker镜像
echo "🔨 构建Docling Docker镜像..."
docker build -f Dockerfile.docling -t docling-service:latest .

# 启动容器
echo "🚀 启动Docling服务容器..."
docker run -d \
  --name docling-pdf-converter \
  --restart unless-stopped \
  -p 8088:8088 \
  -v /tmp/docling:/tmp/docling \
  --memory="2g" \
  --cpus="1.0" \
  docling-service:latest

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 检查服务健康状态..."
for i in {1..30}; do
  if curl -f http://localhost:8088/health &>/dev/null; then
    echo "✅ Docling服务启动成功！"
    echo "📍 健康检查地址: http://localhost:8088/health"
    echo "📍 API文档地址: http://localhost:8088/docs"
    exit 0
  fi
  echo "等待服务响应... ($i/30)"
  sleep 2
done

echo "❌ 服务启动超时，检查容器状态..."
docker logs docling-pdf-converter --tail 50
exit 1
