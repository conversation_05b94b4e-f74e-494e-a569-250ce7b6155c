# Dockerfile for Docling PDF转Markdown服务
FROM python:3.11-slim-bookworm

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV HF_HOME=/tmp/
ENV TORCH_HOME=/tmp/
ENV OMP_NUM_THREADS=4
ENV GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y libgl1 libglib2.0-0 curl wget git procps \
    && rm -rf /var/lib/apt/lists/*

# 安装Docling (使用CPU版本的PyTorch)
RUN pip install --no-cache-dir docling --extra-index-url https://download.pytorch.org/whl/cpu

# 下载模型
RUN docling-tools models download

# 创建工作目录
WORKDIR /app

# 复制转换脚本
COPY docling_converter.py /app/

# 创建输入输出目录
RUN mkdir -p /app/input /app/output

# 设置默认命令
CMD ["python", "/app/docling_converter.py"]