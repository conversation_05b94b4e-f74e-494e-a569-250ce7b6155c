#!/usr/bin/env python3
"""
使用内置库创建简单的测试内容
"""

import os

# 创建一个HTML文件，可以转换为PDF
html_content = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>老年人能力评估量表</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { text-align: center; color: #333; }
        h2 { color: #555; margin-top: 30px; }
        h3 { color: #666; margin-top: 20px; }
        .question { margin: 15px 0; }
        .option { margin-left: 20px; padding: 5px 0; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>老年人能力评估量表</h1>
    
    <h2>一、基本信息</h2>
    <div class="question">
        <p>1.1 姓名: _______________</p>
        <p>1.2 性别: □ 男  □ 女</p>
        <p>1.3 年龄: _______________</p>
        <p>1.4 评估日期: _______________</p>
    </div>
    
    <h2>二、日常生活能力评估</h2>
    
    <h3>1. 进食</h3>
    <div class="option">A. 完全独立，不需要任何帮助 (10分)</div>
    <div class="option">B. 需要部分帮助（如切碎食物） (5分)</div>
    <div class="option">C. 完全依赖他人喂食 (0分)</div>
    
    <h3>2. 洗澡</h3>
    <div class="option">A. 独立完成洗澡 (5分)</div>
    <div class="option">B. 需要他人帮助 (0分)</div>
    
    <h3>3. 穿衣</h3>
    <div class="option">A. 完全独立穿脱衣服 (10分)</div>
    <div class="option">B. 需要部分帮助 (5分)</div>
    <div class="option">C. 完全依赖他人 (0分)</div>
    
    <h2>三、评分说明</h2>
    <table>
        <tr>
            <th>总分范围</th>
            <th>能力等级</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>41-50分</td>
            <td>完全自理</td>
            <td>日常生活完全独立</td>
        </tr>
        <tr>
            <td>31-40分</td>
            <td>轻度依赖</td>
            <td>需要少量帮助</td>
        </tr>
        <tr>
            <td>21-30分</td>
            <td>中度依赖</td>
            <td>需要较多帮助</td>
        </tr>
        <tr>
            <td>0-20分</td>
            <td>重度依赖</td>
            <td>完全依赖他人照顾</td>
        </tr>
    </table>
    
    <p>总分范围: 0-50分</p>
    <p>评估人员签名：________________</p>
    <p>评估日期：____年____月____日</p>
</body>
</html>
"""

# 创建input目录
os.makedirs("input", exist_ok=True)

# 保存HTML文件
with open("input/test-assessment.html", "w", encoding="utf-8") as f:
    f.write(html_content)

print("✅ 已创建 input/test-assessment.html")
print("✅ 已创建 input/test-content.txt")
print("\n现在你可以在容器内测试转换这些文件。")
print("\n如果需要真正的PDF文件，可以：")
print("1. 使用在线工具将HTML转换为PDF")
print("2. 或者复制任何现有的PDF文件到input目录")