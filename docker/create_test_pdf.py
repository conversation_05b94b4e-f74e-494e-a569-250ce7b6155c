#!/usr/bin/env python3
"""
创建测试PDF文件
用于测试Docling PDF转换功能
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_test_pdf():
    """创建测试PDF文件"""
    
    # 输出文件路径
    output_path = "input/test-assessment.pdf"
    os.makedirs("input", exist_ok=True)
    
    # 创建PDF文档
    doc = SimpleDocTemplate(output_path, pagesize=A4)
    story = []
    
    # 获取样式
    styles = getSampleStyleSheet()
    
    # 自定义标题样式
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        textColor=colors.HexColor('#1a1a1a'),
        spaceAfter=30,
        alignment=TA_CENTER
    )
    
    # 章节标题样式
    section_style = ParagraphStyle(
        'SectionTitle',
        parent=styles['Heading2'],
        fontSize=18,
        textColor=colors.HexColor('#333333'),
        spaceAfter=20,
        spaceBefore=20
    )
    
    # 子标题样式
    subsection_style = ParagraphStyle(
        'SubsectionTitle',
        parent=styles['Heading3'],
        fontSize=14,
        textColor=colors.HexColor('#555555'),
        spaceAfter=15,
        spaceBefore=15
    )
    
    # 添加标题
    story.append(Paragraph("老年人能力评估量表", title_style))
    story.append(Spacer(1, 0.5*cm))
    
    # 添加说明
    story.append(Paragraph("本量表用于评估老年人的日常生活能力和认知功能。请根据实际情况选择最符合的选项。", styles['Normal']))
    story.append(Spacer(1, 1*cm))
    
    # 第一部分：基本信息
    story.append(Paragraph("一、基本信息", section_style))
    
    # 基本信息表格
    info_data = [
        ['姓名：', '_______________', '性别：', '□ 男  □ 女'],
        ['年龄：', '_______________', '评估日期：', '_______________'],
        ['住址：', '_______________', '联系电话：', '_______________']
    ]
    
    info_table = Table(info_data, colWidths=[2*cm, 4*cm, 2*cm, 4*cm])
    info_table.setStyle(TableStyle([
        ('FONT', (0, 0), (-1, -1), 'Helvetica', 11),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))
    
    story.append(info_table)
    story.append(Spacer(1, 1*cm))
    
    # 第二部分：日常生活能力评估
    story.append(Paragraph("二、日常生活能力评估", section_style))
    
    # 评估项目1：进食
    story.append(Paragraph("1. 进食", subsection_style))
    eating_options = [
        "A. 完全独立，不需要任何帮助 (10分)",
        "B. 需要部分帮助（如切碎食物） (5分)",
        "C. 完全依赖他人喂食 (0分)"
    ]
    for option in eating_options:
        story.append(Paragraph(f"□ {option}", styles['Normal']))
    story.append(Spacer(1, 0.5*cm))
    
    # 评估项目2：洗澡
    story.append(Paragraph("2. 洗澡", subsection_style))
    bathing_options = [
        "A. 独立完成洗澡 (5分)",
        "B. 需要他人帮助 (0分)"
    ]
    for option in bathing_options:
        story.append(Paragraph(f"□ {option}", styles['Normal']))
    story.append(Spacer(1, 0.5*cm))
    
    # 评估项目3：穿衣
    story.append(Paragraph("3. 穿衣", subsection_style))
    dressing_options = [
        "A. 完全独立穿脱衣服 (10分)",
        "B. 需要部分帮助 (5分)",
        "C. 完全依赖他人 (0分)"
    ]
    for option in dressing_options:
        story.append(Paragraph(f"□ {option}", styles['Normal']))
    story.append(Spacer(1, 0.5*cm))
    
    # 添加分页
    story.append(PageBreak())
    
    # 第三部分：认知功能评估
    story.append(Paragraph("三、认知功能评估", section_style))
    
    # 评估项目4：定向力
    story.append(Paragraph("4. 定向力", subsection_style))
    story.append(Paragraph("请回答以下问题：", styles['Normal']))
    orientation_questions = [
        "• 今天是几月几日？",
        "• 现在是什么季节？",
        "• 我们现在在什么地方？"
    ]
    for question in orientation_questions:
        story.append(Paragraph(question, styles['Normal']))
    story.append(Spacer(1, 0.5*cm))
    
    # 评估项目5：记忆力
    story.append(Paragraph("5. 记忆力", subsection_style))
    memory_options = [
        "A. 记忆力正常，无明显遗忘 (3分)",
        "B. 偶尔忘记一些事情 (2分)",
        "C. 经常忘记重要事情 (1分)",
        "D. 严重记忆障碍 (0分)"
    ]
    for option in memory_options:
        story.append(Paragraph(f"□ {option}", styles['Normal']))
    story.append(Spacer(1, 1*cm))
    
    # 评分说明
    story.append(Paragraph("四、评分说明", section_style))
    
    scoring_data = [
        ['总分范围', '能力等级', '说明'],
        ['41-50分', '完全自理', '日常生活完全独立'],
        ['31-40分', '轻度依赖', '需要少量帮助'],
        ['21-30分', '中度依赖', '需要较多帮助'],
        ['0-20分', '重度依赖', '完全依赖他人照顾']
    ]
    
    scoring_table = Table(scoring_data, colWidths=[3*cm, 3*cm, 8*cm])
    scoring_table.setStyle(TableStyle([
        ('FONT', (0, 0), (-1, -1), 'Helvetica', 11),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
    ]))
    
    story.append(scoring_table)
    story.append(Spacer(1, 1*cm))
    
    # 签名区域
    story.append(Paragraph("评估人员签名：________________", styles['Normal']))
    story.append(Spacer(1, 0.5*cm))
    story.append(Paragraph("评估日期：____年____月____日", styles['Normal']))
    
    # 生成PDF
    doc.build(story)
    print(f"✅ 测试PDF已创建: {output_path}")
    
    # 同时创建一个简单的测试PDF
    simple_output = "input/simple-test.pdf"
    doc2 = SimpleDocTemplate(simple_output, pagesize=A4)
    story2 = []
    
    story2.append(Paragraph("简单测试文档", title_style))
    story2.append(Spacer(1, 1*cm))
    story2.append(Paragraph("这是一个简单的测试PDF文档。", styles['Normal']))
    story2.append(Spacer(1, 0.5*cm))
    story2.append(Paragraph("包含以下内容：", styles['Normal']))
    story2.append(Paragraph("1. 标题", styles['Normal']))
    story2.append(Paragraph("2. 段落文本", styles['Normal']))
    story2.append(Paragraph("3. 列表项目", styles['Normal']))
    
    doc2.build(story2)
    print(f"✅ 简单测试PDF已创建: {simple_output}")

if __name__ == "__main__":
    try:
        create_test_pdf()
    except ImportError:
        print("需要安装 reportlab 库来创建PDF")
        print("请运行: pip install reportlab")