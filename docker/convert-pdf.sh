#!/bin/bash
# 使用Docling Docker容器转换PDF文件

set -e

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 <PDF文件路径>"
    echo "示例: $0 test.pdf"
    echo "批量转换: $0 --batch"
    exit 1
fi

# 进入docker目录
cd "$(dirname "$0")"

# 创建输入输出目录
mkdir -p ./input ./output

# 检查Docker镜像是否存在
if [[ "$(docker images -q docling:latest 2> /dev/null)" == "" ]]; then
    echo "Docker镜像不存在，请先运行 ./start-docling.sh 构建镜像"
    exit 1
fi

if [ "$1" == "--batch" ]; then
    # 批量转换模式
    echo "📁 批量转换模式"
    echo "请将PDF文件放入 $(pwd)/input 目录"
    echo "按回车键开始转换..."
    read
    
    # 运行批量转换
    docker run --rm \
        -v "$(pwd)/input:/app/input" \
        -v "$(pwd)/output:/app/output" \
        docling:latest \
        python /app/docling_converter.py
        
    echo "✅ 批量转换完成！"
    echo "📁 结果保存在: $(pwd)/output"
else
    # 单文件转换模式
    PDF_FILE="$1"
    
    if [ ! -f "$PDF_FILE" ]; then
        echo "❌ 文件不存在: $PDF_FILE"
        exit 1
    fi
    
    # 获取文件名
    FILENAME=$(basename "$PDF_FILE")
    
    # 复制文件到输入目录
    cp "$PDF_FILE" ./input/
    
    echo "📄 转换PDF: $FILENAME"
    
    # 运行转换
    docker run --rm \
        -v "$(pwd)/input:/app/input" \
        -v "$(pwd)/output:/app/output" \
        docling:latest \
        python /app/docling_converter.py --file "/app/input/$FILENAME" --output /app/output
        
    # 清理输入文件
    rm -f "./input/$FILENAME"
    
    # 显示结果
    MD_FILE="./output/${FILENAME%.pdf}.md"
    if [ -f "$MD_FILE" ]; then
        echo "✅ 转换成功！"
        echo "📝 Markdown文件: $MD_FILE"
        echo "📊 元数据文件: ./output/${FILENAME%.pdf}_metadata.json"
    else
        echo "❌ 转换失败"
        exit 1
    fi
fi