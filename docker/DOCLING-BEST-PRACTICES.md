# Docling FastAPI 服务最佳实践指南

## 概述

Docling FastAPI 服务支持在不同环境下的智能部署，自动选择最优的运行模式。

## 🚀 快速启动

使用优化启动脚本，自动检测环境并选择最佳配置：

```bash
./start-docling-optimized.sh
```

## 📱 环境支持

### 1. Apple Silicon Mac (M1/M2/M3/M4) - 原生模式
- **优势**: 支持 MPS (Metal Performance Shaders) 加速
- **性能**: 最佳，可获得 GPU 加速
- **适用**: 开发环境、本地测试

### 2. Docker 容器模式 - 所有平台
- **优势**: 环境隔离、易于部署
- **性能**: CPU 优化，多线程并行
- **适用**: 生产环境、CI/CD、非 Mac 平台

## 🔧 性能优化配置

### Docker 环境优化

```yaml
# docker-compose.docling-fastapi.yml
environment:
  # CPU 优化
  - OMP_NUM_THREADS=4        # OpenMP 线程数
  - MKL_NUM_THREADS=4        # Intel MKL 线程数
  - TORCH_NUM_THREADS=4      # PyTorch 线程数
  - PYTORCH_DISABLE_PIN_MEMORY=1  # 禁用 pin_memory
  - PYTHONWARNINGS=ignore::UserWarning:torch.utils.data.dataloader
```

### 原生 macOS 优化

```bash
# 环境变量设置
export PYTORCH_ENABLE_MPS_FALLBACK=1  # 启用 MPS fallback
export TOKENIZERS_PARALLELISM=false   # 避免并行化警告
```

## 📊 性能对比

| 环境 | 处理速度 | 资源占用 | 推荐场景 |
|------|----------|----------|----------|
| macOS + MPS | ⚡⚡⚡⚡⚡ | 中等 | 开发、测试 |
| Docker + CPU | ⚡⚡⚡ | 较高 | 生产部署 |
| 普通 CPU | ⚡⚡ | 高 | 兼容性优先 |

## 🛠️ 常见问题解决

### 1. pin_memory 警告

**问题**: `UserWarning: 'pin_memory' argument is set as true but no accelerator is found`

**解决方案**: 
- Docker 环境已通过环境变量自动处理
- 不影响功能，仅是性能优化警告

### 2. 转换速度慢

**优化建议**:
1. 使用原生 macOS 模式（如果有 Apple Silicon）
2. 增加 Docker 资源限制：
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '8.0'
         memory: 8G
   ```
3. 减少并发 workers 数量

### 3. 内存不足

**解决方案**:
- 减少 WORKERS 数量（默认 4 → 2）
- 增加 Docker 内存限制
- 使用分页处理大文件

## 🎯 部署建议

### 开发环境
```bash
# Apple Silicon Mac
./start-docling-optimized.sh  # 自动使用 MPS
```

### 生产环境
```bash
# 使用 Docker Compose
docker-compose -f docker-compose.docling-fastapi.yml up -d

# 使用 PM2 管理（原生模式）
pm2 start docling_fastapi_server.py --interpreter python3
```

### CI/CD 集成
```yaml
# GitHub Actions 示例
- name: Start Docling Service
  run: |
    docker-compose -f docker-compose.docling-fastapi.yml up -d
    ./scripts/wait-for-healthy.sh http://localhost:8088/health
```

## 📈 监控和日志

### 查看日志
```bash
# Docker 模式
docker-compose -f docker-compose.docling-fastapi.yml logs -f

# 原生模式
tail -f logs/docling.log
```

### 性能监控
```bash
# 查看资源使用
docker stats docling-fastapi-converter

# 健康检查
curl http://localhost:8088/health
```

## 🔒 安全建议

1. **生产环境**：使用反向代理（Nginx）
2. **API 限流**：配置请求频率限制
3. **文件大小限制**：默认 50MB，可调整
4. **超时设置**：大文件处理时适当增加

## 📝 配置示例

### 最小化部署
```bash
# 仅 CPU，最少资源
WORKERS=1 OMP_NUM_THREADS=2 docker-compose up
```

### 高性能部署
```bash
# Apple Silicon 原生
PYTORCH_ENABLE_MPS_FALLBACK=1 python3 docling_fastapi_server.py
```

### 负载均衡部署
```bash
# 启用 Nginx 代理
docker-compose --profile proxy up -d
```

## 🚨 注意事项

1. **Docker 中无法使用 MPS**：这是 Docker 的限制，不是配置问题
2. **首次启动较慢**：需要下载 AI 模型（约 200MB）
3. **模型缓存**：建议持久化 `/app/models` 目录

## 📞 支持

- 健康检查：`http://localhost:8088/health`
- API 文档：`http://localhost:8088/docs`
- 设备信息：`http://localhost:8088/device`（需添加此端点）