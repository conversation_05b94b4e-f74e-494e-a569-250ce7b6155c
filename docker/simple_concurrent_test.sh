#!/bin/bash

echo "🚀 简单并发测试 - 4个Worker性能验证"
echo "============================================="

PDF_FILE="/Volumes/acasis/Assessment/docker/input/国标评估报告模板.pdf"

# 健康检查
echo "🔍 健康检查..."
if ! curl -s http://localhost:8088/health > /dev/null; then
    echo "❌ 服务不可用"
    exit 1
fi
echo "✅ 服务正常"

# 单个请求基准测试
echo -e "\n📊 单个请求基准测试..."
START_TIME=$(date +%s)
RESULT=$(curl -X POST http://localhost:8088/convert \
    -F "file=@$PDF_FILE" \
    --max-time 300 -s | jq '.success, .metadata.page_count, .metadata.content_length')
END_TIME=$(date +%s)
SINGLE_DURATION=$((END_TIME - START_TIME))
echo "单个请求耗时: ${SINGLE_DURATION}秒"
echo "结果: $RESULT"

# 2个并发请求测试
echo -e "\n🔥 2个并发请求测试..."
START_TIME=$(date +%s)
curl -X POST http://localhost:8088/convert -F "file=@$PDF_FILE" --max-time 300 -s > /tmp/result1.json &
curl -X POST http://localhost:8088/convert -F "file=@$PDF_FILE" --max-time 300 -s > /tmp/result2.json &
wait
END_TIME=$(date +%s)
CONCURRENT_2_DURATION=$((END_TIME - START_TIME))

echo "2个并发请求耗时: ${CONCURRENT_2_DURATION}秒"
echo "请求1结果: $(jq '.success, .metadata.page_count' /tmp/result1.json 2>/dev/null || echo '失败')"
echo "请求2结果: $(jq '.success, .metadata.page_count' /tmp/result2.json 2>/dev/null || echo '失败')"

# 3个并发请求测试
echo -e "\n🚀 3个并发请求测试..."
START_TIME=$(date +%s)
curl -X POST http://localhost:8088/convert -F "file=@$PDF_FILE" --max-time 300 -s > /tmp/result1.json &
curl -X POST http://localhost:8088/convert -F "file=@$PDF_FILE" --max-time 300 -s > /tmp/result2.json &
curl -X POST http://localhost:8088/convert -F "file=@$PDF_FILE" --max-time 300 -s > /tmp/result3.json &
wait
END_TIME=$(date +%s)
CONCURRENT_3_DURATION=$((END_TIME - START_TIME))

echo "3个并发请求耗时: ${CONCURRENT_3_DURATION}秒"
echo "请求1结果: $(jq '.success, .metadata.page_count' /tmp/result1.json 2>/dev/null || echo '失败')"
echo "请求2结果: $(jq '.success, .metadata.page_count' /tmp/result2.json 2>/dev/null || echo '失败')"
echo "请求3结果: $(jq '.success, .metadata.page_count' /tmp/result3.json 2>/dev/null || echo '失败')"

# 性能分析
echo -e "\n📈 性能分析："
echo "单个请求: ${SINGLE_DURATION}秒"
echo "2个并发: ${CONCURRENT_2_DURATION}秒 (效率: $(echo "scale=1; $SINGLE_DURATION * 2 / $CONCURRENT_2_DURATION" | bc)x)"
echo "3个并发: ${CONCURRENT_3_DURATION}秒 (效率: $(echo "scale=1; $SINGLE_DURATION * 3 / $CONCURRENT_3_DURATION" | bc)x)"

# 检查当前资源使用
echo -e "\n💾 当前资源使用："
docker stats docling-fastapi-converter --no-stream

# 清理临时文件
rm -f /tmp/result*.json

echo -e "\n🎉 并发测试完成！"
echo "💡 建议：如果并发效率低于2x，考虑调整worker数量或资源限制"