#!/bin/bash
# 启动Docling PDF转换服务 - 持久化模型版本

set -e

echo "🐳 启动Docling PDF转换服务（持久化模型版本）..."

# 进入docker目录
cd "$(dirname "$0")"

# 创建本地目录用于存储模型和数据
mkdir -p ./models ./input ./output

# 清理旧容器
docker rm -f docling-converter 2>/dev/null || true

# 构建持久化版本的Docker镜像
echo "🔨 构建Docling Docker镜像（持久化模型）..."
docker build -f Dockerfile.docling-persistent -t docling-persistent:latest .

echo "🚀 启动Docling容器（持久化模型）..."

# 启动容器，挂载模型目录以实现持久化
docker run -it --rm \
  --name docling-converter \
  -v "$(pwd)/models:/app/models" \
  -v "$(pwd)/input:/app/input" \
  -v "$(pwd)/output:/app/output" \
  docling-persistent:latest \
  /bin/bash

echo "✅ Docling服务已启动！"
echo "📁 模型目录: $(pwd)/models (持久化存储)"
echo "📁 输入目录: $(pwd)/input"
echo "📁 输出目录: $(pwd)/output"
echo ""
echo "模型将被下载到本地 models 目录，下次启动时会重复使用。"