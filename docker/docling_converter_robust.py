#!/usr/bin/env python3
"""
增强版Docling PDF转换器
添加错误处理和备用转换方案
"""

import os
import sys
import json
import argparse
from pathlib import Path

def convert_with_docling(pdf_path, output_dir):
    """使用Docling转换PDF"""
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        
        print(f"尝试使用Docling转换: {pdf_path}")
        
        # 简化的配置，禁用可能引起问题的功能
        pipeline_options = PdfPipelineOptions(
            do_ocr=False,  # 先禁用OCR，避免字体问题
            do_table_structure=False,  # 禁用表格结构检测
        )
        
        # 初始化转换器
        converter = DocumentConverter(
            allowed_formats=[InputFormat.PDF],
            pdf_pipeline_options=pipeline_options
        )
        
        # 转换文档
        result = converter.convert(pdf_path)
        
        # 导出为Markdown
        markdown_content = result.document.export_to_markdown()
        
        if not markdown_content or markdown_content.strip() == "":
            # 如果Markdown为空，尝试获取纯文本
            print("Markdown内容为空，尝试获取纯文本...")
            markdown_content = result.document.export_to_text()
        
        return True, markdown_content, {
            "method": "docling",
            "page_count": len(result.document.pages) if hasattr(result.document, 'pages') else 0,
            "content_length": len(markdown_content)
        }
        
    except Exception as e:
        print(f"Docling转换失败: {str(e)}")
        return False, "", {"error": str(e)}

def convert_with_basic_text(pdf_path):
    """使用基础文本提取作为备用方案"""
    try:
        print(f"尝试基础文本提取: {pdf_path}")
        
        # 这里可以添加其他PDF文本提取库，比如PyPDF2或pdfplumber
        # 当前返回简单的错误信息
        return False, "", {"error": "需要安装备用PDF处理库"}
        
    except Exception as e:
        return False, "", {"error": str(e)}

def convert_pdf_to_markdown(pdf_path, output_dir):
    """转换PDF为Markdown，使用多种备用方案"""
    try:
        # 方案1：使用Docling（简化配置）
        success, content, metadata = convert_with_docling(pdf_path, output_dir)
        
        if success and content:
            # 保存结果
            pdf_name = Path(pdf_path).stem
            md_path = Path(output_dir) / f"{pdf_name}.md"
            
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 保存元数据
            metadata.update({
                "source_pdf": str(pdf_path),
                "output_md": str(md_path),
                "status": "success"
            })
            
            meta_path = Path(output_dir) / f"{pdf_name}_metadata.json"
            with open(meta_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"转换成功: {md_path}")
            return True, metadata
        
        # 方案2：基础文本提取（备用）
        print("尝试备用转换方案...")
        success, content, metadata = convert_with_basic_text(pdf_path)
        
        if success:
            # 保存备用方案结果
            pdf_name = Path(pdf_path).stem
            md_path = Path(output_dir) / f"{pdf_name}_basic.md"
            
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"备用方案转换成功: {md_path}")
            return True, metadata
        
        # 所有方案都失败
        error_info = {
            "source_pdf": str(pdf_path),
            "status": "failed",
            "error": "所有转换方案都失败",
            "details": metadata
        }
        
        return False, error_info
        
    except Exception as e:
        print(f"转换过程出错: {str(e)}")
        return False, {"error": str(e), "source_pdf": str(pdf_path), "status": "failed"}

def test_simple_conversion():
    """测试简单的Docling功能"""
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat
        
        print("测试Docling基础功能...")
        
        # 最简单的配置
        converter = DocumentConverter(allowed_formats=[InputFormat.PDF])
        print("✅ Docling初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ Docling初始化失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='增强版Docling PDF转换器')
    parser.add_argument('--input', '-i', default='/app/input', help='输入目录')
    parser.add_argument('--output', '-o', default='/app/output', help='输出目录')
    parser.add_argument('--file', '-f', help='单个PDF文件路径')
    parser.add_argument('--test', action='store_true', help='测试Docling功能')
    
    args = parser.parse_args()
    
    if args.test:
        test_simple_conversion()
        return
    
    # 确保输出目录存在
    Path(args.output).mkdir(parents=True, exist_ok=True)
    
    if args.file:
        # 转换单个文件
        if not os.path.exists(args.file):
            print(f"文件不存在: {args.file}")
            sys.exit(1)
        
        success, result = convert_pdf_to_markdown(args.file, args.output)
        if success:
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"转换失败: {result}")
            sys.exit(1)
    else:
        # 批量转换
        input_path = Path(args.input)
        pdf_files = list(input_path.glob("*.pdf"))
        
        if not pdf_files:
            print(f"在 {args.input} 中未找到PDF文件")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        results = []
        for pdf_file in pdf_files:
            success, metadata = convert_pdf_to_markdown(pdf_file, args.output)
            results.append(metadata)
        
        # 保存批量结果
        results_path = Path(args.output) / "batch_results.json"
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n批量转换完成，结果: {results_path}")

if __name__ == "__main__":
    main()