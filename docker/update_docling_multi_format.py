#!/usr/bin/env python3
"""
临时脚本：更新Docling服务以支持多格式
直接运行来替代当前的服务
"""

import os
import json
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any
import uvicorn

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# 尝试导入docling相关模块
try:
    from docling.document_converter import DocumentConverter, PdfFormatOption, WordFormatOption, HTMLFormatOption, ImageFormatOption
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions, PaginatedPipelineOptions, TableFormerMode, EasyOcrOptions
    from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend
    from docling.pipeline.standard_pdf_pipeline import StandardPdfPipeline
    from docling.pipeline.simple_pipeline import SimplePipeline
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Docling 多格式转换服务",
    description="支持多种格式的文档转换API",
    version="2.1.0",
)

# 全局转换器
converter = None

class ConversionResponse(BaseModel):
    success: bool
    filename: str
    markdown: str = None
    metadata: Dict[str, Any] = None
    error: str = None

def init_converter():
    """初始化多格式转换器"""
    global converter
    
    if not DOCLING_AVAILABLE:
        logger.error("Docling不可用，请安装docling包")
        return False
    
    try:
        logger.info("初始化多格式转换器...")
        
        # 配置多格式支持
        converter = DocumentConverter(
            allowed_formats=[
                InputFormat.PDF,      # PDF文档
                InputFormat.DOCX,     # Word文档  
                InputFormat.HTML,     # HTML页面
                InputFormat.HTM,      # HTM页面
                InputFormat.XLSX,     # Excel表格
                InputFormat.IMAGE,    # 图片文件
                InputFormat.MD,       # Markdown文档
                InputFormat.TXT       # 纯文本文件
            ]
        )
        
        logger.info("多格式转换器初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"转换器初始化失败: {e}")
        return False

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "docling-multi-format", 
        "version": "2.1.0",
        "docling_available": DOCLING_AVAILABLE,
        "converter_ready": converter is not None
    }

@app.post("/convert")
async def convert_document(file: UploadFile = File(...)):
    """转换多格式文档为Markdown"""
    
    # 支持的文件扩展名
    supported_extensions = {
        '.pdf', '.docx', '.xlsx', '.html', '.htm', '.md', '.txt',
        '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'
    }
    
    # 验证文件类型
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名无效")
        
    file_ext = '.' + file.filename.lower().split('.')[-1]
    if file_ext not in supported_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件格式: {file_ext}。支持的格式: {', '.join(sorted(supported_extensions))}"
        )
    
    if not converter:
        raise HTTPException(status_code=503, detail="转换器未初始化")
    
    logger.info(f"开始转换文档: {file.filename} (格式: {file_ext})")
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_path = tmp_file.name
        
        try:
            # 执行转换
            result = converter.convert(tmp_path)
            
            # 导出为Markdown
            markdown_content = result.document.export_to_markdown()
            
            # 构建元数据
            metadata = {
                "page_count": len(result.document.pages) if hasattr(result.document, 'pages') else 0,
                "content_length": len(markdown_content),
                "file_size": len(content),
                "format": file_ext
            }
            
            logger.info(f"转换成功: {file.filename} -> {len(markdown_content)} 字符")
            
            return ConversionResponse(
                success=True,
                filename=file.filename,
                markdown=markdown_content,
                metadata=metadata
            )
            
        finally:
            # 清理临时文件
            try:
                os.unlink(tmp_path)
            except OSError:
                pass
                
    except Exception as e:
        logger.error(f"转换失败 {file.filename}: {str(e)}")
        return ConversionResponse(
            success=False,
            filename=file.filename,
            error=str(e)
        )

@app.on_event("startup")
async def startup_event():
    """启动时初始化转换器"""
    success = init_converter()
    if not success:
        logger.error("服务启动失败")

if __name__ == "__main__":
    print("启动Docling多格式转换服务...")
    uvicorn.run(app, host="0.0.0.0", port=8088, log_level="info")