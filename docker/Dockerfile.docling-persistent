# Dockerfile for Docling PDF转Markdown服务 - 持久化模型版本
FROM python:3.11-slim-bookworm

# 设置环境变量 - 将模型保存到持久化目录
ENV PYTHONUNBUFFERED=1
ENV HF_HOME=/app/models/huggingface
ENV TORCH_HOME=/app/models/torch
ENV OMP_NUM_THREADS=4
ENV GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no"

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y libgl1 libglib2.0-0 curl wget git procps \
    && rm -rf /var/lib/apt/lists/*

# 安装Docling (使用CPU版本的PyTorch)
RUN pip install --no-cache-dir docling --extra-index-url https://download.pytorch.org/whl/cpu

# 创建工作目录和模型目录
WORKDIR /app
RUN mkdir -p /app/models/huggingface /app/models/torch /app/input /app/output

# 下载模型到持久化目录
RUN docling-tools models download

# 复制转换脚本
COPY docling_converter.py /app/
COPY docling_api_server.py /app/

# 设置权限
RUN chmod -R 755 /app

# 设置默认命令
CMD ["python", "/app/docling_converter.py"]