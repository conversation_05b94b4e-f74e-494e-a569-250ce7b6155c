#!/bin/bash

# Docling FastAPI服务启动脚本

set -e

echo "启动Docling FastAPI服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 切换到脚本目录
cd "$(dirname "$0")"

# 构建并启动服务
echo "🔨 构建Docker镜像..."
docker-compose -f docker-compose.docling-fastapi.yml build

echo "🚀 启动服务..."
docker-compose -f docker-compose.docling-fastapi.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 检查服务状态..."
if curl -f http://localhost:8088/health > /dev/null 2>&1; then
    echo "✅ Docling FastAPI服务启动成功！"
    echo ""
    echo "服务信息:"
    echo "  - API地址: http://localhost:8088"
    echo "  - 健康检查: http://localhost:8088/health"
    echo "  - API文档: http://localhost:8088/docs"
    echo "  - 容器名称: docling-fastapi-converter"
    echo ""
    echo "使用以下命令查看日志:"
    echo "  docker logs -f docling-fastapi-converter"
    echo ""
    echo "使用以下命令停止服务:"
    echo "  docker-compose -f docker-compose.docling-fastapi.yml down"
else
    echo "❌ 服务启动失败，请检查日志:"
    echo "  docker logs docling-fastapi-converter"
    exit 1
fi