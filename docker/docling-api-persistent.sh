#!/bin/bash
# Docling API服务 - 持久化模型版本

set -e

echo "🚀 启动Docling API服务（持久化模型版本）..."

# 进入docker目录
cd "$(dirname "$0")"

# 创建本地目录
mkdir -p ./models ./input ./output

# 清理旧容器
docker rm -f docling-api 2>/dev/null || true

# 检查持久化镜像是否存在
if [[ "$(docker images -q docling-persistent:latest 2> /dev/null)" == "" ]]; then
    echo "构建持久化Docker镜像..."
    docker build -f Dockerfile.docling-persistent -t docling-persistent:latest .
fi

echo "启动Docling API服务..."

# 运行容器并启动API服务，使用持久化模型存储
docker run -d \
    --name docling-api \
    -p 8088:8088 \
    -v "$(pwd)/models:/app/models" \
    -v "$(pwd)/input:/app/input" \
    -v "$(pwd)/output:/app/output" \
    docling-persistent:latest \
    python /app/docling_api_server.py

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
if curl -s http://localhost:8088/health > /dev/null; then
    echo "✅ Docling API服务已启动！"
    echo "📍 API地址: http://localhost:8088"
    echo "📍 健康检查: http://localhost:8088/health"
    echo "📍 转换接口: POST http://localhost:8088/convert"
    echo "📁 模型存储: $(pwd)/models (持久化)"
    echo ""
    echo "模型已持久化存储，下次启动会更快！"
else
    echo "❌ 服务启动失败，检查日志："
    docker logs docling-api
    exit 1
fi