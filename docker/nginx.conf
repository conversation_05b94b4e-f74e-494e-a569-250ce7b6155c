events {
    worker_connections 1024;
}

http {
    upstream docling_backend {
        server docling-fastapi:8088;
    }

    server {
        listen 80;
        client_max_body_size 100M;
        
        location / {
            proxy_pass http://docling_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
        
        location /health {
            proxy_pass http://docling_backend/health;
            access_log off;
        }
    }
}