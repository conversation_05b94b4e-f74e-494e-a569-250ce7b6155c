# Docker Compose配置 - Docling PDF转换服务
version: '3.8'

services:
  docling-service:
    build:
      context: .
      dockerfile: Dockerfile.docling
    container_name: docling-pdf-converter
    ports:
      - "8088:8088"
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=info
    volumes:
      # 临时文件存储
      - docling-temp:/tmp/docling
      # 日志存储
      - docling-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8088/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    networks:
      - docling-network

volumes:
  docling-temp:
    driver: local
  docling-logs:
    driver: local

networks:
  docling-network:
    driver: bridge