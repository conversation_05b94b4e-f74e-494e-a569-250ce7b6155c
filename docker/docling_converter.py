#!/usr/bin/env python3
"""
Docling PDF转Markdown转换器
简单的命令行工具，用于批量转换PDF文件
"""

import os
import sys
import json
import argparse
from pathlib import Path
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat

def convert_pdf_to_markdown(pdf_path, output_dir):
    """转换单个PDF文件为Markdown"""
    try:
        # 初始化转换器
        converter = DocumentConverter(
            allowed_formats=[InputFormat.PDF],
        )
        
        # 转换文档
        print(f"正在转换: {pdf_path}")
        result = converter.convert(pdf_path)
        
        # 导出为Markdown
        markdown_content = result.document.export_to_markdown()
        
        # 保存Markdown文件
        pdf_name = Path(pdf_path).stem
        md_path = Path(output_dir) / f"{pdf_name}.md"
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        # 保存元数据
        metadata = {
            "source_pdf": str(pdf_path),
            "output_md": str(md_path),
            "page_count": len(result.document.pages) if hasattr(result.document, 'pages') else 0,
            "content_length": len(markdown_content),
            "status": "success"
        }
        
        meta_path = Path(output_dir) / f"{pdf_name}_metadata.json"
        with open(meta_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        print(f"转换成功: {md_path}")
        return True, metadata
        
    except Exception as e:
        print(f"转换失败 {pdf_path}: {str(e)}")
        return False, {"error": str(e), "source_pdf": str(pdf_path), "status": "failed"}

def batch_convert(input_dir, output_dir):
    """批量转换目录中的所有PDF文件"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 确保输出目录存在
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 查找所有PDF文件
    pdf_files = list(input_path.glob("*.pdf"))
    
    if not pdf_files:
        print(f"在 {input_dir} 中未找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件")
    
    results = []
    for pdf_file in pdf_files:
        success, metadata = convert_pdf_to_markdown(pdf_file, output_dir)
        results.append(metadata)
    
    # 保存批量转换结果
    results_path = output_path / "batch_results.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n批量转换完成，结果保存在: {results_path}")

def main():
    parser = argparse.ArgumentParser(description='Docling PDF转Markdown转换器')
    parser.add_argument('--input', '-i', default='/app/input', help='输入目录或PDF文件路径')
    parser.add_argument('--output', '-o', default='/app/output', help='输出目录')
    parser.add_argument('--file', '-f', help='单个PDF文件路径')
    
    args = parser.parse_args()
    
    if args.file:
        # 转换单个文件
        if not os.path.exists(args.file):
            print(f"文件不存在: {args.file}")
            sys.exit(1)
        success, metadata = convert_pdf_to_markdown(args.file, args.output)
        if success:
            print(json.dumps(metadata, ensure_ascii=False, indent=2))
    else:
        # 批量转换
        batch_convert(args.input, args.output)

if __name__ == "__main__":
    main()