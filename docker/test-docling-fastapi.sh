#!/bin/bash

# Docling FastAPI服务测试脚本

set -e

echo "测试Docling FastAPI服务..."

# 配置
API_URL="http://localhost:8088"
TEST_PDF="./input/test_assessment.pdf"

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -f "$API_URL/health" > /dev/null 2>&1; then
    echo "❌ 服务未运行，请先启动服务"
    echo "运行: ./start-docling-fastapi.sh"
    exit 1
fi

echo "✅ 服务运行正常"

# 测试健康检查
echo ""
echo "📋 健康检查响应:"
curl -s "$API_URL/health" | python3 -m json.tool

# 测试PDF转换
if [ -f "$TEST_PDF" ]; then
    echo ""
    echo "📄 测试PDF转换..."
    
    # 使用curl上传PDF文件
    RESPONSE=$(curl -s -X POST \
        -F "file=@$TEST_PDF" \
        "$API_URL/convert")
    
    # 检查响应
    SUCCESS=$(echo "$RESPONSE" | python3 -c "import json,sys; print(json.load(sys.stdin)['success'])" 2>/dev/null || echo "false")
    
    if [ "$SUCCESS" = "True" ]; then
        echo "✅ PDF转换成功"
        echo ""
        echo "📊 转换结果摘要:"
        echo "$RESPONSE" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(f'文件名: {data[\"filename\"]}')
print(f'页数: {data[\"metadata\"][\"page_count\"]}')
print(f'内容长度: {data[\"metadata\"][\"content_length\"]} 字符')
print(f'文件大小: {data[\"metadata\"][\"file_size\"]} 字节')
"
    else
        echo "❌ PDF转换失败"
        echo "错误信息:"
        echo "$RESPONSE" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print(data.get('error', '未知错误'))
except:
    print('响应格式错误')
    print(sys.stdin.read())
"
        exit 1
    fi
else
    echo "⚠️  测试PDF文件不存在: $TEST_PDF"
    echo "请确保测试文件存在或使用其他PDF文件测试"
fi

# 性能测试（可选）
echo ""
echo "⚡ 并发性能测试..."
echo "发送5个并发请求..."

for i in {1..5}; do
    (
        if [ -f "$TEST_PDF" ]; then
            START_TIME=$(date +%s%N)
            curl -s -X POST -F "file=@$TEST_PDF" "$API_URL/convert" > /dev/null
            END_TIME=$(date +%s%N)
            DURATION=$((($END_TIME - $START_TIME) / 1000000))
            echo "请求 $i: ${DURATION}ms"
        fi
    ) &
done

wait

echo ""
echo "🎉 测试完成！"