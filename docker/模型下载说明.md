# Docling模型下载和存储说明

## 当前情况

你观察到的模型下载行为是正常的。以下是详细解释：

### 问题分析

1. **当前配置的问题**：
   - 原始Dockerfile设置了 `ENV HF_HOME=/tmp/` 和 `ENV TORCH_HOME=/tmp/`
   - 模型被下载到容器的临时目录 `/tmp/`
   - 容器重启后，`/tmp/` 目录内容会丢失
   - 每次运行都需要重新下载模型（约几百MB到几GB）

2. **模型下载内容**：
   - Hugging Face的AI模型（用于文档解析）
   - PyTorch模型文件
   - OCR识别模型
   - 表格检测模型

### 解决方案

我已经创建了持久化存储的版本：

#### 方案1：持久化模型存储

使用新的持久化版本：

```bash
# 使用持久化模型版本
./start-docling-persistent.sh

# 或启动API服务
./docling-api-persistent.sh
```

**优势**：
- 模型下载到宿主机的 `./models` 目录
- 容器重启后模型仍然存在
- 只需要下载一次，后续启动会很快

#### 方案2：预构建包含模型的镜像

如果你想要一个完全自包含的Docker镜像：

```dockerfile
# 在构建时就下载好所有模型
FROM python:3.11-slim-bookworm
# ... 其他配置 ...
RUN docling-tools models download
# 模型被永久嵌入到镜像中
```

### 模型文件大小

Docling会下载以下模型：
- 文档布局检测模型：~200MB
- OCR模型（EasyOCR）：~150MB
- 表格识别模型：~100MB
- 其他依赖模型：~50MB

**总计约500MB**

### 当前容器中的操作

在你当前的容器中：
1. 模型正在下载到 `/tmp/` 目录
2. 下载完成后可以正常使用
3. 但容器退出后模型会丢失

### 建议的操作

1. **立即行动**：让当前下载完成，测试转换功能
2. **长期使用**：退出容器后，使用持久化版本
3. **生产环境**：使用预构建包含模型的镜像

### 使用持久化版本的步骤

```bash
# 1. 退出当前容器
exit

# 2. 使用持久化版本
./start-docling-persistent.sh

# 3. 模型会下载到 ./models 目录，永久保存
```

### 检查模型下载状态

在容器中运行：
```bash
# 检查模型目录
ls -la /tmp/
du -sh /tmp/*

# 或者检查Hugging Face缓存
ls -la ~/.cache/huggingface/
```

模型下载完成后，转换速度会显著提升！