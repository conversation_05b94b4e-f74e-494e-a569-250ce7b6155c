#!/usr/bin/env python3
"""
测试通用数据库设计提示词的效果
使用老年人能力评估量表作为测试用例
"""

import requests
import json
import time

def test_universal_prompt():
    """测试通用提示词的效果"""
    print("🧪 测试通用数据库设计提示词")
    print("=" * 60)
    
    # 通用提示词（从文档中复制）
    universal_prompt = """你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为高质量的数据库设计。

## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能
- 添加主键约束
- 根据字段特征添加检查约束
- 为经常查询的字段创建索引
- 考虑数据的实际使用场景

## 输出格式

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
```

### 第二部分：完整SQL设计
```sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件
    {根据文档内容生成合适的CHECK约束}
);

-- 自动生成合适的索引
{根据字段特征和预期查询模式生成索引};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
```

### 第三部分：JSON字段定义
```json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
```

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行"""

    # 测试用的评估量表文档
    test_document = """
# 老年人日常生活活动能力评估量表 (ADL)

## 评估对象基本信息
- 姓名：______________
- 性别：□男 □女  
- 年龄：____岁
- 评估日期：____年____月____日
- 评估员：______________

## 评估项目

### 1. 进食
- (a) 完全独立，能自己进食 【4分】
- (b) 需要预先准备，但能独立进食 【3分】  
- (c) 需要人协助才能进食 【2分】
- (d) 需要完全帮助进食 【1分】
- (e) 鼻饲 【0分】

### 2. 洗澡
- (a) 能独立洗澡 【4分】
- (b) 需要人协助洗澡 【2分】
- (c) 完全需要人帮助洗澡 【1分】

### 3. 修饰
- (a) 能独立修饰 【1分】
- (b) 需要人协助修饰 【0分】

### 4. 穿衣
- (a) 能独立穿脱衣服 【2分】
- (b) 需要人协助，能配合 【1分】
- (c) 完全需要人帮助穿脱衣服 【0分】

### 5. 控制大便
- (a) 能控制大便 【2分】
- (b) 偶尔失禁(每周<1次) 【1分】
- (c) 完全失禁 【0分】

### 6. 控制小便
- (a) 能控制小便 【2分】
- (b) 偶尔失禁 【1分】
- (c) 完全失禁 【0分】

### 7. 如厕
- (a) 能独立如厕 【2分】
- (b) 需要人协助如厕 【1分】
- (c) 完全需要人帮助如厕 【0分】

### 8. 床椅转移
- (a) 能独立转移 【3分】
- (b) 需要少许人力协助或监督 【2分】
- (c) 需要较多人力协助 【1分】
- (d) 完全需要人帮助转移或卧床 【0分】

### 9. 平地行走
- (a) 能独立行走50米 【3分】
- (b) 需要人协助或监督行走50米 【2分】
- (c) 可以行走但不能行走50米 【1分】
- (d) 完全不能行走 【0分】

### 10. 上下楼梯
- (a) 能独立上下楼梯 【2分】
- (b) 需要人协助或监督上下楼梯 【1分】
- (c) 完全不能上下楼梯 【0分】

## 评分标准

**总分范围：0-25分**

- **完全自理(21-25分)**：生活基本自理，仅在复杂的日常生活活动中需要帮助
- **轻度依赖(16-20分)**：在日常生活活动中需要少量帮助  
- **中度依赖(11-15分)**：在日常生活活动中需要部分帮助
- **重度依赖(6-10分)**：在日常生活活动中需要大量帮助
- **完全依赖(0-5分)**：在日常生活活动中完全需要帮助

## 备注
- 评估应在老年人熟悉的环境中进行
- 评估时间建议为30-60分钟
- 建议每6个月重新评估一次
"""
    
    # 完整的提示词 = 通用提示词 + 文档内容
    full_prompt = universal_prompt + "\n\n" + test_document

    try:
        print("🔍 获取LM Studio可用模型...")
        models_response = requests.get("http://192.168.1.231:1234/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
            
        models_data = models_response.json()
        # 选择非嵌入模型
        selected_model = None
        for model in models_data['data']:
            model_id = model['id'].lower()
            if 'embedding' not in model_id and 'whisper' not in model_id:
                selected_model = model['id']
                break
        
        if not selected_model:
            print("❌ 未找到合适的对话模型")
            return False
            
        print(f"✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"📤 发送请求到LM Studio...")
        print(f"📊 提示词总长度: {len(full_prompt)} 字符")
        print("🎯 测试通用提示词的自动识别和适配能力...")
        print("⏳ 等待AI分析...")
        
        start_time = time.time()
        
        response = requests.post(
            "http://192.168.1.231:1234/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=600  # 10分钟超时，给大模型充分时间生成高质量结果
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 通用提示词测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 质量检查
                has_document_analysis = '文档分析结果' in ai_response or '文档类型' in ai_response
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_json = '{' in ai_response and '}' in ai_response
                has_constraints = 'CHECK' in ai_response.upper() or 'CONSTRAINT' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                
                print(f"\n🔍 通用提示词效果评估:")
                print(f"   ✅ 包含文档分析: {'是' if has_document_analysis else '否'}")
                print(f"   ✅ 包含CREATE TABLE语句: {'是' if has_create_table else '否'}")
                print(f"   ✅ 包含JSON字段定义: {'是' if has_json else '否'}")
                print(f"   ✅ 包含约束条件: {'是' if has_constraints else '否'}")
                print(f"   ✅ 包含索引设计: {'是' if has_indexes else '否'}")
                print(f"   ✅ 包含详细注释: {'是' if has_comments else '否'}")
                
                # 保存完整结果到文件
                with open('/tmp/universal_prompt_test_result.txt', 'w', encoding='utf-8') as f:
                    f.write("=" * 80 + "\n")
                    f.write("通用数据库设计提示词测试结果\n")
                    f.write("=" * 80 + "\n")
                    f.write(f"模型: {selected_model}\n")
                    f.write(f"处理时间: {analysis_time:.1f}秒\n")
                    f.write(f"响应长度: {len(ai_response)} 字符\n")
                    f.write("=" * 80 + "\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: /tmp/universal_prompt_test_result.txt")
                
                # 显示部分响应内容
                print(f"\n📄 AI响应预览:")
                print("=" * 60)
                if len(ai_response) > 1500:
                    print(f"{ai_response[:750]}...")
                    print(f"\n[... 中间内容省略，查看完整结果请打开保存的文件 ...]")
                    print(f"...{ai_response[-750:]}")
                else:
                    print(ai_response)
                print("=" * 60)
                
                # 计算总体质量评分
                quality_score = 0
                if has_document_analysis: quality_score += 20
                if has_create_table: quality_score += 25
                if has_json: quality_score += 15
                if has_constraints: quality_score += 15
                if has_indexes: quality_score += 15
                if has_comments: quality_score += 10
                
                print(f"\n📊 通用提示词质量评分: {quality_score}/100")
                
                if quality_score >= 90:
                    print("🏆 评级: 优秀 - 通用提示词效果很好")
                elif quality_score >= 75:
                    print("👍 评级: 良好 - 通用提示词效果不错")
                elif quality_score >= 60:
                    print("📈 评级: 及格 - 通用提示词需要优化")
                else:
                    print("⚠️  评级: 待改进 - 通用提示词需要重新设计")
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ AI分析超时（超过10分钟），可能需要更长处理时间或模型遇到问题")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 通用数据库设计提示词效果测试")
    print("=" * 60)
    print("📝 目标：验证通用提示词的自动识别和适配能力")
    print("🎯 测试用例：老年人ADL能力评估量表")
    print("=" * 60)
    
    if test_universal_prompt():
        print(f"\n🎉 通用提示词测试完成!")
        print(f"✅ 验证结果：通用提示词能够自动识别文档类型并生成相应设计")
        print(f"📋 关键特性:")
        print(f"   - 自动识别评估量表类型")
        print(f"   - 智能生成对应的数据库结构")
        print(f"   - 包含完整的SQL语句和JSON定义")
        print(f"   - 符合PostgreSQL最佳实践")
    else:
        print(f"\n❌ 通用提示词测试失败")

if __name__ == "__main__":
    main()