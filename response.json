{"success": true, "message": "PDF解析预览完成", "data": {"id": null, "name": "长小养照护智能·老年人能力评估系统", "code": "长小养照护智能老年人能力评估系统", "type": "ELDERLY_ABILITY", "version": "1.0", "description": "| A.2.12 医疗费用支 付方式(多选)    | A.2.12 医疗费用支 付方式(多选) | 城镇职工基本医疗保险  城乡居民基本医疗保险  自费  公务员补助 企业补充保险  公费医疗及医疗照顾对象  医疗救助  大病保险 | | A.2.13 经济来源(多           | A.2.13 经济来源(多        | 退休金/养老金  子女补贴  亲友资助  国家普惠型补贴  个人储蓄其他补贴                              |", "formSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "title": "长小养照护智能·老年人能力评估系统", "description": "| A.2.12 医疗费用支 付方式(多选)    | A.2.12 医疗费用支 付方式(多选) | 城镇职工基本医疗保险  城乡居民基本医疗保险  自费  公务员补助 企业补充保险  公费医疗及医疗照顾对象  医疗救助  大病保险 | | A.2.13 经济来源(多           | A.2.13 经济来源(多        | 退休金/养老金  子女补贴  亲友资助  国家普惠型补贴  个人储蓄其他补贴                              |", "properties": {"section_1": {"type": "object", "title": "长小养照护智能·老年人能力评估系统", "properties": {}, "required": []}, "section_2": {"type": "object", "title": "《老年人能力评估报告》", "properties": {"q_1": {"type": "string", "title": "评估标准：\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_"}, "q_2": {"type": "string", "title": "评估机构：\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_"}, "q_3": {"type": "string", "title": "评估人员：\\_\\_\\_\\_\\_\\_"}, "q_4": {"type": "string", "title": "评估地址：\\_\\_\\_"}}, "required": ["q_1", "q_2", "q_3", "q_4"]}, "section_3": {"type": "object", "title": "附 录 录 A （规范性） 老年人能力评估基本信息", "properties": {}, "required": []}, "section_4": {"type": "object", "title": "表 A.1 评估信息表", "properties": {"q_1": {"type": "string", "title": "A.1.1 评估编号"}, "q_2": {"type": "string", "title": "A.1.2 评估基准日期"}, "q_3": {"type": "string", "enum": ["首次评估 常规评估 即时评估 因对评估结果有疑问进行复评   其他"], "title": "A.1.3 评估原因"}}, "required": ["q_1", "q_2", "q_3"]}, "section_5": {"type": "object", "title": "表 A.2 评估对象基本信息表", "properties": {"q_1": {"type": "string", "enum": ["A.2.1 姓名"], "title": "A.2.1 姓名"}, "q_2": {"type": "string", "enum": ["A.2.2 性别", "男  女"], "title": "A.2.2 性别"}, "q_3": {"type": "string", "enum": ["A.2.3 出生日期"], "title": "A.2.3 出生日期"}, "q_4": {"type": "string", "enum": ["A.2.4 身高", "cm"], "title": "A.2.4 身高"}, "q_5": {"type": "string", "enum": ["A.2.5 体重", "kg"], "title": "A.2.5 体重"}, "q_6": {"type": "string", "enum": ["A.2.6 民族", "汉族  少数民族:____ _________  族"], "title": "A.2.6 民族"}, "q_7": {"type": "string", "enum": ["A.2.7 宗教信仰", "无  有 _________________"], "title": "A.2.7 宗教信仰"}, "q_8": {"type": "string", "enum": ["A.2.8 公民身份号码"], "title": "A.2.8 公民身份号码"}, "q_9": {"type": "string", "enum": ["A.2.9 文化程度", "文盲 小学 初中 高中/技校/中专 大学专科及以上 不详"], "title": "A.2.9 文化程度"}, "q_10": {"type": "string", "enum": ["A.2.10 居住情况(多 选)", "独居 与配偶居住 与子女居住 与父母居住 与兄弟姐妹居住与其 他亲属居住 与非亲属关系的人居住 养老机构"], "title": "A.2.10 居住情况(多 选)"}, "q_11": {"type": "string", "enum": ["A.2.11 婚姻状况", "未婚 已婚 丧偶 离婚 未说明"], "title": "A.2.11 婚姻状况"}, "q_12": {"type": "string", "enum": ["A.2.12 医疗费用支 付方式(多选)", "城镇职工基本医疗保险  城乡居民基本医疗保险  自费  公务员补助 企业补充保险  公费医疗及医疗照顾对象  医疗救助  大病保险"], "title": "A.2.12 医疗费用支 付方式(多选)"}, "q_13": {"type": "string", "enum": ["A.2.13 经济来源(多", "退休金/养老金  子女补贴  亲友资助  国家普惠型补贴  个人储蓄其他补贴"], "title": "A.2.13 经济来源(多"}, "q_14": {"type": "string", "enum": ["A.2.14.1 跌倒", "无  发生过1次  发生过2次  发生过3次及以上"], "title": "A.2. 14近 30天 内照 护风 险事 件"}, "q_15": {"type": "string", "enum": ["A.2.14.2 走失", "无  发生过1次  发生过2次  发生过3次及以上"], "title": "A.2. 14近 30天 内照 护风 险事 件"}, "q_16": {"type": "string", "enum": ["A.2.14.3 噎食", "无  发生过1次  发生过2次  发生过3次及以上"], "title": "A.2. 14近 30天 内照 护风 险事 件"}, "q_17": {"type": "string", "enum": ["A.2.14.4 自 杀、自伤", "无  发生过1次  发生过2次  发生过3次及以上"], "title": "A.2. 14近 30天 内照 护风 险事 件"}, "q_18": {"type": "string", "enum": ["A.2.14.5 其他", "无  发生过1次  发生过2次  发生过3次及以上"], "title": "A.2. 14近 30天 内照 护风 险事 件"}}, "required": ["q_1", "q_2", "q_3", "q_4", "q_5", "q_6", "q_7", "q_8", "q_9", "q_10", "q_11", "q_12", "q_13", "q_14", "q_15", "q_16", "q_17", "q_18"]}, "section_6": {"type": "object", "title": "表 A.3 信息提供者及联系人信息表", "properties": {}, "required": []}, "section_7": {"type": "object", "title": "表 A.4 疾病诊断和用药情况表", "properties": {}, "required": []}, "section_8": {"type": "object", "title": "A.4.1 疾病诊断(可多选)", "properties": {}, "required": []}, "section_9": {"type": "object", "title": "A.4.2 用药情况(目前长期服药情况)", "properties": {"q_1": {"type": "string", "enum": ["药物名称", "服药方法", "用药剂量", "用药频率"], "title": "序号"}, "q_2": {"type": "string", "title": "1"}, "q_3": {"type": "string", "title": "2"}, "q_4": {"type": "string", "title": "3"}, "q_5": {"type": "string", "title": "4"}}, "required": ["q_1", "q_2", "q_3", "q_4", "q_5"]}, "section_10": {"type": "object", "title": "表 A.5 健康相关问题", "properties": {"q_1": {"type": "string", "enum": ["无 I 期:皮肤完好 , 出现指压不会变白的红印 Ⅱ期:皮肤真皮层损失、暴露 , 出现水疱 Ⅲ期:全层皮肤缺失 , 可见脂肪、肉芽组织以及边缘内卷 Ⅳ期:全层皮肤、组织缺失 , 可见肌腱、肌肉、腱膜 , 以及边缘内卷 , 伴随隧道、 潜行 不可分期:全身皮肤、组织被腐肉、焦痂掩盖 , 无法确认组织缺失程度 , 去除腐 肉、焦痂才可判断损伤程度"], "title": "A.5.1 压力性损伤"}, "q_2": {"type": "string", "enum": ["无 , 没有影响日常生活功能 是 , 影响日常生活功能 , 部位 _____________________  无法判断"], "title": "A.5.2 关节活动度"}, "q_3": {"type": "string", "enum": ["无 擦伤 烧烫伤 术后伤口 糖尿病足溃疡  血管性溃疡 其他伤口"], "title": "A.5.3 伤口情况(可 多选)"}, "q_4": {"type": "string", "enum": ["无 胃管 尿管 气管切开 胃/肠/膀胱造痿 无创呼吸机 透析  其他"], "title": "A.5.4 特殊护理情况 (可多选)"}, "q_5": {"type": "string", "enum": ["无疼痛 轻度疼痛 中度疼痛(尚可忍受的程度) 重度终痛(无法忍受的程度) 不知道或无法判断"], "title": "A.5.5 终痛感 注:通过表情反应和 询问来判断"}, "q_6": {"type": "string", "enum": ["无缺损 牙体缺损(如龋齿、楔状缺损) 牙列缺损:〇非对位牙缺失 〇单侧对位牙缺失 〇双侧对位牙缺失 牙列缺失:〇上颌牙缺失 〇下颌牙缺失 〇全口牙缺失"], "title": "A.5.6 牙齿缺失情 况(可多选)"}, "q_7": {"type": "string", "enum": ["无义齿  固定义齿  可摘局部义齿  可摘全/半口义齿"], "title": "A.5.7 义齿佩戴情况 (可多选)"}, "q_8": {"type": "string", "enum": ["无 抱怨吞咽困难或吞咽时会疼痛 吃东西或喝水的时出现咳嗽或呛咳 用餐后嘴中仍含养食物或留有残余食物 当喝或吃流质或固体的食物时，食物会从嘴角边流失 有流口水的情况"], "title": "A.5.8 吞咽困难的情 形和症状(可多选)"}, "q_9": {"type": "string", "enum": ["无  有"], "title": "A.5.9 营养不良:体 质指数(BMI)低于正 常值 注:BM1=体重 (kg)/[身高(m)] 2 。"}, "q_10": {"type": "string", "enum": ["无  有"], "title": "A.5.10 清理呼吸道 无效"}, "q_11": {"type": "string", "enum": ["无  有"], "title": "A.5.11 昏迷"}, "q_12": {"type": "string", "enum": ["A.5.12 其他(请补充):"], "title": "A.5.12 其他(请补充):"}}, "required": ["q_1", "q_2", "q_3", "q_4", "q_5", "q_6", "q_7", "q_8", "q_9", "q_10", "q_11", "q_12"]}, "section_11": {"type": "object", "title": "附 录 录 B", "properties": {}, "required": []}, "section_12": {"type": "object", "title": "老年人能力评估", "properties": {}, "required": []}, "section_13": {"type": "object", "title": "表 B.1 老 老年人能力评估表", "properties": {}, "required": []}, "section_14": {"type": "object", "title": "表B.2 基础运动能力评估表", "properties": {}, "required": []}, "section_15": {"type": "object", "title": "B.2.1 床上体位转移:卧床翻身及坐起躺下", "properties": {}, "required": []}, "section_16": {"type": "object", "title": "表 B.3 精神状态评估表", "properties": {}, "required": []}, "section_17": {"type": "object", "title": "表B.4 感知觉与社会参与评估表", "properties": {}, "required": []}, "section_18": {"type": "object", "title": "表B.5 老 老年人能力总得分", "properties": {"q_1": {"type": "string", "title": "老年人能力总得分:"}}, "required": ["q_1"]}, "section_19": {"type": "object", "title": "附录C (规范性)", "properties": {}, "required": []}, "section_20": {"type": "object", "title": "老年人能力评估报告", "properties": {"q_1": {"type": "string", "title": "- C.1.1 自理能力得分:"}, "q_2": {"type": "string", "title": "2 基础运动能力分:"}, "q_3": {"type": "string", "title": "C.2 初步等级得分"}, "q_4": {"type": "string", "title": "C.3 老年人能力初步 等级"}, "q_5": {"type": "string", "title": "能力完好"}, "q_6": {"type": "string", "title": "能力轻度受损(轻度失能)"}, "q_7": {"type": "string", "title": "能力中度受损(中度失能)"}, "q_8": {"type": "string", "title": "能力重度受损(重度失能)"}, "q_9": {"type": "string", "title": "能力完全丧失(完全失能)"}, "q_10": {"type": "string", "title": "C.4 能力等级变更依"}, "q_11": {"type": "string", "title": "依据附录 A 中表 A.5 的 A.5.11“昏迷”、表 A.4 的 A.4.1“疾病诊断 \" 和表 A.2 的 A.2.14“近 30天内照护风险事件 \" 确定是否存在以下导致能力等级变更的项"}, "q_12": {"type": "string", "title": "处于昏迷状态者 , 直接评定为能力完全丧失(完全失能)"}, "q_13": {"type": "string", "title": "(F04~F99),在原有能力级别上提高一个等级"}, "q_14": {"type": "string", "title": "近 30 天内发生过2次及以上照护风险事件(如跌倒、噎食、自杀、自伤、走失 等),在原有能力级别上提高一个等级"}}, "required": ["q_1", "q_2", "q_3", "q_4", "q_5", "q_6", "q_7", "q_8", "q_9", "q_10", "q_11", "q_12", "q_13", "q_14"]}, "section_21": {"type": "object", "title": "C.5 老年人能力最终 等级", "properties": {"q_1": {"type": "string", "title": "综合 C.3“老年人能力初步等级”和 C.4“能力等级变更依据 \" 的结果 , 判定老年"}, "q_2": {"type": "string", "title": "人能力最终等级:"}, "q_3": {"type": "string", "title": "能力完好"}, "q_4": {"type": "string", "title": "能力轻度受损(轻度失能)"}, "q_5": {"type": "string", "title": "能力中度受损(中度失能)"}, "q_6": {"type": "string", "title": "能力重度受损(重度失能)"}, "q_7": {"type": "string", "title": "能力完全丧失(完全失能)"}, "q_8": {"type": "string", "title": "评估地点"}, "q_9": {"type": "string", "title": "评估人员签名"}, "q_10": {"type": "string", "title": "副评估员签名"}}, "required": ["q_1", "q_2", "q_3", "q_4", "q_5", "q_6", "q_7", "q_8", "q_9", "q_10"]}}, "required": ["section_1", "section_2", "section_3", "section_4", "section_5", "section_6", "section_7", "section_8", "section_9", "section_10", "section_11", "section_12", "section_13", "section_14", "section_15", "section_16", "section_17", "section_18", "section_19", "section_20", "section_21"]}, "scoringRules": {"maxPossibleScore": 268, "minPossibleScore": 0, "passScore": 134, "scoringMethod": "累计计分", "sections": [{"sectionId": "section_1", "sectionTitle": "长小养照护智能·老年人能力评估系统", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_2", "sectionTitle": "《老年人能力评估报告》", "maxScore": 16, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_4", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}]}, {"sectionId": "section_3", "sectionTitle": "附 录 录 A （规范性） 老年人能力评估基本信息", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_4", "sectionTitle": "表 A.1 评估信息表", "maxScore": 12, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"首次评估 常规评估 即时评估 因对评估结果有疑问进行复评   其他": 0}}]}, {"sectionId": "section_5", "sectionTitle": "表 A.2 评估对象基本信息表", "maxScore": 72, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"A.2.1 姓名": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"A.2.2 性别": 1, "男  女": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"A.2.3 出生日期": 0}}, {"questionId": "q_4", "maxScore": 4, "scoreMapping": {"A.2.4 身高": 1, "cm": 0}}, {"questionId": "q_5", "maxScore": 4, "scoreMapping": {"A.2.5 体重": 1, "kg": 0}}, {"questionId": "q_6", "maxScore": 4, "scoreMapping": {"A.2.6 民族": 1, "汉族  少数民族:____ _________  族": 0}}, {"questionId": "q_7", "maxScore": 4, "scoreMapping": {"A.2.7 宗教信仰": 1, "无  有 _________________": 0}}, {"questionId": "q_8", "maxScore": 4, "scoreMapping": {"A.2.8 公民身份号码": 0}}, {"questionId": "q_9", "maxScore": 4, "scoreMapping": {"A.2.9 文化程度": 1, "文盲 小学 初中 高中/技校/中专 大学专科及以上 不详": 0}}, {"questionId": "q_10", "maxScore": 4, "scoreMapping": {"A.2.10 居住情况(多 选)": 1, "独居 与配偶居住 与子女居住 与父母居住 与兄弟姐妹居住与其 他亲属居住 与非亲属关系的人居住 养老机构": 0}}, {"questionId": "q_11", "maxScore": 4, "scoreMapping": {"A.2.11 婚姻状况": 1, "未婚 已婚 丧偶 离婚 未说明": 0}}, {"questionId": "q_12", "maxScore": 4, "scoreMapping": {"A.2.12 医疗费用支 付方式(多选)": 1, "城镇职工基本医疗保险  城乡居民基本医疗保险  自费  公务员补助 企业补充保险  公费医疗及医疗照顾对象  医疗救助  大病保险": 0}}, {"questionId": "q_13", "maxScore": 4, "scoreMapping": {"A.2.13 经济来源(多": 1, "退休金/养老金  子女补贴  亲友资助  国家普惠型补贴  个人储蓄其他补贴": 0}}, {"questionId": "q_14", "maxScore": 4, "scoreMapping": {"A.2.14.1 跌倒": 1, "无  发生过1次  发生过2次  发生过3次及以上": 0}}, {"questionId": "q_15", "maxScore": 4, "scoreMapping": {"A.2.14.2 走失": 1, "无  发生过1次  发生过2次  发生过3次及以上": 0}}, {"questionId": "q_16", "maxScore": 4, "scoreMapping": {"A.2.14.3 噎食": 1, "无  发生过1次  发生过2次  发生过3次及以上": 0}}, {"questionId": "q_17", "maxScore": 4, "scoreMapping": {"A.2.14.4 自 杀、自伤": 1, "无  发生过1次  发生过2次  发生过3次及以上": 0}}, {"questionId": "q_18", "maxScore": 4, "scoreMapping": {"A.2.14.5 其他": 1, "无  发生过1次  发生过2次  发生过3次及以上": 0}}]}, {"sectionId": "section_6", "sectionTitle": "表 A.3 信息提供者及联系人信息表", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_7", "sectionTitle": "表 A.4 疾病诊断和用药情况表", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_8", "sectionTitle": "A.4.1 疾病诊断(可多选)", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_9", "sectionTitle": "A.4.2 用药情况(目前长期服药情况)", "maxScore": 20, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"药物名称": 3, "服药方法": 2, "用药剂量": 1, "用药频率": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_4", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_5", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}]}, {"sectionId": "section_10", "sectionTitle": "表 A.5 健康相关问题", "maxScore": 48, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"无 I 期:皮肤完好 , 出现指压不会变白的红印 Ⅱ期:皮肤真皮层损失、暴露 , 出现水疱 Ⅲ期:全层皮肤缺失 , 可见脂肪、肉芽组织以及边缘内卷 Ⅳ期:全层皮肤、组织缺失 , 可见肌腱、肌肉、腱膜 , 以及边缘内卷 , 伴随隧道、 潜行 不可分期:全身皮肤、组织被腐肉、焦痂掩盖 , 无法确认组织缺失程度 , 去除腐 肉、焦痂才可判断损伤程度": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"无 , 没有影响日常生活功能 是 , 影响日常生活功能 , 部位 _____________________  无法判断": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"无 擦伤 烧烫伤 术后伤口 糖尿病足溃疡  血管性溃疡 其他伤口": 0}}, {"questionId": "q_4", "maxScore": 4, "scoreMapping": {"无 胃管 尿管 气管切开 胃/肠/膀胱造痿 无创呼吸机 透析  其他": 0}}, {"questionId": "q_5", "maxScore": 4, "scoreMapping": {"无疼痛 轻度疼痛 中度疼痛(尚可忍受的程度) 重度终痛(无法忍受的程度) 不知道或无法判断": 0}}, {"questionId": "q_6", "maxScore": 4, "scoreMapping": {"无缺损 牙体缺损(如龋齿、楔状缺损) 牙列缺损:〇非对位牙缺失 〇单侧对位牙缺失 〇双侧对位牙缺失 牙列缺失:〇上颌牙缺失 〇下颌牙缺失 〇全口牙缺失": 0}}, {"questionId": "q_7", "maxScore": 4, "scoreMapping": {"无义齿  固定义齿  可摘局部义齿  可摘全/半口义齿": 0}}, {"questionId": "q_8", "maxScore": 4, "scoreMapping": {"无 抱怨吞咽困难或吞咽时会疼痛 吃东西或喝水的时出现咳嗽或呛咳 用餐后嘴中仍含养食物或留有残余食物 当喝或吃流质或固体的食物时，食物会从嘴角边流失 有流口水的情况": 0}}, {"questionId": "q_9", "maxScore": 4, "scoreMapping": {"无  有": 0}}, {"questionId": "q_10", "maxScore": 4, "scoreMapping": {"无  有": 0}}, {"questionId": "q_11", "maxScore": 4, "scoreMapping": {"无  有": 0}}, {"questionId": "q_12", "maxScore": 4, "scoreMapping": {"A.5.12 其他(请补充):": 0}}]}, {"sectionId": "section_11", "sectionTitle": "附 录 录 B", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_12", "sectionTitle": "老年人能力评估", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_13", "sectionTitle": "表 B.1 老 老年人能力评估表", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_14", "sectionTitle": "表B.2 基础运动能力评估表", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_15", "sectionTitle": "B.2.1 床上体位转移:卧床翻身及坐起躺下", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_16", "sectionTitle": "表 B.3 精神状态评估表", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_17", "sectionTitle": "表B.4 感知觉与社会参与评估表", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_18", "sectionTitle": "表B.5 老 老年人能力总得分", "maxScore": 4, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}]}, {"sectionId": "section_19", "sectionTitle": "附录C (规范性)", "maxScore": 0, "weight": 1.0, "questions": []}, {"sectionId": "section_20", "sectionTitle": "老年人能力评估报告", "maxScore": 56, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_4", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_5", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_6", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_7", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_8", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_9", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_10", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_11", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_12", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_13", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_14", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}]}, {"sectionId": "section_21", "sectionTitle": "C.5 老年人能力最终 等级", "maxScore": 40, "weight": 1.0, "questions": [{"questionId": "q_1", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_2", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_3", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_4", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_5", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_6", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_7", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_8", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_9", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}, {"questionId": "q_10", "maxScore": 4, "scoreMapping": {"优秀": 4, "良好": 3, "一般": 2, "较差": 1, "无法完成": 0}}]}]}, "configOptions": null, "isActive": null, "isOfficial": null, "applicableScope": null, "estimatedDuration": 33, "maxScore": 268, "minScore": 0, "sourcePdfPath": "data/pdf-uploads/582337a0-dfc9-4f15-96eb-25cb264232ce.pdf", "parseStatus": null, "parseError": null, "usageCount": null}, "errorCode": null, "timestamp": 1750493091325}