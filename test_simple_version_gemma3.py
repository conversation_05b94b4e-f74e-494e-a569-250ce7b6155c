#!/usr/bin/env python3
"""
使用简洁版提示词测试192.168.1.225:1234中的gemma-3模型
专注于速度和实用性
"""

import requests
import json
import time
from datetime import datetime

def test_simple_version_gemma3():
    """使用简洁版提示词测试gemma-3模型"""
    print("🚀 测试简洁版通用数据库设计提示词（gemma-3模型）")
    print("=" * 80)
    
    # LM Studio配置
    lm_studio_url = "http://192.168.1.225:1234"
    target_model = "gemma-3"
    
    print("⚠️ 检测到token限制问题，将使用简化提示词策略")
    
    # 极简版提示词（突破token限制）
    simple_prompt = """请分析文档并设计PostgreSQL数据库。

输出要求：
1. 表名和用途
2. 完整建表SQL
3. 字段说明JSON

必须包含：主键、created_at、updated_at字段。
SQL必须可执行。"""
    
    # 读取国标评估文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        national_standard_doc = f.read()
    
    # 完整提示词
    full_prompt = simple_prompt + "\n\n" + national_standard_doc

    try:
        print(f"🔍 连接到新的LM Studio: {lm_studio_url}")
        print(f"🎯 查找gemma-3模型...")
        
        # 获取可用模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print(f"❌ 无法获取模型列表: {models_response.status_code}")
            return False
            
        models_data = models_response.json()
        selected_model = None
        
        print("📋 可用模型列表:")
        for model in models_data['data']:
            model_id = model['id']
            print(f"   - {model_id}")
            # 查找gemma-3模型
            if target_model.lower() in model_id.lower():
                selected_model = model_id
                break
        
        if not selected_model:
            print(f"❌ 未找到gemma-3模型")
            print("💡 请确保gemma-3模型已在LM Studio中加载")
            return False
            
        print(f"\n✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送简洁版提示词到gemma-3模型...")
        print(f"🤖 模型: {selected_model}")
        print(f"📊 提示词长度: {len(full_prompt)} 字符")
        print(f"🏆 提示词版本: 简洁版（已验证100分基准）")
        print(f"📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
        print(f"🎯 重点关注: 速度和实用性")
        print("⏳ 等待gemma-3模型生成结果...")
        
        start_time = time.time()
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=900  # 15分钟超时，期望更快
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 简洁版gemma-3模型测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 使用与之前测试相同的检查标准
                has_document_analysis = '文档分析' in ai_response or '文档类型' in ai_response
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_json = '"database_design"' in ai_response and '"fields"' in ai_response
                has_constraints = 'CHECK' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                has_triggers = 'CREATE TRIGGER' in ai_response.upper()
                
                # 检查输出结构（三部分）
                has_three_parts = len([part for part in ['## 文档分析', 'CREATE TABLE', '"database_design"'] 
                                     if part in ai_response]) >= 2
                
                # 检查业务理解（国标特定）
                has_national_standard_fields = any(field in ai_response.lower() for field in 
                    ['assessment', 'elderly', 'ability', '评估', '老年人', '能力'])
                
                # 检查SQL质量
                has_primary_key = 'BIGSERIAL PRIMARY KEY' in ai_response.upper()
                has_audit_fields = all(field in ai_response for field in ['created_at', 'updated_at'])
                
                print(f"\n📊 简洁版gemma-3模型质量检查:")
                print(f"🤖 测试模型: {selected_model}")
                print(f"📦 提示词版本: 简洁版（100分基准）")
                print(f"🖥️  LM Studio: {lm_studio_url}")
                
                print(f"\n1️⃣ 基础功能检查:")
                print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
                print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
                print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
                print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
                print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
                print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
                print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
                
                print(f"\n2️⃣ 结构完整性:")
                print(f"   📋 输出结构: {'通过' if has_three_parts else '❌未通过'}")
                print(f"   🏥 业务理解: {'通过' if has_national_standard_fields else '❌未通过'}")
                
                print(f"\n3️⃣ SQL质量:")
                print(f"   🔑 主键设计: {'通过' if has_primary_key else '❌未通过'}")
                print(f"   🕒 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
                
                # 保存结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/simple_version_gemma3_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 简洁版提示词gemma-3模型测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**LM Studio地址**: {lm_studio_url}\n")
                    f.write(f"**模型名称**: {selected_model}\n")
                    f.write(f"**提示词版本**: 简洁版（已验证100分基准）\n")
                    f.write(f"**处理时间**: {analysis_time:.1f}秒\n")
                    f.write(f"**测试文档**: 老年人能力评估报告（GB/T42195-2022）\n")
                    f.write(f"**对比基准**: deepseek-r1-0528-qwen3-8b-mlx@8bit (125/125分)\n\n")
                    f.write("---\n\n")
                    f.write("## gemma-3模型生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: {result_file}")
                
                # 计算质量评分（与deepseek基准对比）
                basic_score = 0
                if has_document_analysis: basic_score += 15
                if has_create_table: basic_score += 20
                if has_json: basic_score += 20
                if has_constraints: basic_score += 15
                if has_indexes: basic_score += 10
                if has_comments: basic_score += 10
                if has_triggers: basic_score += 10
                
                structure_score = 0
                if has_three_parts: structure_score += 15
                if has_national_standard_fields: structure_score += 10
                
                sql_quality_score = 0
                if has_primary_key: sql_quality_score += 10
                if has_audit_fields: sql_quality_score += 10
                
                total_score = basic_score + structure_score + sql_quality_score
                
                print(f"\n🏆 gemma-3模型评分:")
                print(f"   📋 基础功能: {basic_score}/100分")
                print(f"   📊 结构完整性: {structure_score}/25分")
                print(f"   🗄️ SQL质量: {sql_quality_score}/20分")
                print(f"   📈 总分: {total_score}/145分 ({total_score/145*100:.1f}%)")
                
                # 与deepseek基准对比
                deepseek_baseline = 125
                deepseek_time = 172.2
                performance_ratio = (total_score / 145) * 100
                
                if performance_ratio >= 90:
                    grade = "A级 - 优秀表现"
                elif performance_ratio >= 80:
                    grade = "B级 - 良好表现"
                elif performance_ratio >= 70:
                    grade = "C级 - 基础通过"
                elif performance_ratio >= 60:
                    grade = "D级 - 需要改进"
                else:
                    grade = "F级 - 表现不佳"
                
                print(f"\n🎯 最终评级: {grade}")
                print(f"📊 绝对性能: {performance_ratio:.1f}%")
                
                # 速度对比
                speed_vs_deepseek = analysis_time / deepseek_time
                speed_status = "更快" if speed_vs_deepseek < 1 else "更慢"
                speed_percent = abs(speed_vs_deepseek - 1) * 100
                
                print(f"\n📈 性能对比总结:")
                print(f"   ⏱️ 处理速度: {analysis_time:.1f}s vs deepseek {deepseek_time}s (gemma-3 {speed_status}{speed_percent:.1f}%)")
                print(f"   📝 响应长度: {len(ai_response)} 字符")
                print(f"   📊 质量得分: {total_score}/145 vs deepseek 125/125")
                print(f"   🎯 实用性评估: {'✅ 推荐' if analysis_time < 300 and performance_ratio >= 70 else '⚠️ 一般'}")
                
                # 实用性分析
                is_practical = analysis_time < 300 and performance_ratio >= 70
                print(f"\n💡 实用性分析:")
                print(f"   ⚡ 速度表现: {'优秀' if analysis_time < 180 else '良好' if analysis_time < 300 else '较慢'}")
                print(f"   🎯 质量表现: {'优秀' if performance_ratio >= 85 else '良好' if performance_ratio >= 70 else '一般'}")
                print(f"   📊 综合评价: {'✅ 生产可用' if is_practical else '⚠️ 需要优化'}")
                
                # 显示部分响应预览
                print(f"\n📄 响应预览（前600字符）:")
                print("=" * 80)
                print(ai_response[:600] + "..." if len(ai_response) > 600 else ai_response)
                print("=" * 80)
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ AI分析超时（超过15分钟）")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到LM Studio: {lm_studio_url}")
        print("💡 请检查:")
        print("   1. LM Studio是否在192.168.1.225上运行")
        print("   2. 端口1234是否正确")
        print("   3. 防火墙设置是否允许连接")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🏆 简洁版提示词gemma-3模型测试")
    print("=" * 80)
    print("📍 LM Studio地址: http://192.168.1.225:1234")
    print("🎯 目标模型: gemma-3 (Google Gemma系列)")
    print("📦 提示词版本: 简洁版（已验证100分）")
    print("📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
    print("🔍 测试目标: 验证速度和实用性表现")
    print("⚡ 期望目标: 处理时间 < 5分钟，质量 >= 70%")
    print("=" * 80)
    
    if test_simple_version_gemma3():
        print(f"\n✅ 简洁版gemma-3模型测试成功完成!")
        print(f"📊 请查看详细测试结果文件")
        print(f"🔄 可与之前的deepseek基准结果对比分析")
    else:
        print(f"\n❌ 测试失败，请检查LM Studio连接和模型加载")

if __name__ == "__main__":
    main()