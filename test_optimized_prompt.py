#!/usr/bin/env python3
"""
测试优化版通用数据库设计提示词的效果
重点验证：1.命名规范一致性 2.JSON格式标准化 3.索引策略增强
"""

import requests
import json
import time

def test_optimized_prompt():
    """测试优化版提示词的效果"""
    print("🔧 测试优化版通用数据库设计提示词")
    print("=" * 60)
    
    # 优化版提示词
    optimized_prompt = """你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为高质量的数据库设计。

## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性
- **命名规范**: 严格使用snake_case命名法，所有表名、字段名、约束名、索引名都必须使用下划线分隔

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能优化
- 添加主键约束和外键约束
- 根据字段特征添加检查约束
- 为经常单独查询的字段创建单列索引
- 为常见的多字段组合查询创建复合索引（如：user_id + created_date、institution_id + status等）
- 考虑数据的实际使用场景和查询性能需求

## 输出格式

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
```

### 第二部分：完整SQL设计
```sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名，使用snake_case} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段（严格使用snake_case命名）
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件（约束名使用snake_case）
    {根据文档内容生成合适的CHECK约束}
);

-- 单列索引（为经常单独查询的字段）
{为核心查询字段生成单列索引，索引名使用idx_表名_字段名格式};

-- 复合索引（为常见的多字段组合查询）
{为多字段查询场景生成复合索引，如：
CREATE INDEX idx_表名_用户_日期 ON 表名(user_id, created_at DESC);
CREATE INDEX idx_表名_机构_状态 ON 表名(institution_id, status);
等};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
```

### 第三部分：JSON字段定义
```json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "index_type": "single/composite",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
```

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引（单列+复合）
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景
✅ 严格遵循snake_case命名规范

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行
- 所有命名必须使用snake_case格式，包括表名、字段名、约束名、索引名
- JSON输出必须使用标准英文字段名，如"type"而非"类型"
- 索引设计要考虑实际查询场景，既要有单列索引也要有复合索引"""

    # 使用不同的测试文档 - 情绪快评量表
    test_document = """
# 老年人情绪快评量表

## 基本信息
- 评估对象：______________
- 评估师：______________  
- 评估日期：____年____月____日
- 评估地点：______________

## 评估项目

### 1. 情绪状态
您最近一周的心情如何？
- A. 很愉快，经常感到开心 【3分】
- B. 比较愉快，大部分时间心情不错 【2分】
- C. 一般，心情平静 【1分】
- D. 比较低落，经常感到不开心 【0分】
- E. 很低落，几乎总是感到沮丧 【-1分】

### 2. 睡眠质量
您最近的睡眠情况如何？
- A. 睡眠很好，精神饱满 【2分】
- B. 睡眠较好，偶尔失眠 【1分】
- C. 睡眠一般，经常失眠 【0分】
- D. 睡眠很差，严重失眠 【-1分】

### 3. 食欲状况
您最近的食欲如何？
- A. 食欲很好，用餐愉快 【2分】
- B. 食欲较好，正常用餐 【1分】
- C. 食欲一般，勉强用餐 【0分】
- D. 没有食欲，不想吃东西 【-1分】

### 4. 社交活动
您是否愿意参加社交活动？
- A. 很愿意，积极参与各种活动 【2分】
- B. 比较愿意，会参加一些活动 【1分】
- C. 不太愿意，很少参加活动 【0分】
- D. 完全不愿意，避免社交 【-1分】

### 5. 自我评价
您对自己的生活满意度如何？
- A. 很满意，觉得生活很有意义 【3分】
- B. 比较满意，生活基本如意 【2分】
- C. 一般满意，没什么特别感受 【1分】
- D. 不太满意，经常感到失落 【0分】
- E. 很不满意，觉得生活没有希望 【-1分】

## 评分标准

**总分范围：-5分到12分**

- **情绪良好(8-12分)**：心理状态健康，适应能力强
- **情绪稳定(4-7分)**：心理状态基本正常，需要关注
- **情绪波动(0-3分)**：存在情绪问题，需要干预
- **情绪低落(-1到-1分)**：存在明显情绪障碍，需要专业帮助
- **严重抑郁(-5到-2分)**：需要立即心理或医疗干预

## 注意事项
- 本量表仅供参考，不能代替专业诊断
- 如发现严重情绪问题，应及时寻求专业帮助
- 建议每月进行一次评估，跟踪情绪变化
"""
    
    # 完整的提示词 = 优化版提示词 + 文档内容
    full_prompt = optimized_prompt + "\n\n" + test_document

    try:
        print("🔍 获取LM Studio可用模型...")
        models_response = requests.get("http://192.168.1.231:1234/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
            
        models_data = models_response.json()
        selected_model = None
        for model in models_data['data']:
            model_id = model['id'].lower()
            if 'embedding' not in model_id and 'whisper' not in model_id:
                selected_model = model['id']
                break
        
        if not selected_model:
            print("❌ 未找到合适的对话模型")
            return False
            
        print(f"✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"📤 发送优化版提示词到LM Studio...")
        print(f"📊 提示词总长度: {len(full_prompt)} 字符")
        print("🎯 重点验证: 命名规范、JSON格式、索引策略")
        print("⏳ 等待AI分析中...")
        
        start_time = time.time()
        
        response = requests.post(
            "http://192.168.1.231:1234/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=600
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 优化版提示词测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 基础质量检查
                has_document_analysis = '文档分析结果' in ai_response or '文档类型' in ai_response
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_json = '{' in ai_response and '}' in ai_response
                has_constraints = 'CHECK' in ai_response.upper() or 'CONSTRAINT' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                
                # 优化点检查
                snake_case_check = '_' in ai_response and 'CREATE TABLE' in ai_response.upper()
                composite_index_check = 'CREATE INDEX' in ai_response.upper() and (',' in ai_response or 'DESC' in ai_response.upper())
                json_format_check = '"type"' in ai_response and '"类型"' not in ai_response
                
                print(f"\n🔍 基础质量检查:")
                print(f"   ✅ 包含文档分析: {'是' if has_document_analysis else '否'}")
                print(f"   ✅ 包含CREATE TABLE语句: {'是' if has_create_table else '否'}")
                print(f"   ✅ 包含JSON字段定义: {'是' if has_json else '否'}")
                print(f"   ✅ 包含约束条件: {'是' if has_constraints else '否'}")
                print(f"   ✅ 包含索引设计: {'是' if has_indexes else '否'}")
                print(f"   ✅ 包含详细注释: {'是' if has_comments else '否'}")
                
                print(f"\n🔧 优化点验证:")
                print(f"   ✅ snake_case命名规范: {'是' if snake_case_check else '否'}")
                print(f"   ✅ 复合索引设计: {'是' if composite_index_check else '否'}")
                print(f"   ✅ 标准JSON格式: {'是' if json_format_check else '否'}")
                
                # 保存结果到文件
                with open('/tmp/optimized_prompt_test_result.txt', 'w', encoding='utf-8') as f:
                    f.write("=" * 80 + "\n")
                    f.write("优化版通用数据库设计提示词测试结果\n")
                    f.write("=" * 80 + "\n")
                    f.write(f"模型: {selected_model}\n")
                    f.write(f"处理时间: {analysis_time:.1f}秒\n")
                    f.write(f"响应长度: {len(ai_response)} 字符\n")
                    f.write(f"测试文档: 老年人情绪快评量表\n")
                    f.write("=" * 80 + "\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: /tmp/optimized_prompt_test_result.txt")
                
                # 显示响应预览
                print(f"\n📄 AI响应预览:")
                print("=" * 60)
                if len(ai_response) > 1500:
                    print(f"{ai_response[:750]}...")
                    print(f"\n[... 中间内容省略，查看完整结果请打开保存的文件 ...]")
                    print(f"...{ai_response[-750:]}")
                else:
                    print(ai_response)
                print("=" * 60)
                
                # 计算总体质量评分
                basic_score = 0
                if has_document_analysis: basic_score += 15
                if has_create_table: basic_score += 20
                if has_json: basic_score += 15
                if has_constraints: basic_score += 15
                if has_indexes: basic_score += 10
                if has_comments: basic_score += 10
                
                optimization_score = 0
                if snake_case_check: optimization_score += 10
                if composite_index_check: optimization_score += 10
                if json_format_check: optimization_score += 5
                
                total_score = basic_score + optimization_score
                
                print(f"\n📊 优化版提示词质量评分:")
                print(f"   📋 基础功能得分: {basic_score}/85")
                print(f"   🔧 优化改进得分: {optimization_score}/25")
                print(f"   🏆 总体得分: {total_score}/110")
                
                if total_score >= 100:
                    print("🏆 评级: 卓越 - 优化版提示词效果显著提升")
                elif total_score >= 90:
                    print("🌟 评级: 优秀 - 优化版提示词效果很好")
                elif total_score >= 75:
                    print("👍 评级: 良好 - 优化版提示词有明显改进")
                else:
                    print("📈 评级: 待改进 - 优化版提示词需要进一步调整")
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ AI分析超时（超过10分钟）")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 优化版通用数据库设计提示词测试")
    print("=" * 60)
    print("📝 目标：验证命名规范、JSON格式、索引策略的优化效果")
    print("🎯 测试用例：老年人情绪快评量表（不同于之前的ADL量表）")
    print("🔍 重点验证：")
    print("   1. snake_case命名规范一致性")
    print("   2. JSON格式标准化（type而非类型）")
    print("   3. 复合索引策略设计")
    print("=" * 60)
    
    if test_optimized_prompt():
        print(f"\n🎉 优化版提示词测试完成!")
        print(f"✅ 验证成功：优化措施有效提升了生成质量")
        print(f"📋 关键改进:")
        print(f"   - 强化了snake_case命名规范")
        print(f"   - 标准化了JSON输出格式")
        print(f"   - 增强了索引策略设计")
        print(f"   - 提升了整体代码质量")
    else:
        print(f"\n❌ 优化版提示词测试失败")

if __name__ == "__main__":
    main()