#!/usr/bin/env python3
"""
测试最终优化版通用数据库设计提示词
使用当前LM Studio地址：http://192.168.1.231:1234
"""

import requests
import json
import time
from datetime import datetime

def test_final_optimized_prompt():
    """测试最终优化版提示词的效果"""
    print("🚀 测试最终优化版通用数据库设计提示词")
    print("=" * 80)
    
    # 读取最终优化版提示词
    print("📖 读取最终优化版提示词...")
    with open("/Volumes/acasis/Assessment/docs/最终优化版通用数据库设计提示词.md", "r", encoding="utf-8") as f:
        prompt_content = f.read()
    
    # 提取提示词部分（在```text标记之间）
    start_marker = "```text"
    end_marker = "```"
    start_idx = prompt_content.find(start_marker) + len(start_marker)
    end_idx = prompt_content.find(end_marker, start_idx)
    final_optimized_prompt = prompt_content[start_idx:end_idx].strip()
    
    # 使用新的测试文档 - interRAI评估量表（更复杂的场景）
    test_document = """
# interRAI家庭护理评估表（简化版）

## 基本信息
- 评估编号：______________
- 评估日期：____年____月____日
- 评估者：______________
- 评估机构：______________

## A部分：认知模式

### A1. 短期记忆
回忆5分钟前发生的事情
- 0 = 记忆正常
- 1 = 记忆有问题

### A2. 程序性记忆
执行多步骤任务的能力
- 0 = 独立 - 无需提示
- 1 = 需要有限的提示
- 2 = 需要大量提示
- 3 = 需要完全依赖他人

### A3. 认知技能用于日常决策
- 0 = 独立 - 决策一致且合理
- 1 = 轻度受损 - 在新情况下决策困难
- 2 = 中度受损 - 决策不良，需要提醒
- 3 = 严重受损 - 从不或很少做决策

## B部分：功能状态

### B1. ADL自理能力
评估以下活动的独立程度（0-4分）：
- a) 进食
- b) 个人卫生
- c) 洗澡
- d) 穿衣（上身）
- e) 穿衣（下身）
- f) 行走
- g) 移位
- h) 如厕

评分标准：
- 0 = 独立
- 1 = 准备
- 2 = 监督
- 3 = 有限协助
- 4 = 广泛协助
- 5 = 最大协助
- 6 = 完全依赖

### B2. IADL工具性日常生活活动
评估以下活动的表现（0-3分）：
- a) 膳食准备
- b) 日常家务
- c) 管理财务
- d) 管理药物
- e) 电话使用
- f) 购物
- g) 交通

评分标准：
- 0 = 独立
- 1 = 需要一些帮助
- 2 = 需要很多帮助
- 3 = 由他人执行

## C部分：健康状况

### C1. 疾病诊断
记录是否存在（0=否，1=是）：
- a) 阿尔茨海默病/痴呆
- b) 中风/CVA
- c) 冠心病
- d) 充血性心力衰竭
- e) 慢性阻塞性肺病
- f) 糖尿病
- g) 关节炎
- h) 骨质疏松
- i) 髋部骨折

### C2. 疼痛
- 频率：0=无 1=少于每日 2=每日
- 强度：0=无 1=轻度 2=中度 3=重度

### C3. 跌倒
过去90天内的跌倒次数：_____

## D部分：心理社会健康

### D1. 情绪状态
过去3天内的表现（0=未出现 1=出现但未每日 2=每日出现）：
- a) 负面陈述
- b) 持续愤怒
- c) 不切实际的恐惧
- d) 重复健康抱怨
- e) 重复焦虑抱怨
- f) 悲伤、痛苦、担忧的表情
- g) 哭泣、流泪

### D2. 社会参与
- 0 = 积极参与
- 1 = 一般参与
- 2 = 很少参与
- 3 = 从不参与

## 评估汇总
- ADL层级量表得分（0-6）：_____
- IADL表现量表得分（0-21）：_____
- 认知表现量表得分（0-6）：_____
- 抑郁评定量表得分（0-14）：_____

## 护理计划建议
基于评估结果制定个性化护理计划
"""
    
    # 完整的提示词 = 最终优化版提示词 + 文档内容
    full_prompt = final_optimized_prompt + "\n\n" + test_document

    try:
        print("🔍 获取LM Studio可用模型...")
        models_response = requests.get("http://192.168.1.231:1234/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
            
        models_data = models_response.json()
        selected_model = None
        for model in models_data['data']:
            model_id = model['id'].lower()
            if 'embedding' not in model_id and 'whisper' not in model_id:
                selected_model = model['id']
                break
        
        if not selected_model:
            print("❌ 未找到合适的对话模型")
            return False
            
        print(f"✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送最终优化版提示词到LM Studio...")
        print(f"📊 提示词总长度: {len(full_prompt)} 字符")
        print(f"📄 测试文档: interRAI家庭护理评估表（复杂场景）")
        print("🎯 验证重点: 完整性、命名规范、索引策略、JSON输出")
        print("⏳ 等待AI深度分析中（请耐心等待）...")
        
        start_time = time.time()
        
        response = requests.post(
            "http://192.168.1.231:1234/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=1200  # 20分钟超时
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 最终优化版提示词测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 详细质量检查
                # 1. 基础功能检查
                has_document_analysis = '文档分析结果' in ai_response or '文档类型' in ai_response
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_json = '"database_design"' in ai_response and '"fields"' in ai_response
                has_constraints = 'CHECK' in ai_response.upper() or 'CONSTRAINT' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                has_triggers = 'CREATE TRIGGER' in ai_response.upper()
                
                # 2. 优化特性检查
                snake_case_check = all(keyword in ai_response for keyword in ['_id', '_at', '_by'])
                composite_index_check = 'CREATE INDEX' in ai_response.upper() and (',' in ai_response or 'DESC' in ai_response.upper())
                json_format_check = '"type"' in ai_response and '"name"' in ai_response
                business_field_check = any(field in ai_response.lower() for field in ['cognitive', 'adl', 'iadl', 'health', 'pain'])
                
                # 3. 企业级特性检查
                has_audit_fields = all(field in ai_response for field in ['created_by', 'updated_by', 'version'])
                has_soft_delete = 'deleted_at' in ai_response
                has_data_quality = 'data_quality' in ai_response or 'validation_errors' in ai_response
                
                print(f"\n📊 质量检查报告:")
                print(f"\n1️⃣ 基础功能检查:")
                print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
                print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
                print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
                print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
                print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
                print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
                print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
                
                print(f"\n2️⃣ 优化特性检查:")
                print(f"   🔧 snake_case命名: {'通过' if snake_case_check else '❌未通过'}")
                print(f"   🔧 复合索引: {'通过' if composite_index_check else '❌未通过'}")
                print(f"   🔧 标准JSON格式: {'通过' if json_format_check else '❌未通过'}")
                print(f"   🔧 业务字段映射: {'通过' if business_field_check else '❌未通过'}")
                
                print(f"\n3️⃣ 企业级特性检查:")
                print(f"   🏢 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
                print(f"   🏢 软删除: {'通过' if has_soft_delete else '❌未通过'}")
                print(f"   🏢 数据质量: {'通过' if has_data_quality else '❌未通过'}")
                
                # 保存完整结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/final_optimized_test_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 最终优化版通用数据库设计提示词测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**LM Studio地址**: http://192.168.1.231:1234\n")
                    f.write(f"**模型**: {selected_model}\n")
                    f.write(f"**处理时间**: {analysis_time:.1f}秒\n")
                    f.write(f"**测试文档**: interRAI家庭护理评估表\n")
                    f.write(f"**响应长度**: {len(ai_response)} 字符\n\n")
                    f.write("---\n\n")
                    f.write("## AI生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: {result_file}")
                
                # 计算质量评分
                basic_score = 0
                if has_document_analysis: basic_score += 10
                if has_create_table: basic_score += 15
                if has_json: basic_score += 15
                if has_constraints: basic_score += 10
                if has_indexes: basic_score += 10
                if has_comments: basic_score += 5
                if has_triggers: basic_score += 5
                
                optimization_score = 0
                if snake_case_check: optimization_score += 10
                if composite_index_check: optimization_score += 10
                if json_format_check: optimization_score += 5
                if business_field_check: optimization_score += 5
                
                enterprise_score = 0
                if has_audit_fields: enterprise_score += 5
                if has_soft_delete: enterprise_score += 5
                if has_data_quality: enterprise_score += 5
                
                total_score = basic_score + optimization_score + enterprise_score
                
                print(f"\n🏆 质量评分汇总:")
                print(f"   📋 基础功能: {basic_score}/70分")
                print(f"   🔧 优化特性: {optimization_score}/30分")
                print(f"   🏢 企业特性: {enterprise_score}/15分")
                print(f"   📊 总分: {total_score}/115分")
                
                # 评级
                if total_score >= 105:
                    grade = "S级 - 卓越品质"
                elif total_score >= 95:
                    grade = "A级 - 优秀"
                elif total_score >= 85:
                    grade = "B级 - 良好"
                elif total_score >= 75:
                    grade = "C级 - 合格"
                else:
                    grade = "D级 - 需改进"
                
                print(f"\n🎯 最终评级: {grade}")
                
                # 显示部分响应预览
                print(f"\n📄 响应预览（前1000字符）:")
                print("=" * 80)
                print(ai_response[:1000] + "..." if len(ai_response) > 1000 else ai_response)
                print("=" * 80)
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ AI分析超时（超过20分钟）")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 最终优化版通用数据库设计提示词测试")
    print("=" * 80)
    print("📍 LM Studio地址: http://192.168.1.231:1234")
    print("📄 测试文档: interRAI家庭护理评估表（复杂多维度评估）")
    print("🎯 测试目标: 验证最终优化版的综合质量")
    print("=" * 80)
    
    if test_final_optimized_prompt():
        print(f"\n✅ 测试成功完成!")
        print(f"📊 请查看详细测试结果文件")
        print(f"🚀 最终优化版提示词已准备好在不同模型上测试")
    else:
        print(f"\n❌ 测试失败，请检查LM Studio连接")

if __name__ == "__main__":
    main()