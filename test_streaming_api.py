#!/usr/bin/env python3
"""
测试LM Studio真流式分析API
验证新实现的流式输出功能
"""

import json
import requests
import time
from datetime import datetime

def test_streaming_analysis():
    """测试流式分析API"""
    
    # 构建测试数据
    test_request = {
        "markdownContent": """
# 老年人日常生活能力评估表

## 基本信息
- 姓名：________
- 年龄：________
- 性别：□男 □女
- 评估日期：________

## 日常生活活动评估

### 1. 洗澡
□ 1分：完全需要帮助
□ 5分：需要部分帮助  
□ 10分：能够独立完成

### 2. 穿衣
□ 1分：完全需要帮助
□ 5分：需要部分帮助
□ 10分：能够独立完成

### 3. 如厕
□ 1分：完全需要帮助
□ 5分：需要部分帮助
□ 10分：能够独立完成

### 4. 行走
□ 1分：完全需要帮助/轮椅
□ 5分：需要辅助器具
□ 10分：能够独立行走

### 5. 进食
□ 1分：完全需要帮助
□ 5分：需要部分帮助
□ 10分：能够独立进食

## 评估结果
总分：_______ (满分50分)

评估等级：
- 重度依赖：5-20分
- 中度依赖：25-35分  
- 轻度依赖：40-45分
- 完全独立：50分

评估师签名：________
        """,
        "fileName": "老年人日常生活能力评估表.md",
        "customPrompt": """请分析这个老年人日常生活能力评估表，设计PostgreSQL数据库表结构。

要求：
1. 创建主表存储基本信息和总分
2. 创建评估项目表存储各项能力评估详情
3. 设计合理的字段类型和约束
4. 添加必要的索引和注释

请生成完整的建表SQL语句和JSON格式的分析结果。""",
        "useStream": True
    }
    
    print("🚀 开始测试真流式分析API")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 发送流式请求
    url = "http://localhost:8181/api/ai/analyze-document-structure-stream"
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    try:
        print("📡 发送SSE请求到后端...")
        response = requests.post(url, json=test_request, headers=headers, stream=True, timeout=120)
        
        if response.status_code == 200:
            print("✅ 连接成功，开始接收流式数据...")
            print("-" * 60)
            
            ai_content_buffer = ""
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"📨 收到原始数据: {line}")
                    
                    if line.startswith("data: "):
                        event_data = line[6:]  # 移除 "data: " 前缀
                        
                        # 尝试解析为JSON (如果是result事件)
                        try:
                            if event_data.startswith('{') and event_data.endswith('}'):
                                result_data = json.loads(event_data)
                                print(f"📊 解析结果数据: {json.dumps(result_data, ensure_ascii=False, indent=2)}")
                            else:
                                print(f"📝 事件内容: {event_data}")
                        except json.JSONDecodeError:
                            print(f"📝 事件内容: {event_data}")
                    
                    elif line.startswith("event: "):
                        event_type = line[7:]  # 移除 "event: " 前缀
                        print(f"🎯 事件类型: {event_type}")
                        
                        if event_type == "ai_content":
                            print("🤖 开始接收AI生成内容...")
                    
                    print()  # 空行分隔
            
            print("-" * 60)
            print("✅ 流式数据接收完成")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误响应: {response.text}")
    
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误，请检查后端服务是否运行")
    except Exception as e:
        print(f"💥 测试异常: {str(e)}")

if __name__ == "__main__":
    test_streaming_analysis()