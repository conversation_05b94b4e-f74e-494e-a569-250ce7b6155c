#!/usr/bin/env python3
"""
LM Studio API 测试脚本
用于调试 Java 后端与 LM Studio 的连接问题
"""

import requests
import json

# LM Studio配置
LM_STUDIO_URL = "http://192.168.1.225:1234"

def test_models_endpoint():
    """测试获取模型列表接口"""
    print("🔍 测试模型列表接口...")
    
    try:
        response = requests.get(f"{LM_STUDIO_URL}/v1/models", 
                              headers={"Content-Type": "application/json"})
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if "data" in data and len(data["data"]) > 0:
                model_id = data["data"][0]["id"]
                print(f"✅ 获取到模型: {model_id}")
                return model_id
            else:
                print("❌ 没有可用的模型")
                return None
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_chat_completions(model_id):
    """测试对话完成接口"""
    print(f"\n💬 测试对话完成接口 (模型: {model_id})...")
    
    # 构建请求数据
    request_data = {
        "model": model_id,
        "messages": [
            {
                "role": "user",
                "content": "你好，请简单回复一句话。"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 100,
        "stream": False
    }
    
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(f"{LM_STUDIO_URL}/v1/chat/completions",
                               headers={"Content-Type": "application/json"},
                               json=request_data,
                               timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                print(f"✅ AI回复: {content}")
                return True
            else:
                print("❌ 响应格式异常")
                return False
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_complex_prompt(model_id):
    """测试复杂提示词（类似Java后端的提示词）"""
    print(f"\n📝 测试复杂提示词 (模型: {model_id})...")
    
    prompt = """
你是一个专业的数据库设计师，请分析以下Markdown文档并生成数据库设计。

文档内容：
# 老年人能力评估量表
## 基本信息
- 姓名：___
- 年龄：___
- 性别：___

## 评估项目
1. 日常生活能力 (0-10分)
2. 认知能力 (0-10分)
3. 情感状态 (0-10分)

请生成JSON格式的分析结果：
{
  "scaleType": "老年人能力评估",
  "tableName": "elderly_assessment",
  "confidence": 85,
  "fields": [
    {"name": "name", "type": "VARCHAR", "length": "100", "comment": "姓名"},
    {"name": "age", "type": "INT", "comment": "年龄"}
  ]
}
"""
    
    request_data = {
        "model": model_id,
        "messages": [
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 2000,
        "stream": False
    }
    
    try:
        response = requests.post(f"{LM_STUDIO_URL}/v1/chat/completions",
                               headers={"Content-Type": "application/json"},
                               json=request_data,
                               timeout=60)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                print(f"✅ AI分析结果:")
                print(content)
                return True
            else:
                print("❌ 响应格式异常")
                print(f"响应内容: {response.text}")
                return False
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    print("🚀 LM Studio API 连接测试")
    print(f"目标地址: {LM_STUDIO_URL}")
    print("=" * 50)
    
    # 测试模型列表
    model_id = test_models_endpoint()
    
    if model_id:
        # 测试简单对话
        if test_chat_completions(model_id):
            # 测试复杂提示词
            test_complex_prompt(model_id)
        else:
            print("❌ 简单对话测试失败，跳过复杂提示词测试")
    else:
        print("❌ 无法获取模型信息，跳过后续测试")

if __name__ == "__main__":
    main()