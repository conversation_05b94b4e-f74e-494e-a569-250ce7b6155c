#!/bin/bash

# GitHub Branch Protection Setup Script
# This script sets up branch protection rules for the Assessment Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPO_OWNER=${1:-"your-github-username"}
REPO_NAME=${2:-"Assessment"}
GITHUB_TOKEN=${GITHUB_TOKEN:-""}

echo -e "${BLUE}🔧 Setting up branch protection for ${REPO_OWNER}/${REPO_NAME}${NC}"

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo -e "${RED}❌ GitHub CLI (gh) is not installed. Please install it first.${NC}"
    echo -e "${YELLOW}   Visit: https://cli.github.com/manual/installation${NC}"
    exit 1
fi

# Check if authenticated
if ! gh auth status &> /dev/null; then
    echo -e "${YELLOW}⚠️ Please authenticate with GitHub CLI first:${NC}"
    echo -e "${YELLOW}   gh auth login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ GitHub CLI authenticated${NC}"

# Function to create branch protection rule
setup_branch_protection() {
    local branch_name=$1
    local required_reviews=$2
    local required_checks=$3
    
    echo -e "${BLUE}📝 Setting up protection for branch: ${branch_name}${NC}"
    
    # Create the protection rule using GitHub API
    local protection_config='{
        "required_status_checks": {
            "strict": true,
            "contexts": '"$required_checks"'
        },
        "enforce_admins": true,
        "required_pull_request_reviews": {
            "required_approving_review_count": '"$required_reviews"',
            "dismiss_stale_reviews": true,
            "require_code_owner_reviews": false
        },
        "restrictions": null,
        "required_conversation_resolution": true
    }'
    
    # Apply the protection rule
    if gh api repos/"${REPO_OWNER}"/"${REPO_NAME}"/branches/"${branch_name}"/protection \
        --method PUT \
        --input - <<< "$protection_config" &> /dev/null; then
        echo -e "${GREEN}  ✅ Protection rule applied successfully${NC}"
    else
        echo -e "${RED}  ❌ Failed to apply protection rule${NC}"
        return 1
    fi
}

# Main branch protection
echo -e "\n${BLUE}🔒 Configuring main branch protection...${NC}"
MAIN_REQUIRED_CHECKS='[
    "Backend Tests & Coverage",
    "Frontend Web Tests",
    "Mobile/Uni-app Tests", 
    "Quality Gate",
    "Coverage Enforcement"
]'

setup_branch_protection "main" 1 "$MAIN_REQUIRED_CHECKS"

# Develop branch protection
echo -e "\n${BLUE}🔒 Configuring develop branch protection...${NC}"
DEVELOP_REQUIRED_CHECKS='[
    "Backend Tests & Coverage",
    "Quality Gate"
]'

setup_branch_protection "develop" 1 "$DEVELOP_REQUIRED_CHECKS"

# Display configuration summary
echo -e "\n${GREEN}📋 Branch Protection Summary${NC}"
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}Main Branch:${NC}"
echo -e "  - Required reviews: 1"
echo -e "  - Required status checks: Backend Tests, Frontend Tests, Mobile Tests, Quality Gate"
echo -e "  - Dismiss stale reviews: Yes"
echo -e "  - Conversation resolution required: Yes"
echo -e "  - Include administrators: Yes"

echo -e "\n${GREEN}Develop Branch:${NC}"
echo -e "  - Required reviews: 1"
echo -e "  - Required status checks: Backend Tests, Quality Gate"
echo -e "  - Dismiss stale reviews: Yes"

# Create GitHub Pages deployment (if needed)
echo -e "\n${BLUE}📄 Checking GitHub Pages configuration...${NC}"
if gh api repos/"${REPO_OWNER}"/"${REPO_NAME}"/pages &> /dev/null; then
    echo -e "${GREEN}  ✅ GitHub Pages already configured${NC}"
else
    echo -e "${YELLOW}  ⚠️ GitHub Pages not configured (optional)${NC}"
    echo -e "${YELLOW}     You can set this up manually for documentation hosting${NC}"
fi

# Create issue templates directory
echo -e "\n${BLUE}📝 Setting up issue templates...${NC}"
mkdir -p .github/ISSUE_TEMPLATE

# Bug report template
cat > .github/ISSUE_TEMPLATE/bug_report.md << 'EOF'
---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment
- **Platform**: [e.g., Web Admin, Mobile H5, WeChat Mini Program]
- **Browser**: [e.g., Chrome, Safari]
- **Version**: [e.g., 1.0.0]
- **Device**: [e.g., iPhone 12, Desktop]

## Additional Context
Add any other context about the problem here.

## Test Coverage
- [ ] This bug is covered by existing tests
- [ ] New tests should be added
- [ ] No tests needed
EOF

# Feature request template
cat > .github/ISSUE_TEMPLATE/feature_request.md << 'EOF'
---
name: Feature Request
about: Suggest an idea for the Assessment Platform
title: '[FEATURE] '
labels: enhancement
assignees: ''
---

## Feature Description
A clear and concise description of what the problem is and what you want to happen.

## Use Case
Describe the use case that this feature would address.

## Proposed Solution
A clear and concise description of what you want to happen.

## Alternative Solutions
A clear and concise description of any alternative solutions or features you've considered.

## Implementation Notes
- **Affected Components**: [e.g., Backend API, Frontend Admin, Mobile App]
- **Estimated Complexity**: [e.g., Low, Medium, High]
- **Breaking Changes**: [e.g., Yes/No]

## Additional Context
Add any other context, mockups, or screenshots about the feature request here.

## Acceptance Criteria
- [ ] Functional requirement 1
- [ ] Functional requirement 2
- [ ] Test coverage requirement
- [ ] Documentation requirement
EOF

echo -e "${GREEN}  ✅ Issue templates created${NC}"

# Create pull request template
cat > .github/pull_request_template.md << 'EOF'
## 📋 Change Summary
Brief description of the changes in this PR.

## 🔗 Related Issue
Fixes #(issue number)

## 🧪 Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] Coverage requirements met (85%+)

## 📱 Platform Testing
- [ ] Backend API tested
- [ ] Web admin interface tested
- [ ] Mobile H5 tested
- [ ] WeChat mini-program tested (if applicable)

## 🔍 Code Quality
- [ ] Code follows project standards
- [ ] ESLint/Checkstyle passes
- [ ] Security scan passes
- [ ] No breaking changes (or documented)

## 📖 Documentation
- [ ] Code is self-documenting
- [ ] README updated (if needed)
- [ ] API documentation updated (if needed)

## 🚀 Deployment Notes
Any special deployment instructions or considerations.

## 📸 Screenshots (if applicable)
Visual changes or new features.
EOF

echo -e "${GREEN}  ✅ Pull request template created${NC}"

# Final setup verification
echo -e "\n${GREEN}🎉 CI/CD Setup Complete!${NC}"
echo -e "${GREEN}================================${NC}"
echo -e "Next steps:"
echo -e "1. ${YELLOW}Push these changes to your repository${NC}"
echo -e "2. ${YELLOW}Verify branch protection rules in GitHub Settings${NC}"
echo -e "3. ${YELLOW}Test the CI/CD pipeline with a test PR${NC}"
echo -e "4. ${YELLOW}Configure any additional secrets (CODECOV_TOKEN, etc.)${NC}"

echo -e "\n${BLUE}📚 Documentation:${NC}"
echo -e "- CI/CD Guide: .github/README_CI_CD.md"
echo -e "- JaCoCo Setup: backend/JaCoCo_Coverage_Setup_Complete.md"

echo -e "\n${GREEN}✅ Assessment Platform CI/CD is production-ready!${NC}"
EOF