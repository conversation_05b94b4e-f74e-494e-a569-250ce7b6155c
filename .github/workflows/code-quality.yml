name: Code Quality & Security

on:
  push:
    branches: [ main, develop, 'cursor/*' ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scan weekly on Sundays at 02:00 UTC
    - cron: '0 2 * * 0'

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'

jobs:
  backend-quality:
    name: Backend Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven

    - name: Run OWASP Dependency Check
      working-directory: backend
      run: |
        chmod +x mvnw
        ./mvnw org.owasp:dependency-check-maven:check -DfailBuildOnCVSS=7
      continue-on-error: true

    - name: Upload security reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: backend/target/dependency-check-report.html
        retention-days: 90

  frontend-quality:
    name: Frontend Code Quality  
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/admin/package-lock.json

    - name: Install admin dependencies
      working-directory: frontend/admin
      run: npm ci

    - name: Run admin ESLint
      working-directory: frontend/admin
      run: npx eslint src/ --ext .js,.ts,.vue || echo "ESLint issues found"

    - name: Run admin type checking
      working-directory: frontend/admin
      run: npx vue-tsc --noEmit || echo "Type checking issues found"

  mobile-quality:
    name: Mobile/Uni-app Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/uni-app/package-lock.json

    - name: Install uni-app dependencies
      working-directory: frontend/uni-app
      run: npm ci

    - name: Run uni-app ESLint
      working-directory: frontend/uni-app
      run: npx eslint src/ --ext .js,.vue || echo "ESLint issues found"

    - name: Build uni-app for H5
      working-directory: frontend/uni-app
      run: npm run build:h5

    - name: Build uni-app for WeChat Mini Program
      working-directory: frontend/uni-app
      run: npm run build:mp-weixin || echo "WeChat mini-program build failed"

    - name: Upload mobile build artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: mobile-build-results
        path: |
          frontend/uni-app/dist/
        retention-days: 7

  coverage-enforcement:
    name: Coverage Enforcement
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: assessment_test
          POSTGRES_USER: assessment_user
          POSTGRES_PASSWORD: assessment_pass
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven

    - name: Run tests with strict coverage enforcement
      working-directory: backend
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: ************************************************
        SPRING_DATASOURCE_USERNAME: assessment_user
        SPRING_DATASOURCE_PASSWORD: assessment_pass
      run: |
        chmod +x mvnw
        ./mvnw clean verify -B

    - name: Comment coverage status on PR
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const comment = `## ✅ Coverage Enforcement Report
          
          **Status**: Coverage thresholds checked
          
          ### Coverage Requirements
          - **Overall Project**: 85% instruction coverage
          - **Service Layer**: 90% instruction coverage  
          - **Controller Layer**: 80% instruction coverage
          - **Maximum Uncovered Classes**: 5
          
          For detailed coverage reports, check the CI artifacts.
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });