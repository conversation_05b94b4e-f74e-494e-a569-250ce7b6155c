name: Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security scan daily at 2 AM UTC
    - cron: '0 2 * * *'

permissions:
  contents: read
  security-events: write
  actions: read

jobs:
  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read
    
    strategy:
      fail-fast: false
      matrix:
        language: ['java', 'javascript']
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
        queries: security-extended,security-and-quality
        
    - name: Set up JDK 21
      if: matrix.language == 'java'
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Set up Node.js 18
      if: matrix.language == 'javascript'
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'frontend/package-lock.json'
        
    - name: Install frontend dependencies
      if: matrix.language == 'javascript'
      working-directory: ./frontend
      run: npm ci
      
    - name: Build backend
      if: matrix.language == 'java'
      working-directory: ./backend
      run: mvn clean compile -DskipTests
      
    - name: Build frontend
      if: matrix.language == 'javascript'
      working-directory: ./frontend
      run: npm run build
      
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{ matrix.language }}"

  trivy:
    name: Trivy Security Scan
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner in repo mode
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        severity: 'CRITICAL,HIGH,MEDIUM'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: Generate Trivy report
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'table'
        output: 'trivy-report.txt'
        
    - name: Upload Trivy report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: trivy-security-report
        path: trivy-report.txt
        retention-days: 30

  dependency-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Set up Node.js 18
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install frontend dependencies
      run: |
        cd frontend && npm ci || npm install
        cd admin && npm ci || npm install
        cd ../uni-app && npm ci || npm install
        
    - name: Run OWASP Dependency Check (Backend)
      working-directory: ./backend
      run: |
        mvn org.owasp:dependency-check-maven:check \
          -DfailBuildOnCVSS=7 \
          -DsuppressedVulnerabilitiesXml=.owasp-suppressions.xml
      continue-on-error: true
        
    - name: Run npm audit (Frontend - Main)
      working-directory: ./frontend
      run: |
        npm audit --audit-level=moderate --json > npm-audit-main.json || true
        
    - name: Run npm audit (Frontend - Admin)
      working-directory: ./frontend/admin
      run: |
        npm audit --audit-level=moderate --json > npm-audit-admin.json || true
        
    - name: Run npm audit (Frontend - Uni-app)
      working-directory: ./frontend/uni-app
      run: |
        npm audit --audit-level=moderate --json > npm-audit-uni-app.json || true
        
    - name: Upload dependency check reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: dependency-security-reports
        path: |
          backend/target/dependency-check-report.html
          frontend/npm-audit-main.json
          frontend/admin/npm-audit-admin.json
          frontend/uni-app/npm-audit-uni-app.json
        retention-days: 30