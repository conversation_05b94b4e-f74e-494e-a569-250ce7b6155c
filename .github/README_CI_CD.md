# CI/CD Pipeline Documentation

## 概述

智能评估平台已完成全面的CI/CD集成，包括测试覆盖率强制执行、代码质量检查和多平台构建验证。

## 工作流概览

### 1. 主要测试与覆盖率工作流 (`ci-test-coverage.yml`)

**触发条件**:
- Push to: `main`, `develop`, `cursor/*` 分支
- Pull Request to: `main`, `develop` 分支

**包含测试**:
- ✅ **后端测试**: Spring Boot + JaCoCo覆盖率
- ✅ **前端Web测试**: Vue.js管理后台
- ✅ **移动端测试**: uni-app多平台构建
- ✅ **安全扫描**: OWASP依赖检查

### 2. 代码质量与安全工作流 (`code-quality.yml`)

**触发条件**:
- Push/PR事件
- 每周日02:00 UTC定时安全扫描

**质量检查**:
- 后端代码质量 (OWASP Dependency Check)
- 前端代码质量 (ESLint, TypeScript检查)
- 移动端代码质量 (ESLint, 多平台构建)
- PR覆盖率强制执行

## 测试覆盖率要求

### 强制执行标准
```yaml
整体项目: 85% 指令覆盖率
服务层: 90% 指令覆盖率  
控制器层: 80% 指令覆盖率
最大未覆盖类: 5个
```

### 覆盖率报告
- **单元测试**: `target/site/jacoco-ut/index.html`
- **集成测试**: `target/site/jacoco-it/index.html`
- **合并报告**: `target/site/jacoco-merged/index.html`

## 移动端测试策略

### Uni-app多平台支持
1. **H5构建**: Web浏览器兼容性测试
2. **微信小程序**: 微信小程序平台构建
3. **App Plus**: Android/iOS原生应用构建 (CI环境可能失败)

### 移动端验证
- 组件存在性检查
- pages.json配置验证
- 基础语法检查
- 多平台构建兼容性

## 质量门禁

### PR合并要求
1. ✅ **后端测试必须通过** (强制)
2. ⚠️ **前端测试问题** (非阻塞，但建议修复)
3. ⚠️ **移动端测试问题** (非阻塞，但建议修复)
4. ✅ **覆盖率达标** (强制)

### 安全要求
- 高危漏洞检测自动创建Issue
- 依赖安全扫描报告
- OWASP CVSS 7.0+阻塞构建

## GitHub Actions配置

### 必需的Secrets
```yaml
# 可选，用于Codecov集成
CODECOV_TOKEN: <codecov_token>
```

### 推荐的分支保护规则

#### Main分支
```yaml
# GitHub Repository Settings > Branches > Add rule
Branch name pattern: main

Protection rules:
☑️ Require a pull request before merging
  ☑️ Require approvals: 1
  ☑️ Dismiss stale PR approvals when new commits are pushed
  ☑️ Require review from code owners

☑️ Require status checks to pass before merging
  ☑️ Require branches to be up to date before merging
  Required status checks:
    - Backend Tests & Coverage
    - Frontend Web Tests  
    - Mobile/Uni-app Tests
    - Quality Gate

☑️ Require conversation resolution before merging
☑️ Include administrators
```

#### Develop分支
```yaml
Branch name pattern: develop

Protection rules:
☑️ Require a pull request before merging
  ☑️ Require approvals: 1

☑️ Require status checks to pass before merging
  Required status checks:
    - Backend Tests & Coverage
    - Quality Gate
```

## 工作流特性

### 🚀 性能优化
- Maven/Node.js依赖缓存
- 并行任务执行
- 增量构建支持

### 📊 报告生成
- 自动化测试结果摘要
- 覆盖率PR评论
- 安全漏洞Issue创建
- 构建产物归档

### 🔧 CI/CD最佳实践
- 失败快速反馈
- 详细错误日志
- 多平台构建验证
- 安全优先策略

## 本地开发集成

### 预提交检查
```bash
# 运行完整测试套件
cd backend && ./mvnw clean verify

# 检查覆盖率要求
./mvnw jacoco:check

# 前端测试
cd frontend/admin && npm run test:unit

# 移动端构建
cd frontend/uni-app && npm run build:h5
```

### 代码质量检查
```bash
# 后端安全扫描
./mvnw org.owasp:dependency-check-maven:check

# 前端代码检查
npx eslint src/ --ext .js,.ts,.vue
```

## 故障排除

### 常见问题

1. **测试覆盖率不达标**
   - 检查`target/site/jacoco-merged/index.html`
   - 补充缺失的测试用例
   - 确认排除配置正确

2. **移动端构建失败**
   - 检查uni-app配置
   - 验证pages.json语法
   - 确认组件导入路径

3. **安全扫描误报**
   - 更新`owasp-dependency-check-suppressions.xml`
   - 升级依赖版本
   - 添加漏洞抑制配置

### 调试工具
- GitHub Actions日志
- CI产物下载
- 本地Maven/Node.js命令
- Coverage报告分析

## 持续改进

### 下一步计划
1. **SonarQube集成**: 代码质量深度分析
2. **性能测试**: JMeter/Lighthouse集成
3. **Docker化CI**: 容器化构建环境
4. **多环境部署**: 自动化部署流水线

### 监控指标
- 测试通过率
- 覆盖率趋势
- 构建时间
- 安全漏洞数量

---

**配置完成时间**: 2025-06-22  
**配置状态**: ✅ 生产就绪  
**维护团队**: 开发团队