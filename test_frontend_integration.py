#!/usr/bin/env python3
"""
测试前端集成的AI分析功能
验证自定义提示词和优化后的界面
"""

import requests
import json
import time
from datetime import datetime

def test_frontend_integration():
    """测试前端集成功能"""
    print("🚀 测试前端AI分析集成功能")
    print("=" * 70)
    
    # 测试配置
    frontend_url = "http://localhost:5274"
    backend_url = "http://localhost:8181/api"
    
    # 读取测试文档
    try:
        with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
            test_document = f.read()
        print(f"✅ 测试文档读取成功，长度: {len(test_document)} 字符")
    except FileNotFoundError:
        print("❌ 未找到测试文档")
        return False
    
    # 读取最佳提示词
    try:
        with open("/Volumes/acasis/Assessment/docs/简洁版LM_Studio数据库设计提示词.md", "r", encoding="utf-8") as f:
            content = f.read()
            # 提取提示词部分
            start_marker = "```text"
            end_marker = "```"
            start_idx = content.find(start_marker) + len(start_marker)
            end_idx = content.find(end_marker, start_idx)
            optimal_prompt = content[start_idx:end_idx].strip()
        print(f"✅ 最佳提示词读取成功，长度: {len(optimal_prompt)} 字符")
    except Exception as e:
        print(f"❌ 读取最佳提示词失败: {e}")
        return False
    
    # 添加当前日期到提示词
    current_date = datetime.now().strftime('%Y年%m月%d日')
    system_prompt = f"""你是一个经验丰富的PostgreSQL数据库设计师，当前时间是{current_date}。
专门负责将中文文档内容转换为高质量的数据库设计。
请使用专业的数据库知识和最佳实践来完成任务。

特别注意：
- 充分利用你的深度推理能力分析文档结构
- 准确理解中文业务术语的含义
- 生成符合PostgreSQL最佳实践的高质量SQL"""
    
    full_prompt = system_prompt + "\n\n" + optimal_prompt
    
    print(f"\n📊 测试数据准备完成:")
    print(f"   📄 测试文档: {len(test_document)} 字符")
    print(f"   📝 优化提示词: {len(full_prompt)} 字符") 
    print(f"   🎯 预期效果: 136/136分验证版本")
    
    # 测试后端AI分析接口
    print(f"\n🔗 测试后端AI分析接口...")
    try:
        test_data = {
            "markdownContent": test_document,
            "fileName": "国标评估报告模板1.md",
            "customPrompt": full_prompt,
            "useStream": False  # 先测试非流式
        }
        
        start_time = time.time()
        response = requests.post(
            f"{backend_url}/ai/analyze-document-structure",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            processing_time = time.time() - start_time
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"✅ 后端分析成功!")
                print(f"   ⏱️ 处理时间: {processing_time:.1f}秒")
                print(f"   📊 识别表名: {data.get('tableName', '未知')}")
                print(f"   📈 置信度: {data.get('confidence', 0)}%")
                print(f"   🗄️ 字段数量: {len(data.get('fields', []))}")
                sql_statements = data.get('sqlStatements', '')
                print(f"   📝 SQL长度: {len(sql_statements) if sql_statements else 0}")
                
                # 显示部分结果预览
                if sql_statements:
                    sql_preview = sql_statements[:200] + "..." if len(sql_statements) > 200 else sql_statements
                    print(f"   🔍 SQL预览: {sql_preview}")
                
                return True
            else:
                print(f"❌ 后端分析失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return False

def test_frontend_access():
    """测试前端页面访问"""
    print(f"\n🌐 测试前端页面访问...")
    
    try:
        response = requests.get("http://localhost:5274", timeout=10)
        if response.status_code == 200:
            print(f"✅ 前端页面可访问")
            print(f"   📄 页面大小: {len(response.text)} 字符")
            return True
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端访问异常: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🧪 前端AI分析集成测试")
    print("=" * 70)
    print("📝 测试内容:")
    print("   1. 前端页面可访问性")
    print("   2. 后端AI分析接口")
    print("   3. 自定义提示词支持")
    print("   4. 集成质量验证")
    print("=" * 70)
    
    # 测试前端访问
    frontend_ok = test_frontend_access()
    
    # 测试集成功能
    integration_ok = test_frontend_integration()
    
    print(f"\n📊 测试结果总结:")
    print(f"   🌐 前端访问: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    print(f"   🤖 AI分析集成: {'✅ 通过' if integration_ok else '❌ 失败'}")
    
    if frontend_ok and integration_ok:
        print(f"\n🎉 所有测试通过！")
        print(f"🚀 优化后的PDF上传页面已准备就绪:")
        print(f"   📍 访问地址: http://localhost:5274/assessment/pdf-upload")
        print(f"   ✨ 新功能: 提示词编辑器、自定义提示词、已验证的最佳提示词")
        print(f"   🎯 性能: 136/136分验证版本，3-4分钟处理时间")
        return True
    else:
        print(f"\n❌ 部分测试失败，请检查相关服务")
        return False

if __name__ == "__main__":
    main()