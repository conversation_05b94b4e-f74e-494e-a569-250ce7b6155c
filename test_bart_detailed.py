#!/usr/bin/env python3
"""
深度测试 facebook/bart-large-cnn 模型
使用真实的评估文档进行完整测试
"""

import requests
import json
import time
from datetime import datetime

def test_bart_with_real_document():
    """使用真实文档测试BART模型"""
    print("🎯 BART模型深度测试")
    print("=" * 60)
    
    model = "facebook/bart-large-cnn"
    token = "*************************************"
    
    # 读取真实的国标评估文档片段
    print("📄 读取国标评估文档...")
    try:
        with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
            full_document = f.read()
        
        # 取前1000字符用于测试
        document_snippet = full_document[:1000]
        print(f"📊 文档长度: {len(document_snippet)} 字符")
        
    except FileNotFoundError:
        print("⚠️ 未找到国标文档，使用模拟数据")
        document_snippet = """
# 老年人能力评估表（国标GB/T42195-2022）

## 基本信息
- 姓名：_____
- 性别：_____
- 年龄：_____
- 评估日期：_____

## 日常生活活动能力评估

### 1. 进食能力
- 1分：能够独立进食，包括使用餐具
- 2分：需要部分帮助（如切菜、开瓶等）
- 3分：需要完全帮助或管饲

### 2. 洗澡能力  
- 1分：能够独立洗澡（包括进出浴室）
- 2分：需要部分帮助（如擦背、洗头等）
- 3分：需要完全帮助

### 3. 穿衣能力
- 1分：能够独立穿脱衣服和鞋袜
- 2分：需要部分帮助（如系扣子、系鞋带等）
- 3分：需要完全帮助

### 4. 如厕能力
- 1分：能够独立如厕（包括整理衣物）
- 2分：需要部分帮助（如擦拭、整理等）
- 3分：需要完全帮助或失禁

### 5. 转移能力
- 1分：能够独立在床、椅、厕所间转移
- 2分：需要部分帮助或监督
- 3分：需要完全帮助

### 6. 行走能力
- 1分：能够独立行走50米以上
- 2分：需要辅助工具或扶持
- 3分：轮椅或卧床

## 评分标准
- 6-10分：轻度失能
- 11-15分：中度失能  
- 16-18分：重度失能
"""
    
    # 创建专门的SQL生成提示词
    sql_prompt = f"""请根据以下评估表文档，设计完整的PostgreSQL数据库结构。

要求：
1. 分析文档内容，识别所有评估项目
2. 生成完整的CREATE TABLE语句
3. 包含主键、业务字段、时间戳字段
4. 添加适当的约束条件
5. 提供字段说明

文档内容：
{document_snippet}

请按以下格式输出：
表名：[表名]
CREATE TABLE语句：[完整SQL]
字段说明：[JSON格式]"""
    
    api_url = f"https://api-inference.huggingface.co/models/{model}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # BART是摘要模型，我们调整参数
    payload = {
        "inputs": sql_prompt,
        "parameters": {
            "max_length": 1024,  # BART用max_length而不是max_new_tokens
            "min_length": 200,
            "do_sample": True,
            "temperature": 0.7,
            "top_p": 0.9
        }
    }
    
    try:
        print(f"🤖 测试模型: {model}")
        print(f"📊 提示词总长度: {len(sql_prompt)} 字符")
        print("⏳ 发送API请求...")
        
        start_time = time.time()
        response = requests.post(api_url, headers=headers, json=payload, timeout=60)
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            api_response = response.json()
            
            # 处理BART响应
            if isinstance(api_response, list) and len(api_response) > 0:
                generated_text = api_response[0].get('summary_text', '')
                if not generated_text:
                    generated_text = api_response[0].get('generated_text', str(api_response[0]))
            else:
                generated_text = str(api_response)
            
            print(f"✅ API调用成功")
            print(f"⏱️ 响应时间: {processing_time:.2f}秒")
            print(f"📝 响应长度: {len(generated_text)} 字符")
            
            # 详细质量分析
            response_upper = generated_text.upper()
            response_lower = generated_text.lower()
            
            # SQL语法检查
            has_create_table = 'CREATE TABLE' in response_upper
            has_primary_key = 'PRIMARY KEY' in response_upper or 'PRIMARY' in response_upper
            has_data_types = any(dtype in response_upper for dtype in 
                               ['INTEGER', 'VARCHAR', 'TEXT', 'TIMESTAMP', 'BIGSERIAL'])
            has_constraints = any(constraint in response_upper for constraint in
                                ['CHECK', 'NOT NULL', 'UNIQUE', 'DEFAULT'])
            
            # 业务理解检查
            assessment_fields = ['eating', 'bathing', 'dressing', 'toilet', 'transfer', 'walking', 
                               '进食', '洗澡', '穿衣', '如厕', '转移', '行走']
            has_assessment_fields = any(field in response_lower for field in assessment_fields)
            
            has_scoring_logic = any(score in response_lower for score in 
                                  ['score', '分', 'level', '等级', 'total'])
            
            # 时间戳和审计字段
            has_timestamps = any(ts_field in response_lower for ts_field in 
                               ['created_at', 'updated_at', 'timestamp', 'time'])
            has_audit_fields = any(audit in response_lower for audit in 
                                 ['record_id', 'id', 'user_id', 'assessor'])
            
            # 结构化输出检查
            has_json_format = '{' in generated_text and '}' in generated_text
            has_table_name = any(indicator in response_lower for indicator in 
                               ['表名', 'table name', 'table:', '表:'])
            
            print(f"\n📊 详细质量分析:")
            print(f"🔧 SQL语法质量:")
            print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌失败'}")
            print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌失败'}")
            print(f"   ✅ 数据类型: {'通过' if has_data_types else '❌失败'}")
            print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌失败'}")
            
            print(f"🏥 业务理解质量:")
            print(f"   ✅ 评估字段: {'通过' if has_assessment_fields else '❌失败'}")
            print(f"   ✅ 评分逻辑: {'通过' if has_scoring_logic else '❌失败'}")
            
            print(f"🗄️ 数据库设计质量:")
            print(f"   ✅ 时间戳字段: {'通过' if has_timestamps else '❌失败'}")
            print(f"   ✅ 审计字段: {'通过' if has_audit_fields else '❌失败'}")
            
            print(f"📋 输出格式质量:")
            print(f"   ✅ JSON格式: {'通过' if has_json_format else '❌失败'}")
            print(f"   ✅ 表名说明: {'通过' if has_table_name else '❌失败'}")
            
            # 计算综合评分
            all_checks = [
                has_create_table, has_primary_key, has_data_types, has_constraints,
                has_assessment_fields, has_scoring_logic, has_timestamps, 
                has_audit_fields, has_json_format, has_table_name
            ]
            
            quality_score = sum(all_checks) * 10  # 满分100分
            
            print(f"\n🏆 综合质量评分: {quality_score}/100分")
            
            # 与本地模型对比
            print(f"\n📈 与本地deepseek对比:")
            print(f"本地deepseek: 125/125分(100%), 172.2秒")
            print(f"BART模型: {quality_score}/100分, {processing_time:.2f}秒")
            
            speed_improvement = (172.2 - processing_time) / 172.2 * 100
            quality_ratio = quality_score / 100
            
            print(f"⚡ 速度提升: {speed_improvement:.1f}%")
            print(f"📊 质量比率: {quality_ratio*100:.1f}%")
            
            # 实用性评估
            is_usable = quality_score >= 60 and processing_time < 60
            print(f"\n💡 实用性评估:")
            if is_usable:
                print("✅ 可投入生产使用")
                print("🎯 建议: 可作为本地模型的云端替代方案")
                print("💰 成本优势: 免费额度 + 按需付费")
            else:
                print("⚠️ 需要改进后再投入生产")
                if quality_score < 60:
                    print("🔧 主要问题: 生成质量需要提升")
                if processing_time >= 60:
                    print("⏱️ 主要问题: 响应时间过长")
            
            # 显示完整生成内容
            print(f"\n📄 完整生成内容:")
            print("=" * 80)
            print(generated_text)
            print("=" * 80)
            
            # 保存详细测试结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = f"/Volumes/acasis/Assessment/test_results/bart_detailed_test_{timestamp}.md"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write("# BART模型详细测试报告\n\n")
                f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**模型**: {model}\n")
                f.write(f"**HF Token**: {token}\n")
                f.write(f"**处理时间**: {processing_time:.2f}秒\n")
                f.write(f"**质量评分**: {quality_score}/100分\n")
                f.write(f"**文档长度**: {len(document_snippet)} 字符\n\n")
                
                f.write("## 质量分析详情\n\n")
                f.write("### SQL语法质量\n")
                f.write(f"- CREATE TABLE: {'✅' if has_create_table else '❌'}\n")
                f.write(f"- 主键设计: {'✅' if has_primary_key else '❌'}\n")
                f.write(f"- 数据类型: {'✅' if has_data_types else '❌'}\n")
                f.write(f"- 约束条件: {'✅' if has_constraints else '❌'}\n\n")
                
                f.write("### 业务理解质量\n")
                f.write(f"- 评估字段: {'✅' if has_assessment_fields else '❌'}\n")
                f.write(f"- 评分逻辑: {'✅' if has_scoring_logic else '❌'}\n\n")
                
                f.write("### 数据库设计质量\n")
                f.write(f"- 时间戳字段: {'✅' if has_timestamps else '❌'}\n")
                f.write(f"- 审计字段: {'✅' if has_audit_fields else '❌'}\n\n")
                
                f.write("## 完整生成内容\n\n")
                f.write("```\n")
                f.write(generated_text)
                f.write("\n```\n\n")
                
                f.write("## 结论\n\n")
                if is_usable:
                    f.write("✅ **推荐使用**: BART模型可作为云端SQL生成方案\n")
                    f.write("📊 **优势**: 速度快，成本低，基本质量可接受\n")
                    f.write("🎯 **建议**: 配合后处理逻辑优化输出质量\n")
                else:
                    f.write("⚠️ **谨慎使用**: 需要进一步优化\n")
                    f.write("💡 **建议**: 继续使用本地模型或尝试更强的云端模型\n")
            
            print(f"\n📄 详细报告已保存: {result_file}")
            
            return {
                "success": True,
                "quality_score": quality_score,
                "processing_time": processing_time,
                "generated_text": generated_text,
                "is_usable": is_usable
            }
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return {"success": False, "error": response.text}
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {"success": False, "error": str(e)}

def provide_integration_guidance(test_result):
    """提供集成指导"""
    print(f"\n🚀 集成指导建议")
    print("=" * 60)
    
    if test_result.get("success") and test_result.get("is_usable"):
        print("✅ BART模型可以集成到您的项目中")
        
        print(f"\n📋 集成步骤:")
        print("1. **API包装器开发**")
        print("   - 封装HF API调用逻辑")
        print("   - 添加错误处理和重试机制")
        print("   - 实现超时和降级策略")
        
        print(f"\n2. **输出后处理**")
        print("   - SQL语法验证和修复")
        print("   - 添加缺失的约束条件")
        print("   - 统一字段命名风格")
        
        print(f"\n3. **质量保证**")
        print("   - 建立测试用例覆盖")
        print("   - 实施人工审核流程")
        print("   - 监控生成质量指标")
        
        print(f"\n4. **生产部署**")
        print("   - 渐进式替换本地调用")
        print("   - 建立回退机制")
        print("   - 监控性能和成本")
        
        print(f"\n💰 成本估算:")
        print(f"- 每次调用耗时: ~{test_result['processing_time']:.1f}秒")
        print(f"- 免费额度: 每月1000次调用")
        print(f"- 超出后按需付费: 约$0.001/请求")
        print(f"- 预估月成本: $10-50 (处理1000-5000个文档)")
        
    else:
        print("⚠️ BART模型暂不建议直接集成")
        print(f"\n💡 替代方案:")
        print("1. **继续优化本地环境**")
        print("   - 升级硬件配置")
        print("   - 优化模型推理速度")
        
        print(f"\n2. **尝试HF Inference Endpoints**")
        print("   - 部署更强大的模型")
        print("   - 享受专用GPU性能")
        print("   - 预估成本: $50-200/月")
        
        print(f"\n3. **混合架构**")
        print("   - 简单任务用BART")
        print("   - 复杂任务用本地deepseek")
        print("   - 根据文档复杂度自动路由")

if __name__ == "__main__":
    print("🎯 BART模型深度测试与评估")
    print("=" * 60)
    print("📝 目标: 验证facebook/bart-large-cnn在SQL生成任务上的实用性")
    print("🔑 使用真实评估文档进行测试")
    print("=" * 60)
    
    # 运行详细测试
    result = test_bart_with_real_document()
    
    # 提供集成指导
    provide_integration_guidance(result)
    
    print(f"\n🎉 深度测试完成!")
    if result.get("success"):
        if result.get("is_usable"):
            print("✅ 结论: BART模型具备生产使用潜力")
            print("🎯 建议: 可以开始POC集成测试")
        else:
            print("⚠️ 结论: 需要进一步改进")
            print("💡 建议: 考虑其他更强的云端模型")
    else:
        print("❌ 结论: 测试失败")
        print("🔧 建议: 检查网络连接和API配置")