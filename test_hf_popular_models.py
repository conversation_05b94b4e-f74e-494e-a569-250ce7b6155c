#!/usr/bin/env python3
"""
测试HF热门可用模型
基于2025年实际可用的模型列表
"""

import requests
import json
import time
from datetime import datetime

def test_popular_models():
    """测试热门可用模型"""
    print("🚀 HF热门模型SQL生成测试")
    print("=" * 60)
    
    # 2025年HF上确实可用的热门模型
    models_to_test = [
        "gpt2",                              # 经典生成模型
        "distilbert-base-uncased",           # 轻量级BERT
        "facebook/bart-large-cnn",           # 文本生成
        "t5-small",                          # T5小模型
        "microsoft/DialoGPT-medium"          # 对话生成
    ]
    
    # 专门为SQL生成设计的简化提示词
    sql_prompt = """Convert to SQL:
Assessment: eating (1-3), bathing (1-3), dressing (1-3)
Create PostgreSQL table with primary key, timestamps."""
    
    token = "*************************************"
    results = []
    
    for model in models_to_test:
        print(f"\n🤖 测试模型: {model}")
        print("-" * 50)
        
        api_url = f"https://api-inference.huggingface.co/models/{model}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": sql_prompt,
            "parameters": {
                "max_new_tokens": 200,
                "temperature": 0.7,
                "do_sample": True
            }
        }
        
        try:
            start_time = time.time()
            print("⏳ 发送API请求...")
            
            response = requests.post(api_url, headers=headers, json=payload, timeout=30)
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"📡 HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                api_response = response.json()
                
                # 处理响应
                generated_text = ""
                if isinstance(api_response, list) and len(api_response) > 0:
                    if 'generated_text' in api_response[0]:
                        generated_text = api_response[0]['generated_text']
                    elif 'summary_text' in api_response[0]:  # BART模型
                        generated_text = api_response[0]['summary_text']
                    else:
                        generated_text = str(api_response[0])
                elif isinstance(api_response, dict):
                    generated_text = api_response.get('generated_text', str(api_response))
                else:
                    generated_text = str(api_response)[:300]
                
                print(f"✅ API调用成功")
                print(f"⏱️ 响应时间: {processing_time:.2f}秒")
                print(f"📝 响应长度: {len(generated_text)} 字符")
                
                # 评估生成质量
                response_upper = generated_text.upper()
                has_sql_keywords = any(keyword in response_upper for keyword in 
                                     ['CREATE', 'TABLE', 'PRIMARY', 'INTEGER', 'VARCHAR'])
                has_assessment_terms = any(term in generated_text.lower() for term in 
                                         ['eating', 'bathing', 'dressing', 'assessment'])
                has_timestamps = any(time_field in generated_text.lower() for time_field in 
                                   ['created_at', 'updated_at', 'timestamp'])
                
                quality_score = 0
                if has_sql_keywords: quality_score += 40
                if has_assessment_terms: quality_score += 30  
                if has_timestamps: quality_score += 30
                
                print(f"📊 生成质量评估:")
                print(f"   ✅ SQL关键词: {'通过' if has_sql_keywords else '❌失败'}")
                print(f"   ✅ 业务理解: {'通过' if has_assessment_terms else '❌失败'}")
                print(f"   ✅ 时间戳字段: {'通过' if has_timestamps else '❌失败'}")
                print(f"🏆 质量评分: {quality_score}/100分")
                
                # 显示生成内容
                print(f"📄 生成内容:")
                print("=" * 40)
                print(generated_text[:200])
                if len(generated_text) > 200:
                    print("...")
                print("=" * 40)
                
                results.append({
                    "model": model,
                    "success": True,
                    "processing_time": processing_time,
                    "quality_score": quality_score,
                    "generated_text": generated_text,
                    "has_sql_keywords": has_sql_keywords,
                    "has_assessment_terms": has_assessment_terms,
                    "has_timestamps": has_timestamps
                })
                
            elif response.status_code == 503:
                print("⏳ 模型正在加载，稍后重试")
                time.sleep(5)
                continue
            else:
                print(f"❌ API错误: {response.status_code}")
                print(f"错误信息: {response.text[:200]}")
                results.append({
                    "model": model,
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({
                "model": model,
                "success": False,
                "error": str(e)
            })
        
        time.sleep(3)  # 避免请求过于频繁
    
    return results

def analyze_hf_results(results):
    """分析HF测试结果"""
    print(f"\n📊 HF模型测试结果分析")
    print("=" * 60)
    
    successful = [r for r in results if r.get('success')]
    failed = [r for r in results if not r.get('success')]
    
    print(f"✅ 成功: {len(successful)}/{len(results)} 个模型")
    print(f"❌ 失败: {len(failed)} 个模型")
    
    if successful:
        print(f"\n🏆 模型性能排名:")
        successful.sort(key=lambda x: x['quality_score'], reverse=True)
        
        for i, result in enumerate(successful, 1):
            print(f"{i}. {result['model']}")
            print(f"   📊 质量: {result['quality_score']}/100分")
            print(f"   ⏱️ 速度: {result['processing_time']:.2f}秒")
            if result['quality_score'] >= 50:
                print(f"   ✅ 可用性: 推荐")
            else:
                print(f"   ⚠️ 可用性: 需改进")
            print()
        
        # 与本地deepseek对比
        if successful:
            best = successful[0]
            print(f"🔄 与本地模型对比:")
            print(f"本地deepseek: 125/125分(100%), 172.2秒")
            print(f"最佳HF模型: {best['quality_score']}/100分, {best['processing_time']:.2f}秒")
            
            if best['quality_score'] >= 60:
                speed_gain = max(0, (172.2 - best['processing_time']) / 172.2 * 100)
                print(f"✅ HF优势: 速度提升{speed_gain:.1f}%")
                print(f"💡 建议: 可考虑迁移，但质量需提升")
            else:
                print(f"⚠️ HF劣势: 质量差距明显")
                print(f"💡 建议: 继续使用本地，或考虑Inference Endpoints")
    
    if failed:
        print(f"\n❌ 失败原因:")
        for result in failed:
            print(f"- {result['model']}: {result.get('error')}")
    
    return successful

def recommend_hf_strategy(successful_models):
    """推荐HF使用策略"""
    print(f"\n🎯 HF使用策略建议")
    print("=" * 60)
    
    if not successful_models:
        print("❌ 当前Serverless API模型质量不足")
        print("💡 推荐方案:")
        print("1. **继续使用本地deepseek** - 质量最高")
        print("2. **尝试HF Inference Endpoints** - 部署更强模型")
        print("3. **混合架构** - 本地开发+云端生产")
        
        print(f"\n🚀 Inference Endpoints优势:")
        print("- 可部署如CodeLlama-13B等大模型")
        print("- 专用GPU资源，性能更好")
        print("- 支持Private VPC部署")
        print("- 按小时计费: $0.5-2/小时")
        
    else:
        best = successful_models[0]
        if best['quality_score'] >= 60:
            print(f"✅ 可用方案: {best['model']}")
            print(f"📊 预期表现: {best['quality_score']}分, {best['processing_time']:.2f}秒")
            print(f"💰 成本优势: 免费额度 + 按需付费")
            
            print(f"\n📋 集成步骤:")
            print("1. 使用此模型创建API包装器")
            print("2. 添加后处理逻辑优化SQL输出")
            print("3. 建立质量验证机制")
            print("4. 渐进式替换本地调用")
            
        else:
            print(f"⚠️ 当前模型质量不足({best['quality_score']}分)")
            print(f"💡 建议升级到Inference Endpoints")

if __name__ == "__main__":
    print("🎯 HF热门模型SQL生成能力测试")
    print("=" * 60)
    print("🔑 使用您的HF Token测试实际可用模型")
    print("📝 目标: 验证Serverless API的SQL生成能力")
    print("=" * 60)
    
    # 运行测试
    results = test_popular_models()
    
    # 分析结果
    successful_models = analyze_hf_results(results)
    
    # 提供策略建议
    recommend_hf_strategy(successful_models)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"/Volumes/acasis/Assessment/test_results/hf_popular_models_{timestamp}.md"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# HF热门模型SQL生成测试报告\n\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"HF Token: *************************************\n\n")
        
        for result in results:
            f.write(f"## {result['model']}\n")
            if result.get('success'):
                f.write(f"- 质量评分: {result['quality_score']}/100\n")
                f.write(f"- 响应时间: {result['processing_time']:.2f}秒\n")
                f.write(f"- 生成内容: {result['generated_text'][:200]}...\n\n")
            else:
                f.write(f"- 状态: 失败 - {result.get('error')}\n\n")
    
    print(f"\n📄 详细报告: {report_file}")
    print(f"🎉 测试完成！")