#!/usr/bin/env python3
"""
直接测试后端API，排除前端缓存问题
"""

import requests
import json

def test_backend_directly():
    """直接测试后端API"""
    print("🔍 直接测试后端API")
    print("=" * 70)
    
    # 1. 检查AI状态
    print("1️⃣ 检查AI服务状态...")
    try:
        response = requests.get("http://localhost:8181/api/ai/status")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 2. 检查模型信息
    print("\n2️⃣ 检查当前模型信息...")
    try:
        response = requests.get("http://localhost:8181/api/ai/model-info")
        data = response.json()
        if data.get('success'):
            model_info = data.get('data', {})
            print(f"   模型ID: {model_info.get('id')}")
            print(f"   服务URL: {model_info.get('url')}")
            print(f"   错误信息: {model_info.get('error')}")
            print(f"   可用状态: {model_info.get('available')}")
        else:
            print(f"   ❌ 失败: {data}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 3. 简单的AI分析测试
    print("\n3️⃣ 测试AI分析功能...")
    test_data = {
        "markdownContent": "# 测试表\n包含姓名和年龄字段",
        "fileName": "test.md",
        "customPrompt": "请生成PostgreSQL表结构",
        "useStream": True
    }
    
    try:
        response = requests.post(
            "http://localhost:8181/api/ai/analyze-document-structure",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        result = response.json()
        if result.get('success'):
            data = result.get('data', {})
            print(f"   ✅ 分析成功")
            print(f"   表名: {data.get('tableName')}")
            print(f"   置信度: {data.get('confidence')}%")
            print(f"   字段数: {len(data.get('fields', []))}")
            
            # 检查是否是降级结果
            if data.get('confidence', 0) <= 30:
                print(f"   ⚠️ 这是降级分析结果")
            else:
                print(f"   ✅ 这是AI生成的结果")
        else:
            print(f"   ❌ 分析失败: {result.get('message')}")
    except requests.exceptions.Timeout:
        print(f"   ⏰ 请求超时（可能正在处理）")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 4. 检查LM Studio连接
    print("\n4️⃣ 直接测试LM Studio连接...")
    lm_studio_urls = [
        "http://*************:1234",
        "http://*************:1234",
        "http://localhost:1234"
    ]
    
    for url in lm_studio_urls:
        try:
            response = requests.get(f"{url}/v1/models", timeout=2)
            if response.status_code == 200:
                models = response.json().get('data', [])
                print(f"   ✅ {url} - 可用 ({len(models)} 个模型)")
            else:
                print(f"   ❌ {url} - 状态码 {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url} - 无法连接")

if __name__ == "__main__":
    test_backend_directly()