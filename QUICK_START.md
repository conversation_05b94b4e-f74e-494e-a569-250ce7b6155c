# 快速开始指南

## 📋 环境要求

- **操作系统**: macOS / Linux / Windows (WSL2)
- **Conda**: Miniconda 或 Anaconda
- **Docker**: 20.10+ 
- **Git**: 2.0+

## 🚀 一键启动

### 1. 环境初始化
```bash
# 克隆项目（如果还未克隆）
git clone <repository-url>
cd Assessment

# 运行M4兼容性检查
./scripts/check-m4-compatibility.sh

# 运行环境设置脚本
./scripts/setup-env.sh
```

### 2. 启动开发环境

#### Apple M4 优化版本（推荐）
```bash
# 启动Apple Silicon优化的开发环境
./scripts/dev-start-m4.sh
```

#### 标准版本
```bash
# 启动标准开发环境
./scripts/dev-start.sh
```

### 3. 性能监控
```bash
# 查看系统状态
./scripts/monitor-m4.sh

# 实时监控
./scripts/monitor-m4.sh realtime
```

### 4. 停止开发环境
```bash
# 停止所有服务
./scripts/dev-stop.sh
```

## 🔗 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 后端API | http://localhost:8181 | Spring Boot 应用 |
| API文档 | http://localhost:8181/swagger-ui.html | Swagger UI |
| 前端H5 | http://localhost:5273 | uni-app H5版本 |
| 管理后台 | http://localhost:5274 | Vue3 管理系统 |
| MinIO控制台 | http://localhost:9001 | 文件存储管理 |

## 🔐 默认账号

| 系统 | 用户名 | 密码 |
|------|--------|------|
| 管理后台 | admin | admin123 |
| MinIO | minioadmin | minioadmin |

## 📊 数据库连接

| 数据库 | 连接地址 | 凭据 |
|--------|----------|------|
| PostgreSQL | localhost:5433/elderly_assessment | assessment_user / assessment123 |
| Redis | localhost:6380 | 密码: redis123 |

## 🛠️ 开发工具

### 激活 Conda 环境
```bash
conda activate Assessment
```

### 单独运行服务

#### 后端开发
```bash
conda activate Assessment
cd backend
./mvnw spring-boot:run
```

#### 前端开发 (uni-app)
```bash
cd frontend/uni-app

# H5开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# App开发  
npm run dev:app
```

#### 管理后台开发
```bash
cd frontend/admin
npm run dev
```

### 数据库管理
```bash
# 启动数据库服务
docker-compose up -d postgres redis minio

# 连接PostgreSQL (使用新端口5433)
docker exec -it assessment-postgres-dev psql -U assessment_user -d elderly_assessment

# 查看Redis (使用新端口6380)
docker exec -it assessment-redis-dev redis-cli -a redis123
```

## 📁 项目结构

```
Assessment/
├── backend/                 # 后端 Java Spring Boot
│   ├── src/main/java/      # Java 源代码
│   ├── src/main/resources/ # 配置文件
│   └── pom.xml             # Maven 配置
├── frontend/               # 前端项目
│   ├── uni-app/           # 移动端/小程序 (uni-app)
│   └── admin/             # 管理后台 (Vue3)
├── scripts/               # 便捷脚本
│   ├── setup-env.sh       # 环境初始化
│   ├── dev-start.sh       # 启动开发环境
│   └── dev-stop.sh        # 停止开发环境
├── docker-compose.yml     # Docker 服务编排
├── environment.yml        # Conda 环境配置
└── README.md              # 项目文档
```

## 🔧 常见问题

### 端口被占用
```bash
# 查看端口占用
lsof -i :8181
lsof -i :5273
lsof -i :5274

# 杀死进程
kill -9 <PID>
```

### 重置数据库
```bash
# 停止服务
docker-compose down

# 删除数据
rm -rf data/postgres

# 重新启动
docker-compose up -d postgres
```

### 重新安装依赖
```bash
# 重新创建 Conda 环境
conda env remove -n Assessment
conda env create -f environment.yml

# 重新安装前端依赖
cd frontend/uni-app && rm -rf node_modules && npm install
cd ../admin && rm -rf node_modules && npm install
```

### Maven 构建问题
```bash
# 清理并重新构建
cd backend
./mvnw clean install -DskipTests

# 如果 mvnw 有问题，使用系统 Maven
mvn clean install -DskipTests
```

## 📚 技术栈版本

| 技术 | 版本 | 说明 |
|------|------|------|
| Java | 21 LTS | 最新长期支持版本 |
| Spring Boot | 3.2.4 | 最新稳定版 |
| Vue | 3.4.25 | 最新稳定版 |
| uni-app | 3.0.0+ | 最新稳定版 |
| PostgreSQL | 15 | 已安装版本 |
| Node.js | 20.11.0 LTS | 最新长期支持版本 |
| Python | 3.11 | Conda 环境版本 |

## 🤝 开发规范

- **代码提交**: 使用 Conventional Commits 规范
- **代码风格**: 后端遵循阿里巴巴 Java 规范，前端使用 ESLint + Prettier
- **分支管理**: Git Flow 工作流
- **测试要求**: 单元测试覆盖率 > 80%

## 📞 获取帮助

- 查看详细文档: [README.md](README.md)
- 技术架构: [docs/plan/Local_Deployment_Architecture.md](docs/plan/Local_Deployment_Architecture.md)
- 产品需求: [docs/plan/PRD.md](docs/plan/PRD.md)

有问题请查看项目文档或联系开发团队。