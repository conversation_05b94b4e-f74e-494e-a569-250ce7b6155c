<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
    
    <!-- Spring Boot Test相关的已知误报 -->
    <suppress>
        <notes>Spring Boot Test框架内部依赖，非应用直接使用</notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot\-starter\-test@.*$</packageUrl>
        <cve>CVE-2022-22965</cve>
    </suppress>

    <!-- H2数据库仅用于测试环境 -->
    <suppress>
        <notes>H2数据库仅在测试环境使用，生产环境使用PostgreSQL</notes>
        <packageUrl regex="true">^pkg:maven/com\.h2database/h2@.*$</packageUrl>
        <cve>CVE-2022-45868</cve>
    </suppress>

    <!-- Lombok编译时依赖 -->
    <suppress>
        <notes>Lombok仅在编译时使用，不会打包到最终产品中</notes>
        <packageUrl regex="true">^pkg:maven/org\.projectlombok/lombok@.*$</packageUrl>
        <cve>CVE-2022-45685</cve>
    </suppress>

    <!-- BouncyCastle旧版本漏洞已通过版本更新修复 -->
    <suppress>
        <notes>已更新到BouncyCastle 1.80版本，修复安全漏洞</notes>
        <packageUrl regex="true">^pkg:maven/org\.bouncycastle/.*@1\.69$</packageUrl>
        <cve>CVE-2023-33201</cve>
        <cve>CVE-2023-33202</cve>
        <cve>CVE-2024-29857</cve>
        <cve>CVE-2024-30171</cve>
        <cve>CVE-2024-34447</cve>
    </suppress>

    <!-- 待添加更多抑制规则，根据实际扫描结果调整 -->
    
</suppressions>