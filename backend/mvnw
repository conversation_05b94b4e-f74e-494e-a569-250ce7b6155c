#!/bin/sh
# Maven Wrapper Script
# This script allows you to run Maven without having it installed locally

set -e

MAVEN_WRAPPER_VERSION="3.9.6"
MAVEN_WRAPPER_DOWNLOAD_URL="https://repo.maven.apache.org/maven2/org/apache/maven/wrapper/maven-wrapper/${MAVEN_WRAPPER_VERSION}/maven-wrapper-${MAVEN_WRAPPER_VERSION}.jar"

# Check if Maven Wrapper jar exists
if [ ! -f .mvn/wrapper/maven-wrapper.jar ]; then
    echo "Downloading Maven Wrapper..."
    mkdir -p .mvn/wrapper
    curl -s -L "$MAVEN_WRAPPER_DOWNLOAD_URL" -o .mvn/wrapper/maven-wrapper.jar
fi

# Find Java
if [ -n "$JAVA_HOME" ]; then
    JAVA_EXE="$JAVA_HOME/bin/java"
else
    JAVA_EXE="java"
fi

# Set Maven properties
MAVEN_PROJECTBASEDIR=${MAVEN_BASEDIR:-"$(cd "$(dirname "$0")" && pwd)"}
MAVEN_OPTS="${MAVEN_OPTS} -Dmaven.multiModuleProjectDirectory=${MAVEN_PROJECTBASEDIR}"

# Execute Maven
exec "$JAVA_EXE" $MAVEN_OPTS -cp .mvn/wrapper/maven-wrapper.jar org.apache.maven.wrapper.MavenWrapperMain "$@"