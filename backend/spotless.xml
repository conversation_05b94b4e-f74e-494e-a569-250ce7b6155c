<?xml version="1.0" encoding="UTF-8"?>
<!-- Spotless配置文件 - 专门针对智慧养老评估平台的代码格式化规则 -->
<spotless-config>
    <!-- 
    此配置文件定义了与checkstyle.xml兼容的格式化规则
    主要解决以下checkstyle违规问题：
    1. 缺失大括号 (NeedBraces)
    2. 操作符换行问题 (OperatorWrap)
    3. 星号导入 (AvoidStarImport)
    4. 行长度限制 (LineLength)
    5. 空格问题 (WhitespaceAfter, WhitespaceAround)
    -->
    
    <!-- Java代码格式化规则 -->
    <java>
        <!-- 文件包含模式 -->
        <includes>
            <include>src/main/java/**/*.java</include>
            <include>src/test/java/**/*.java</include>
        </includes>
        
        <!-- 排除文件模式 -->
        <excludes>
            <exclude>target/**</exclude>
            <exclude>**/*Generated*.java</exclude>
            <exclude>**/generated/**</exclude>
        </excludes>
        
        <!-- 导入语句组织规则 -->
        <importOrder>
            <!-- Java标准库 -->
            <order>java</order>
            <order>javax</order>
            <order>jakarta</order>
            <!-- Spring框架 -->
            <order>org.springframework</order>
            <!-- 其他第三方库 -->
            <order>org</order>
            <order>com</order>
            <order>io</order>
            <order>net</order>
            <!-- 本项目包 -->
            <order>com.assessment</order>
            <!-- 静态导入 -->
            <order>\#</order>
        </importOrder>
        
        <!-- 移除未使用的导入 -->
        <removeUnusedImports/>
        
        <!-- 自定义格式化器：处理checkstyle特定问题 -->
        <custom class="com.diffplug.spotless.java.JavaStep" name="checkstyle-compatibility">
            <!-- 确保操作符在行尾换行 (解决OperatorWrap问题) -->
            <replaceRegex>
                <name>Operator wrap - && at end of line</name>
                <searchRegex>\s+&amp;&amp;\s*\n</searchRegex>
                <replacement> &amp;&amp;\n</replacement>
            </replaceRegex>
            
            <replaceRegex>
                <name>Operator wrap - || at end of line</name>
                <searchRegex>\s+\|\|\s*\n</searchRegex>
                <replacement> ||\n</replacement>
            </replaceRegex>
            
            <!-- 确保类型转换后有空格 (解决WhitespaceAfter问题) -->
            <replaceRegex>
                <name>Whitespace after cast</name>
                <searchRegex>\)([a-zA-Z_$])</searchRegex>
                <replacement>) $1</replacement>
            </replaceRegex>
            
            <!-- 确保if语句使用大括号 (解决NeedBraces问题) -->
            <replaceRegex>
                <name>Add braces to single-line if</name>
                <searchRegex>if\s*\(([^)]+)\)\s*([^{;]+;)\s*$</searchRegex>
                <replacement>if ($1) {
    $2
}</replacement>
            </replaceRegex>
            
            <!-- 确保for循环使用大括号 -->
            <replaceRegex>
                <name>Add braces to single-line for</name>
                <searchRegex>for\s*\(([^)]+)\)\s*([^{;]+;)\s*$</searchRegex>
                <replacement>for ($1) {
    $2
}</replacement>
            </replaceRegex>
            
            <!-- 确保while循环使用大括号 -->
            <replaceRegex>
                <name>Add braces to single-line while</name>
                <searchRegex>while\s*\(([^)]+)\)\s*([^{;]+;)\s*$</searchRegex>
                <replacement>while ($1) {
    $2
}</replacement>
            </replaceRegex>
        </custom>
        
        <!-- Eclipse代码格式化器 -->
        <eclipse>
            <version>4.26</version>
            <file>${basedir}/eclipse-formatter.xml</file>
        </eclipse>
    </java>
    
    <!-- 其他文件类型格式化 -->
    <formats>
        <!-- XML文件 -->
        <format>
            <includes>
                <include>*.xml</include>
                <include>src/main/resources/**/*.xml</include>
                <include>src/test/resources/**/*.xml</include>
            </includes>
            <trimTrailingWhitespace/>
            <endWithNewline/>
        </format>
        
        <!-- YAML文件 -->
        <format>
            <includes>
                <include>src/main/resources/**/*.yml</include>
                <include>src/main/resources/**/*.yaml</include>
            </includes>
            <trimTrailingWhitespace/>
            <endWithNewline/>
            <indent>
                <spaces>true</spaces>
                <spacesPerTab>2</spacesPerTab>
            </indent>
        </format>
        
        <!-- Properties文件 -->
        <format>
            <includes>
                <include>src/main/resources/**/*.properties</include>
            </includes>
            <trimTrailingWhitespace/>
            <endWithNewline/>
        </format>
    </formats>
</spotless-config>