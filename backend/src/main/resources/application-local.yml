# Apple Silicon本地开发环境配置
spring:
  application:
    name: assessment-platform-local


  datasource:
    url: *******************************************************
    username: assessment_user
    password: ${DB_PASSWORD:assessment123}
    hikari:
      minimum-idle: 2
      maximum-pool-size: 8
      idle-timeout: 300000
      max-lifetime: 900000
      connection-timeout: 20000
      # Apple Silicon优化
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        '[format_sql]': true
        '[show_sql]': true
        '[use_sql_comments]': true
        jdbc:
          '[batch_size]': 10
          '[batch_versioned_data]': true
        '[order_inserts]': true
        '[order_updates]': true
        # Apple Silicon内存优化
        cache:
          '[use_second_level_cache]': false
          '[use_query_cache]': false

  data:
    redis:
      host: localhost
      port: 6380
      password: redis123
      database: 0
      timeout: 5000ms
      connect-timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 4
          min-idle: 2
          max-wait: 5000ms
        shutdown-timeout: 200ms

  cache:
    type: redis
    redis:
      time-to-live: 1800000 # 30分钟
      cache-null-values: false

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 20MB

server:
  port: 8181
  compression:
    enabled: true
    min-response-size: 1024
  # Apple Silicon性能优化
  tomcat:
    threads:
      max: 100
      min-spare: 10
    connection-timeout: 20000ms
    max-connections: 8192
    accept-count: 100

# MinIO配置
minio:
  endpoint: ${MINIO_ENDPOINT:http://localhost:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  bucket-name: assessment-files
  secure: false

# JWT配置
jwt:
  secret: ${JWT_SECRET:D0fJov+robFjgi6kSig+WmQkxqaML8T3jES2YdhJJyM=}
  expiration: 86400000 # 24小时
  refresh-expiration: 604800000 # 7天

# 日志配置（开发环境）
logging:
  level:
    root: INFO
    '[com.assessment]': DEBUG
    '[org.springframework.web]': DEBUG
    '[org.hibernate.SQL]': DEBUG
    '[org.hibernate.type.descriptor.sql.BasicBinder]': TRACE
    '[org.springframework.security]': DEBUG
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/assessment-local.log
    max-size: 50MB
    max-history: 10

# Actuator配置（开发环境）
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
    shutdown:
      access: unrestricted
  prometheus:
    metrics:
      export:
        enabled: true

# API文档配置
springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    try-it-out-enabled: true

# 业务配置
assessment:
  # 评估相关配置
  evaluation:
    max-duration: 120 # 最大评估时长（分钟）
    auto-save-interval: 30 # 自动保存间隔（秒）

  # 文件上传配置
  upload:
    allowed-types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
      - application/msword
      - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    max-size: 10485760 # 10MB

  # 安全配置
  security:
    password-min-length: 6 # 开发环境降低密码复杂度
    password-require-special: false
    max-login-attempts: 10 # 开发环境放宽限制
    lock-duration: 300 # 5分钟

# 开发环境特殊配置
debug: true

# Apple Silicon特定配置
app:
  performance:
    # 启用Apple Silicon优化
    apple-silicon-optimized: true
    # 内存使用优化
    memory-conservative: true
    # 并发优化
    max-concurrent-assessments: 20
