{"groups": [{"name": "minio", "type": "com.assessment.config.MinioProperties", "sourceType": "com.assessment.config.MinioProperties", "description": "MinIO对象存储配置属性"}, {"name": "jwt", "type": "com.assessment.config.JwtProperties", "sourceType": "com.assessment.config.JwtProperties", "description": "JWT配置属性"}, {"name": "assessment", "type": "com.assessment.config.AssessmentProperties", "sourceType": "com.assessment.config.AssessmentProperties", "description": "评估系统配置属性"}, {"name": "app", "type": "com.assessment.config.AppProperties", "sourceType": "com.assessment.config.AppProperties", "description": "应用程序性能配置属性"}, {"name": "app.assessment", "type": "com.assessment.config.ApplicationProperties", "sourceType": "com.assessment.config.ApplicationProperties", "description": "应用程序评估配置属性"}, {"name": "docling", "type": "com.assessment.config.DoclingProperties", "sourceType": "com.assessment.config.DoclingProperties", "description": "Docling PDF转换服务配置"}, {"name": "ai", "type": "com.assessment.config.AIProperties", "sourceType": "com.assessment.config.AIProperties", "description": "AI分析服务配置"}], "properties": [{"name": "minio.endpoint", "type": "java.lang.String", "description": "MinIO服务端点", "defaultValue": "http://localhost:9000"}, {"name": "minio.access-key", "type": "java.lang.String", "description": "MinIO访问密钥", "defaultValue": "minioadmin"}, {"name": "minio.secret-key", "type": "java.lang.String", "description": "MinIO秘密密钥", "defaultValue": "minioadmin"}, {"name": "minio.bucket-name", "type": "java.lang.String", "description": "MinIO存储桶名称", "defaultValue": "assessment-files"}, {"name": "minio.secure", "type": "java.lang.Bo<PERSON>an", "description": "是否启用HTTPS", "defaultValue": false}, {"name": "jwt.secret", "type": "java.lang.String", "description": "JWT密钥"}, {"name": "jwt.expiration", "type": "java.lang.Long", "description": "JWT过期时间（毫秒）", "defaultValue": 86400000}, {"name": "jwt.refresh-expiration", "type": "java.lang.Long", "description": "刷新令牌过期时间（毫秒）", "defaultValue": 604800000}, {"name": "app.assessment.evaluation.max-duration", "type": "java.lang.Integer", "description": "最大评估时长（分钟）", "defaultValue": 120}, {"name": "app.assessment.evaluation.auto-save-interval", "type": "java.lang.Integer", "description": "自动保存间隔（秒）", "defaultValue": 30}, {"name": "app.assessment.upload.allowed-types", "type": "java.lang.String[]", "description": "允许的文件类型"}, {"name": "app.assessment.upload.max-size", "type": "java.lang.Long", "description": "最大文件大小", "defaultValue": 10485760}, {"name": "app.assessment.security.password-min-length", "type": "java.lang.Integer", "description": "密码最小长度", "defaultValue": 8}, {"name": "app.assessment.security.password-require-special", "type": "java.lang.Bo<PERSON>an", "description": "是否要求特殊字符", "defaultValue": true}, {"name": "app.assessment.security.max-login-attempts", "type": "java.lang.Integer", "description": "最大登录尝试次数", "defaultValue": 5}, {"name": "app.assessment.security.lock-duration", "type": "java.lang.Integer", "description": "锁定时长（秒）", "defaultValue": 1800}, {"name": "app.performance.apple-silicon-optimized", "type": "java.lang.Bo<PERSON>an", "description": "启用Apple Silicon优化", "defaultValue": true}, {"name": "app.performance.memory-conservative", "type": "java.lang.Bo<PERSON>an", "description": "内存使用优化", "defaultValue": true}, {"name": "app.performance.max-concurrent-assessments", "type": "java.lang.Integer", "description": "最大并发评估数", "defaultValue": 20}, {"name": "assessment.evaluation.max-duration", "type": "java.lang.Integer", "description": "最大评估时长（分钟）", "defaultValue": 120}, {"name": "assessment.evaluation.auto-save-interval", "type": "java.lang.Integer", "description": "自动保存间隔（秒）", "defaultValue": 30}, {"name": "assessment.upload.allowed-types", "type": "java.lang.String[]", "description": "允许的文件类型"}, {"name": "assessment.upload.max-size", "type": "java.lang.Long", "description": "最大文件大小", "defaultValue": 10485760}, {"name": "assessment.security.password-min-length", "type": "java.lang.Integer", "description": "密码最小长度", "defaultValue": 8}, {"name": "assessment.security.password-require-special", "type": "java.lang.Bo<PERSON>an", "description": "是否要求特殊字符", "defaultValue": true}, {"name": "assessment.security.max-login-attempts", "type": "java.lang.Integer", "description": "最大登录尝试次数", "defaultValue": 5}, {"name": "assessment.security.lock-duration", "type": "java.lang.Integer", "description": "锁定时长（秒）", "defaultValue": 1800}, {"name": "assessment.threshold.field-mapping-overall-confidence", "type": "java.lang.Double", "description": "字段映射整体置信度阈值", "defaultValue": 0.85}, {"name": "assessment.threshold.name-field-confidence", "type": "java.lang.Double", "description": "姓名字段置信度", "defaultValue": 0.95}, {"name": "assessment.threshold.age-field-confidence", "type": "java.lang.Double", "description": "年龄字段置信度", "defaultValue": 0.9}, {"name": "assessment.threshold.rating-field-confidence", "type": "java.lang.Double", "description": "评分字段置信度", "defaultValue": 0.88}, {"name": "assessment.threshold.daily-living-ability-weight", "type": "java.lang.Double", "description": "日常生活能力权重", "defaultValue": 0.3}, {"name": "assessment.threshold.max-age", "type": "java.lang.Double", "description": "最大年龄", "defaultValue": 120.0}, {"name": "assessment.threshold.rating-min-value", "type": "java.lang.Double", "description": "评分最小值", "defaultValue": 1.0}, {"name": "assessment.threshold.rating-max-value", "type": "java.lang.Double", "description": "评分最大值", "defaultValue": 5.0}, {"name": "docling.service.url", "type": "java.lang.String", "description": "Docling服务URL", "defaultValue": "http://localhost:8088"}, {"name": "docling.service.timeout", "type": "java.lang.Integer", "description": "请求超时时间（秒）", "defaultValue": 60}, {"name": "docling.service.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Docling服务", "defaultValue": true}, {"name": "ai.lmstudio.url", "type": "java.lang.String", "description": "LM Studio服务URL", "defaultValue": "http://*************:1234"}, {"name": "ai.lmstudio.model", "type": "java.lang.String", "description": "模型名称", "defaultValue": "deepseek/deepseek-r1-0528-qwen3-8b"}, {"name": "ai.analysis.timeout", "type": "java.lang.Integer", "description": "分析超时时间（毫秒）", "defaultValue": 60000}, {"name": "ai.analysis.max-content-length", "type": "java.lang.Integer", "description": "最大内容长度", "defaultValue": 100000}, {"name": "ai.analysis.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用AI分析", "defaultValue": true}]}