# LM Studio 配置文件
# 用于集中管理LM Studio服务地址和模型配置

lm-studio:
  # 服务配置
  server:
    # 主服务地址
    primary-url: "http://*************:1234"
    # 备用服务地址列表
    backup-urls:
      - "http://*************:1234"
      - "http://localhost:1234"
    # 连接超时时间（毫秒）
    connection-timeout: 10000
    # 读取超时时间（毫秒）
    read-timeout: 60000
    # 重试次数
    max-retries: 3
    # 健康检查间隔（秒）
    health-check-interval: 30

  # 模型配置
  models:
    # 模型选择策略
    selection:
      # 优先选择策略（按顺序尝试）
      preferred-patterns:
        - "deepseek-r1-0528-qwen3-8b-mlx@8bit"  # 最佳验证模型 (136/136分)
        - "deepseek.*r1.*qwen"     # DeepSeek R1 系列模型
        - "deepseek.*coder"        # DeepSeek Coder 系列
        - "qwen.*8b"               # Qwen 8B 系列
        - "gemma.*[2-9][0-9]b"     # Gemma 大参数模型 (20B+)
        - "llama.*[1-9][0-9]b"     # Llama 大参数模型 (10B+)
        - ".*chat.*"               # 包含chat的模型
        - ".*instruct.*"           # 包含instruct的模型

      # 模型能力推断规则
      capability-inference:
        code:
          - "coder"
          - "code"
          - "programming"
        chinese:
          - "qwen"
          - "chinese"
          - "zh"
        reasoning:
          - "reasoning"
          - "think"
          - "logic"
        chat:
          - "chat"
          - "instruct"
          - "conversation"

    # 排除的模型（不用于对话）
    excluded-patterns:
      - ".*embedding.*"
      - ".*whisper.*"
      - ".*tts.*"
      - ".*vision.*"             # 暂时排除视觉模型
      - ".*translate.*"          # 排除翻译专用模型

  # 所有模型参数（temperature、max_tokens、top_p等）现在由LM Studio管理
  # 本项目只发送提示词和MD内容给LM Studio，然后返回分析结果

  # 自动切换配置
  auto-switch:
    # 启用自动模型切换
    enabled: true
    # 模型不可用时自动切换到备用模型
    fallback-enabled: true
    # 服务不可用时自动切换到备用服务
    server-fallback-enabled: true
    # 模型响应时间阈值（毫秒），超过则切换
    response-time-threshold: 30000

# 环境变量覆盖配置
environment-overrides:
  # 开发环境
  development:
    primary-url: "${LM_STUDIO_DEV_URL:http://localhost:1234}"
    preferred-model: "${LM_STUDIO_DEV_MODEL:deepseek/deepseek-r1-0528-qwen3-8b}"

  # 生产环境
  production:
    primary-url: "${LM_STUDIO_PROD_URL:http://*************:1234}"
    preferred-model: "${LM_STUDIO_PROD_MODEL:deepseek/deepseek-r1-0528-qwen3-8b}"

  # 测试环境
  test:
    primary-url: "${LM_STUDIO_TEST_URL:http://*************:1234}"
    preferred-model: "${LM_STUDIO_TEST_MODEL:gemma-3-27b}"
