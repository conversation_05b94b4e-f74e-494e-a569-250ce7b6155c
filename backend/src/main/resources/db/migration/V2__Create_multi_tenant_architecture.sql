-- ========================================
-- 智能评估平台 - 分层多租户架构数据库设计
-- V2__Create_multi_tenant_architecture.sql
-- 创建日期: 2025-06-21
-- 设计理念: SaaS多租户架构，支持租户自定义量表
-- ========================================

-- 删除旧的数据库结构（保留备份）
DO $$
BEGIN
    -- 如果存在旧表，先重命名为备份
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'institutions') THEN
        ALTER TABLE institutions RENAME TO institutions_backup_20250621;
        ALTER TABLE users RENAME TO users_backup_20250621;
        ALTER TABLE elderly_persons RENAME TO elderly_persons_backup_20250621;
        ALTER TABLE assessment_scales RENAME TO assessment_scales_backup_20250621;
        ALTER TABLE assessment_records RENAME TO assessment_records_backup_20250621;
        ALTER TABLE assessment_results RENAME TO assessment_results_backup_20250621;
        ALTER TABLE assessment_tasks RENAME TO assessment_tasks_backup_20250621;
        -- 其他表按需重命名...
    END IF;
END $$;

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- ========================================
-- 第一层：SaaS平台管理层（全局共享）
-- ========================================

-- 1. 租户管理表
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    industry VARCHAR(50) NOT NULL,
    
    -- 联系信息
    contact_person VARCHAR(100),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(50),
    address TEXT,
    
    -- 服务配置
    subscription_plan VARCHAR(50) DEFAULT 'basic', -- basic, standard, premium, enterprise
    subscription_status VARCHAR(20) DEFAULT 'active', -- active, suspended, expired
    subscription_start_date DATE,
    subscription_end_date DATE,
    
    -- 功能配额
    max_users INTEGER DEFAULT 50,
    max_monthly_assessments INTEGER DEFAULT 1000,
    max_custom_scales INTEGER DEFAULT 10,
    max_storage_mb INTEGER DEFAULT 1024,
    
    -- 定制配置
    custom_config JSONB DEFAULT '{}',
    branding_config JSONB DEFAULT '{}', -- 品牌定制
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',
    is_trial BOOLEAN DEFAULT false,
    trial_end_date DATE,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 2. 租户数据库映射表
CREATE TABLE tenant_databases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    database_name VARCHAR(100) NOT NULL,
    connection_string TEXT, -- 加密存储
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. 全局量表注册中心
CREATE TABLE global_scale_registry (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    version VARCHAR(20) NOT NULL,
    
    -- 分类和标签
    category VARCHAR(50) NOT NULL,
    industry_tags TEXT[], -- 适用行业标签
    keywords TEXT[], -- 搜索关键词
    
    -- 量表定义
    form_schema JSONB NOT NULL,
    scoring_rules JSONB NOT NULL,
    validation_rules JSONB DEFAULT '{}',
    report_template JSONB DEFAULT '{}',
    
    -- 发布信息
    publisher_type VARCHAR(20) NOT NULL, -- 'platform', 'tenant', 'partner'
    publisher_id UUID, -- 发布者ID（可能是租户ID）
    
    -- 可见性和权限
    visibility VARCHAR(20) DEFAULT 'public', -- public, private, premium
    price DECIMAL(10,2) DEFAULT 0.00, -- 收费量表价格
    
    -- 使用统计
    usage_count BIGINT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',
    is_official BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    published_at TIMESTAMP,
    deprecated_at TIMESTAMP
);

-- 4. 租户量表订阅表
CREATE TABLE tenant_scale_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    scale_id UUID NOT NULL REFERENCES global_scale_registry(id),
    
    -- 订阅信息
    subscription_type VARCHAR(20) NOT NULL, -- 'free', 'paid', 'trial'
    subscribed_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    
    -- 定制配置
    custom_name VARCHAR(200), -- 租户自定义名称
    custom_config JSONB DEFAULT '{}',
    field_customizations JSONB DEFAULT '{}',
    scoring_adjustments JSONB DEFAULT '{}',
    
    -- 权限配置
    allowed_roles TEXT[] DEFAULT '{"ADMIN","ASSESSOR"}',
    usage_limit INTEGER, -- 使用次数限制
    usage_count INTEGER DEFAULT 0,
    
    -- 状态
    is_active BOOLEAN DEFAULT true,
    
    UNIQUE(tenant_id, scale_id)
);

-- 5. 租户自定义量表表
CREATE TABLE tenant_custom_scales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    
    -- 基本信息
    code VARCHAR(100) NOT NULL, -- 租户内唯一
    name VARCHAR(200) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    
    -- 量表定义
    form_schema JSONB NOT NULL,
    scoring_rules JSONB NOT NULL,
    validation_rules JSONB DEFAULT '{}',
    
    -- 模板信息（如果基于现有量表创建）
    based_on_scale_id UUID REFERENCES global_scale_registry(id),
    
    -- 发布到市场
    is_published_to_market BOOLEAN DEFAULT false,
    market_listing_id UUID,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft', -- draft, active, archived
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID,
    
    UNIQUE(tenant_id, code)
);

-- 6. 用户管理表（跨租户）
CREATE TABLE platform_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- 基本信息
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    
    -- 个人信息
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    phone VARCHAR(50),
    
    -- 平台级别角色
    platform_role VARCHAR(20) DEFAULT 'user', -- admin, user
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 7. 租户用户关联表
CREATE TABLE tenant_user_memberships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    user_id UUID NOT NULL REFERENCES platform_users(id),
    
    -- 租户内角色和权限
    tenant_role VARCHAR(50) NOT NULL, -- ADMIN, SUPERVISOR, ASSESSOR, REVIEWER, VIEWER
    permissions JSONB DEFAULT '[]',
    
    -- 个人信息（租户级别）
    display_name VARCHAR(100),
    professional_title VARCHAR(100),
    license_number VARCHAR(100),
    department VARCHAR(100),
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',
    joined_at TIMESTAMP DEFAULT NOW(),
    last_active_at TIMESTAMP,
    
    UNIQUE(tenant_id, user_id)
);

-- ========================================
-- 第二层：租户数据隔离层（分区表）
-- ========================================

-- 8. 评估对象表（按租户分区）
CREATE TABLE assessment_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL, -- 分区键
    
    -- 基本信息
    name VARCHAR(100) NOT NULL,
    id_number VARCHAR(100), -- 加密存储身份证号
    gender VARCHAR(10),
    birth_date DATE,
    
    -- 联系信息
    phone VARCHAR(50), -- 加密存储
    address TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(50), -- 加密存储
    
    -- 医疗信息
    medical_insurance_number VARCHAR(100), -- 加密存储
    medical_history JSONB DEFAULT '{}',
    medications JSONB DEFAULT '[]',
    allergies TEXT[],
    
    -- 护理信息
    current_care_level VARCHAR(50),
    care_notes TEXT,
    
    -- 扩展信息（租户自定义字段）
    custom_fields JSONB DEFAULT '{}',
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
) PARTITION BY HASH (tenant_id);

-- 9. 租户评估记录表（按租户分区）
CREATE TABLE tenant_assessment_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL, -- 分区键
    
    -- 基本信息
    record_number VARCHAR(100) NOT NULL, -- 租户内唯一
    subject_id UUID NOT NULL,
    scale_id UUID NOT NULL, -- 引用global_scale_registry或tenant_custom_scales
    scale_type VARCHAR(20) NOT NULL, -- 'global', 'custom'
    assessor_id UUID NOT NULL,
    
    -- 评估信息
    assessment_date TIMESTAMP NOT NULL,
    assessment_type VARCHAR(20) DEFAULT 'regular', -- regular, followup, emergency
    
    -- 表单数据
    form_data JSONB NOT NULL,
    score_data JSONB,
    total_score DECIMAL(10,2),
    result_level VARCHAR(50),
    result_details JSONB DEFAULT '{}',
    
    -- 工作流状态
    status VARCHAR(20) DEFAULT 'draft', -- draft, submitted, reviewed, approved, archived
    workflow_stage VARCHAR(50),
    
    -- 审核信息
    reviewer_id UUID,
    review_notes TEXT,
    reviewed_at TIMESTAMP,
    
    -- 质量控制
    quality_score DECIMAL(3,2), -- 评估质量评分
    completeness_score DECIMAL(3,2), -- 完整性评分
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    
    UNIQUE(tenant_id, record_number)
) PARTITION BY HASH (tenant_id);

-- 10. 租户操作日志表（按租户分区）
CREATE TABLE tenant_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL, -- 分区键
    
    -- 操作信息
    user_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    
    -- 数据变更
    old_values JSONB,
    new_values JSONB,
    
    -- 请求信息
    client_ip INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT NOW()
) PARTITION BY HASH (tenant_id);

-- ========================================
-- 第三层：业务扩展层
-- ========================================

-- 11. 评估任务调度表
CREATE TABLE assessment_task_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    
    -- 任务信息
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    scale_id UUID NOT NULL,
    scale_type VARCHAR(20) NOT NULL,
    
    -- 调度配置
    schedule_type VARCHAR(20) NOT NULL, -- 'once', 'recurring', 'conditional'
    schedule_config JSONB NOT NULL, -- cron表达式等
    
    -- 目标对象
    target_subjects UUID[], -- 特定对象列表
    target_criteria JSONB, -- 动态筛选条件
    
    -- 分配规则
    assignment_rule VARCHAR(20) DEFAULT 'auto', -- auto, manual, round_robin
    assigned_assessors UUID[],
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    next_run_time TIMESTAMP,
    last_run_time TIMESTAMP,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID
);

-- 12. 评估报告模板表
CREATE TABLE assessment_report_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID,
    
    -- 模板信息
    name VARCHAR(200) NOT NULL,
    description TEXT,
    template_type VARCHAR(20) NOT NULL, -- 'individual', 'comparative', 'statistical'
    
    -- 适用范围
    applicable_scales UUID[], -- 适用的量表ID列表
    applicable_industries TEXT[],
    
    -- 模板定义
    template_structure JSONB NOT NULL,
    chart_configs JSONB DEFAULT '[]',
    style_config JSONB DEFAULT '{}',
    
    -- 可见性
    visibility VARCHAR(20) DEFAULT 'private', -- private, tenant, public
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    version VARCHAR(20) DEFAULT '1.0',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID
);

-- 13. 系统配置表（支持租户级别配置）
CREATE TABLE system_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID, -- NULL表示全局配置
    
    -- 配置信息
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json, array
    
    -- 配置元数据
    description TEXT,
    category VARCHAR(50),
    is_sensitive BOOLEAN DEFAULT false, -- 敏感配置加密存储
    is_readonly BOOLEAN DEFAULT false,
    
    -- 默认值和验证
    default_value TEXT,
    validation_rules JSONB DEFAULT '{}',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by UUID,
    
    UNIQUE(tenant_id, config_key)
);

-- ========================================
-- 创建分区表
-- ========================================

-- 为assessment_subjects创建8个分区
CREATE TABLE assessment_subjects_p0 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 0);

CREATE TABLE assessment_subjects_p1 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 1);

CREATE TABLE assessment_subjects_p2 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 2);

CREATE TABLE assessment_subjects_p3 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 3);

CREATE TABLE assessment_subjects_p4 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 4);

CREATE TABLE assessment_subjects_p5 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 5);

CREATE TABLE assessment_subjects_p6 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 6);

CREATE TABLE assessment_subjects_p7 PARTITION OF assessment_subjects
FOR VALUES WITH (MODULUS 8, REMAINDER 7);

-- 为tenant_assessment_records创建8个分区
CREATE TABLE tenant_assessment_records_p0 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 0);

CREATE TABLE tenant_assessment_records_p1 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 1);

CREATE TABLE tenant_assessment_records_p2 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 2);

CREATE TABLE tenant_assessment_records_p3 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 3);

CREATE TABLE tenant_assessment_records_p4 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 4);

CREATE TABLE tenant_assessment_records_p5 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 5);

CREATE TABLE tenant_assessment_records_p6 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 6);

CREATE TABLE tenant_assessment_records_p7 PARTITION OF tenant_assessment_records
FOR VALUES WITH (MODULUS 8, REMAINDER 7);

-- 为tenant_audit_logs创建8个分区
CREATE TABLE tenant_audit_logs_p0 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 0);

CREATE TABLE tenant_audit_logs_p1 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 1);

CREATE TABLE tenant_audit_logs_p2 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 2);

CREATE TABLE tenant_audit_logs_p3 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 3);

CREATE TABLE tenant_audit_logs_p4 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 4);

CREATE TABLE tenant_audit_logs_p5 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 5);

CREATE TABLE tenant_audit_logs_p6 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 6);

CREATE TABLE tenant_audit_logs_p7 PARTITION OF tenant_audit_logs
FOR VALUES WITH (MODULUS 8, REMAINDER 7);

-- ========================================
-- 创建索引
-- ========================================

-- 租户管理索引
CREATE INDEX idx_tenants_code ON tenants(code);
CREATE INDEX idx_tenants_industry ON tenants(industry);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_subscription ON tenants(subscription_plan, subscription_status);

-- 量表注册中心索引
CREATE INDEX idx_global_scales_code ON global_scale_registry(code);
CREATE INDEX idx_global_scales_category ON global_scale_registry(category);
CREATE INDEX idx_global_scales_industry ON global_scale_registry USING GIN(industry_tags);
CREATE INDEX idx_global_scales_keywords ON global_scale_registry USING GIN(keywords);
CREATE INDEX idx_global_scales_publisher ON global_scale_registry(publisher_type, publisher_id);
CREATE INDEX idx_global_scales_status ON global_scale_registry(status, visibility);
CREATE INDEX idx_global_scales_usage ON global_scale_registry(usage_count DESC);

-- 租户量表订阅索引
CREATE INDEX idx_tenant_subscriptions_tenant ON tenant_scale_subscriptions(tenant_id);
CREATE INDEX idx_tenant_subscriptions_scale ON tenant_scale_subscriptions(scale_id);
CREATE INDEX idx_tenant_subscriptions_active ON tenant_scale_subscriptions(tenant_id, is_active);

-- 用户管理索引
CREATE INDEX idx_platform_users_username ON platform_users(username);
CREATE INDEX idx_platform_users_email ON platform_users(email);
CREATE INDEX idx_platform_users_active ON platform_users(is_active);

CREATE INDEX idx_tenant_memberships_tenant ON tenant_user_memberships(tenant_id);
CREATE INDEX idx_tenant_memberships_user ON tenant_user_memberships(user_id);
CREATE INDEX idx_tenant_memberships_role ON tenant_user_memberships(tenant_id, tenant_role);

-- 分区表索引（在每个分区上创建）
DO $$
DECLARE
    partition_name TEXT;
BEGIN
    -- 为assessment_subjects分区创建索引
    FOR i IN 0..7 LOOP
        partition_name := 'assessment_subjects_p' || i;
        
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, name)', 
            'idx_' || partition_name || '_tenant_name', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, is_active)', 
            'idx_' || partition_name || '_tenant_active', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, created_at DESC)', 
            'idx_' || partition_name || '_tenant_created', partition_name);
    END LOOP;
    
    -- 为tenant_assessment_records分区创建索引
    FOR i IN 0..7 LOOP
        partition_name := 'tenant_assessment_records_p' || i;
        
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, subject_id, assessment_date DESC)', 
            'idx_' || partition_name || '_tenant_subject_date', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, scale_id, scale_type)', 
            'idx_' || partition_name || '_tenant_scale', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, assessor_id, status)', 
            'idx_' || partition_name || '_tenant_assessor_status', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, status, assessment_date DESC)', 
            'idx_' || partition_name || '_tenant_status_date', partition_name);
    END LOOP;
    
    -- 为tenant_audit_logs分区创建索引
    FOR i IN 0..7 LOOP
        partition_name := 'tenant_audit_logs_p' || i;
        
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, user_id, created_at DESC)', 
            'idx_' || partition_name || '_tenant_user_created', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, action, resource_type)', 
            'idx_' || partition_name || '_tenant_action_resource', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I (tenant_id, created_at DESC)', 
            'idx_' || partition_name || '_tenant_created', partition_name);
    END LOOP;
END $$;

-- ========================================
-- 行级安全策略
-- ========================================

-- 启用行级安全
ALTER TABLE assessment_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_assessment_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_custom_scales ENABLE ROW LEVEL SECURITY;

-- 创建租户隔离策略
CREATE POLICY tenant_isolation_subjects ON assessment_subjects
    USING (tenant_id = current_setting('app.current_tenant_id', true)::UUID);

CREATE POLICY tenant_isolation_records ON tenant_assessment_records
    USING (tenant_id = current_setting('app.current_tenant_id', true)::UUID);

CREATE POLICY tenant_isolation_audit ON tenant_audit_logs
    USING (tenant_id = current_setting('app.current_tenant_id', true)::UUID);

CREATE POLICY tenant_isolation_custom_scales ON tenant_custom_scales
    USING (tenant_id = current_setting('app.current_tenant_id', true)::UUID);

-- 创建基于角色的访问策略
CREATE POLICY assessor_own_records ON tenant_assessment_records
    FOR SELECT
    USING (
        tenant_id = current_setting('app.current_tenant_id', true)::UUID
        AND (
            assessor_id = current_setting('app.current_user_id', true)::UUID
            OR current_setting('app.current_user_role', true) IN ('ADMIN', 'SUPERVISOR')
        )
    );

-- ========================================
-- 触发器和函数
-- ========================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 审计日志触发器函数
CREATE OR REPLACE FUNCTION log_data_changes()
RETURNS TRIGGER AS $$
DECLARE
    tenant_id_val UUID;
    user_id_val UUID;
    old_data JSONB;
    new_data JSONB;
BEGIN
    -- 获取当前租户和用户ID
    BEGIN
        tenant_id_val := current_setting('app.current_tenant_id', true)::UUID;
        user_id_val := current_setting('app.current_user_id', true)::UUID;
    EXCEPTION
        WHEN OTHERS THEN
            -- 如果无法获取上下文，跳过审计
            RETURN COALESCE(NEW, OLD);
    END;
    
    -- 如果没有租户上下文，跳过审计
    IF tenant_id_val IS NULL THEN
        RETURN COALESCE(NEW, OLD);
    END IF;
    
    -- 构建审计数据
    IF TG_OP = 'DELETE' THEN
        old_data := to_jsonb(OLD);
        new_data := NULL;
    ELSIF TG_OP = 'UPDATE' THEN
        old_data := to_jsonb(OLD);
        new_data := to_jsonb(NEW);
    ELSIF TG_OP = 'INSERT' THEN
        old_data := NULL;
        new_data := to_jsonb(NEW);
    END IF;
    
    -- 插入审计日志
    INSERT INTO tenant_audit_logs (
        tenant_id, user_id, action, resource_type, resource_id,
        old_values, new_values
    ) VALUES (
        tenant_id_val,
        user_id_val,
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        old_data,
        new_data
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 为需要的表创建触发器
CREATE TRIGGER update_tenants_updated_at 
    BEFORE UPDATE ON tenants 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_platform_users_updated_at 
    BEFORE UPDATE ON platform_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessment_subjects_updated_at 
    BEFORE UPDATE ON assessment_subjects 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenant_assessment_records_updated_at 
    BEFORE UPDATE ON tenant_assessment_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加审计触发器
CREATE TRIGGER audit_assessment_subjects
    AFTER INSERT OR UPDATE OR DELETE ON assessment_subjects
    FOR EACH ROW EXECUTE FUNCTION log_data_changes();

CREATE TRIGGER audit_tenant_assessment_records
    AFTER INSERT OR UPDATE OR DELETE ON tenant_assessment_records
    FOR EACH ROW EXECUTE FUNCTION log_data_changes();

-- ========================================
-- 数据加密函数
-- ========================================

-- 敏感数据加密函数
CREATE OR REPLACE FUNCTION encrypt_pii(data TEXT)
RETURNS TEXT AS $$
BEGIN
    IF data IS NULL OR data = '' THEN
        RETURN data;
    END IF;
    
    -- 使用AES-256加密，密钥从环境变量或配置获取
    RETURN encode(
        encrypt(
            data::bytea, 
            current_setting('app.encryption_key', true)::bytea, 
            'aes'
        ), 
        'base64'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 敏感数据解密函数
CREATE OR REPLACE FUNCTION decrypt_pii(encrypted_data TEXT)
RETURNS TEXT AS $$
BEGIN
    IF encrypted_data IS NULL OR encrypted_data = '' THEN
        RETURN encrypted_data;
    END IF;
    
    RETURN convert_from(
        decrypt(
            decode(encrypted_data, 'base64'), 
            current_setting('app.encryption_key', true)::bytea, 
            'aes'
        ),
        'UTF8'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 视图定义
-- ========================================

-- 租户活跃度统计视图
CREATE VIEW tenant_activity_stats AS
SELECT 
    t.id as tenant_id,
    t.code as tenant_code,
    t.name as tenant_name,
    t.industry,
    t.subscription_plan,
    
    -- 用户统计
    COUNT(DISTINCT tum.user_id) as total_users,
    COUNT(DISTINCT CASE WHEN tum.last_active_at >= NOW() - INTERVAL '7 days' 
          THEN tum.user_id END) as weekly_active_users,
    COUNT(DISTINCT CASE WHEN tum.last_active_at >= NOW() - INTERVAL '30 days' 
          THEN tum.user_id END) as monthly_active_users,
    
    -- 评估统计
    COUNT(tar.id) as total_assessments,
    COUNT(CASE WHEN tar.assessment_date >= NOW() - INTERVAL '30 days' 
          THEN tar.id END) as monthly_assessments,
    COUNT(CASE WHEN tar.status = 'approved' THEN tar.id END) as completed_assessments,
    
    -- 配额使用情况
    COUNT(DISTINCT tcs.id) as custom_scales_count,
    t.max_custom_scales,
    COUNT(CASE WHEN tar.assessment_date >= date_trunc('month', NOW()) 
          THEN tar.id END) as current_month_assessments,
    t.max_monthly_assessments,
    
    -- 数据质量
    AVG(tar.quality_score) as avg_quality_score,
    COUNT(tar.id)::FLOAT / NULLIF(COUNT(DISTINCT tar.subject_id), 0) as assessments_per_subject

FROM tenants t
LEFT JOIN tenant_user_memberships tum ON t.id = tum.tenant_id
LEFT JOIN tenant_assessment_records tar ON t.id = tar.tenant_id
LEFT JOIN tenant_custom_scales tcs ON t.id = tcs.tenant_id AND tcs.status = 'active'
WHERE t.status = 'active'
GROUP BY t.id, t.code, t.name, t.industry, t.subscription_plan, 
         t.max_custom_scales, t.max_monthly_assessments;

-- 量表使用统计视图
CREATE VIEW scale_usage_stats AS
SELECT 
    gsr.id as scale_id,
    gsr.code as scale_code,
    gsr.name as scale_name,
    gsr.category,
    gsr.publisher_type,
    
    -- 订阅统计
    COUNT(DISTINCT tss.tenant_id) as subscribed_tenants,
    COUNT(DISTINCT CASE WHEN tss.is_active = true THEN tss.tenant_id END) as active_tenants,
    
    -- 使用统计
    gsr.usage_count as total_usage,
    COUNT(tar.id) as recent_usage,
    COUNT(CASE WHEN tar.assessment_date >= NOW() - INTERVAL '30 days' 
          THEN tar.id END) as monthly_usage,
    
    -- 评分统计
    gsr.rating,
    gsr.rating_count,
    AVG(tar.total_score) as avg_assessment_score,
    
    -- 质量指标
    AVG(tar.quality_score) as avg_quality_score,
    COUNT(tar.id)::FLOAT / NULLIF(COUNT(DISTINCT tar.tenant_id), 0) as usage_per_tenant

FROM global_scale_registry gsr
LEFT JOIN tenant_scale_subscriptions tss ON gsr.id = tss.scale_id
LEFT JOIN tenant_assessment_records tar ON gsr.id = tar.scale_id AND tar.scale_type = 'global'
WHERE gsr.status = 'active'
GROUP BY gsr.id, gsr.code, gsr.name, gsr.category, gsr.publisher_type, 
         gsr.usage_count, gsr.rating, gsr.rating_count;

-- ========================================
-- 初始化数据
-- ========================================

-- 插入初始租户（演示数据）
INSERT INTO tenants (code, name, industry, subscription_plan, max_users, max_monthly_assessments, max_custom_scales) VALUES
('demo_hospital', '演示医院', 'healthcare', 'premium', 200, 5000, 50),
('demo_nursing', '演示养老院', 'nursing', 'standard', 100, 2000, 20),
('demo_community', '演示社区中心', 'community', 'basic', 50, 1000, 10);

-- 插入初始全局量表
INSERT INTO global_scale_registry (
    code, name, version, category, industry_tags, 
    form_schema, scoring_rules, publisher_type, is_official, is_verified
) VALUES (
    'elderly_ability_v2',
    '老年人能力评估量表',
    '2.0',
    'elderly_ability',
    '{"healthcare","nursing","community"}',
    '{
        "title": "老年人能力评估量表",
        "sections": [
            {
                "id": "daily_living",
                "title": "日常生活能力",
                "weight": 0.4,
                "fields": [
                    {
                        "id": "eating",
                        "type": "radio",
                        "title": "进食能力",
                        "required": true,
                        "options": [
                            {"value": 1, "label": "完全依赖", "score": 1},
                            {"value": 2, "label": "需要帮助", "score": 2},
                            {"value": 3, "label": "基本独立", "score": 3}
                        ]
                    }
                ]
            }
        ]
    }',
    '{
        "algorithm": "weighted_sum",
        "weights": {
            "daily_living": 0.4,
            "cognitive": 0.3,
            "mobility": 0.3
        },
        "total_range": {"min": 1, "max": 3},
        "levels": [
            {"range": [1.0, 1.5], "level": "重度依赖", "color": "#ff4d4f"},
            {"range": [1.5, 2.5], "level": "中度依赖", "color": "#faad14"},
            {"range": [2.5, 3.0], "level": "轻度依赖", "color": "#52c41a"}
        ]
    }',
    'platform',
    true,
    true
);

-- 插入系统配置
INSERT INTO system_configurations (config_key, config_value, config_type, description, category) VALUES
('system.name', '智慧养老评估平台', 'string', '系统名称', 'general'),
('system.version', '2.0.0', 'string', '系统版本', 'general'),
('encryption.enabled', 'true', 'boolean', '是否启用数据加密', 'security'),
('audit.retention_days', '365', 'number', '审计日志保留天数', 'audit'),
('assessment.auto_save_interval', '30', 'number', '评估表单自动保存间隔(秒)', 'assessment'),
('report.max_export_records', '10000', 'number', '报告导出最大记录数', 'report');

-- ========================================
-- 表和字段注释
-- ========================================

COMMENT ON TABLE tenants IS 'SaaS租户管理表 - 存储租户基本信息和订阅配置';
COMMENT ON TABLE global_scale_registry IS '全局量表注册中心 - 存储所有可用的评估量表定义';
COMMENT ON TABLE tenant_scale_subscriptions IS '租户量表订阅表 - 管理租户对量表的订阅关系';
COMMENT ON TABLE tenant_custom_scales IS '租户自定义量表表 - 存储租户创建的自定义量表';
COMMENT ON TABLE platform_users IS '平台用户表 - 存储跨租户的用户基本信息';
COMMENT ON TABLE tenant_user_memberships IS '租户用户关联表 - 管理用户在各租户中的角色和权限';
COMMENT ON TABLE assessment_subjects IS '评估对象表 - 按租户分区存储被评估人信息';
COMMENT ON TABLE tenant_assessment_records IS '租户评估记录表 - 按租户分区存储评估数据';
COMMENT ON TABLE tenant_audit_logs IS '租户审计日志表 - 按租户分区记录操作日志';

COMMENT ON COLUMN tenants.subscription_plan IS '订阅计划: basic, standard, premium, enterprise';
COMMENT ON COLUMN global_scale_registry.visibility IS '可见性: public(公开), private(私有), premium(付费)';
COMMENT ON COLUMN tenant_assessment_records.scale_type IS '量表类型: global(全局量表), custom(自定义量表)';
COMMENT ON COLUMN system_configurations.is_sensitive IS '敏感配置需要加密存储';