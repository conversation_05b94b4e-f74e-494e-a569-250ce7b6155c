-- ========================================
-- 智能评估平台 - 清理单租户架构遗留表
-- V3__Clean_single_tenant_tables.sql
-- 创建日期: 2025-06-21
-- 目的: 移除单租户架构遗留的表，确保纯多租户架构
-- ========================================

-- 注意：这个脚本会删除单租户架构的表
-- 在生产环境执行前请确保数据已迁移到多租户表中

-- 删除单租户评估记录表（如果存在）
DROP TABLE IF EXISTS assessment_records CASCADE;

-- 删除单租户量表表（如果存在）
DROP TABLE IF EXISTS assessment_scales CASCADE;

-- 删除单租户用户表（如果存在，但保留institutions表作为租户迁移参考）
-- DROP TABLE IF EXISTS users CASCADE;

-- 删除单租户老年人表（如果存在）
DROP TABLE IF EXISTS elderly_persons CASCADE;

-- 删除单租户评估任务表（如果存在）
DROP TABLE IF EXISTS assessment_tasks CASCADE;

-- 删除单租户评估结果表（如果存在）
DROP TABLE IF EXISTS assessment_results CASCADE;

-- 添加索引优化（如果还没有的话）
DO $$
BEGIN
    -- 为多租户表添加性能优化索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_platform_users_email_active') THEN
        CREATE INDEX idx_platform_users_email_active ON platform_users(email) WHERE is_active = true;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tenants_status_active') THEN
        CREATE INDEX idx_tenants_status_active ON tenants(status) WHERE status = 'active';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_global_scales_public_active') THEN
        CREATE INDEX idx_global_scales_public_active ON global_scale_registry(visibility, status) 
        WHERE visibility = 'PUBLIC' AND status = 'ACTIVE';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tenant_assessments_status') THEN
        CREATE INDEX idx_tenant_assessments_status ON tenant_assessment_records(tenant_id, status);
    END IF;
END $$;

-- 添加表注释以明确多租户架构
COMMENT ON TABLE platform_users IS '平台用户表 - 多租户架构核心用户表';
COMMENT ON TABLE tenants IS '租户表 - 存储所有租户(机构)信息';
COMMENT ON TABLE tenant_user_memberships IS '租户用户关系表 - 管理用户在各租户中的角色';
COMMENT ON TABLE global_scale_registry IS '全局量表注册中心 - 存储所有可用的评估量表';
COMMENT ON TABLE tenant_assessment_records IS '租户评估记录表 - 存储各租户的评估数据';
COMMENT ON TABLE assessment_subjects IS '评估对象表 - 存储被评估人员信息';

-- 记录架构清理完成
INSERT INTO schema_migrations_log (version, description, executed_at) 
VALUES ('V3', 'Cleaned single-tenant legacy tables, pure multi-tenant architecture achieved', NOW())
ON CONFLICT DO NOTHING;