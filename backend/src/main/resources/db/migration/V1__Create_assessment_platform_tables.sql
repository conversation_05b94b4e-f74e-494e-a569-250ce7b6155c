-- 智能评估平台数据库结构
-- V1__Create_assessment_platform_tables.sql

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ================================
-- 1. 基础数据表
-- ================================

-- 机构表
CREATE TABLE institutions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL, -- NURSING_HOME, COMMUNITY_CENTER, HOSPITAL, GOVERNMENT
    level VARCHAR(20), -- NATIONAL, PROVINCIAL, CITY, DISTRICT
    address TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(100),
    license_number VARCHAR(100), -- 许可证号
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(50),
    role VARCHAR(50) NOT NULL, -- ADMIN, ASSESSOR, REVIEWER, VIEWER
    institution_id UUID REFERENCES institutions(id),
    professional_title VARCHAR(100), -- 专业职称
    license_number VARCHAR(100), -- 执业证号
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 被评估人表
CREATE TABLE elderly_persons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    id_number VARCHAR(50), -- 身份证号
    gender VARCHAR(10), -- MALE, FEMALE
    birth_date DATE,
    age INTEGER,
    phone VARCHAR(50),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(50),
    address TEXT,
    medical_insurance_number VARCHAR(100), -- 医保号
    care_level VARCHAR(50), -- 护理等级
    institution_id UUID REFERENCES institutions(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- ================================
-- 2. 评估量表相关表
-- ================================

-- 量表分类表
CREATE TABLE scale_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES scale_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估量表表
CREATE TABLE assessment_scales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL, -- ELDERLY_ABILITY, EMOTIONAL_QUICK, INTER_RAI, LONG_CARE_INSURANCE, CUSTOM
    category_id UUID REFERENCES scale_categories(id),
    version VARCHAR(20) DEFAULT '1.0',
    description TEXT,
    form_schema JSONB NOT NULL, -- 表单结构Schema
    scoring_rules JSONB NOT NULL, -- 评分规则
    config_options JSONB, -- 配置选项
    is_active BOOLEAN DEFAULT true,
    is_official BOOLEAN DEFAULT false, -- 是否官方量表
    applicable_scope VARCHAR(500), -- 适用范围
    estimated_duration INTEGER, -- 预估时长(分钟)
    max_score INTEGER,
    min_score INTEGER,
    source_pdf_path VARCHAR(500), -- 来源PDF路径
    parse_status VARCHAR(20) DEFAULT 'SUCCESS', -- PENDING, PARSING, SUCCESS, FAILED, REVIEWING, APPROVED
    parse_error TEXT,
    usage_count BIGINT DEFAULT 0,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 量表版本历史表
CREATE TABLE scale_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scale_id UUID NOT NULL REFERENCES assessment_scales(id),
    version VARCHAR(20) NOT NULL,
    form_schema JSONB NOT NULL,
    scoring_rules JSONB NOT NULL,
    change_description TEXT,
    is_current BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- ================================
-- 3. 评估记录相关表
-- ================================

-- 评估任务表
CREATE TABLE assessment_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_number VARCHAR(100) UNIQUE NOT NULL, -- 任务编号
    elderly_id UUID NOT NULL REFERENCES elderly_persons(id),
    scale_id UUID NOT NULL REFERENCES assessment_scales(id),
    assessor_id UUID NOT NULL REFERENCES users(id),
    reviewer_id UUID REFERENCES users(id),
    task_type VARCHAR(50) NOT NULL, -- INITIAL, REVIEW, FOLLOW_UP
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, IN_PROGRESS, COMPLETED, REVIEWED, REJECTED
    priority VARCHAR(20) DEFAULT 'NORMAL', -- HIGH, NORMAL, LOW
    scheduled_date TIMESTAMP,
    deadline TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 评估记录表
CREATE TABLE assessment_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    record_number VARCHAR(100) UNIQUE NOT NULL, -- 评估编号
    task_id UUID REFERENCES assessment_tasks(id),
    elderly_id UUID NOT NULL REFERENCES elderly_persons(id),
    scale_id UUID NOT NULL REFERENCES assessment_scales(id),
    assessor_id UUID NOT NULL REFERENCES users(id),
    reviewer_id UUID REFERENCES users(id),
    assessment_date TIMESTAMP NOT NULL,
    form_data JSONB NOT NULL, -- 评估表单数据
    score_data JSONB, -- 评分数据
    total_score DECIMAL(10,2),
    result_level VARCHAR(50), -- 评估结果等级
    status VARCHAR(50) DEFAULT 'DRAFT', -- DRAFT, SUBMITTED, REVIEWED, APPROVED, REJECTED
    review_notes TEXT, -- 审核意见
    reviewed_at TIMESTAMP,
    is_valid BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 评估结果详情表
CREATE TABLE assessment_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    record_id UUID NOT NULL REFERENCES assessment_records(id),
    section_name VARCHAR(200), -- 章节名称
    section_score DECIMAL(10,2), -- 章节得分
    section_max_score DECIMAL(10,2), -- 章节满分
    section_result JSONB, -- 章节详细结果
    recommendations TEXT, -- 建议
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 4. 评估历史和统计表
-- ================================

-- 评估历史比较表
CREATE TABLE assessment_comparisons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    elderly_id UUID NOT NULL REFERENCES elderly_persons(id),
    scale_id UUID NOT NULL REFERENCES assessment_scales(id),
    previous_record_id UUID REFERENCES assessment_records(id),
    current_record_id UUID NOT NULL REFERENCES assessment_records(id),
    score_change DECIMAL(10,2), -- 分数变化
    level_change VARCHAR(100), -- 等级变化
    improvement_areas JSONB, -- 改善领域
    decline_areas JSONB, -- 下降领域
    comparison_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估统计表
CREATE TABLE assessment_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    institution_id UUID REFERENCES institutions(id),
    scale_id UUID REFERENCES assessment_scales(id),
    stat_date DATE NOT NULL,
    stat_type VARCHAR(50) NOT NULL, -- DAILY, WEEKLY, MONTHLY, YEARLY
    total_assessments INTEGER DEFAULT 0,
    completed_assessments INTEGER DEFAULT 0,
    avg_score DECIMAL(10,2),
    score_distribution JSONB, -- 分数分布
    level_distribution JSONB, -- 等级分布
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 5. 系统配置和日志表
-- ================================

-- 系统配置表
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(50), -- STRING, NUMBER, BOOLEAN, JSON
    description TEXT,
    is_system BOOLEAN DEFAULT false, -- 是否系统配置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100)
);

-- 操作日志表
CREATE TABLE operation_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL, -- CREATE, UPDATE, DELETE, VIEW, EXPORT
    module VARCHAR(50) NOT NULL, -- SCALE, ASSESSMENT, USER, SYSTEM
    resource_type VARCHAR(50), -- 资源类型
    resource_id UUID, -- 资源ID
    operation_desc TEXT, -- 操作描述
    request_data JSONB, -- 请求数据
    response_data JSONB, -- 响应数据
    ip_address VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文件管理表
CREATE TABLE file_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50), -- PDF, IMAGE, EXCEL, WORD
    file_size BIGINT, -- 文件大小(字节)
    mime_type VARCHAR(100),
    related_type VARCHAR(50), -- SCALE_SOURCE, ASSESSMENT_ATTACHMENT, EXPORT_RESULT
    related_id UUID, -- 关联资源ID
    uploaded_by UUID REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 6. 创建索引
-- ================================

-- 机构索引
CREATE INDEX idx_institutions_code ON institutions(code);
CREATE INDEX idx_institutions_type ON institutions(type);
CREATE INDEX idx_institutions_active ON institutions(is_active);

-- 用户索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_institution ON users(institution_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- 被评估人索引
CREATE INDEX idx_elderly_id_number ON elderly_persons(id_number);
CREATE INDEX idx_elderly_institution ON elderly_persons(institution_id);
CREATE INDEX idx_elderly_name ON elderly_persons(name);
CREATE INDEX idx_elderly_active ON elderly_persons(is_active);

-- 评估量表索引
CREATE INDEX idx_scales_code ON assessment_scales(code);
CREATE INDEX idx_scales_type ON assessment_scales(type);
CREATE INDEX idx_scales_category ON assessment_scales(category_id);
CREATE INDEX idx_scales_active ON assessment_scales(is_active);
CREATE INDEX idx_scales_usage ON assessment_scales(usage_count DESC);

-- 评估任务索引
CREATE INDEX idx_tasks_elderly ON assessment_tasks(elderly_id);
CREATE INDEX idx_tasks_scale ON assessment_tasks(scale_id);
CREATE INDEX idx_tasks_assessor ON assessment_tasks(assessor_id);
CREATE INDEX idx_tasks_status ON assessment_tasks(status);
CREATE INDEX idx_tasks_date ON assessment_tasks(scheduled_date);

-- 评估记录索引
CREATE INDEX idx_records_elderly ON assessment_records(elderly_id);
CREATE INDEX idx_records_scale ON assessment_records(scale_id);
CREATE INDEX idx_records_assessor ON assessment_records(assessor_id);
CREATE INDEX idx_records_date ON assessment_records(assessment_date);
CREATE INDEX idx_records_status ON assessment_records(status);
CREATE INDEX idx_records_number ON assessment_records(record_number);

-- 评估结果索引
CREATE INDEX idx_results_record ON assessment_results(record_id);

-- 统计索引
CREATE INDEX idx_statistics_institution ON assessment_statistics(institution_id);
CREATE INDEX idx_statistics_scale ON assessment_statistics(scale_id);
CREATE INDEX idx_statistics_date ON assessment_statistics(stat_date);
CREATE INDEX idx_statistics_type ON assessment_statistics(stat_type);

-- 日志索引
CREATE INDEX idx_logs_user ON operation_logs(user_id);
CREATE INDEX idx_logs_operation ON operation_logs(operation_type);
CREATE INDEX idx_logs_module ON operation_logs(module);
CREATE INDEX idx_logs_created ON operation_logs(created_at);

-- 文件索引
CREATE INDEX idx_files_related ON file_records(related_type, related_id);
CREATE INDEX idx_files_uploader ON file_records(uploaded_by);
CREATE INDEX idx_files_deleted ON file_records(is_deleted);

-- ================================
-- 7. 创建触发器函数
-- ================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳触发器
CREATE TRIGGER update_institutions_updated_at BEFORE UPDATE ON institutions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_elderly_persons_updated_at BEFORE UPDATE ON elderly_persons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_scales_updated_at BEFORE UPDATE ON assessment_scales FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_tasks_updated_at BEFORE UPDATE ON assessment_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_records_updated_at BEFORE UPDATE ON assessment_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- 8. 初始化基础数据
-- ================================

-- 插入量表分类
INSERT INTO scale_categories (id, name, code, description, sort_order) VALUES
(uuid_generate_v4(), '老年人能力评估', 'ELDERLY_ABILITY', '用于评估老年人日常生活能力的量表分类', 1),
(uuid_generate_v4(), '认知功能评估', 'COGNITIVE', '用于评估认知功能的量表分类', 2),
(uuid_generate_v4(), '心理健康评估', 'MENTAL_HEALTH', '用于评估心理健康状况的量表分类', 3),
(uuid_generate_v4(), '护理等级评估', 'CARE_LEVEL', '用于确定护理等级的量表分类', 4),
(uuid_generate_v4(), '国际标准量表', 'INTERNATIONAL', '国际标准化评估量表', 5),
(uuid_generate_v4(), '自定义量表', 'CUSTOM', '用户自定义的评估量表', 99);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_system) VALUES
('system.name', '智慧养老评估平台', 'STRING', '系统名称', true),
('system.version', '1.0.0', 'STRING', '系统版本', true),
('assessment.auto_review', 'false', 'BOOLEAN', '是否自动审核评估结果', false),
('assessment.max_duration', '120', 'NUMBER', '评估最大时长(分钟)', false),
('export.max_records', '10000', 'NUMBER', '导出最大记录数', false),
('file.max_size', '52428800', 'NUMBER', '文件最大大小(字节)', false),
('notification.email_enabled', 'true', 'BOOLEAN', '是否启用邮件通知', false);

-- 添加表注释
COMMENT ON TABLE institutions IS '机构表 - 存储评估机构信息';
COMMENT ON TABLE users IS '用户表 - 存储系统用户信息';
COMMENT ON TABLE elderly_persons IS '被评估人表 - 存储老年人基本信息';
COMMENT ON TABLE scale_categories IS '量表分类表 - 评估量表的分类管理';
COMMENT ON TABLE assessment_scales IS '评估量表表 - 存储各种评估量表的结构和规则';
COMMENT ON TABLE scale_versions IS '量表版本历史表 - 记录量表的版本变更历史';
COMMENT ON TABLE assessment_tasks IS '评估任务表 - 管理评估任务的分配和调度';
COMMENT ON TABLE assessment_records IS '评估记录表 - 存储具体的评估数据和结果';
COMMENT ON TABLE assessment_results IS '评估结果详情表 - 存储评估的详细结果分析';
COMMENT ON TABLE assessment_comparisons IS '评估历史比较表 - 用于对比不同时期的评估结果';
COMMENT ON TABLE assessment_statistics IS '评估统计表 - 存储各种维度的评估统计数据';
COMMENT ON TABLE system_configs IS '系统配置表 - 存储系统的配置参数';
COMMENT ON TABLE operation_logs IS '操作日志表 - 记录用户的所有操作行为';
COMMENT ON TABLE file_records IS '文件管理表 - 管理系统中的文件资源';