#!/usr/bin/env python3
"""
Docling PDF to Markdown Conversion Service
用于将PDF文件转换为Markdown格式，以便更好地解析评估量表结构
"""

import sys
import json
import tempfile
import os
from pathlib import Path
from typing import Optional, Dict, Any

def install_docling():
    """安装Docling依赖"""
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "docling"])
        print("Docling安装成功", file=sys.stderr)
    except subprocess.CalledProcessError as e:
        print(f"Docling安装失败: {e}", file=sys.stderr)
        sys.exit(1)

try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PipelineOptions
    from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend
except ImportError:
    print("Docling未安装，正在安装...", file=sys.stderr)
    install_docling()
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PipelineOptions
    from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend

class DoclingPDFConverter:
    """Docling PDF转换器"""
    
    def __init__(self):
        """初始化转换器"""
        # 配置管道选项
        pipeline_options = PipelineOptions()
        pipeline_options.do_ocr = True  # 启用OCR
        pipeline_options.do_table_structure = True  # 启用表格结构识别
        pipeline_options.table_structure_options.do_cell_matching = True
        
        # 初始化转换器
        self.converter = DocumentConverter(
            allowed_formats=[InputFormat.PDF],
            pipeline_options=pipeline_options
        )
        
    def convert_pdf_to_markdown(self, pdf_path: str) -> Dict[str, Any]:
        """
        将PDF转换为Markdown
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            包含转换结果的字典
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(pdf_path):
                return {
                    "success": False,
                    "error": f"PDF文件不存在: {pdf_path}",
                    "markdown": "",
                    "metadata": {}
                }
            
            # 转换文档
            source = Path(pdf_path)
            result = self.converter.convert(source)
            
            # 导出为Markdown
            markdown_content = result.document.export_to_markdown()
            
            # 提取元数据
            metadata = {
                "page_count": len(result.document.pages),
                "title": getattr(result.document, 'title', ''),
                "source_path": str(pdf_path),
                "tables_count": self._count_tables(result.document),
                "images_count": self._count_images(result.document)
            }
            
            return {
                "success": True,
                "error": None,
                "markdown": markdown_content,
                "metadata": metadata,
                "original_document": str(result.document)[:1000] + "..." if len(str(result.document)) > 1000 else str(result.document)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"PDF转换失败: {str(e)}",
                "markdown": "",
                "metadata": {}
            }
    
    def _count_tables(self, document) -> int:
        """统计文档中的表格数量"""
        try:
            table_count = 0
            for page in document.pages:
                for item in page.predictions.layout.bboxes:
                    if hasattr(item, 'label') and 'table' in item.label.lower():
                        table_count += 1
            return table_count
        except:
            return 0
    
    def _count_images(self, document) -> int:
        """统计文档中的图像数量"""
        try:
            image_count = 0
            for page in document.pages:
                for item in page.predictions.layout.bboxes:
                    if hasattr(item, 'label') and 'figure' in item.label.lower():
                        image_count += 1
            return image_count
        except:
            return 0

def main():
    """主函数 - 命令行接口"""
    if len(sys.argv) != 2:
        print(json.dumps({
            "success": False,
            "error": "用法: python docling_service.py <pdf_file_path>",
            "markdown": "",
            "metadata": {}
        }))
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    # 初始化转换器
    converter = DoclingPDFConverter()
    
    # 执行转换
    result = converter.convert_pdf_to_markdown(pdf_path)
    
    # 输出JSON结果
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()