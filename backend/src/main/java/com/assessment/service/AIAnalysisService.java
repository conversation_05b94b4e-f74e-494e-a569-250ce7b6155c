package com.assessment.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** AI文档结构分析服务 使用LM Studio本地模型进行智能分析 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AIAnalysisService {

  // 默认值常量 - 在没有量表配置时使用
  private static final int DEFAULT_CONFIDENCE = 75;
  private static final int DEFAULT_FIELD_IMPORTANCE = 50;
  private static final int FALLBACK_CONFIDENCE = 30;

  private final LMStudioService lmStudioService;
  private final RestTemplate restTemplate;
  private final ObjectMapper objectMapper = new ObjectMapper();

  /** 分析文档结构 */
  public DocumentAnalysisResult analyzeDocument(DocumentAnalysisRequest request) {
    long startTime = System.currentTimeMillis();

    if (request == null) {
        return createInvalidRequestFallback(startTime);
    }

    try {
      if (request.getMarkdownContent() == null || request.getMarkdownContent().trim().isEmpty()) {
          log.warn("Markdown内容为空，执行基础降级分析。文件名: {}", request.getFileName());
          return createFallbackAnalysis(request, "内容为空，执行基础降级分析", System.currentTimeMillis() - startTime);
      }

      log.info(
          "开始AI分析文档: {}, 内容长度: {}, 流式输出: {}",
          request.getFileName(),
          request.getMarkdownContent().length(),
          request.getUseStream());

      if (!lmStudioService.isServiceAvailable()) {
          log.warn("AI服务不可用，切换到基础降级分析。");
          return createFallbackAnalysis(request, "AI服务不可用，切换到基础降级分析", System.currentTimeMillis() - startTime);
      }

      // 构建AI分析提示词
      String prompt = buildAnalysisPrompt(request);

      // 调用LM Studio API
      String aiResponse = chatWithAI(prompt, null);

      // 解析AI响应
      DocumentAnalysisResult result = parseAIResponse(aiResponse);

      long analysisTime = System.currentTimeMillis() - startTime;
      result.setParsingTimeMs(analysisTime);

      log.info(
          "AI分析完成: 表名={}, 字段数={}, 耗时={}ms",
          result.getTableName(),
          result.getFields().size(),
          analysisTime);

      return result;

    } catch (Exception e) {
      log.error("AI分析失败，切换到基础降级分析", e);
      // 返回降级分析结果
      return createFallbackAnalysis(request, "AI服务调用失败，切换到基础降级分析", System.currentTimeMillis() - startTime);
    }
  }

  /** 检查AI服务是否可用 */
  public boolean isAIServiceAvailable() {
    return lmStudioService.isServiceAvailable();
  }

  /** 构建AI分析提示词 */
  private String buildAnalysisPrompt(DocumentAnalysisRequest request) {
    String content = request.getMarkdownContent();
    String customPrompt = request.getCustomPrompt();

    // 如果有自定义提示词，使用自定义提示词
    if (customPrompt != null && !customPrompt.trim().isEmpty()) {
      log.info("使用自定义提示词进行分析，提示词长度: {}", customPrompt.length());

      // 在自定义提示词后面添加文档内容
      return customPrompt + "\n\n## 待分析文档:\n" + content;
    }

    // 使用优化的结构化提示词
    return String.format(
        """
            # 智能数据库设计分析任务

            你是一位专业的数据库架构师。请使用以下结构化输出格式来分析文档并生成PostgreSQL数据库表结构设计。

            ## 输出格式要求（重要）

            1. **思考过程**：使用 `<think>` 标签包围你的分析思路
            2. **SQL代码**：使用 ```sql 代码块
            3. **JSON数据**：使用 ```json 代码块
            4. **说明文本**：使用普通段落

            ## 待分析文档
            ```markdown
            %s
            ```

            ## 请严格按以下格式输出：

            <think>
            我需要分析这个文档的结构：
            1. 识别关键的数据字段和实体
            2. 确定字段类型和约束关系
            3. 设计合理的表结构和索引
            4. 生成JSON格式的字段定义
            </think>

            ### 数据库表结构设计

            基于文档分析，我设计了以下PostgreSQL表结构：

            ```sql
            CREATE TABLE assessment_scale (
                id SERIAL PRIMARY KEY,
                -- 主要字段根据文档内容生成
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            -- 索引优化
            CREATE INDEX idx_assessment_scale_xxx ON assessment_scale(xxx);
            ```

            ### 系统集成JSON格式

            ```json
            {
              "tableName": "assessment_scale",
              "tableComment": "评估量表数据表",
              "fields": [
                {
                  "name": "id",
                  "type": "INT",
                  "length": "",
                  "nullable": false,
                  "comment": "主键ID",
                  "defaultValue": "AUTO_INCREMENT",
                  "isPrimaryKey": true,
                  "importance": 100
                }
              ]
            }
            ```

            ### 设计说明

            详细说明表设计理由、字段选择依据和性能优化建议。
            """,
        content);
  }

  /** 调用LM Studio API（支持流式输出） */
  private String callLMStudioAPI(String prompt) {
    // This method is now effectively replaced by chatWithAI for non-streaming cases
    // and callLMStudioAPIWithStream for streaming cases.
    // It can be removed or kept for backward compatibility if needed.
    // For this refactoring, we assume it's no longer directly called by analyzeDocument.
    return chatWithAI(prompt, null);
  }

  /** 获取当前模型信息 */
  public Map<String, Object> getCurrentModelInfo() {
    return lmStudioService.getCurrentModelInfo();
  }

  /**
   * 解析AI响应，提取JSON和SQL
   *
   * @param aiResponse AI返回的完整文本
   * @return 解析后的文档分析结果
   */
  private DocumentAnalysisResult parseAIResponse(
      String aiResponse) {
    long startTime = System.currentTimeMillis();
    DocumentAnalysisResult result = new DocumentAnalysisResult();
    result.setAiRawResponse(aiResponse);

    try {
      // 提取JSON
      String jsonString = extractJSON(aiResponse);

      if (jsonString.isEmpty()) {
        log.warn("在AI响应中未找到有效的JSON块");
        throw new RuntimeException("无法从AI响应中提取JSON");
      }

      JsonNode rootNode = objectMapper.readTree(jsonString);

      // 提取基本信息
      result.setTableName(getStringValue(rootNode, "tableName", ""));
      result.setTableComment(getStringValue(rootNode, "tableComment", ""));
      result.setConfidence(getIntValue(rootNode, "confidence", DEFAULT_CONFIDENCE));

      // 提取字段信息
      List<DocumentAnalysisResult.DatabaseField> fields = new ArrayList<>();
      JsonNode fieldsNode = rootNode.get("fields");
      if (fieldsNode != null && fieldsNode.isArray()) {
        for (JsonNode fieldNode : fieldsNode) {
          DocumentAnalysisResult.DatabaseField field =
              DocumentAnalysisResult.DatabaseField.builder()
                  .name(getStringValue(fieldNode, "name", "unknown_field"))
                  .type(getStringValue(fieldNode, "type", "TEXT"))
                  .length(getStringValue(fieldNode, "length", ""))
                  .nullable(getBooleanValue(fieldNode, "nullable", true))
                  .comment(getStringValue(fieldNode, "comment", ""))
                  .defaultValue(getStringValue(fieldNode, "defaultValue", null))
                  .isPrimaryKey(getBooleanValue(fieldNode, "isPrimaryKey", false))
                  .isForeignKey(getBooleanValue(fieldNode, "isForeignKey", false))
                  .foreignKeyTable(getStringValue(fieldNode, "foreignKeyTable", null))
                  .foreignKeyColumn(getStringValue(fieldNode, "foreignKeyColumn", null))
                  .importance(getIntValue(fieldNode, "importance", DEFAULT_FIELD_IMPORTANCE))
                  .confidence(getIntValue(fieldNode, "confidence", DEFAULT_CONFIDENCE))
                  .build();
          fields.add(field);
        }
      }

      // 确保基础字段存在
      result.setFields(ensureBaseFields(fields));

      // 提取SQL
      result.setGeneratedSql(extractSQL(aiResponse));

      // 提取思考过程和设计说明
      result.setAiThinkingProcess(extractSection(aiResponse, "think"));
      result.setDesignExplanation(extractSection(aiResponse, "设计说明"));

      result.setSuccess(true);
      result.setMessage("AI分析成功");

    } catch (JsonProcessingException | RuntimeException e) {
      log.error("解析AI响应失败", e);
      result.setSuccess(false);
      result.setMessage("解析AI响应失败: " + e.getMessage());
    }

    result.setParsingTimeMs(System.currentTimeMillis() - startTime);
    return result;
  }

  /** 提取被```json ```包围的JSON字符串 */
  private String extractJSON(String response) {
    Pattern pattern = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```");
    Matcher matcher = pattern.matcher(response);
    if (matcher.find()) {
      return matcher.group(1).trim();
    }
    return "";
  }

  /** 从JSON节点安全地获取字符串值 */
  private String getStringValue(JsonNode node, String fieldName, String defaultValue) {
    JsonNode valueNode = node.get(fieldName);
    return (valueNode != null && !valueNode.isNull()) ? valueNode.asText() : defaultValue;
  }

  /** 从JSON节点安全地获取整数值 */
  private Integer getIntValue(JsonNode node, String fieldName, Integer defaultValue) {
    JsonNode valueNode = node.get(fieldName);
    try {
      if (valueNode != null && !valueNode.isNull() && valueNode.canConvertToInt()) {
        return valueNode.asInt();
      }
      return defaultValue;
    } catch (Exception e) {
      log.warn("无法将字段 '{}' 的值 '{}' 解析为整数", fieldName, valueNode);
      return defaultValue;
    }
  }

  /** 从JSON节点安全地获取布尔值 */
  private Boolean getBooleanValue(JsonNode node, String fieldName, Boolean defaultValue) {
    JsonNode valueNode = node.get(fieldName);
    try {
      if (valueNode != null && !valueNode.isNull() && valueNode.isBoolean()) {
        return valueNode.asBoolean();
      }
      return defaultValue;
    } catch (Exception e) {
      log.warn("无法将字段 '{}' 的值 '{}' 解析为布尔值", fieldName, valueNode);
      return defaultValue;
    }
  }

  /** 确保基础字段（如id, created_at, updated_at）存在 */
  private List<DocumentAnalysisResult.DatabaseField> ensureBaseFields(
      List<DocumentAnalysisResult.DatabaseField> fields) {
    // 检查是否存在id字段
    boolean hasId = fields.stream().anyMatch(f -> "id".equalsIgnoreCase(f.getName()));
    if (!hasId) {
      fields.add(
          0,
          DocumentAnalysisResult.DatabaseField.builder()
              .name("id")
              .type("SERIAL")
              .isPrimaryKey(true)
              .nullable(false)
              .comment("主键")
              .importance(100)
              .build());
    }

    // 检查是否存在created_at字段
    boolean hasCreatedAt =
        fields.stream().anyMatch(f -> "created_at".equalsIgnoreCase(f.getName()));
    if (!hasCreatedAt) {
      fields.add(
          DocumentAnalysisResult.DatabaseField.builder()
              .name("created_at")
              .type("TIMESTAMP")
              .defaultValue("CURRENT_TIMESTAMP")
              .nullable(false)
              .comment("创建时间")
              .importance(20)
              .build());
    }

    // 检查是否存在updated_at字段
    boolean hasUpdatedAt =
        fields.stream().anyMatch(f -> "updated_at".equalsIgnoreCase(f.getName()));
    if (!hasUpdatedAt) {
      fields.add(
          DocumentAnalysisResult.DatabaseField.builder()
              .name("updated_at")
              .type("TIMESTAMP")
              .defaultValue("CURRENT_TIMESTAMP")
              .nullable(false)
              .comment("更新时间")
              .importance(20)
              .build());
    }
    return fields;
  }

  /**
   * 生成默认表名
   *
   * @param fileName 文件名
   * @return 默认表名
   */
  private String generateDefaultTableName(String fileName) {
    if (fileName == null || fileName.trim().isEmpty()) {
      return "assessment_scale_unknown";
    }

    String baseName = fileName.replaceAll("\\.[^.]*$", ""); // 去掉扩展名
    baseName = baseName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_"); // 替换特殊字符
    baseName = baseName.toLowerCase();

    return "assessment_" + baseName;
  }

  /**
   * 创建降级分析结果
   *
   * @param request 原始请求
   * @param analysisTime 分析耗时
   * @return 降级的文档分析结果
   */
  private DocumentAnalysisResult createFallbackAnalysis(
      DocumentAnalysisRequest request, String reason, Long analysisTime) {
    DocumentAnalysisResult fallbackResult = new DocumentAnalysisResult();
    fallbackResult.setSuccess(true); // Fallback is a successful operation
    fallbackResult.setMessage(reason);
    fallbackResult.setTableName(generateDefaultTableName(request.getFileName()));
    fallbackResult.setTableComment("（降级模式）从" + request.getFileName() + "生成的表");
    fallbackResult.setParsingTimeMs(analysisTime);
    fallbackResult.setAiRawResponse("Fallback due to: " + reason);

    // 尝试从Markdown中提取基本的文本行作为字段
    List<DocumentAnalysisResult.DatabaseField> fields = new ArrayList<>();
    if (request.getMarkdownContent() != null && !request.getMarkdownContent().isEmpty()) {
        String[] lines = request.getMarkdownContent().split("\\r?\\n");
        int lineCount = 0;
        for (String line : lines) {
            if (line.trim().isEmpty() || line.trim().startsWith("#")) {
                continue;
            }
            fields.add(
                DocumentAnalysisResult.DatabaseField.builder()
                    .name("field_" + lineCount)
                    .type("TEXT")
                    .comment(line.substring(0, Math.min(line.length(), 50))) // 使用行内容作为注释
                    .confidence(FALLBACK_CONFIDENCE)
                    .importance(DEFAULT_FIELD_IMPORTANCE)
                    .build());
            lineCount++;
        }
    }

    fallbackResult.setFields(ensureBaseFields(fields));
    if (fallbackResult.getFields() == null) {
      fallbackResult.setFields(new ArrayList<>());
    }
    return fallbackResult;
  }

  private DocumentAnalysisResult createInvalidRequestFallback(long startTime) {
      DocumentAnalysisResult fallbackResult = new DocumentAnalysisResult();
      fallbackResult.setSuccess(true); // Considered a handled case
      fallbackResult.setMessage("请求无效，无法进行分析");
      fallbackResult.setTableName("invalid_request");
      fallbackResult.setParsingTimeMs(System.currentTimeMillis() - startTime);
      fallbackResult.setAiRawResponse("Request was null or invalid.");
      fallbackResult.setFields(new ArrayList<>());
      return fallbackResult;
  }

  /**
   * 与AI进行通用聊天
   *
   * @param message 用户消息
   * @param context 上下文（可选）
   * @return AI响应
   */
  public String chatWithAI(String message, String context) {
    if (!isAIServiceAvailable()) {
      return "AI服务当前不可用";
    }

    try {
      // 获取当前LM Studio服务信息
      String currentModelId =
          lmStudioService.getCurrentModel() != null
              ? lmStudioService.getCurrentModel().getId()
              : "unknown";
      String url = lmStudioService.getCurrentServerUrl() + "/v1/chat/completions";

      log.info("准备与AI聊天: url={}, model={}", url, currentModelId);

      // 构建消息列表
      List<Map<String, Object>> messages = new ArrayList<>();

      // 添加系统消息
      Map<String, Object> systemMessage = new HashMap<>();
      systemMessage.put("role", "system");
      systemMessage.put("content", "你是一个有用的AI助手。" + (context != null ? "上下文: " + context : ""));
      messages.add(systemMessage);

      // 添加用户消息
      Map<String, Object> userMessage = new HashMap<>();
      userMessage.put("role", "user");
      userMessage.put("content", message);
      messages.add(userMessage);

      // 构建请求体
      Map<String, Object> requestBody = new HashMap<>();
      requestBody.put("model", currentModelId);
      requestBody.put("messages", messages);

      // 设置请求头
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.set("Authorization", "Bearer lm-studio");

      // 创建HttpEntity
      HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

      // 发送请求
      ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

      if (response.getStatusCode() == HttpStatus.OK) {
        // 解析响应获取生成的文本
        try {
          JsonNode responseJson = objectMapper.readTree(response.getBody());
          return responseJson.get("choices").get(0).get("message").get("content").asText();
        } catch (JsonProcessingException e) {
          throw new RuntimeException("解析响应JSON失败: " + e.getMessage());
        }
      }

      throw new RuntimeException("未能从LM Studio获取有效响应，状态码: " + response.getStatusCode());
    } catch (RuntimeException e) {
      log.error("调用LM Studio API失败: {}", e.getMessage());
      return "与AI聊天失败: " + e.getMessage();
    }
  }

  /**
   * 使用流式输出分析文档
   *
   * @param request 分析请求
   * @param emitter SSE发射器
   */
  @Async
  public void analyzeDocumentWithStream(DocumentAnalysisRequest request, SseEmitter emitter) {
    try {
      // 构建AI分析提示词
      String prompt = buildAnalysisPrompt(request);

      // 调用LM Studio API（流式）
      String finalResponse = callLMStudioAPIWithStream(prompt);

      // 分析最终结果并发送
      DocumentAnalysisResult result = parseAIResponse(finalResponse);
      emitter.send(SseEmitter.event().name("analysis_result").data(result));

      emitter.complete();
    } catch (IOException | RuntimeException e) {
      log.error("流式AI分析失败", e);
      try {
        emitter.send(SseEmitter.event().name("error").data("流式分析失败: " + e.getMessage()));
        emitter.completeWithError(e);
      } catch (java.io.IOException ex) {
        log.error("无法发送SSE错误事件", ex);
      }
    }
  }

  /**
   * 调用LM Studio API并处理流式响应
   *
   * @param prompt 提示词
   * @return 完整的AI响应
   */
  private String callLMStudioAPIWithStream(String prompt)
      throws java.io.IOException {
    // [之前的实现，这里简化]
    // 实际实现需要使用支持流式读取的HTTP客户端，例如WebClient或OkHttp
    // 并逐行读取响应，通过emitter.send()发送给客户端
    // 这里返回一个模拟的完整响应
    log.debug("流式API调用，但当前实现为降级模式。");
    return callLMStudioAPI(prompt); // 降级为非流式调用
  }

  /** 提取SQL代码块 */
  private String extractSQL(String response) {
    Pattern pattern = Pattern.compile("```sql\\s*([\\s\\S]*?)\\s*```");
    Matcher matcher = pattern.matcher(response);
    StringBuilder sqlBuilder = new StringBuilder();
    while (matcher.find()) {
      sqlBuilder.append(matcher.group(1).trim()).append("\n\n");
    }
    return sqlBuilder.toString().trim();
  }

  /** 提取指定标签/标题下的内容 */
  private String extractSection(String response, String sectionName) {
    Pattern pattern;
    if ("think".equalsIgnoreCase(sectionName)) {
      pattern = Pattern.compile("<think>([\\s\\S]*?)</think>");
    } else {
      pattern =
          Pattern.compile(
              "###\\s*" + Pattern.quote(sectionName) + "\\s*\\n([\\s\\S]*?)(?=\\n###|$)");
    }

    Matcher matcher = pattern.matcher(response);
    if (matcher.find()) {
      return matcher.group(1).trim();
    }
    return "";
  }

}
