package com.assessment.service.scoring;

import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认评分策略，基于评分规则 JSON 计算总分并确定结果等级。
 *
 * <p>目前支持以下能力：
 *
 * <ul>
 *   <li>questionScoring：指定各章节/题目的选项得分
 *   <li>sectionWeights：按章节应用权重
 *   <li>scoringLevels：根据得分区间确定等级
 * </ul>
 */
@Component
@Slf4j
public class DefaultScoringStrategy implements ScoringStrategy {

  @Override
  public BigDecimal calculateTotalScore(JsonNode formData, JsonNode scoringRules) {
    if (formData == null || scoringRules == null) {
      return BigDecimal.ZERO;
    }

    AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);

    // 题目得分
    if (scoringRules.has("questionScoring")) {
      JsonNode questionScoring = scoringRules.get("questionScoring");
      questionScoring
          .fieldNames()
          .forEachRemaining(
              sectionKey -> {
                JsonNode sectionScoring = questionScoring.get(sectionKey);
                JsonNode sectionData = formData.get(sectionKey);
                if (sectionData != null) {
                  BigDecimal sectionScore = calculateSectionScore(sectionScoring, sectionData);
                  total.updateAndGet(cur -> cur.add(sectionScore));
                }
              });
    }

    // 加权得分
    if (scoringRules.has("sectionWeights")) {
      total.set(applyWeightedScoring(scoringRules, formData));
    }

    return total.get();
  }

  @Override
  public String determineResultLevel(BigDecimal totalScore, JsonNode scoringRules) {
    if (!scoringRules.has("scoringLevels")) {
      return "未知";
    }
    JsonNode levels = scoringRules.get("scoringLevels");
    for (JsonNode level : levels) {
      JsonNode minNode = level.get("minScore");
      JsonNode maxNode = level.get("maxScore");
      JsonNode nameNode = level.get("name");
      if (minNode != null && maxNode != null && nameNode != null) {
        int min = minNode.asInt();
        int max = maxNode.asInt();
        int val = totalScore.intValue();
        if (val >= min && val <= max) {
          return nameNode.asText();
        }
      }
    }
    return "未知";
  }

  /** 计算章节得分 */
  private BigDecimal calculateSectionScore(JsonNode sectionScoring, JsonNode sectionData) {
    BigDecimal sectionTotal = BigDecimal.ZERO;
    for (var it = sectionScoring.fieldNames(); it.hasNext(); ) {
      String questionKey = it.next();
      JsonNode questionRule = sectionScoring.get(questionKey);
      JsonNode questionValue = sectionData.get(questionKey);

      if (questionValue != null && questionRule.has("optionScores")) {
        JsonNode optionScores = questionRule.get("optionScores");
        if (questionValue.isArray()) {
          // 多选题
          for (JsonNode v : questionValue) {
            sectionTotal = sectionTotal.add(getOptionScore(optionScores, v.asText()));
          }
        } else {
          // 单选/其他
          sectionTotal = sectionTotal.add(getOptionScore(optionScores, questionValue.asText()));
        }
      }
    }
    return sectionTotal;
  }

  private BigDecimal getOptionScore(JsonNode optionScores, String key) {
    if (optionScores.has(key)) {
      JsonNode scoreNode = optionScores.get(key);
      if (scoreNode != null) {
        return BigDecimal.valueOf(scoreNode.asDouble());
      }
    }
    return BigDecimal.ZERO;
  }

  /** 应用章节权重计算得分 */
  private BigDecimal applyWeightedScoring(JsonNode scoringRules, JsonNode formData) {
    try {
      JsonNode sectionWeights = scoringRules.get("sectionWeights");
      JsonNode questionScoring = scoringRules.get("questionScoring");
      if (sectionWeights == null || questionScoring == null) {
        log.warn("权重或评分规则缺失，跳过加权计算");
        return BigDecimal.ZERO;
      }
      BigDecimal total = BigDecimal.ZERO;
      for (var it = sectionWeights.fieldNames(); it.hasNext(); ) {
        String sectionKey = it.next();
        JsonNode weightNode = sectionWeights.get(sectionKey);
        JsonNode sectionScoring = questionScoring.get(sectionKey);
        JsonNode sectionData = formData.get(sectionKey);
        if (weightNode != null && sectionScoring != null && sectionData != null) {
          BigDecimal weight = BigDecimal.valueOf(weightNode.asDouble());
          BigDecimal sectionScore = calculateSectionScore(sectionScoring, sectionData);
          total = total.add(sectionScore.multiply(weight));
        }
      }
      return total;
    } catch (Exception e) {
      log.error("加权评分失败", e);
      return BigDecimal.ZERO;
    }
  }
}
