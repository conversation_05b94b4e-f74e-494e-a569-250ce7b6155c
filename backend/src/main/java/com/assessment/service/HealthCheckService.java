package com.assessment.service;

import org.springframework.stereotype.Service;

/**
 * 健康检查服务 提供应用程序健康状态检查功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-13
 */
@Service
public class HealthCheckService {

  /**
   * 检查应用程序是否健康
   *
   * @return true表示健康，false表示不健康
   */
  public boolean isHealthy() {
    return true;
  }

  /**
   * 获取应用程序状态信息
   *
   * @return 状态信息字符串
   */
  public String getStatus() {
    return "应用程序运行正常";
  }

  /**
   * 获取应用程序版本
   *
   * @return 版本号
   */
  public String getVersion() {
    return "1.0.0-SNAPSHOT";
  }
}
