package com.assessment.service;

import com.assessment.dto.CreateAssessmentRequest;
import com.assessment.entity.multitenant.AssessmentSubject;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.AssessmentSubjectRepository;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.service.scoring.ScoringStrategy;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 评估服务 - 多租户架构 */
@Service
@Transactional
@Slf4j
public class AssessmentService {

  private static final Random RANDOM = new Random();

  // 多租户 Repositories
  @Autowired private TenantAssessmentRecordRepository tenantAssessmentRecordRepository;
  @Autowired private AssessmentSubjectRepository assessmentSubjectRepository;
  @Autowired private GlobalScaleRegistryRepository globalScaleRegistryRepository;
  @Autowired private PlatformUserRepository platformUserRepository;
  @Autowired private TenantUserMembershipRepository tenantUserMembershipRepository;
  @Autowired private ScoringStrategy scoringStrategy;

  /** 创建多租户评估记录 */
  public TenantAssessmentRecord createTenantAssessment(
      String tenantId, CreateAssessmentRequest request) {
    log.info(
        "创建租户评估记录: 租户ID={}, 评估对象ID={}, 量表ID={}, 评估员ID={}",
        tenantId,
        request.getElderlyId(),
        request.getScaleId(),
        request.getAssessorId());

    // 验证数据
    validateTenantAssessmentRequest(tenantId, request);

    // 生成评估编号
    String recordNumber = generateRecordNumber();

    // 创建评估记录
    TenantAssessmentRecord record =
        TenantAssessmentRecord.builder()
            .tenantId(tenantId)
            .recordNumber(recordNumber)
            .subjectId(request.getElderlyId())
            .scaleId(request.getScaleId())
            .scaleType(
                TenantAssessmentRecord.ScaleType.valueOf(determineScaleType(request.getScaleId())))
            .assessorId(request.getAssessorId())
            .assessmentDate(LocalDateTime.now())
            .assessmentType(TenantAssessmentRecord.AssessmentType.REGULAR)
            .formData(request.getFormData())
            .status(TenantAssessmentRecord.RecordStatus.DRAFT)
            .build();

    // 计算评分
    calculateTenantScore(record);

    // 保存记录
    TenantAssessmentRecord savedRecord = tenantAssessmentRecordRepository.save(record);

    // 更新量表使用统计
    globalScaleRegistryRepository.incrementUsageCount(request.getScaleId());

    log.info("租户评估记录创建成功: ID={}, 编号={}", savedRecord.getId(), recordNumber);
    return savedRecord;
  }

  /** 确定量表类型 */
  private String determineScaleType(String scaleId) {
    // 检查是否为全局量表
    if (globalScaleRegistryRepository.existsById(scaleId)) {
      return "GLOBAL";
    }
    return "CUSTOM";
  }

  /** 计算多租户评估分数 */
  private void calculateTenantScore(TenantAssessmentRecord record) {
    try {
      GlobalScaleRegistry scale =
          globalScaleRegistryRepository
              .findById(record.getScaleId())
              .orElseThrow(() -> new RuntimeException("量表不存在"));

      JsonNode scoringRules = scale.getScoringRules();
      JsonNode formData = record.getFormData();

      if (scoringRules == null || formData == null) {
        record.setTotalScore(BigDecimal.ZERO);
        record.setResultLevel("未知");
        return;
      }

      BigDecimal total = scoringStrategy.calculateTotalScore(formData, scoringRules);
      String level = scoringStrategy.determineResultLevel(total, scoringRules);

      record.setTotalScore(total);
      record.setResultLevel(level);

    } catch (Exception e) {
      log.error("计算评分失败", e);
      record.setTotalScore(BigDecimal.ZERO);
      record.setResultLevel("未知");
    }
  }

  /** 提交多租户评估记录 */
  public void submitTenantAssessment(String tenantId, String recordId) {
    TenantAssessmentRecord record =
        tenantAssessmentRecordRepository
            .findByTenantIdAndRecordNumber(tenantId, recordId)
            .orElseThrow(() -> new RuntimeException("评估记录不存在"));

    record.setStatus(TenantAssessmentRecord.RecordStatus.SUBMITTED);
    tenantAssessmentRecordRepository.save(record);

    log.info("租户评估记录已提交: 租户ID={}, 记录ID={}", tenantId, recordId);
  }

  /** 审核多租户评估记录 */
  public void reviewTenantAssessment(
      String tenantId, String recordId, boolean approved, String reviewNotes, String reviewerId) {
    TenantAssessmentRecord record =
        tenantAssessmentRecordRepository
            .findByTenantIdAndRecordNumber(tenantId, recordId)
            .orElseThrow(() -> new RuntimeException("评估记录不存在"));

    if (approved) {
      record.approve(reviewNotes, reviewerId);
    } else {
      record.reject(reviewNotes, reviewerId);
    }

    tenantAssessmentRecordRepository.save(record);

    log.info("租户评估记录审核完成: 租户ID={}, 记录ID={}, 结果={}", tenantId, recordId, approved ? "通过" : "拒绝");
  }

  /** 获取租户评估记录列表 */
  public Page<TenantAssessmentRecord> getTenantAssessmentRecords(
      String tenantId,
      String subjectId,
      String scaleId,
      String assessorId,
      String status,
      Pageable pageable) {

    // 根据不同的参数组合使用不同的查询方法
    if (status != null) {
      return tenantAssessmentRecordRepository.findByTenantIdAndStatus(tenantId, status, pageable);
    } else if (assessorId != null) {
      return tenantAssessmentRecordRepository.findByTenantIdAndAssessorId(
          tenantId, assessorId, pageable);
    } else if (scaleId != null) {
      return tenantAssessmentRecordRepository.findByTenantIdAndScaleId(tenantId, scaleId, pageable);
    } else {
      return tenantAssessmentRecordRepository.findByTenantId(tenantId, pageable);
    }
  }

  /** 获取租户评估记录详情 */
  public TenantAssessmentRecord getTenantAssessmentRecord(String tenantId, String recordId) {
    return tenantAssessmentRecordRepository
        .findByTenantIdAndRecordNumber(tenantId, recordId)
        .orElseThrow(() -> new RuntimeException("评估记录不存在"));
  }

  /** 删除租户评估记录 */
  public void deleteTenantAssessment(String tenantId, String recordId) {
    TenantAssessmentRecord record =
        tenantAssessmentRecordRepository
            .findByTenantIdAndRecordNumber(tenantId, recordId)
            .orElseThrow(() -> new RuntimeException("评估记录不存在"));

    tenantAssessmentRecordRepository.delete(record);

    log.info("租户评估记录已删除: 租户ID={}, 记录ID={}", tenantId, recordId);
  }

  /** 获取评估对象的评估历史 */
  public List<TenantAssessmentRecord> getSubjectAssessmentHistory(
      String tenantId, String subjectId) {
    return tenantAssessmentRecordRepository.findByTenantIdAndSubjectIdOrderByAssessmentDateDesc(
        tenantId, subjectId);
  }

  /** 验证多租户评估请求 */
  private void validateTenantAssessmentRequest(String tenantId, CreateAssessmentRequest request) {
    // 验证租户ID不能为空
    if (tenantId == null || tenantId.trim().isEmpty()) {
      throw new RuntimeException("租户ID不能为空");
    }

    // 验证评估对象是否存在且属于指定租户
    AssessmentSubject subject = 
        assessmentSubjectRepository.findById(request.getElderlyId())
            .orElseThrow(() -> new RuntimeException("被评估对象不存在"));
    
    // 租户隔离验证 - 确保评估对象属于指定租户
    if (!tenantId.equals(subject.getTenantId())) {
      throw new RuntimeException("评估对象不属于当前租户，访问被拒绝");
    }

    // 验证量表是否存在且可用
    GlobalScaleRegistry scale =
        globalScaleRegistryRepository
            .findById(request.getScaleId())
            .orElseThrow(() -> new RuntimeException("评估量表不存在"));

    if (!GlobalScaleRegistry.ScaleStatus.ACTIVE.equals(scale.getStatus())) {
      throw new RuntimeException("评估量表已停用");
    }

    // 验证评估员是否存在
    if (!platformUserRepository.existsById(UUID.fromString(request.getAssessorId()))) {
      throw new RuntimeException("评估员不存在");
    }

    // 租户成员关系验证 - 确保评估员属于指定租户
    if (!tenantUserMembershipRepository.existsByTenantIdAndUserId(tenantId, request.getAssessorId())) {
      throw new RuntimeException("评估员不属于当前租户，无权限执行评估");
    }

    // 验证表单数据
    if (request.getFormData() == null) {
      throw new RuntimeException("评估数据不能为空");
    }

    log.debug(
        "租户评估请求验证通过: 租户={}, 评估对象={}, 量表={}, 评估员={}",
        tenantId,
        request.getElderlyId(),
        request.getScaleId(),
        request.getAssessorId());
  }

  private static final int RECORD_NUMBER_BOUND = 10000;

  /** 生成评估编号 */
  private String generateRecordNumber() {
    String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    String randomSuffix = String.format("%04d", RANDOM.nextInt(RECORD_NUMBER_BOUND));
    return "ASS" + datePrefix + randomSuffix;
  }
}
