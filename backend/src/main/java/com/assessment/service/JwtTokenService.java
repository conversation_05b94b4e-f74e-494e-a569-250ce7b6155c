package com.assessment.service;

import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantUserMembership;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.SecretKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/** JWT Token服务 处理多租户JWT Token的生成和验证 */
@Slf4j
@Service
public class JwtTokenService {

  @Value("${jwt.secret:D0fJov+robFjgi6kSig+WmQkxqaML8T3jES2YdhJJyM=}")
  private String jwtSecret;

  @Value("${jwt.access-token-expiration:3600}") // 1小时
  private int accessTokenExpiration;

  @Value("${jwt.refresh-token-expiration:2592000}") // 30天
  private int refreshTokenExpiration;

  /** 生成访问Token（多租户） */
  public String generateAccessToken(
      PlatformUser user, Tenant tenant, TenantUserMembership membership) {
    Map<String, Object> claims = new HashMap<>();

    // 用户信息
    claims.put("userId", user.getId());
    claims.put("username", user.getUsername());
    claims.put("email", user.getEmail());
    claims.put("platformRole", user.getPlatformRole().name());

    // 租户信息
    claims.put("tenantId", tenant.getId());
    claims.put("tenantCode", tenant.getCode());
    claims.put("tenantName", tenant.getName());

    // 租户角色信息
    claims.put("tenantRole", membership.getTenantRole().name());
    claims.put("displayName", membership.getDisplayName());
    claims.put("permissions", membership.getTenantRole().getDefaultPermissions());

    // Token类型
    claims.put("tokenType", "access");
    claims.put("isSuperAdmin", false);

    return createToken(claims, user.getUsername(), accessTokenExpiration);
  }

  /** 生成超级管理员Token */
  public String generateSuperAdminToken(PlatformUser superAdmin) {
    Map<String, Object> claims = new HashMap<>();

    // 超级管理员信息
    claims.put("userId", superAdmin.getId());
    claims.put("username", superAdmin.getUsername());
    claims.put("email", superAdmin.getEmail());
    claims.put("platformRole", superAdmin.getPlatformRole().name());

    // 超级管理员标识
    claims.put("isSuperAdmin", true);
    claims.put("tenantId", "PLATFORM");
    claims.put("tenantCode", "PLATFORM");
    claims.put("tenantRole", "SUPER_ADMIN");
    claims.put("permissions", new String[] {"ALL"});

    // Token类型
    claims.put("tokenType", "access");

    return createToken(claims, superAdmin.getUsername(), accessTokenExpiration);
  }

  /** 生成刷新Token */
  public String generateRefreshToken(String userId) {
    Map<String, Object> claims = new HashMap<>();
    claims.put("userId", userId);
    claims.put("tokenType", "refresh");

    return createToken(claims, userId, refreshTokenExpiration);
  }

  /** 创建Token */
  private String createToken(Map<String, Object> claims, String subject, int expiration) {
    Date now = new Date();
    Date expirationDate = new Date(now.getTime() + expiration * 1000L);

    return Jwts.builder()
        .claims(claims)
        .subject(subject)
        .issuedAt(now)
        .expiration(expirationDate)
        .signWith(getSigningKey())
        .compact();
  }

  /** 验证Token */
  public boolean validateToken(String token) {
    try {
      Jwts.parser().verifyWith(getSigningKey()).build().parseSignedClaims(token);
      return true;
    } catch (Exception e) {
      log.error("Token验证失败: {}", e.getMessage());
      return false;
    }
  }

  /** 从Token中提取Claims */
  public Claims extractClaims(String token) {
    return Jwts.parser().verifyWith(getSigningKey()).build().parseSignedClaims(token).getPayload();
  }

  /** 提取用户名 */
  public String extractUsername(String token) {
    return extractClaims(token).getSubject();
  }

  /** 提取用户ID */
  public String extractUserId(String token) {
    return (String) extractClaims(token).get("userId");
  }

  /** 提取租户ID */
  public String extractTenantId(String token) {
    return (String) extractClaims(token).get("tenantId");
  }

  /** 提取租户代码 */
  public String extractTenantCode(String token) {
    return (String) extractClaims(token).get("tenantCode");
  }

  /** 提取租户角色 */
  public String extractTenantRole(String token) {
    return (String) extractClaims(token).get("tenantRole");
  }

  /** 检查是否为超级管理员Token */
  public boolean isSuperAdmin(String token) {
    Object isSuperAdmin = extractClaims(token).get("isSuperAdmin");
    return Boolean.TRUE.equals(isSuperAdmin);
  }

  /** 检查Token是否过期 */
  public boolean isTokenExpired(String token) {
    Date expiration = extractClaims(token).getExpiration();
    return expiration.before(new Date());
  }

  /** 获取Token类型 */
  public String getTokenType(String token) {
    return (String) extractClaims(token).get("tokenType");
  }

  /** 获取签名密钥 */
  private SecretKey getSigningKey() {
    // 如果密钥看起来像Base64编码，则解码它
    if (jwtSecret.contains("=") || jwtSecret.contains("+") || jwtSecret.contains("/")) {
      try {
        byte[] keyBytes = java.util.Base64.getDecoder().decode(jwtSecret);
        return Keys.hmacShaKeyFor(keyBytes);
      } catch (Exception e) {
        log.warn("Base64解码失败，使用原始字符串: {}", e.getMessage());
      }
    }

    // 如果不是Base64或解码失败，直接使用字符串字节
    byte[] keyBytes = jwtSecret.getBytes(java.nio.charset.StandardCharsets.UTF_8);

    // 确保密钥长度至少为32字节(256位)
    if (keyBytes.length < 32) {
      // 如果太短，重复密钥内容直到满足最小长度
      byte[] extendedKey = new byte[32];
      for (int i = 0; i < 32; i++) {
        extendedKey[i] = keyBytes[i % keyBytes.length];
      }
      return Keys.hmacShaKeyFor(extendedKey);
    }

    return Keys.hmacShaKeyFor(keyBytes);
  }

  /** 刷新访问Token */
  public String refreshAccessToken(String refreshToken) {
    if (!validateToken(refreshToken)) {
      throw new IllegalArgumentException("无效的刷新Token");
    }

    Claims claims = extractClaims(refreshToken);
    String tokenType = (String) claims.get("tokenType");

    if (!"refresh".equals(tokenType)) {
      throw new IllegalArgumentException("不是刷新Token");
    }

    // 这里需要重新查询用户信息生成新的访问Token
    // 具体实现需要注入Repository，为了简化先返回原Token
    // 实际项目中应该重新查询用户和租户信息

    return refreshToken; // 临时实现
  }
}
