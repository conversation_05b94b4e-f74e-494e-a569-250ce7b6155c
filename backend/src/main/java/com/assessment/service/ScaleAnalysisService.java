package com.assessment.service;

import com.assessment.exception.MarkdownAnalysisException;
import com.assessment.pdf.AssessmentMetadata;
import com.assessment.pdf.AssessmentQuestion;
import com.assessment.pdf.AssessmentSection;
import com.assessment.pdf.AssessmentStructure;
import com.assessment.pdf.ScoringRules;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** 量表分析服务 负责分析Markdown格式的量表内容，提取结构化信息 */
@Service
@Slf4j
public class ScaleAnalysisService {

  private final ObjectMapper objectMapper = new ObjectMapper();

  /** 分析Markdown内容并提取评估量表结构 */
  public AssessmentStructure analyzeMarkdownToStructure(String markdown, String fileName) {
    log.info("开始分析Markdown文档: {}, 长度: {} 字符", fileName, markdown.length());

    try {
      // 1. 提取元数据
      AssessmentMetadata metadata = extractMetadata(markdown, fileName);

      // 2. 提取章节和问题
      List<AssessmentSection> sections = extractSections(markdown);

      // 3. 生成评分规则
      ScoringRules scoringRules = generateScoringRules(sections);

      log.info(
          "Markdown分析完成: {} 个章节, {} 个问题",
          sections.size(),
          sections.stream().mapToInt(s -> s.getQuestions().size()).sum());

      return AssessmentStructure.builder()
          .metadata(metadata)
          .sections(sections)
          .scoringRules(scoringRules)
          .build();

    } catch (Exception e) {
      log.error("Markdown分析失败: {}", e.getMessage(), e);
      throw new MarkdownAnalysisException("Markdown分析失败: " + e.getMessage(), e);
    }
  }

  /** 提取元数据信息 */
  private AssessmentMetadata extractMetadata(String markdown, String fileName) {
    String title = extractTitle(markdown, fileName);
    String description = extractDescription(markdown);
    String version = extractVersion(markdown);
    String type = determineScaleType(markdown);

    return AssessmentMetadata.builder()
        .title(title)
        .description(description)
        .version(version)
        .type(type)
        .author("系统自动识别")
        .build();
  }

  /** 提取标题 */
  private String extractTitle(String markdown, String fileName) {
    // 查找第一个一级标题
    Pattern pattern = Pattern.compile("^#\\s+(.+)$", Pattern.MULTILINE);
    Matcher matcher = pattern.matcher(markdown);

    if (matcher.find()) {
      return matcher.group(1).trim();
    }

    // 查找包含"评估"、"量表"、"规范"等关键词的标题
    String[] keywords = {"评估", "量表", "规范", "标准", "能力"};
    String[] lines = markdown.split("\n");

    for (String line : lines) {
      line = line.trim();
      if (line.startsWith("#")) {
        String titleText = line.replaceAll("^#+\\s*", "");
        for (String keyword : keywords) {
          if (titleText.contains(keyword)) {
            return titleText;
          }
        }
      }
    }

    // 从文件名提取
    if (fileName != null) {
      String nameWithoutExt = fileName.replaceAll("\\.[^.]+$", "");
      return nameWithoutExt.replaceAll("[_-]", " ");
    }

    return "未知评估量表";
  }

  /** 提取描述信息 */
  private String extractDescription(String markdown) {
    // 查找描述性段落
    String[] lines = markdown.split("\n");
    StringBuilder description = new StringBuilder();
    boolean inDescription = false;

    for (String line : lines) {
      line = line.trim();

      if (line.contains("描述") || line.contains("说明") || line.contains("简介")) {
        inDescription = true;
        continue;
      }

      if (inDescription && !line.isEmpty() && !line.startsWith("#")) {
        description.append(line).append(" ");
        if (description.length() > 200) break; // 限制长度
      }

      if (inDescription && line.startsWith("#")) {
        break; // 遇到新标题停止
      }
    }

    String result = description.toString().trim();
    return result.isEmpty() ? "评估量表，用于老年人能力评估" : result;
  }

  /** 提取版本信息 */
  private String extractVersion(String markdown) {
    Pattern pattern =
        Pattern.compile(
            "版本[：:]?\\s*([\\d.]+)|V([\\d.]+)|version\\s*([\\d.]+)", Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(markdown);

    if (matcher.find()) {
      for (int i = 1; i <= matcher.groupCount(); i++) {
        if (matcher.group(i) != null) {
          return matcher.group(i);
        }
      }
    }

    return "1.0";
  }

  /** 确定量表类型 */
  private String determineScaleType(String markdown) {
    String lowerMarkdown = markdown.toLowerCase(Locale.ROOT);

    if (lowerMarkdown.contains("老年人能力") || lowerMarkdown.contains("elderly ability")) {
      return "ELDERLY_ABILITY";
    } else if (lowerMarkdown.contains("情绪") || lowerMarkdown.contains("emotion")) {
      return "EMOTIONAL";
    } else if (lowerMarkdown.contains("interrai")) {
      return "INTERRAI";
    } else if (lowerMarkdown.contains("长护险") || lowerMarkdown.contains("护理保险")) {
      return "LONG_TERM_CARE";
    } else {
      return "CUSTOM";
    }
  }

  /** 提取章节和问题 */
  private List<AssessmentSection> extractSections(String markdown) {
    List<AssessmentSection> sections = new ArrayList<>();
    String[] lines = markdown.split("\\R");

    AssessmentSection currentSection = null;
    List<AssessmentQuestion> currentQuestions = new ArrayList<>();
    AssessmentQuestion currentQuestion = null;

    for (String line1 : lines) {
      String line = line1.trim();
      if (line.isEmpty()) continue;

      Pattern optionPattern = Pattern.compile("^\\s*-\\s+(.+)$");
      Matcher optionMatcher = optionPattern.matcher(line);

      if (line.matches("^#{2,3}\\s+.+")) { // Chapter
        if (currentQuestion != null) {
          currentQuestions.add(currentQuestion);
          currentQuestion = null;
        }
        if (currentSection != null) {
          currentSection.setQuestions(new ArrayList<>(currentQuestions));
          sections.add(currentSection);
        }
        String sectionTitle = line.replaceAll("^#+\\s*", "");
        currentSection =
            AssessmentSection.builder()
                .id("section_" + (sections.size() + 1))
                .title(sectionTitle)
                .order(sections.size() + 1)
                .build();
        currentQuestions.clear();
      } else if (isQuestionLine(line)) { // Question
        if (currentQuestion != null) {
          currentQuestions.add(currentQuestion);
        }
        currentQuestion = parseQuestion(line, currentQuestions.size() + 1);
      } else if (optionMatcher.matches() && currentQuestion != null) { // Option
        String optionText = optionMatcher.group(1).trim();
        currentQuestion.addOption(optionText);
      }
    }

    if (currentQuestion != null) {
      currentQuestions.add(currentQuestion);
    }
    if (currentSection != null) {
      currentSection.setQuestions(new ArrayList<>(currentQuestions));
      sections.add(currentSection);
    }

    log.info("Markdown分析完成: {} 个章节, {} 个问题", sections.size(), currentQuestions.size());
    return sections;
  }

  /** 判断是否为问题行 */
  private boolean isQuestionLine(String line) {
    // 标题行不是问题行
    if (line.startsWith("#")) {
      return false;
    }

    // 表格行
    if (line.startsWith("|") && line.endsWith("|")) {
      return !line.contains("---") && line.split("\\|").length > 2;
    }

    // 编号列表
    if (line.matches("^[A-Z]?\\.?\\d+\\.?\\s+.+") || line.matches("^\\d+[）)．.]\\s+.+")) {
      return true;
    }

    // 问题关键词
    return line.contains("评估") || line.contains("能力") || line.contains("分数") || line.contains("等级");
  }

  /** 解析问题 */
  private AssessmentQuestion parseQuestion(String line, int order) {
    Pattern pattern = Pattern.compile("^\\d+\\.\\s*(.+?)(?:（(\\d+)分）)?$");
    Matcher matcher = pattern.matcher(line);

    if (matcher.find()) {
      String title = matcher.group(1).trim();

      return AssessmentQuestion.builder()
          .id("q_" + order)
          .order(order)
          .title(title)
          .text(title) // Use title as text
          .type("SINGLE_CHOICE")
          .required(true)
          .build();
    }
    return null;
  }

  /** 生成评分规则 */
  private ScoringRules generateScoringRules(List<AssessmentSection> sections) {
    int totalQuestions = sections.stream().mapToInt(s -> s.getQuestions().size()).sum();

    return ScoringRules.builder()
        .maxPossibleScore(totalQuestions * 4) // 假设每题最高4分
        .minPossibleScore(0)
        .passScore(totalQuestions * 2) // 及格分为一半
        .scoringMethod("累计计分")
        .build();
  }

  /** 生成JSON Schema */
  public JsonNode generateJsonSchema(AssessmentStructure structure) {
    ObjectNode schema = objectMapper.createObjectNode();
    schema.put("$schema", "http://json-schema.org/draft-07/schema#");
    schema.put("type", "object");
    schema.put("title", structure.getMetadata().getTitle());
    schema.put("description", structure.getMetadata().getDescription());

    ObjectNode properties = objectMapper.createObjectNode();
    ArrayNode required = objectMapper.createArrayNode();

    for (AssessmentSection section : structure.getSections()) {
      ObjectNode sectionSchema = objectMapper.createObjectNode();
      sectionSchema.put("type", "object");
      sectionSchema.put("title", section.getTitle());

      ObjectNode sectionProperties = objectMapper.createObjectNode();
      ArrayNode sectionRequired = objectMapper.createArrayNode();

      for (AssessmentQuestion question : section.getQuestions()) {
        ObjectNode questionSchema = objectMapper.createObjectNode();

        if (question.getOptions() != null && !question.getOptions().isEmpty()) {
          questionSchema.put("type", "string");
          ArrayNode enumValues = objectMapper.createArrayNode();
          question.getOptions().forEach(enumValues::add);
          questionSchema.set("enum", enumValues);
        } else {
          questionSchema.put("type", "string");
        }

        questionSchema.put("title", question.getText());
        sectionProperties.set(question.getId(), questionSchema);

        if (question.isRequired()) {
          sectionRequired.add(question.getId());
        }
      }

      sectionSchema.set("properties", sectionProperties);
      sectionSchema.set("required", sectionRequired);
      properties.set(section.getId(), sectionSchema);
      required.add(section.getId());
    }

    schema.set("properties", properties);
    schema.set("required", required);

    return schema;
  }

  /** 生成评分规则JSON */
  public JsonNode generateScoringRulesJson(AssessmentStructure structure) {
    ObjectNode rules = objectMapper.createObjectNode();

    ScoringRules scoringRules = structure.getScoringRules();
    rules.put("maxPossibleScore", scoringRules.getMaxPossibleScore());
    rules.put("minPossibleScore", scoringRules.getMinPossibleScore());
    rules.put("passScore", scoringRules.getPassScore());
    rules.put("scoringMethod", scoringRules.getScoringMethod());

    ArrayNode sectionRules = objectMapper.createArrayNode();

    for (AssessmentSection section : structure.getSections()) {
      ObjectNode sectionRule = objectMapper.createObjectNode();
      sectionRule.put("sectionId", section.getId());
      sectionRule.put("sectionTitle", section.getTitle());
      sectionRule.put("maxScore", section.getQuestions().size() * 4);
      sectionRule.put("weight", 1.0);

      ArrayNode questionRules = objectMapper.createArrayNode();
      for (AssessmentQuestion question : section.getQuestions()) {
        ObjectNode questionRule = objectMapper.createObjectNode();
        questionRule.put("questionId", question.getId());
        questionRule.put("maxScore", 4);
        questionRule.set("scoreMapping", generateScoreMapping(question));
        questionRules.add(questionRule);
      }

      sectionRule.set("questions", questionRules);
      sectionRules.add(sectionRule);
    }

    rules.set("sections", sectionRules);

    return rules;
  }

  /** 生成分数映射 */
  private ObjectNode generateScoreMapping(AssessmentQuestion question) {
    ObjectNode mapping = objectMapper.createObjectNode();

    if (question.getOptions() != null && !question.getOptions().isEmpty()) {
      // 为选项分配分数
      List<String> options = question.getOptions();
      for (int i = 0; i < options.size(); i++) {
        mapping.put(options.get(i), options.size() - i - 1); // 降序分配
      }
    } else {
      // 文本题的默认评分规则
      mapping.put("优秀", 4);
      mapping.put("良好", 3);
      mapping.put("一般", 2);
      mapping.put("较差", 1);
      mapping.put("无法完成", 0);
    }

    return mapping;
  }
}
