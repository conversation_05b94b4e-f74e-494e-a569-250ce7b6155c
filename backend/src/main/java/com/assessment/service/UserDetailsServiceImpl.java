package com.assessment.service;

import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.repository.multitenant.PlatformUserRepository;
import java.util.ArrayList;
import java.util.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {

  @Autowired private PlatformUserRepository platformUserRepository;

  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    PlatformUser user =
        platformUserRepository
            .findByUsername(username)
            .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + username));

    return new org.springframework.security.core.userdetails.User(
        user.getUsername(),
        user.getPasswordHash(),
        user.getIsActive(),
        true, // accountNonExpired
        true, // credentialsNonExpired
        true, // accountNonLocked
        getAuthorities(user));
  }

  private Collection<? extends GrantedAuthority> getAuthorities(PlatformUser user) {
    Collection<GrantedAuthority> authorities = new ArrayList<>();
    authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getPlatformRole().name()));
    return authorities;
  }
}
