package com.assessment.security;

import com.assessment.constants.SecurityConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
@Order(1)
public class SecurityConfig {

  @Autowired private JwtAuthenticationFilter jwtAuthenticationFilter;

  @Autowired private JwtAuthenticationEntryPoint unauthorizedHandler;

  @Bean
  SecurityFilterChain securityFilterChain(final HttpSecurity http) throws Exception {
    return http.csrf(csrf -> csrf.disable())
        .cors(cors -> cors.configurationSource(corsConfigurationSource()))
        .sessionManagement(this::configureSessionManagement)
        .exceptionHandling(this::configureExceptionHandling)
        .authorizeHttpRequests(this::configureAuthorization)
        .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
        .build();
  }

  private void configureSessionManagement(
      final org.springframework.security.config.annotation.web.configurers
                  .SessionManagementConfigurer<
              HttpSecurity>
          session) {
    session.sessionCreationPolicy(SessionCreationPolicy.STATELESS);
  }

  private void configureExceptionHandling(
      final org.springframework.security.config.annotation.web.configurers
                  .ExceptionHandlingConfigurer<
              HttpSecurity>
          exceptions) {
    exceptions.authenticationEntryPoint(unauthorizedHandler);
  }

  private void configureAuthorization(
      final org.springframework.security.config.annotation.web.configurers
                      .AuthorizeHttpRequestsConfigurer<
                  HttpSecurity>
              .AuthorizationManagerRequestMatcherRegistry
          authz) {
    authz
        .requestMatchers(HttpMethod.OPTIONS, "/**")
        .permitAll()
        .requestMatchers("/api/hash/**")
        .permitAll()
        .requestMatchers("/api/test/**")
        .permitAll()
        .requestMatchers("/api/auth/**")
        .permitAll()
        .requestMatchers("/api/multi-tenant/auth/**")
        .permitAll()
        .requestMatchers("/", "/error")
        .permitAll()
        .requestMatchers("/api/health/**")
        .permitAll()
        .requestMatchers("/actuator/**")
        .permitAll()
        .requestMatchers("/swagger-ui/**")
        .permitAll()
        .requestMatchers("/swagger-ui.html")
        .permitAll()
        .requestMatchers("/swagger-resources/**")
        .permitAll()
        .requestMatchers("/v2/api-docs/**")
        .permitAll()
        .requestMatchers("/v3/api-docs/**")
        .permitAll()
        .requestMatchers("/api-docs/**")
        .permitAll()
        .requestMatchers("/webjars/**")
        .permitAll()
        .requestMatchers("/api/public/**")
        .permitAll()
        .requestMatchers("/api/ai/**")
        .permitAll() // 允许AI相关接口
        .requestMatchers("/api/pdf-import/**")
        .permitAll() // 临时允许PDF导入测试
        .requestMatchers("/api/docling/**")
        .permitAll() // 临时允许Docling测试
        .anyRequest()
        .authenticated();
  }

  @Bean
  PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }

  @Bean
  AuthenticationManager authenticationManager(final AuthenticationConfiguration config)
      throws Exception {
    return config.getAuthenticationManager();
  }

  @Bean
  UrlBasedCorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowedOriginPatterns(java.util.Arrays.asList("*"));
    configuration.setAllowedMethods(
        java.util.Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
    configuration.setAllowedHeaders(java.util.Arrays.asList("*"));
    configuration.setAllowCredentials(true);
    configuration.setMaxAge(SecurityConstants.CORS_MAX_AGE_SECONDS);

    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }
}
