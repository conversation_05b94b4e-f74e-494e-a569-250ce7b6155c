package com.assessment.security;

import com.assessment.config.JwtProperties;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import javax.crypto.SecretKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

@Component
public class JwtTokenProvider {

  private static final int MIN_SECRET_LENGTH = 32;
  private static final String DEFAULT_SECRET =
      "ThisIsAVeryLongAndSecureSecretKeyForJWTTokenGeneration"
          + "ThatDefinitelyMeetsThe256BitRequirement";

  @Autowired private JwtProperties jwtProperties;

  private SecretKey getSigningKey() {
    // 使用配置的密钥，如果不够长则扩展到256位
    String secret = jwtProperties.getSecret();
    if (secret == null || secret.length() < MIN_SECRET_LENGTH) {
      // 如果配置的密钥不够长，使用默认的安全密钥
      secret = DEFAULT_SECRET;
    }
    return Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
  }

  public String generateToken(Authentication authentication) {
    UserDetails userPrincipal = (UserDetails) authentication.getPrincipal();
    Date expiryDate = new Date(System.currentTimeMillis() + jwtProperties.getExpiration());

    return Jwts.builder()
        .subject(userPrincipal.getUsername())
        .issuedAt(new Date())
        .expiration(expiryDate)
        .signWith(getSigningKey())
        .compact();
  }

  public String generateRefreshToken(String username) {
    Date expiryDate = new Date(System.currentTimeMillis() + jwtProperties.getRefreshExpiration());

    return Jwts.builder()
        .subject(username)
        .issuedAt(new Date())
        .expiration(expiryDate)
        .signWith(getSigningKey())
        .compact();
  }

  public String getUsernameFromToken(String token) {
    Claims claims =
        Jwts.parser().verifyWith(getSigningKey()).build().parseSignedClaims(token).getPayload();

    return claims.getSubject();
  }

  public boolean validateToken(String token) {
    try {
      Jwts.parser().verifyWith(getSigningKey()).build().parseSignedClaims(token);
      return true;
    } catch (SecurityException ex) {
      System.err.println("Invalid JWT signature");
    } catch (MalformedJwtException ex) {
      System.err.println("Invalid JWT token");
    } catch (ExpiredJwtException ex) {
      System.err.println("Expired JWT token");
    } catch (UnsupportedJwtException ex) {
      System.err.println("Unsupported JWT token");
    } catch (IllegalArgumentException ex) {
      System.err.println("JWT claims string is empty");
    }
    return false;
  }

  public long getJwtExpirationMs() {
    return jwtProperties.getExpiration();
  }
}
