package com.assessment.security;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

  @Override
  public void commence(
      HttpServletRequest request,
      HttpServletResponse response,
      AuthenticationException authException)
      throws IOException, ServletException {
    System.err.println("Authentication error for path: " + request.getRequestURI());
    System.err.println("Error message: " + authException.getMessage());
    response.sendError(
        HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized: " + authException.getMessage());
  }
}
