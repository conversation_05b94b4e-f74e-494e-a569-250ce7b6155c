package com.assessment.constants;

/**
 * 安全相关常量定义 - 患者数据安全关键配置
 *
 * <p>这些常量直接影响系统安全性，包括JWT认证、CORS配置等 所有安全相关的魔法数字都应该在此处定义，便于统一管理和审计
 */
public final class SecurityConstants {

  /** 私有构造函数，防止实例化 */
  private SecurityConstants() {
    throw new UnsupportedOperationException("Utility class cannot be instantiated");
  }

  // ========== JWT相关常量 ==========

  /** JWT Token过期时间 - 24小时 (毫秒) */
  public static final long JWT_EXPIRATION_TIME = 86400000L;

  /** JWT刷新Token过期时间 - 7天 (毫秒) */
  public static final long JWT_REFRESH_EXPIRATION_TIME = 604800000L;

  /** Bearer Token前缀长度 - "Bearer " 的字符数 */
  public static final int BEARER_PREFIX_LENGTH = 7;

  // ========== CORS相关常量 ==========

  /** CORS预检请求最大缓存时间 - 1小时 (秒) */
  public static final long CORS_MAX_AGE_SECONDS = 3600L;

  // ========== 密码和认证相关常量 ==========

  /** 密码最小长度要求 */
  public static final int PASSWORD_MIN_LENGTH = 8;

  /** 登录失败最大尝试次数 */
  public static final int MAX_LOGIN_ATTEMPTS = 5;

  /** 账户锁定持续时间 - 30分钟 (秒) */
  public static final int ACCOUNT_LOCK_DURATION_SECONDS = 1800;

  // ========== 文件上传安全常量 ==========

  /** 文件上传大小限制 - 10MB (字节) */
  public static final long MAX_UPLOAD_FILE_SIZE_BYTES = 10485760L;
}
