package com.assessment.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/** 应用程序自定义配置属性 */
@Component
@ConfigurationProperties(prefix = "app.assessment")
public final class ApplicationProperties {

  private Evaluation evaluation = new Evaluation();
  private Upload upload = new Upload();
  private Security security = new Security();

  public static final class Evaluation {
    private int maxDuration = 120;
    private int autoSaveInterval = 30;

    public int getMaxDuration() {
      return maxDuration;
    }

    public void setMaxDuration(final int maxDuration) {
      this.maxDuration = maxDuration;
    }

    public int getAutoSaveInterval() {
      return autoSaveInterval;
    }

    public void setAutoSaveInterval(final int autoSaveInterval) {
      this.autoSaveInterval = autoSaveInterval;
    }
  }

  public static final class Upload {
    private String[] allowedTypes = {"image/jpeg", "image/png", "image/gif", "application/pdf"};
    private long maxSize = 10485760L; // 10MB

    public String[] getAllowedTypes() {
      return allowedTypes.clone();
    }

    public void setAllowedTypes(final String[] allowedTypes) {
      this.allowedTypes = allowedTypes != null ? allowedTypes.clone() : null;
    }

    public long getMaxSize() {
      return maxSize;
    }

    public void setMaxSize(final long maxSize) {
      this.maxSize = maxSize;
    }
  }

  public static final class Security {
    private int passwordMinLength = 8;
    private boolean passwordRequireSpecial = true;
    private int maxLoginAttempts = 5;
    private int lockDuration = 1800; // 30分钟

    public int getPasswordMinLength() {
      return passwordMinLength;
    }

    public void setPasswordMinLength(final int passwordMinLength) {
      this.passwordMinLength = passwordMinLength;
    }

    public boolean isPasswordRequireSpecial() {
      return passwordRequireSpecial;
    }

    public void setPasswordRequireSpecial(final boolean passwordRequireSpecial) {
      this.passwordRequireSpecial = passwordRequireSpecial;
    }

    public int getMaxLoginAttempts() {
      return maxLoginAttempts;
    }

    public void setMaxLoginAttempts(final int maxLoginAttempts) {
      this.maxLoginAttempts = maxLoginAttempts;
    }

    public int getLockDuration() {
      return lockDuration;
    }

    public void setLockDuration(final int lockDuration) {
      this.lockDuration = lockDuration;
    }
  }

  public Evaluation getEvaluation() {
    // 返回副本以避免内部表示暴露
    Evaluation copy = new Evaluation();
    copy.setMaxDuration(evaluation.getMaxDuration());
    copy.setAutoSaveInterval(evaluation.getAutoSaveInterval());
    return copy;
  }

  public void setEvaluation(final Evaluation evaluation) {
    if (evaluation != null) {
      // 创建副本以避免外部修改
      Evaluation copy = new Evaluation();
      copy.setMaxDuration(evaluation.getMaxDuration());
      copy.setAutoSaveInterval(evaluation.getAutoSaveInterval());
      this.evaluation = copy;
    }
  }

  public Upload getUpload() {
    // 返回副本以避免内部表示暴露
    Upload copy = new Upload();
    copy.setAllowedTypes(upload.getAllowedTypes());
    copy.setMaxSize(upload.getMaxSize());
    return copy;
  }

  public void setUpload(final Upload upload) {
    if (upload != null) {
      // 创建副本以避免外部修改
      Upload copy = new Upload();
      copy.setAllowedTypes(upload.getAllowedTypes());
      copy.setMaxSize(upload.getMaxSize());
      this.upload = copy;
    }
  }

  public Security getSecurity() {
    // 返回副本以避免内部表示暴露
    Security copy = new Security();
    copy.setPasswordMinLength(security.getPasswordMinLength());
    copy.setPasswordRequireSpecial(security.isPasswordRequireSpecial());
    copy.setMaxLoginAttempts(security.getMaxLoginAttempts());
    copy.setLockDuration(security.getLockDuration());
    return copy;
  }

  public void setSecurity(final Security security) {
    if (security != null) {
      // 创建副本以避免外部修改
      Security copy = new Security();
      copy.setPasswordMinLength(security.getPasswordMinLength());
      copy.setPasswordRequireSpecial(security.isPasswordRequireSpecial());
      copy.setMaxLoginAttempts(security.getMaxLoginAttempts());
      copy.setLockDuration(security.getLockDuration());
      this.security = copy;
    }
  }
}
