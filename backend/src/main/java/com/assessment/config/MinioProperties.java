package com.assessment.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/** MinIO对象存储配置属性 */
@Component
@ConfigurationProperties(prefix = "minio")
public class MinioProperties {

  private String endpoint = "http://localhost:9000";
  private String accessKey = "minioadmin";
  private String secretKey = "minioadmin";
  private String bucketName = "assessment-files";
  private boolean secure = false;

  /** 获取MinIO服务端点 */
  public String getEndpoint() {
    return endpoint;
  }

  /** 设置MinIO服务端点 */
  public void setEndpoint(final String endpoint) {
    this.endpoint = endpoint;
  }

  /** 获取访问密钥 */
  public String getAccessKey() {
    return accessKey;
  }

  /** 设置访问密钥 */
  public void setAccessKey(final String accessKey) {
    this.accessKey = accessKey;
  }

  /** 获取秘密密钥 */
  public String getSecretKey() {
    return secretKey;
  }

  /** 设置秘密密钥 */
  public void setSecretKey(final String secretKey) {
    this.secretKey = secretKey;
  }

  /** 获取存储桶名称 */
  public String getBucketName() {
    return bucketName;
  }

  /** 设置存储桶名称 */
  public void setBucketName(final String bucketName) {
    this.bucketName = bucketName;
  }

  /** 是否启用HTTPS */
  public boolean isSecure() {
    return secure;
  }

  /** 设置是否启用HTTPS */
  public void setSecure(final boolean secure) {
    this.secure = secure;
  }
}
