package com.assessment.config;

// CORS configuration is now handled in SecurityConfig.java
// This file is kept for potential future CORS customizations
// but the main CORS configuration is disabled to avoid conflicts

// @Configuration
// public class CorsConfig implements WebMvcConfigurer {
//
//     @Override
//     public void addCorsMappings(CorsRegistry registry) {
//         registry.addMapping("/**")
//                 .allowedOriginPatterns("*")
//                 .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
//                 .allowedHeaders("*")
//                 .allowCredentials(true)
//                 .maxAge(3600);
//     }
// }
