package com.assessment.config;

import com.assessment.constants.SecurityConstants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/** JWT配置属性 */
@Component
@ConfigurationProperties(prefix = "jwt")
public class JwtProperties {

  private String secret;
  private long expiration = SecurityConstants.JWT_EXPIRATION_TIME;
  private long refreshExpiration = SecurityConstants.JWT_REFRESH_EXPIRATION_TIME;

  /** 获取JWT密钥 */
  public String getSecret() {
    return secret;
  }

  /** 设置JWT密钥 */
  public void setSecret(final String secret) {
    this.secret = secret;
  }

  /** 获取JWT过期时间（毫秒） */
  public long getExpiration() {
    return expiration;
  }

  /** 设置JWT过期时间（毫秒） */
  public void setExpiration(final long expiration) {
    this.expiration = expiration;
  }

  /** 获取刷新令牌过期时间（毫秒） */
  public long getRefreshExpiration() {
    return refreshExpiration;
  }

  /** 设置刷新令牌过期时间（毫秒） */
  public void setRefreshExpiration(final long refreshExpiration) {
    this.refreshExpiration = refreshExpiration;
  }
}
