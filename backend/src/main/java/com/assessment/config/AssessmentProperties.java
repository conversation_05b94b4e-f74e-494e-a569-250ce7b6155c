package com.assessment.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** 评估系统配置属性 */
@Configuration
@ConfigurationProperties(prefix = "assessment")
public class AssessmentProperties {

  /** 评估相关配置 */
  private Evaluation evaluation = new Evaluation();

  /** 文件上传配置 */
  private Upload upload = new Upload();

  /** 安全配置 */
  private Security security = new Security();

  /** 评估阈值配置 */
  private Threshold threshold = new Threshold();

  public Evaluation getEvaluation() {
    return this.evaluation == null ? null : new Evaluation(this.evaluation);
  }

  public void setEvaluation(Evaluation evaluation) {
    this.evaluation = evaluation == null ? null : new Evaluation(evaluation);
  }

  public Upload getUpload() {
    return this.upload == null ? null : new Upload(this.upload);
  }

  public void setUpload(Upload upload) {
    this.upload = upload == null ? null : new Upload(upload);
  }

  public Security getSecurity() {
    return this.security == null ? null : new Security(this.security);
  }

  public void setSecurity(Security security) {
    this.security = security == null ? null : new Security(security);
  }

  public Threshold getThreshold() {
    return this.threshold == null ? null : new Threshold(this.threshold);
  }

  public void setThreshold(Threshold threshold) {
    this.threshold = threshold == null ? null : new Threshold(threshold);
  }

  public static class Evaluation {
    /** 最大评估时长（分钟） */
    private Integer maxDuration = 120;

    /** 自动保存间隔（秒） */
    private Integer autoSaveInterval = 30;

    public Evaluation() {}

    public Evaluation(Evaluation other) {
      if (other != null) {
        this.maxDuration = other.maxDuration;
        this.autoSaveInterval = other.autoSaveInterval;
      }
    }

    public Integer getMaxDuration() {
      return maxDuration;
    }

    public void setMaxDuration(Integer maxDuration) {
      this.maxDuration = maxDuration;
    }

    public Integer getAutoSaveInterval() {
      return autoSaveInterval;
    }

    public void setAutoSaveInterval(Integer autoSaveInterval) {
      this.autoSaveInterval = autoSaveInterval;
    }
  }

  public static class Upload {
    /** 允许的文件类型 */
    private String[] allowedTypes = {"image/jpeg", "image/png", "image/gif", "application/pdf"};

    /** 最大文件大小 */
    private Long maxSize = 10485760L; // 10MB

    public Upload() {}

    public Upload(Upload other) {
      if (other != null) {
        this.maxSize = other.maxSize;
        this.allowedTypes = other.allowedTypes == null ? null : other.allowedTypes.clone();
      }
    }

    public String[] getAllowedTypes() {
      return this.allowedTypes == null ? null : this.allowedTypes.clone();
    }

    public void setAllowedTypes(String[] allowedTypes) {
      this.allowedTypes = allowedTypes == null ? null : allowedTypes.clone();
    }

    public Long getMaxSize() {
      return this.maxSize;
    }

    public void setMaxSize(Long maxSize) {
      this.maxSize = maxSize;
    }
  }

  public static class Security {
    /** 密码最小长度 */
    private Integer passwordMinLength = 8;

    /** 密码需要特殊字符 */
    private Boolean passwordRequireSpecial = true;

    /** 最大登录尝试次数 */
    private Integer maxLoginAttempts = 5;

    /** 锁定时长（秒） */
    private Integer lockDuration = 1800; // 30分钟

    public Security() {}

    public Security(Security other) {
      if (other != null) {
        this.passwordMinLength = other.passwordMinLength;
        this.passwordRequireSpecial = other.passwordRequireSpecial;
        this.maxLoginAttempts = other.maxLoginAttempts;
        this.lockDuration = other.lockDuration;
      }
    }

    public Integer getPasswordMinLength() {
      return passwordMinLength;
    }

    public void setPasswordMinLength(Integer passwordMinLength) {
      this.passwordMinLength = passwordMinLength;
    }

    public Boolean getPasswordRequireSpecial() {
      return passwordRequireSpecial;
    }

    public void setPasswordRequireSpecial(Boolean passwordRequireSpecial) {
      this.passwordRequireSpecial = passwordRequireSpecial;
    }

    public Integer getMaxLoginAttempts() {
      return maxLoginAttempts;
    }

    public void setMaxLoginAttempts(Integer maxLoginAttempts) {
      this.maxLoginAttempts = maxLoginAttempts;
    }

    public Integer getLockDuration() {
      return lockDuration;
    }

    public void setLockDuration(Integer lockDuration) {
      this.lockDuration = lockDuration;
    }
  }

  public static class Threshold {
    /** 字段映射整体置信度阈值 */
    private Double fieldMappingOverallConfidence = 0.85;

    /** 姓名字段置信度 */
    private Double nameFieldConfidence = 0.95;

    /** 年龄字段置信度 */
    private Double ageFieldConfidence = 0.90;

    /** 评分字段置信度 */
    private Double ratingFieldConfidence = 0.88;

    /** 日常生活能力权重 */
    private Double dailyLivingAbilityWeight = 0.3;

    /** 最大年龄 */
    private Double maxAge = 120.0;

    /** 评分最小值 */
    private Double ratingMinValue = 1.0;

    /** 评分最大值 */
    private Double ratingMaxValue = 5.0;

    public Threshold() {}

    public Threshold(Threshold other) {
      if (other != null) {
        this.fieldMappingOverallConfidence = other.fieldMappingOverallConfidence;
        this.nameFieldConfidence = other.nameFieldConfidence;
        this.ageFieldConfidence = other.ageFieldConfidence;
        this.ratingFieldConfidence = other.ratingFieldConfidence;
        this.dailyLivingAbilityWeight = other.dailyLivingAbilityWeight;
        this.maxAge = other.maxAge;
        this.ratingMinValue = other.ratingMinValue;
        this.ratingMaxValue = other.ratingMaxValue;
      }
    }

    public Double getFieldMappingOverallConfidence() {
      return fieldMappingOverallConfidence;
    }

    public void setFieldMappingOverallConfidence(Double fieldMappingOverallConfidence) {
      this.fieldMappingOverallConfidence = fieldMappingOverallConfidence;
    }

    public Double getNameFieldConfidence() {
      return nameFieldConfidence;
    }

    public void setNameFieldConfidence(Double nameFieldConfidence) {
      this.nameFieldConfidence = nameFieldConfidence;
    }

    public Double getAgeFieldConfidence() {
      return ageFieldConfidence;
    }

    public void setAgeFieldConfidence(Double ageFieldConfidence) {
      this.ageFieldConfidence = ageFieldConfidence;
    }

    public Double getRatingFieldConfidence() {
      return ratingFieldConfidence;
    }

    public void setRatingFieldConfidence(Double ratingFieldConfidence) {
      this.ratingFieldConfidence = ratingFieldConfidence;
    }

    public Double getDailyLivingAbilityWeight() {
      return dailyLivingAbilityWeight;
    }

    public void setDailyLivingAbilityWeight(Double dailyLivingAbilityWeight) {
      this.dailyLivingAbilityWeight = dailyLivingAbilityWeight;
    }

    public Double getMaxAge() {
      return maxAge;
    }

    public void setMaxAge(Double maxAge) {
      this.maxAge = maxAge;
    }

    public Double getRatingMinValue() {
      return ratingMinValue;
    }

    public void setRatingMinValue(Double ratingMinValue) {
      this.ratingMinValue = ratingMinValue;
    }

    public Double getRatingMaxValue() {
      return ratingMaxValue;
    }

    public void setRatingMaxValue(Double ratingMaxValue) {
      this.ratingMaxValue = ratingMaxValue;
    }
  }
}
