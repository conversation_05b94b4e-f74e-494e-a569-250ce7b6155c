package com.assessment.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/** 应用程序性能配置属性 */
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

  private Performance performance = new Performance();

  public static class Performance {
    private boolean appleSiliconOptimized = true;
    private boolean memoryConservative = true;
    private int maxConcurrentAssessments = 20;

    public boolean isAppleSiliconOptimized() {
      return appleSiliconOptimized;
    }

    public void setAppleSiliconOptimized(final boolean appleSiliconOptimized) {
      this.appleSiliconOptimized = appleSiliconOptimized;
    }

    public boolean isMemoryConservative() {
      return memoryConservative;
    }

    public void setMemoryConservative(final boolean memoryConservative) {
      this.memoryConservative = memoryConservative;
    }

    public int getMaxConcurrentAssessments() {
      return maxConcurrentAssessments;
    }

    public void setMaxConcurrentAssessments(final int maxConcurrentAssessments) {
      this.maxConcurrentAssessments = maxConcurrentAssessments;
    }
  }

  public Performance getPerformance() {
    // 返回副本以避免内部表示暴露
    Performance copy = new Performance();
    copy.setAppleSiliconOptimized(performance.isAppleSiliconOptimized());
    copy.setMemoryConservative(performance.isMemoryConservative());
    copy.setMaxConcurrentAssessments(performance.getMaxConcurrentAssessments());
    return copy;
  }

  public void setPerformance(final Performance performance) {
    if (performance != null) {
      // 创建副本以避免外部修改
      Performance copy = new Performance();
      copy.setAppleSiliconOptimized(performance.isAppleSiliconOptimized());
      copy.setMemoryConservative(performance.isMemoryConservative());
      copy.setMaxConcurrentAssessments(performance.getMaxConcurrentAssessments());
      this.performance = copy;
    }
  }
}
