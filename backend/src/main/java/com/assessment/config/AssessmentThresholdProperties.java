package com.assessment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 评估阈值配置属性 - 患者安全关键配置
 *
 * <p>这些阈值直接影响评估结果和患者风险等级判断，必须确保准确性和可配置性以适应不同的业务需求
 */
@Data
@Component
@ConfigurationProperties(prefix = "assessment.threshold")
public class AssessmentThresholdProperties {

  /** 字段映射整体置信度阈值 - 用于判断PDF解析结果是否可信 */
  private Double fieldMappingOverallConfidence = 0.85;

  /** 姓名字段置信度阈值 - 姓名识别准确性要求最高 */
  private Double nameFieldConfidence = 0.95;

  /** 年龄字段置信度阈值 - 年龄是重要的基础信息 */
  private Double ageFieldConfidence = 0.90;

  /** 评分字段置信度阈值 - 评分字段的识别准确性要求 */
  private Double ratingFieldConfidence = 0.88;

  /** 日常生活能力权重 - 用于综合评估计算 */
  private Double dailyLivingAbilityWeight = 0.3;

  /** 年龄字段最大值 - 合理的年龄上限 */
  private Double maxAge = 120.0;

  /** 评分最小值 - 评分量表的最小值 */
  private Double ratingMinValue = 1.0;

  /** 评分最大值 - 评分量表的最大值 */
  private Double ratingMaxValue = 5.0;
}
