package com.assessment.config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/** LM Studio配置管理类 用于集中管理LM Studio服务地址、模型信息和API参数 */
@Configuration
@ConfigurationProperties(prefix = "lm-studio")
public class LMStudioConfig {

  /** 服务配置 */
  private ServerConfig server = new ServerConfig();

  /** 模型配置 */
  private ModelsConfig models = new ModelsConfig();

  /** 自动切换配置 */
  private AutoSwitchConfig autoSwitch = new AutoSwitchConfig();

  public LMStudioConfig() {}

  public LMStudioConfig(LMStudioConfig other) {
    if (other != null) {
      this.server = new ServerConfig(other.server);
      this.models = new ModelsConfig(other.models);
      this.autoSwitch = new AutoSwitchConfig(other.autoSwitch);
    }
  }

  public ServerConfig getServer() {
    return this.server == null ? null : new ServerConfig(this.server);
  }

  public void setServer(ServerConfig server) {
    this.server = server == null ? null : new ServerConfig(server);
  }

  public ModelsConfig getModels() {
    return this.models == null ? null : new ModelsConfig(this.models);
  }

  public void setModels(ModelsConfig models) {
    this.models = models == null ? null : new ModelsConfig(models);
  }

  public AutoSwitchConfig getAutoSwitch() {
    return this.autoSwitch == null ? null : new AutoSwitchConfig(this.autoSwitch);
  }

  public void setAutoSwitch(AutoSwitchConfig autoSwitch) {
    this.autoSwitch = autoSwitch == null ? null : new AutoSwitchConfig(autoSwitch);
  }

  @Data
  public static class ServerConfig {
    /** 主服务地址 */
    private String primaryUrl = "http://*************:1234";

    /** 备用服务地址列表 */
    private List<String> backupUrls = List.of("http://*************:1234", "http://localhost:1234");

    /** 连接超时时间（毫秒） */
    private Integer connectionTimeout = 10000;

    /** 读取超时时间（毫秒） */
    private Integer readTimeout = 60000;

    /** 重试次数 */
    private Integer maxRetries = 3;

    /** 健康检查间隔（秒） */
    private Integer healthCheckInterval = 30;

    public ServerConfig() {}

    public ServerConfig(ServerConfig other) {
      if (other != null) {
        this.primaryUrl = other.primaryUrl;
        this.setBackupUrls(other.backupUrls);
        this.connectionTimeout = other.connectionTimeout;
        this.readTimeout = other.readTimeout;
        this.maxRetries = other.maxRetries;
        this.healthCheckInterval = other.healthCheckInterval;
      }
    }

    public List<String> getBackupUrls() {
      return this.backupUrls == null ? null : new ArrayList<>(this.backupUrls);
    }

    public void setBackupUrls(List<String> backupUrls) {
      this.backupUrls = backupUrls == null ? null : new ArrayList<>(backupUrls);
    }
  }

  @Data
  public static class ModelsConfig {
    /** 模型选择策略 */
    private SelectionConfig selection = new SelectionConfig();

    /** 排除的模型模式 */
    private List<String> excludedPatterns =
        List.of(".*embedding.*", ".*whisper.*", ".*tts.*", ".*vision.*", ".*translate.*");

    public ModelsConfig() {}

    public ModelsConfig(ModelsConfig other) {
      if (other != null) {
        this.setSelection(other.selection);
        this.setExcludedPatterns(other.excludedPatterns);
      }
    }

    public SelectionConfig getSelection() {
      return this.selection == null ? null : new SelectionConfig(this.selection);
    }

    public void setSelection(SelectionConfig selection) {
      this.selection = selection == null ? null : new SelectionConfig(selection);
    }

    public List<String> getExcludedPatterns() {
      return this.excludedPatterns == null ? null : new ArrayList<>(this.excludedPatterns);
    }

    public void setExcludedPatterns(List<String> excludedPatterns) {
      this.excludedPatterns = excludedPatterns == null ? null : new ArrayList<>(excludedPatterns);
    }
  }

  @Data
  public static class SelectionConfig {
    /** 优先选择模式（按优先级排序） */
    private List<String> preferredPatterns =
        List.of(
            "deepseek.*r1.*qwen", // DeepSeek R1 系列模型
            "deepseek.*coder", // DeepSeek Coder 系列
            "qwen.*8b", // Qwen 8B 系列
            "gemma.*[2-9][0-9]b", // Gemma 大参数模型 (20B+)
            "llama.*[1-9][0-9]b", // Llama 大参数模型 (10B+)
            ".*chat.*", // 包含chat的模型
            ".*instruct.*" // 包含instruct的模型
            );

    /** 模型能力推断规则 */
    private Map<String, List<String>> capabilityInference =
        Map.of(
            "code", List.of("coder", "code", "programming"),
            "chinese", List.of("qwen", "chinese", "zh"),
            "reasoning", List.of("reasoning", "think", "logic"),
            "chat", List.of("chat", "instruct", "conversation"));

    public SelectionConfig() {}

    public SelectionConfig(SelectionConfig other) {
      if (other != null) {
        this.setPreferredPatterns(other.preferredPatterns);
        this.setCapabilityInference(other.capabilityInference);
      }
    }

    public List<String> getPreferredPatterns() {
      return this.preferredPatterns == null ? null : new ArrayList<>(this.preferredPatterns);
    }

    public void setPreferredPatterns(List<String> preferredPatterns) {
      this.preferredPatterns =
          preferredPatterns == null ? null : new ArrayList<>(preferredPatterns);
    }

    public Map<String, List<String>> getCapabilityInference() {
      return this.capabilityInference == null ? null : new HashMap<>(this.capabilityInference);
    }

    public void setCapabilityInference(Map<String, List<String>> capabilityInference) {
      this.capabilityInference =
          capabilityInference == null ? null : new HashMap<>(capabilityInference);
    }
  }

  @Data
  public static class ModelInfo {
    /** 模型ID */
    private String id;

    /** 显示名称 */
    private String displayName;

    /** 描述 */
    private String description;

    /** 优先级（数字越小优先级越高） */
    private Integer priority;

    /** 能力标签 */
    private List<String> capabilities;

    public ModelInfo() {}

    public ModelInfo(ModelInfo other) {
      if (other != null) {
        this.id = other.id;
        this.displayName = other.displayName;
        this.description = other.description;
        this.priority = other.priority;
        this.setCapabilities(other.capabilities);
      }
    }

    public ModelInfo(
        String id,
        String displayName,
        String description,
        Integer priority,
        List<String> capabilities) {
      this.id = id;
      this.displayName = displayName;
      this.description = description;
      this.priority = priority;
      this.setCapabilities(capabilities);
    }

    public List<String> getCapabilities() {
      return this.capabilities == null ? null : new ArrayList<>(this.capabilities);
    }

    public void setCapabilities(List<String> capabilities) {
      this.capabilities = capabilities == null ? null : new ArrayList<>(capabilities);
    }
  }

  // API配置类已移除，所有模型参数（temperature、max_tokens、top_p等）由LM Studio管理

  @Data
  public static class AutoSwitchConfig {
    /** 启用自动模型切换 */
    private Boolean enabled = true;

    /** 模型不可用时自动切换到备用模型 */
    private Boolean fallbackEnabled = true;

    /** 服务不可用时自动切换到备用服务 */
    private Boolean serverFallbackEnabled = true;

    /** 模型响应时间阈值（毫秒），超过则切换 */
    private Long responseTimeThreshold = 30000L;

    public AutoSwitchConfig() {}

    public AutoSwitchConfig(AutoSwitchConfig other) {
      if (other != null) {
        this.enabled = other.enabled;
        this.fallbackEnabled = other.fallbackEnabled;
        this.serverFallbackEnabled = other.serverFallbackEnabled;
        this.responseTimeThreshold = other.responseTimeThreshold;
      }
    }
  }

  /**
   * 根据优先级模式匹配模型.
   *
   * @param modelId 模型ID
   * @return 优先级
   */
  public int getModelPriority(String modelId) {
    String lowerCaseModelId = modelId.toLowerCase(Locale.ROOT);
    List<String> preferredPatterns = this.getModels().getSelection().getPreferredPatterns();
    for (int i = 0; i < preferredPatterns.size(); i++) {
      String pattern = preferredPatterns.get(i).toLowerCase(Locale.ROOT);
      if (Pattern.compile(pattern).matcher(lowerCaseModelId).find()) {
        return i + 1; // 优先级从1开始
      }
    }
    return 999; // 默认优先级
  }

  /**
   * 根据模型ID推断其能力.
   *
   * @param modelId 模型ID
   * @return 能力列表
   */
  public List<String> inferModelCapabilities(String modelId) {
    String lowerCaseModelId = modelId.toLowerCase(Locale.ROOT);
    List<String> capabilities = new ArrayList<>();
    Map<String, List<String>> inferenceRules =
        this.getModels().getSelection().getCapabilityInference();

    for (Map.Entry<String, List<String>> entry : inferenceRules.entrySet()) {
      String capability = entry.getKey();
      List<String> keywords = entry.getValue();
      for (String keyword : keywords) {
        if (lowerCaseModelId.contains(keyword.toLowerCase(Locale.ROOT))) {
          if (!capabilities.contains(capability)) {
            capabilities.add(capability);
          }
        }
      }
    }

    // 根据测试要求，所有模型都应默认具备"chat"能力
    if (!capabilities.contains("chat")) {
      capabilities.add("chat");
    }

    return capabilities;
  }

  /**
   * 检查模型是否被排除.
   *
   * @param modelId 模型ID
   * @return 是否被排除
   */
  public boolean isModelExcluded(String modelId) {
    String lowerCaseModelId = modelId.toLowerCase(Locale.ROOT);
    for (String pattern : this.getModels().getExcludedPatterns()) {
      if (Pattern.compile(pattern.toLowerCase(Locale.ROOT)).matcher(lowerCaseModelId).find()) {
        return true;
      }
    }
    return false;
  }

  /**
   * 生成模型的显示名称.
   *
   * @param modelId 模型ID
   * @return 显示名称
   */
  public String generateDisplayName(String modelId) {
    // 移除常见的组织/作者前缀
    String name = modelId.replaceAll("^[a-zA-Z0-9-_]+/", "");
    // 将连字符和下划线替换为空格
    name = name.replace('-', ' ').replace('_', ' ');
    // 将每个单词的首字母大写
    String[] words = name.split(" ");
    StringBuilder displayName = new StringBuilder();
    for (String word : words) {
      if (!word.isEmpty()) {
        displayName
            .append(word.substring(0, 1).toUpperCase(Locale.ROOT))
            .append(word.substring(1).toLowerCase(Locale.ROOT))
            .append(" ");
      }
    }
    return displayName.toString().trim();
  }
}
