package com.assessment.repository.multitenant;

import com.assessment.entity.multitenant.AssessmentSubject;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** 评估对象（老人）Repository 支持多租户的评估对象数据访问 */
@Repository
public interface AssessmentSubjectRepository extends JpaRepository<AssessmentSubject, String> {

  /** 根据租户ID和身份证号查找评估对象 */
  Optional<AssessmentSubject> findByTenantIdAndIdNumber(String tenantId, String idNumber);

  /** 查找租户下的所有活跃评估对象 */
  List<AssessmentSubject> findByTenantIdAndIsActiveTrue(String tenantId);

  /** 分页查找租户下的评估对象 */
  Page<AssessmentSubject> findByTenantId(String tenantId, Pageable pageable);

  /** 根据姓名模糊查询 */
  @Query(
      "SELECT s FROM AssessmentSubject s WHERE s.tenantId = :tenantId AND s.name LIKE %:name% AND s.isActive = true")
  List<AssessmentSubject> findByTenantIdAndNameContaining(
      @Param("tenantId") String tenantId, @Param("name") String name);

  /** 根据护理等级查询 */
  List<AssessmentSubject> findByTenantIdAndCurrentCareLevel(String tenantId, String careLevel);

  /** 统计租户下的评估对象数量 */
  long countByTenantIdAndIsActiveTrue(String tenantId);

  /** 检查身份证号是否已存在 */
  boolean existsByTenantIdAndIdNumber(String tenantId, String idNumber);

  /** 批量查找评估对象 */
  @Query("SELECT s FROM AssessmentSubject s WHERE s.tenantId = :tenantId AND s.id IN :ids")
  List<AssessmentSubject> findByTenantIdAndIds(
      @Param("tenantId") String tenantId, @Param("ids") List<String> ids);
}
