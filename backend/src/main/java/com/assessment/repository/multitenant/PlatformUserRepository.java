package com.assessment.repository.multitenant;

import com.assessment.entity.multitenant.PlatformUser;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** 平台用户数据访问层 */
@Repository
public interface PlatformUserRepository extends JpaRepository<PlatformUser, UUID> {

  /** 根据用户名查找用户 */
  Optional<PlatformUser> findByUsername(String username);

  /** 根据邮箱查找用户 */
  Optional<PlatformUser> findByEmail(String email);

  /** 根据用户名或邮箱查找用户 */
  @Query(
      "SELECT u FROM PlatformUser u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
  Optional<PlatformUser> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

  /** 根据状态查找用户 */
  List<PlatformUser> findByIsActive(Boolean isActive);

  /** 根据状态统计用户数量 */
  long countByIsActive(Boolean isActive);

  /** 根据平台角色查找用户 */
  List<PlatformUser> findByPlatformRole(PlatformUser.PlatformRole platformRole);

  /** 根据平台角色统计用户数量 */
  long countByPlatformRole(PlatformUser.PlatformRole platformRole);

  /** 根据用户名、邮箱或姓名模糊查询 */
  Page<PlatformUser>
      findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCaseOrFullNameContainingIgnoreCase(
          String username, String email, String fullName, Pageable pageable);

  /** 检查用户名是否存在 */
  boolean existsByUsername(String username);

  /** 检查邮箱是否存在 */
  boolean existsByEmail(String email);

  /** 查找平台管理员用户 */
  @Query("SELECT u FROM PlatformUser u WHERE u.platformRole = 'ADMIN' AND u.isActive = true")
  List<PlatformUser> findActiveAdminUsers();
}
