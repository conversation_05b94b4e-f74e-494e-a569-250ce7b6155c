package com.assessment.repository.multitenant;

import com.assessment.entity.multitenant.TenantAssessmentRecord;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** 租户评估记录Repository 支持多租户的评估记录数据访问 */
@Repository
public interface TenantAssessmentRecordRepository
    extends JpaRepository<TenantAssessmentRecord, String> {

  /** 根据租户ID和记录编号查找 */
  Optional<TenantAssessmentRecord> findByTenantIdAndRecordNumber(
      String tenantId, String recordNumber);

  /** 查找租户下某个评估对象的所有记录 */
  List<TenantAssessmentRecord> findByTenantIdAndSubjectIdOrderByAssessmentDateDesc(
      String tenantId, String subjectId);

  /** 分页查找租户的评估记录 */
  Page<TenantAssessmentRecord> findByTenantId(String tenantId, Pageable pageable);

  /** 根据状态查找评估记录 */
  Page<TenantAssessmentRecord> findByTenantIdAndStatus(
      String tenantId, String status, Pageable pageable);

  /** 根据状态查找所有评估记录 */
  @Query("SELECT r FROM TenantAssessmentRecord r WHERE r.status = :status")
  List<TenantAssessmentRecord> findAllByStatus(@Param("status") String status, Pageable pageable);

  /** 查找待审核的记录 */
  @Query(
      "SELECT r FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.status = 'SUBMITTED' ORDER BY r.createdAt ASC")
  List<TenantAssessmentRecord> findPendingReviews(@Param("tenantId") String tenantId);

  /** 根据评估员查找记录 */
  Page<TenantAssessmentRecord> findByTenantIdAndAssessorId(
      String tenantId, String assessorId, Pageable pageable);

  /** 根据时间范围查找记录 */
  @Query(
      "SELECT r FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.assessmentDate BETWEEN :startDate AND :endDate")
  List<TenantAssessmentRecord> findByTenantIdAndDateRange(
      @Param("tenantId") String tenantId,
      @Param("startDate") LocalDateTime startDate,
      @Param("endDate") LocalDateTime endDate);

  /** 统计租户的评估记录数量 */
  long countByTenantId(String tenantId);

  /** 统计租户下某个状态的记录数量 */
  long countByTenantIdAndStatus(String tenantId, String status);

  /** 统计某个时间段的评估数量 */
  @Query(
      "SELECT COUNT(r) FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.assessmentDate BETWEEN :startDate AND :endDate")
  long countByTenantIdAndAssessmentDateBetween(
      @Param("tenantId") String tenantId,
      @Param("startDate") LocalDateTime startDate,
      @Param("endDate") LocalDateTime endDate);

  /** 统计某个状态的评估数量 */
  long countByStatus(String status);

  /** 统计某个状态和时间段的评估数量 */
  @Query(
      "SELECT COUNT(r) FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.status = :status AND r.assessmentDate BETWEEN :startDate AND :endDate")
  long countByTenantIdAndStatusAndAssessmentDateBetween(
      @Param("tenantId") String tenantId,
      @Param("status") String status,
      @Param("startDate") LocalDateTime startDate,
      @Param("endDate") LocalDateTime endDate);

  /** 统计某个时间段的评估数量 */
  @Query(
      "SELECT COUNT(r) FROM TenantAssessmentRecord r WHERE r.assessmentDate BETWEEN :startDate AND :endDate")
  long countByAssessmentDateBetween(
      @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

  /** 统计某个状态和时间段的评估数量 */
  @Query(
      "SELECT COUNT(r) FROM TenantAssessmentRecord r WHERE r.status = :status AND r.assessmentDate BETWEEN :startDate AND :endDate")
  long countByStatusAndAssessmentDateBetween(
      @Param("status") String status,
      @Param("startDate") LocalDateTime startDate,
      @Param("endDate") LocalDateTime endDate);

  /** 统计某个时间段的评估数量 */
  @Query(
      "SELECT COUNT(r) FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.assessmentDate >= :startDate")
  long countByTenantIdAndAssessmentDateAfter(
      @Param("tenantId") String tenantId, @Param("startDate") LocalDateTime startDate);

  /** 获取最近的评估记录 */
  @Query(
      "SELECT r FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.subjectId = :subjectId ORDER BY r.assessmentDate DESC")
  Optional<TenantAssessmentRecord> findLatestBySubject(
      @Param("tenantId") String tenantId, @Param("subjectId") String subjectId);

  /** 检查记录编号是否已存在 */
  boolean existsByTenantIdAndRecordNumber(String tenantId, String recordNumber);

  /** 批量查找评估记录 */
  @Query("SELECT r FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.id IN :ids")
  List<TenantAssessmentRecord> findByTenantIdAndIds(
      @Param("tenantId") String tenantId, @Param("ids") List<String> ids);

  /** 根据量表ID查找使用该量表的记录 */
  Page<TenantAssessmentRecord> findByTenantIdAndScaleId(
      String tenantId, String scaleId, Pageable pageable);

  /** 获取质量评分低于阈值的记录 */
  @Query(
      "SELECT r FROM TenantAssessmentRecord r WHERE r.tenantId = :tenantId AND r.qualityScore < :threshold")
  List<TenantAssessmentRecord> findLowQualityRecords(
      @Param("tenantId") String tenantId, @Param("threshold") Double threshold);
}
