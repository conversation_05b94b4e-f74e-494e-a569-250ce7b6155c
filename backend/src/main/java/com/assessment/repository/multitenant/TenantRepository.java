package com.assessment.repository.multitenant;

import com.assessment.entity.multitenant.Tenant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/** 租户数据访问层 */
@Repository
public interface TenantRepository extends JpaRepository<Tenant, UUID> {

  /** 根据租户代码查找租户 */
  Optional<Tenant> findByCode(String code);

  /** 根据状态查找租户 */
  List<Tenant> findByStatus(Tenant.TenantStatus status);

  Page<Tenant> findByStatus(Tenant.TenantStatus status, Pageable pageable);

  /** 根据状态统计租户数量 */
  long countByStatus(Tenant.TenantStatus status);

  /** 根据名称或代码模糊查询 */
  Page<Tenant> findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
      String name, String code, Pageable pageable);

  /** 根据名称或代码模糊查询，同时过滤状态 */
  Page<Tenant> findByNameContainingIgnoreCaseOrCodeContainingIgnoreCaseAndStatus(
      String name, String code, Tenant.TenantStatus status, Pageable pageable);

  /** 根据订阅计划查找租户 */
  List<Tenant> findBySubscriptionPlan(Tenant.SubscriptionPlan subscriptionPlan);

  /** 查找活跃租户 */
  @Query("SELECT t FROM Tenant t WHERE t.status = 'ACTIVE'")
  List<Tenant> findActiveTenants();

  /** 根据租户代码和状态查找 */
  Optional<Tenant> findByCodeAndStatus(String code, Tenant.TenantStatus status);

  /** 检查租户代码是否存在 */
  boolean existsByCode(String code);
}
