package com.assessment.entity.multitenant;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/** 全局量表注册中心实体 存储所有可用的评估量表定义 */
@Entity
@Table(
    name = "global_scale_registry",
    indexes = {
      @Index(name = "idx_global_scales_code", columnList = "code"),
      @Index(name = "idx_global_scales_category", columnList = "category"),
      @Index(name = "idx_global_scales_publisher", columnList = "publisherType,publisherId"),
      @Index(name = "idx_global_scales_status", columnList = "status,visibility"),
      @Index(name = "idx_global_scales_usage", columnList = "usageCount")
    })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GlobalScaleRegistry {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(length = 36)
  private String id;

  /** 量表代码（全局唯一） */
  @Column(nullable = false, unique = true, length = 100)
  private String code;

  /** 量表名称 */
  @Column(nullable = false, length = 200)
  private String name;

  /** 版本号 */
  @Column(nullable = false, length = 20)
  private String version;

  // 分类和标签
  @Column(nullable = false, length = 50)
  private String category;

  @Column(name = "industry_tags", columnDefinition = "VARCHAR(1000)") // H2兼容：使用JSON格式存储数组
  private String industryTags; // JSON格式存储行业标签数组

  @Column(columnDefinition = "VARCHAR(1000)") // H2兼容：使用JSON格式存储数组
  private String keywords; // JSON格式存储关键词数组

  // 量表定义
  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT", nullable = false) // H2兼容：使用TEXT存储JSON
  private JsonNode formSchema;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT", nullable = false) // H2兼容：使用TEXT存储JSON
  private JsonNode scoringRules;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT") // H2兼容：使用TEXT存储JSON
  @Builder.Default
  private JsonNode validationRules = null;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT") // H2兼容：使用TEXT存储JSON
  @Builder.Default
  private JsonNode reportTemplate = null;

  // 发布信息
  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private PublisherType publisherType;

  @Column(length = 36)
  private String publisherId; // 发布者ID（可能是租户ID）

  // 可见性和权限
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private Visibility visibility = Visibility.PUBLIC;

  @Column(precision = 10, scale = 2)
  @Builder.Default
  private BigDecimal price = BigDecimal.ZERO;

  // 使用统计
  @Column(nullable = false)
  @Builder.Default
  private Long usageCount = 0L;

  @Column(precision = 3, scale = 2)
  @Builder.Default
  private BigDecimal rating = BigDecimal.ZERO;

  @Column(nullable = false)
  @Builder.Default
  private Integer ratingCount = 0;

  // 状态管理
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private ScaleStatus status = ScaleStatus.ACTIVE;

  @Column(nullable = false)
  @Builder.Default
  private Boolean isOfficial = false;

  @Column(nullable = false)
  @Builder.Default
  private Boolean isVerified = false;

  // 审计字段
  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime createdAt = LocalDateTime.now();

  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime updatedAt = LocalDateTime.now();

  private LocalDateTime publishedAt;

  private LocalDateTime deprecatedAt;

  /** 发布者类型枚举 */
  public enum PublisherType {
    PLATFORM("平台官方"),
    TENANT("租户"),
    PARTNER("合作伙伴");

    private final String displayName;

    PublisherType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  /** 可见性枚举 */
  public enum Visibility {
    PUBLIC("公开"),
    PRIVATE("私有"),
    PREMIUM("付费");

    private final String displayName;

    Visibility(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  /** 量表状态枚举 */
  public enum ScaleStatus {
    ACTIVE("活跃"),
    INACTIVE("非活跃"),
    DEPRECATED("已弃用"),
    UNDER_REVIEW("审核中");

    private final String displayName;

    ScaleStatus(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 业务方法

  /** 增加使用次数 */
  public void incrementUsage() {
    this.usageCount++;
    this.updatedAt = LocalDateTime.now();
  }

  /** 更新评分 */
  public void updateRating(BigDecimal newRating) {
    if (this.ratingCount == 0) {
      this.rating = newRating;
    } else {
      // 计算加权平均评分
      BigDecimal totalScore = this.rating.multiply(BigDecimal.valueOf(this.ratingCount));
      totalScore = totalScore.add(newRating);
      this.rating =
          totalScore.divide(
              BigDecimal.valueOf(this.ratingCount + 1), 2, java.math.RoundingMode.HALF_UP);
    }
    this.ratingCount++;
    this.updatedAt = LocalDateTime.now();
  }

  /** 检查是否为免费量表 */
  public boolean isFree() {
    return price.compareTo(BigDecimal.ZERO) == 0;
  }

  /** 检查是否为付费量表 */
  public boolean isPremium() {
    return visibility == Visibility.PREMIUM || price.compareTo(BigDecimal.ZERO) > 0;
  }

  /** 检查是否可用 */
  public boolean isAvailable() {
    return status == ScaleStatus.ACTIVE
        && (deprecatedAt == null || deprecatedAt.isAfter(LocalDateTime.now()));
  }

  /** 发布量表 */
  public void publish() {
    this.status = ScaleStatus.ACTIVE;
    this.publishedAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  /** 弃用量表 */
  public void deprecate() {
    this.status = ScaleStatus.DEPRECATED;
    this.deprecatedAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  @PreUpdate
  protected void onUpdate() {
    this.updatedAt = LocalDateTime.now();
  }
}
