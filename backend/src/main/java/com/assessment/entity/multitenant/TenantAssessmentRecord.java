package com.assessment.entity.multitenant;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/** 租户评估记录实体（按租户分区） 存储具体的评估数据和结果 */
@Entity
@Table(
    name = "tenant_assessment_records",
    uniqueConstraints = @UniqueConstraint(columnNames = {"tenantId", "recordNumber"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantAssessmentRecord {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(length = 36)
  private String id;

  /** 租户ID（分区键） */
  @Column(nullable = false, length = 36)
  private String tenantId;

  // 基本信息
  @Column(nullable = false, length = 100)
  private String recordNumber; // 租户内唯一

  @Column(nullable = false, length = 36)
  private String subjectId;

  @Column(nullable = false, length = 36)
  private String scaleId; // 引用global_scale_registry或tenant_custom_scales

  @Enumerated(EnumType.STRING)
  @Column(nullable = false, length = 20)
  private ScaleType scaleType;

  @Column(nullable = false, length = 36)
  private String assessorId;

  // 评估信息
  @Column(nullable = false)
  private LocalDateTime assessmentDate;

  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private AssessmentType assessmentType = AssessmentType.REGULAR;

  // 表单数据
  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT", nullable = false)
  private JsonNode formData;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT")
  private JsonNode scoreData;

  @Column(precision = 10, scale = 2)
  private BigDecimal totalScore;

  @Column(length = 50)
  private String resultLevel;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT")
  @Builder.Default
  private JsonNode resultDetails = null;

  // 工作流状态
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private RecordStatus status = RecordStatus.DRAFT;

  @Column(length = 50)
  private String workflowStage;

  // 审核信息
  @Column(length = 36)
  private String reviewerId;

  @Column(columnDefinition = "TEXT")
  private String reviewNotes;

  private LocalDateTime reviewedAt;

  // 质量控制
  @Column(precision = 3, scale = 2)
  private BigDecimal qualityScore; // 评估质量评分

  @Column(precision = 3, scale = 2)
  private BigDecimal completenessScore; // 完整性评分

  // 审计字段
  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime createdAt = LocalDateTime.now();

  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime updatedAt = LocalDateTime.now();

  @Column(length = 36)
  private String createdBy;

  @Column(length = 36)
  private String updatedBy;

  /** Copy constructor for defensive copying */
  public TenantAssessmentRecord(TenantAssessmentRecord other) {
    if (other != null) {
      this.id = other.id;
      this.tenantId = other.tenantId;
      this.recordNumber = other.recordNumber;
      this.subjectId = other.subjectId;
      this.scaleId = other.scaleId;
      this.scaleType = other.scaleType;
      this.assessorId = other.assessorId;
      this.assessmentDate = other.assessmentDate;
      this.assessmentType = other.assessmentType;
      this.formData = other.formData != null ? other.formData.deepCopy() : null;
      this.scoreData = other.scoreData != null ? other.scoreData.deepCopy() : null;
      this.totalScore = other.totalScore;
      this.resultLevel = other.resultLevel;
      this.resultDetails = other.resultDetails != null ? other.resultDetails.deepCopy() : null;
      this.status = other.status;
      this.workflowStage = other.workflowStage;
      this.reviewerId = other.reviewerId;
      this.reviewNotes = other.reviewNotes;
      this.reviewedAt = other.reviewedAt;
      this.qualityScore = other.qualityScore;
      this.completenessScore = other.completenessScore;
      this.createdAt = other.createdAt;
      this.updatedAt = other.updatedAt;
      this.createdBy = other.createdBy;
      this.updatedBy = other.updatedBy;
    }
  }

  /** 量表类型枚举 */
  public enum ScaleType {
    GLOBAL("全局量表"),
    CUSTOM("自定义量表");

    private final String displayName;

    ScaleType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  /** 评估类型枚举 */
  public enum AssessmentType {
    REGULAR("常规评估"),
    FOLLOWUP("跟踪评估"),
    EMERGENCY("紧急评估"),
    REASSESSMENT("重新评估");

    private final String displayName;

    AssessmentType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  /** 记录状态枚举 */
  public enum RecordStatus {
    DRAFT("草稿"),
    SUBMITTED("已提交"),
    REVIEWED("已审核"),
    APPROVED("已批准"),
    ARCHIVED("已归档"),
    REJECTED("已拒绝");

    private final String displayName;

    RecordStatus(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 业务方法

  /** 提交评估记录 */
  public void submit() {
    if (status != RecordStatus.DRAFT) {
      throw new IllegalStateException("只能提交草稿状态的记录");
    }
    this.status = RecordStatus.SUBMITTED;
    this.updatedAt = LocalDateTime.now();
  }

  /** 审核通过 */
  public void approve(String reviewNotes, String reviewerId) {
    if (status != RecordStatus.SUBMITTED) {
      throw new IllegalStateException("只能审核已提交的记录");
    }
    this.status = RecordStatus.APPROVED;
    this.reviewNotes = reviewNotes;
    this.reviewerId = reviewerId;
    this.reviewedAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  /** 审核拒绝 */
  public void reject(String reviewNotes, String reviewerId) {
    if (status != RecordStatus.SUBMITTED) {
      throw new IllegalStateException("只能审核已提交的记录");
    }
    this.status = RecordStatus.REJECTED;
    this.reviewNotes = reviewNotes;
    this.reviewerId = reviewerId;
    this.reviewedAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  /** 归档记录 */
  public void archive() {
    if (status != RecordStatus.APPROVED) {
      throw new IllegalStateException("只能归档已批准的记录");
    }
    this.status = RecordStatus.ARCHIVED;
    this.updatedAt = LocalDateTime.now();
  }

  /** 检查是否可以编辑 */
  public boolean isEditable() {
    return status == RecordStatus.DRAFT || status == RecordStatus.REJECTED;
  }

  /** 检查是否已完成 */
  public boolean isCompleted() {
    return status == RecordStatus.APPROVED || status == RecordStatus.ARCHIVED;
  }

  /** 检查是否需要审核 */
  public boolean needsReview() {
    return status == RecordStatus.SUBMITTED;
  }

  /** 计算评估完成度 */
  public double getCompletionPercentage() {
    if (formData == null) {
      return 0.0;
    }

    // 这里需要根据量表Schema计算完成度
    // 具体实现在Service层
    return completenessScore != null ? completenessScore.doubleValue() * 100 : 0.0;
  }

  /** 获取评估耗时（分钟） */
  public Long getDurationMinutes() {
    return java.time.Duration.between(
            createdAt, status == RecordStatus.DRAFT ? LocalDateTime.now() : updatedAt)
        .toMinutes();
  }

  /** 更新质量评分 */
  public void updateQualityScore(BigDecimal qualityScore, BigDecimal completenessScore) {
    this.qualityScore = qualityScore;
    this.completenessScore = completenessScore;
    this.updatedAt = LocalDateTime.now();
  }

  /** 设置工作流阶段 */
  public void setWorkflowStage(String stage) {
    this.workflowStage = stage;
    this.updatedAt = LocalDateTime.now();
  }

  @PreUpdate
  protected void onUpdate() {
    this.updatedAt = LocalDateTime.now();
  }
}
