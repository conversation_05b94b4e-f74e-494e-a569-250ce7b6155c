package com.assessment.entity.multitenant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 平台用户实体 跨租户的用户基本信息 */
@Entity
@Table(
    name = "platform_users",
    indexes = {
      @Index(name = "idx_platform_users_username", columnList = "username"),
      @Index(name = "idx_platform_users_email", columnList = "email"),
      @Index(name = "idx_platform_users_active", columnList = "isActive")
    })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformUser {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(length = 36)
  private java.util.UUID id;

  // 基本信息
  @Column(nullable = false, unique = true, length = 100)
  private String username;

  @Column(nullable = false, unique = true, length = 100)
  private String email;

  @Column(nullable = false, length = 255)
  private String passwordHash;

  // 个人信息
  @Column(length = 100)
  private String fullName;

  @Column(length = 50)
  private String firstName;

  @Column(length = 50)
  private String lastName;

  @Column(length = 50)
  private String phone;

  @Column(length = 500)
  private String avatarUrl;

  // 平台级别角色
  @Enumerated(EnumType.STRING)
  @Column(length = 20, nullable = false)
  @Builder.Default
  private PlatformRole platformRole = PlatformRole.USER;

  // 状态管理
  @Column(nullable = false)
  @Builder.Default
  private Boolean isActive = true;

  @Column(nullable = false)
  @Builder.Default
  private Boolean emailVerified = false;

  private LocalDateTime lastLoginAt;

  // 审计字段
  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime createdAt = LocalDateTime.now();

  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime updatedAt = LocalDateTime.now();

  /** Copy constructor for defensive copying */
  public PlatformUser(PlatformUser other) {
    if (other != null) {
      this.id = other.id;
      this.username = other.username;
      this.email = other.email;
      this.passwordHash = other.passwordHash;
      this.fullName = other.fullName;
      this.firstName = other.firstName;
      this.lastName = other.lastName;
      this.phone = other.phone;
      this.avatarUrl = other.avatarUrl;
      this.platformRole = other.platformRole;
      this.isActive = other.isActive;
      this.emailVerified = other.emailVerified;
      this.lastLoginAt = other.lastLoginAt;
      this.createdAt = other.createdAt;
      this.updatedAt = other.updatedAt;
    }
  }

  /** 平台角色枚举 */
  public enum PlatformRole {
    ADMIN("平台管理员"),
    USER("普通用户");

    private final String displayName;

    PlatformRole(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 业务方法

  /** 获取全名 */
  public String getFullName() {
    // 优先使用直接设置的全名
    if (fullName != null && !fullName.trim().isEmpty()) {
      return fullName;
    }
    // 否则从名和姓构造
    if (firstName != null && lastName != null) {
      return firstName + " " + lastName;
    } else if (firstName != null) {
      return firstName;
    } else if (lastName != null) {
      return lastName;
    } else {
      return username;
    }
  }

  /** 更新最后登录时间 */
  public void updateLastLoginTime() {
    this.lastLoginAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  /** 验证邮箱 */
  public void verifyEmail() {
    this.emailVerified = true;
    this.updatedAt = LocalDateTime.now();
  }

  /** 激活用户 */
  public void activate() {
    this.isActive = true;
    this.updatedAt = LocalDateTime.now();
  }

  /** 停用用户 */
  public void deactivate() {
    this.isActive = false;
    this.updatedAt = LocalDateTime.now();
  }

  @PreUpdate
  protected void onUpdate() {
    this.updatedAt = LocalDateTime.now();
  }
}
