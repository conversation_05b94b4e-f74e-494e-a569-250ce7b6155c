package com.assessment.entity.multitenant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

/** 评估对象实体（按租户分区） 存储被评估人的基本信息 */
@Entity
@Table(name = "assessment_subjects")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentSubject {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(length = 36)
  private String id;

  /** 租户ID（分区键） */
  @Column(nullable = false, length = 36)
  private String tenantId;

  // 基本信息
  @Column(nullable = false, length = 100)
  private String name;

  @Column(length = 100) // 加密存储身份证号
  private String idNumber;

  @Enumerated(EnumType.STRING)
  @Column(length = 10)
  private Gender gender;

  private LocalDate birthDate;

  // 联系信息
  @Column(length = 50) // 加密存储
  private String phone;

  @Column(columnDefinition = "TEXT")
  private String address;

  @Column(length = 100)
  private String emergencyContactName;

  @Column(length = 50) // 加密存储
  private String emergencyContactPhone;

  // 医疗信息
  @Column(length = 100) // 加密存储
  private String medicalInsuranceNumber;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT") // H2兼容：使用TEXT存储JSON
  @Builder.Default
  private JsonNode medicalHistory = null;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT") // H2兼容：使用TEXT存储JSON
  @Builder.Default
  private JsonNode medications = null;

  @Column(columnDefinition = "VARCHAR(1000)") // H2兼容：使用JSON格式存储数组
  private String allergies; // JSON格式存储过敏信息数组

  // 护理信息
  @Column(length = 50)
  private String currentCareLevel;

  @Column(columnDefinition = "TEXT")
  private String careNotes;

  // 扩展信息（租户自定义字段）
  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "TEXT") // H2兼容：使用TEXT存储JSON
  @Builder.Default
  private JsonNode customFields = null;

  // 状态管理
  @Column(nullable = false)
  @Builder.Default
  private Boolean isActive = true;

  // 审计字段
  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime createdAt = LocalDateTime.now();

  @Column(nullable = false)
  @Builder.Default
  private LocalDateTime updatedAt = LocalDateTime.now();

  @Column(length = 36)
  private String createdBy;

  @Column(length = 36)
  private String updatedBy;

  /** 性别枚举 */
  public enum Gender {
    MALE("男"),
    FEMALE("女"),
    OTHER("其他");

    private final String displayName;

    Gender(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 业务方法

  /** 计算年龄 */
  public Integer getAge() {
    if (birthDate == null) {
      return null;
    }
    return LocalDate.now().getYear() - birthDate.getYear();
  }

  /** 获取年龄组 */
  public String getAgeGroup() {
    Integer age = getAge();
    if (age == null) {
      return "未知";
    }

    if (age < 60) {
      return "60岁以下";
    } else if (age < 70) {
      return "60-69岁";
    } else if (age < 80) {
      return "70-79岁";
    } else if (age < 90) {
      return "80-89岁";
    } else {
      return "90岁以上";
    }
  }

  /** 检查是否有紧急联系人 */
  public boolean hasEmergencyContact() {
    return emergencyContactName != null
        && !emergencyContactName.trim().isEmpty()
        && emergencyContactPhone != null
        && !emergencyContactPhone.trim().isEmpty();
  }

  /** 获取显示名称 */
  public String getDisplayName() {
    StringBuilder sb = new StringBuilder(name);
    if (gender != null) {
      sb.append("(").append(gender.getDisplayName()).append(")");
    }
    Integer age = getAge();
    if (age != null) {
      sb.append(" ").append(age).append("岁");
    }
    return sb.toString();
  }

  /** 添加自定义字段值 */
  public void setCustomField(String fieldName, Object value) {
    if (customFields == null) {
      ObjectMapper mapper = new ObjectMapper();
      customFields = mapper.createObjectNode();
    }

    if (customFields instanceof ObjectNode) {
      ObjectNode objectNode = (ObjectNode) customFields;
      if (value instanceof String) {
        objectNode.put(fieldName, (String) value);
      } else if (value instanceof Integer) {
        objectNode.put(fieldName, (Integer) value);
      } else if (value instanceof Boolean) {
        objectNode.put(fieldName, (Boolean) value);
      } else if (value != null) {
        objectNode.put(fieldName, value.toString());
      }
    }
  }

  /** 获取自定义字段值 */
  public String getCustomField(String fieldName) {
    if (customFields != null && customFields.has(fieldName)) {
      return customFields.get(fieldName).asText();
    }
    return null;
  }

  /** 激活评估对象 */
  public void activate() {
    this.isActive = true;
    this.updatedAt = LocalDateTime.now();
  }

  /** 停用评估对象 */
  public void deactivate() {
    this.isActive = false;
    this.updatedAt = LocalDateTime.now();
  }

  @PreUpdate
  protected void onUpdate() {
    this.updatedAt = LocalDateTime.now();
  }
}
