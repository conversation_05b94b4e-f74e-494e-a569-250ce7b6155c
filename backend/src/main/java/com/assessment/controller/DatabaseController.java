package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import com.assessment.service.DatabaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** 数据库操作控制器 */
@RestController
@RequestMapping("/api/database")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Database", description = "数据库操作接口")
public class DatabaseController {

  private final DatabaseService databaseService;

  /** 执行DDL语句创建表 */
  @PostMapping("/execute-ddl")
  @Operation(summary = "执行DDL语句", description = "执行数据库DDL语句创建表结构")
  public ResponseEntity<ApiResponse<ExecuteDDLResult>> executeDDL(
      @RequestBody ExecuteDDLRequest request) {

    log.info("开始执行DDL: 表名={}", request.getTableName());

    try {
      // 验证请求参数
      if (request.getSql() == null || request.getSql().trim().isEmpty()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("SQL语句不能为空"));
      }

      if (request.getTableName() == null || request.getTableName().trim().isEmpty()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("表名不能为空"));
      }

      // 执行DDL
      ExecuteDDLResult result = databaseService.executeDDL(request);

      log.info("DDL执行完成: 表名={}, 成功={}", request.getTableName(), result.getSuccess());

      return ResponseEntity.ok(ApiResponse.success(result));

    } catch (Exception e) {
      log.error("执行DDL失败", e);
      return ResponseEntity.ok(ApiResponse.error("执行DDL失败: " + e.getMessage()));
    }
  }

  /** 检查表是否存在 */
  @GetMapping("/table-exists/{tableName}")
  @Operation(summary = "检查表是否存在")
  public ResponseEntity<ApiResponse<Boolean>> checkTableExists(@PathVariable String tableName) {
    try {
      boolean exists = databaseService.tableExists(tableName);
      return ResponseEntity.ok(ApiResponse.success(exists));
    } catch (Exception e) {
      log.error("检查表存在性失败", e);
      return ResponseEntity.ok(ApiResponse.error("检查表存在性失败: " + e.getMessage()));
    }
  }

  /** 获取表结构信息 */
  @GetMapping("/table-structure/{tableName}")
  @Operation(summary = "获取表结构信息")
  public ResponseEntity<ApiResponse<Object>> getTableStructure(@PathVariable String tableName) {
    try {
      Object structure = databaseService.getTableStructure(tableName);
      return ResponseEntity.ok(ApiResponse.success(structure));
    } catch (Exception e) {
      log.error("获取表结构失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取表结构失败: " + e.getMessage()));
    }
  }
}
