package com.assessment.controller;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** 多租户量表管理控制器 */
@Slf4j
@RestController
@RequestMapping("/api/multi-tenant/scales")
@RequiredArgsConstructor
@Tag(name = "多租户量表管理", description = "全局量表注册中心管理接口")
public class MultiTenantScaleController {

  private final GlobalScaleRegistryRepository scaleRepository;

  /** 获取公开可用的量表列表 */
  @GetMapping("/public")
  @Operation(summary = "获取公开量表", description = "获取所有公开可见的评估量表")
  @ApiResponse(responseCode = "200", description = "成功获取量表列表")
  public ResponseEntity<Map<String, Object>> getPublicScales(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "20") int size,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) String search) {

    try {
      Pageable pageable = PageRequest.of(page, size, Sort.by("usageCount").descending());
      Page<GlobalScaleRegistry> scalesPage;

      if (search != null && !search.trim().isEmpty()) {
        scalesPage =
            scaleRepository.findByNameContainingAndVisibilityAndStatus(
                search,
                GlobalScaleRegistry.Visibility.PUBLIC,
                GlobalScaleRegistry.ScaleStatus.ACTIVE,
                pageable);
      } else if (category != null && !category.trim().isEmpty()) {
        scalesPage =
            scaleRepository.findByCategoryAndVisibilityAndStatus(
                category,
                GlobalScaleRegistry.Visibility.PUBLIC,
                GlobalScaleRegistry.ScaleStatus.ACTIVE,
                pageable);
      } else {
        scalesPage =
            scaleRepository.findByVisibilityAndStatus(
                GlobalScaleRegistry.Visibility.PUBLIC,
                GlobalScaleRegistry.ScaleStatus.ACTIVE,
                pageable);
      }

      Map<String, Object> response =
          Map.of(
              "content", scalesPage.getContent(),
              "totalElements", scalesPage.getTotalElements(),
              "totalPages", scalesPage.getTotalPages(),
              "size", scalesPage.getSize(),
              "number", scalesPage.getNumber());

      return ResponseEntity.ok(response);
    } catch (Exception e) {
      log.error("获取公开量表失败", e);
      return ResponseEntity.badRequest().body(Map.of("error", "获取量表列表失败: " + e.getMessage()));
    }
  }

  /** 获取量表详情 */
  @GetMapping("/{scaleId}")
  @Operation(summary = "获取量表详情", description = "获取指定量表的详细信息")
  public ResponseEntity<Object> getScaleDetails(@PathVariable String scaleId) {
    try {
      GlobalScaleRegistry scale = scaleRepository.findById(scaleId).orElse(null);

      if (scale == null) {
        return ResponseEntity.notFound().build();
      }

      // 只返回公开可见的量表或租户有权限的量表
      if (scale.getVisibility() != GlobalScaleRegistry.Visibility.PUBLIC) {
        return ResponseEntity.notFound().build();
      }

      return ResponseEntity.ok(scale);
    } catch (Exception e) {
      log.error("获取量表详情失败: {}", scaleId, e);
      return ResponseEntity.badRequest().body(Map.of("error", "获取量表详情失败: " + e.getMessage()));
    }
  }

  /** 获取量表分类列表 */
  @GetMapping("/categories")
  @Operation(summary = "获取量表分类", description = "获取所有可用的量表分类")
  public ResponseEntity<List<String>> getCategories() {
    try {
      List<String> categories = scaleRepository.findDistinctCategories();
      return ResponseEntity.ok(categories);
    } catch (Exception e) {
      log.error("获取量表分类失败", e);
      return ResponseEntity.badRequest().build();
    }
  }

  /** 增加量表使用次数 */
  @PostMapping("/{scaleId}/usage")
  @Operation(summary = "记录量表使用", description = "增加量表的使用计数")
  public ResponseEntity<Object> incrementUsage(@PathVariable String scaleId) {
    try {
      GlobalScaleRegistry scale = scaleRepository.findById(scaleId).orElse(null);

      if (scale == null) {
        return ResponseEntity.notFound().build();
      }

      scale.incrementUsage();
      scaleRepository.save(scale);

      return ResponseEntity.ok(Map.of("message", "使用次数已更新"));
    } catch (Exception e) {
      log.error("更新量表使用次数失败: {}", scaleId, e);
      return ResponseEntity.badRequest().body(Map.of("error", "更新使用次数失败: " + e.getMessage()));
    }
  }

  /** 获取热门量表 */
  @GetMapping("/popular")
  @Operation(summary = "获取热门量表", description = "获取使用次数最多的量表")
  public ResponseEntity<List<GlobalScaleRegistry>> getPopularScales(
      @RequestParam(defaultValue = "10") int limit) {
    try {
      Pageable pageable = PageRequest.of(0, limit, Sort.by("usageCount").descending());
      List<GlobalScaleRegistry> popularScales =
          scaleRepository
              .findByVisibilityAndStatus(
                  GlobalScaleRegistry.Visibility.PUBLIC,
                  GlobalScaleRegistry.ScaleStatus.ACTIVE,
                  pageable)
              .getContent();

      return ResponseEntity.ok(popularScales);
    } catch (Exception e) {
      log.error("获取热门量表失败", e);
      return ResponseEntity.badRequest().build();
    }
  }
}
