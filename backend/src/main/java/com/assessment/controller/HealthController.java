package com.assessment.controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/health")
public class HealthController {

  @GetMapping
  public Map<String, Object> health() {
    Map<String, Object> response = new HashMap<>();
    response.put("status", "OK");
    response.put("timestamp", LocalDateTime.now());
    response.put("message", "智慧养老评估平台后端服务运行正常");
    return response;
  }

  @GetMapping("/info")
  public Map<String, Object> info() {
    Map<String, Object> response = new HashMap<>();
    response.put("name", "Elderly Assessment Platform");
    response.put("version", "1.0.0-SNAPSHOT");
    response.put("description", "智慧养老评估平台后端服务");
    response.put("java.version", System.getProperty("java.version"));
    return response;
  }
}
