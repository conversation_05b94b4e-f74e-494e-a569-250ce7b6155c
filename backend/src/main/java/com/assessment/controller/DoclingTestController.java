package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.PDFFormatInfo;
import com.assessment.service.DoclingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/** Docling服务测试控制器 用于测试Docling Docker集成功能 */
@RestController
@RequestMapping("/api/docling")
@Tag(name = "Docling测试", description = "Docling Docker服务测试接口")
@Slf4j
public class DoclingTestController {

  @Autowired private DoclingService doclingService;

  /** 检查Docling服务状态 */
  @GetMapping("/health")
  @Operation(summary = "检查Docling服务状态")
  public ResponseEntity<ApiResponse<String>> checkHealth() {
    try {
      boolean available = doclingService.isAvailable();
      String status = available ? "Docling服务可用" : "Docling服务不可用";

      if (available) {
        String serviceInfo = doclingService.getServiceInfo();
        return ResponseEntity.ok(ApiResponse.success(serviceInfo));
      } else {
        return ResponseEntity.ok(ApiResponse.error(status));
      }
    } catch (Exception e) {
      log.error("检查Docling服务状态失败", e);
      return ResponseEntity.ok(ApiResponse.error("检查服务状态失败: " + e.getMessage()));
    }
  }

  /** 上传并转换文档 - 兼容前端upload接口 */
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @Operation(summary = "上传并转换文档")
  public ResponseEntity<ApiResponse<PDFFormatInfo>> uploadAndConvert(
      @RequestParam MultipartFile file, @RequestParam(defaultValue = "markdown") String format) {

    if (file.isEmpty()) {
      return ResponseEntity.badRequest().body(ApiResponse.error("请选择文件"));
    }

    // 检查支持的输入格式
    if (!isSupportedInputFormat(file)) {
      return ResponseEntity.badRequest()
          .body(
              ApiResponse.error(
                  "不支持的文件格式。支持的格式：PDF, DOCX, XLSX, HTML, 图片文件(PNG, JPG, JPEG, GIF, BMP, TIFF)"));
    }

    // 检查支持的输出格式
    if (!isSupportedOutputFormat(format)) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("不支持的输出格式。支持的格式：markdown, html, json"));
    }

    try {
      log.info("开始上传并转换文档: {}, 输出格式: {}", file.getOriginalFilename(), format);
      PDFFormatInfo result = doclingService.convertDocumentWithInfo(file, format);

      return ResponseEntity.ok(ApiResponse.success(result));

    } catch (Exception e) {
      log.error("文档转换失败", e);
      return ResponseEntity.ok(ApiResponse.error("文档转换失败: " + e.getMessage()));
    }
  }

  /** 转换PDF为Markdown */
  @PostMapping(value = "/convert", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @Operation(summary = "转换PDF为Markdown")
  public ResponseEntity<ApiResponse<String>> convertPdfToMarkdown(
      @RequestParam MultipartFile file) {

    if (file.isEmpty()) {
      return ResponseEntity.badRequest().body(ApiResponse.error("请选择PDF文件"));
    }

    String originalFilename = file.getOriginalFilename();
    if (originalFilename == null || !originalFilename.toLowerCase(Locale.ROOT).endsWith(".pdf")) {
      return ResponseEntity.badRequest().body(ApiResponse.error("请上传PDF格式文件"));
    }

    try {
      log.info("开始转换PDF文件: {}", originalFilename);
      String markdown = doclingService.convertPdfToMarkdown(file);

      return ResponseEntity.ok(ApiResponse.success(markdown));

    } catch (Exception e) {
      log.error("PDF转换失败", e);
      return ResponseEntity.ok(ApiResponse.error("PDF转换失败: " + e.getMessage()));
    }
  }

  /** 转换文档为指定格式并返回详细信息 */
  @PostMapping(value = "/convert-with-info", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @Operation(summary = "转换文档为指定格式并返回详细信息")
  public ResponseEntity<ApiResponse<PDFFormatInfo>> convertDocumentWithInfo(
      @RequestParam MultipartFile file,
      @RequestParam(defaultValue = "markdown") String outputFormat) {

    if (file.isEmpty()) {
      return ResponseEntity.badRequest().body(ApiResponse.error("请选择文件"));
    }

    // 检查支持的输入格式
    if (!isSupportedInputFormat(file)) {
      return ResponseEntity.badRequest()
          .body(
              ApiResponse.error(
                  "不支持的文件格式。支持的格式：PDF, DOCX, XLSX, HTML, 图片文件(PNG, JPG, JPEG, GIF, BMP, TIFF)"));
    }

    // 检查支持的输出格式
    if (!isSupportedOutputFormat(outputFormat)) {
      return ResponseEntity.badRequest()
          .body(ApiResponse.error("不支持的输出格式。支持的格式：markdown, html, json"));
    }

    try {
      log.info("开始转换文档（包含详细信息）: {}, 输出格式: {}", file.getOriginalFilename(), outputFormat);
      PDFFormatInfo result = doclingService.convertDocumentWithInfo(file, outputFormat);

      return ResponseEntity.ok(ApiResponse.success(result));

    } catch (Exception e) {
      log.error("文档转换失败", e);
      return ResponseEntity.ok(ApiResponse.error("文档转换失败: " + e.getMessage()));
    }
  }

  /** 检查是否为支持的输入格式 */
  private boolean isSupportedInputFormat(MultipartFile file) {
    String filename = file.getOriginalFilename();
    if (filename == null) return false;

    String extension = filename.toLowerCase(Locale.ROOT);
    String contentType = file.getContentType();

    // 支持的文件扩展名
    String[] supportedExtensions = {
      ".pdf", ".docx", ".xlsx", ".html", ".htm", ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff"
    };

    // 支持的MIME类型
    String[] supportedMimeTypes = {
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/html",
      "image/png",
      "image/jpeg",
      "image/gif",
      "image/bmp",
      "image/tiff"
    };

    // 检查扩展名
    for (String ext : supportedExtensions) {
      if (extension.endsWith(ext)) return true;
    }

    // 检查MIME类型
    if (contentType != null) {
      for (String mimeType : supportedMimeTypes) {
        if (contentType.equals(mimeType)) return true;
      }
    }

    return false;
  }

  /** 检查是否为支持的输出格式 */
  private boolean isSupportedOutputFormat(String outputFormat) {
    String[] supportedFormats = {"markdown", "html", "json"};
    for (String format : supportedFormats) {
      if (format.equalsIgnoreCase(outputFormat)) return true;
    }
    return false;
  }
}
