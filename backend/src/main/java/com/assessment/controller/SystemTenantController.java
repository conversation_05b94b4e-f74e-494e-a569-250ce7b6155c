package com.assessment.controller;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.repository.multitenant.TenantRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** 系统租户管理控制器 仅系统管理员可访问 */
@Slf4j
@RestController
@RequestMapping("/api/system/tenants")
@RequiredArgsConstructor
@Tag(name = "系统租户管理", description = "系统级租户管理接口，仅管理员可访问")
@PreAuthorize("hasRole('ADMIN')")
public class SystemTenantController {

  private final TenantRepository tenantRepository;

  /** 获取租户列表 */
  @GetMapping
  @Operation(summary = "获取租户列表", description = "分页查询所有租户信息")
  @ApiResponses(
      value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "成功获取租户列表")
      })
  public ResponseEntity<ApiResponse<Map<String, Object>>> getTenants(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "20") int size,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) String sortField,
      @RequestParam(required = false) String sortOrder) {

    try {
      // 构建排序
      Sort sort = Sort.by(Sort.Direction.DESC, "createdAt"); // 默认按创建时间倒序
      if (sortField != null && !sortField.isEmpty()) {
        Sort.Direction direction =
            "ASC".equalsIgnoreCase(sortOrder) ? Sort.Direction.ASC : Sort.Direction.DESC;
        sort = Sort.by(direction, sortField);
      }

      Pageable pageable = PageRequest.of(page, size, sort);
      Page<Tenant> tenantsPage;

      // 根据条件查询
      if (search != null
          && !search.trim().isEmpty()
          && status != null
          && !status.trim().isEmpty()) {
        // 同时有搜索和状态条件
        Tenant.TenantStatus tenantStatus =
            Tenant.TenantStatus.valueOf(status.toUpperCase(Locale.ROOT));
        tenantsPage =
            tenantRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCaseAndStatus(
                search, search, tenantStatus, pageable);
      } else if (search != null && !search.trim().isEmpty()) {
        // 只有搜索条件
        tenantsPage =
            tenantRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
                search, search, pageable);
      } else if (status != null && !status.trim().isEmpty()) {
        // 只有状态条件
        Tenant.TenantStatus tenantStatus =
            Tenant.TenantStatus.valueOf(status.toUpperCase(Locale.ROOT));
        tenantsPage = tenantRepository.findByStatus(tenantStatus, pageable);
      } else {
        // 无条件查询
        tenantsPage = tenantRepository.findAll(pageable);
      }

      Map<String, Object> response =
          Map.of(
              "content", tenantsPage.getContent(),
              "totalElements", tenantsPage.getTotalElements(),
              "totalPages", tenantsPage.getTotalPages(),
              "size", tenantsPage.getSize(),
              "number", tenantsPage.getNumber());

      return ResponseEntity.ok(ApiResponse.success(response));
    } catch (Exception e) {
      log.error("获取租户列表失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取租户列表失败: " + e.getMessage()));
    }
  }

  /** 获取租户详情 */
  @GetMapping("/{id}")
  @Operation(summary = "获取租户详情", description = "根据ID获取租户详细信息")
  public ResponseEntity<ApiResponse<Tenant>> getTenant(@PathVariable String id) {
    try {
      UUID tenantId = UUID.fromString(id);
      Tenant tenant =
          tenantRepository.findById(tenantId).orElseThrow(() -> new RuntimeException("租户不存在"));

      return ResponseEntity.ok(ApiResponse.success(tenant));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的租户ID格式"));
    } catch (Exception e) {
      log.error("获取租户详情失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取租户详情失败: " + e.getMessage()));
    }
  }

  /** 创建租户 */
  @PostMapping
  @Operation(summary = "创建租户", description = "创建新的租户")
  public ResponseEntity<ApiResponse<Tenant>> createTenant(
      @Valid @RequestBody CreateTenantRequest request) {
    try {
      // 检查租户代码是否已存在
      if (tenantRepository.existsByCode(request.getCode())) {
        return ResponseEntity.badRequest().body(ApiResponse.error("租户代码已存在"));
      }

      // 创建租户
      Tenant tenant =
          Tenant.builder()
              .code(request.getCode().toUpperCase(Locale.ROOT))
              .name(request.getName())
              .description(request.getDescription())
              .logoUrl(request.getLogoUrl())
              .contactEmail(request.getContactEmail())
              .contactPhone(request.getContactPhone())
              .address(request.getAddress())
              .status(
                  request.getStatus() != null ? request.getStatus() : Tenant.TenantStatus.ACTIVE)
              .subscriptionPlan(
                  request.getSubscriptionPlan() != null
                      ? request.getSubscriptionPlan()
                      : Tenant.SubscriptionPlan.BASIC)
              .subscriptionExpiresAt(request.getSubscriptionExpiresAt())
              .build();

      // 设置审计字段
      tenant.setCreatedAt(LocalDateTime.now());
      tenant.setUpdatedAt(LocalDateTime.now());

      Tenant savedTenant = tenantRepository.save(tenant);
      log.info("租户创建成功: {}", savedTenant.getCode());

      return ResponseEntity.ok(ApiResponse.success(savedTenant, "租户创建成功"));
    } catch (Exception e) {
      log.error("创建租户失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("创建租户失败: " + e.getMessage()));
    }
  }

  /** 更新租户 */
  @PutMapping("/{id}")
  @Operation(summary = "更新租户", description = "更新租户信息")
  public ResponseEntity<ApiResponse<Tenant>> updateTenant(
      @PathVariable String id, @Valid @RequestBody UpdateTenantRequest request) {
    try {
      UUID tenantId = UUID.fromString(id);
      Tenant tenant =
          tenantRepository.findById(tenantId).orElseThrow(() -> new RuntimeException("租户不存在"));

      // 更新租户信息
      tenant.setName(request.getName());
      tenant.setDescription(request.getDescription());
      tenant.setLogoUrl(request.getLogoUrl());
      tenant.setContactEmail(request.getContactEmail());
      tenant.setContactPhone(request.getContactPhone());
      tenant.setAddress(request.getAddress());

      if (request.getStatus() != null) {
        tenant.setStatus(request.getStatus());
      }
      if (request.getSubscriptionPlan() != null) {
        tenant.setSubscriptionPlan(request.getSubscriptionPlan());
      }
      if (request.getSubscriptionExpiresAt() != null) {
        tenant.setSubscriptionExpiresAt(request.getSubscriptionExpiresAt());
      }

      tenant.setUpdatedAt(LocalDateTime.now());

      Tenant updatedTenant = tenantRepository.save(tenant);
      log.info("租户更新成功: {}", updatedTenant.getCode());

      return ResponseEntity.ok(ApiResponse.success(updatedTenant, "租户更新成功"));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的租户ID格式"));
    } catch (Exception e) {
      log.error("更新租户失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("更新租户失败: " + e.getMessage()));
    }
  }

  /** 切换租户状态 */
  @PatchMapping("/{id}/toggle")
  @Operation(summary = "切换租户状态", description = "启用或禁用租户")
  public ResponseEntity<ApiResponse<Tenant>> toggleTenant(
      @PathVariable String id, @RequestBody ToggleTenantRequest request) {
    try {
      UUID tenantId = UUID.fromString(id);
      Tenant tenant =
          tenantRepository.findById(tenantId).orElseThrow(() -> new RuntimeException("租户不存在"));

      // 切换状态
      if (request.isActive()) {
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
      } else {
        tenant.setStatus(Tenant.TenantStatus.SUSPENDED);
      }
      tenant.setUpdatedAt(LocalDateTime.now());

      Tenant updatedTenant = tenantRepository.save(tenant);
      String statusText = request.isActive() ? "启用" : "暂停";
      log.info("租户{}成功: {}", statusText, updatedTenant.getCode());

      return ResponseEntity.ok(ApiResponse.success(updatedTenant, "租户" + statusText + "成功"));
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body(ApiResponse.error("无效的租户ID格式"));
    } catch (Exception e) {
      log.error("切换租户状态失败: {}", id, e);
      return ResponseEntity.badRequest().body(ApiResponse.error("操作失败: " + e.getMessage()));
    }
  }

  /** 获取租户统计信息 */
  @GetMapping("/stats")
  @Operation(summary = "获取租户统计", description = "获取租户统计信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getTenantStats() {
    try {
      long totalTenants = tenantRepository.count();
      long activeTenants = tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE);
      long suspendedTenants = tenantRepository.countByStatus(Tenant.TenantStatus.SUSPENDED);
      long disabledTenants = tenantRepository.countByStatus(Tenant.TenantStatus.DISABLED);

      Map<String, Object> stats =
          Map.of(
              "totalTenants", totalTenants,
              "activeTenants", activeTenants,
              "suspendedTenants", suspendedTenants,
              "disabledTenants", disabledTenants);

      return ResponseEntity.ok(ApiResponse.success(stats));

    } catch (Exception e) {
      log.error("获取租户统计信息失败", e);
      return ResponseEntity.badRequest().body(ApiResponse.error("获取租户统计信息失败"));
    }
  }

  // DTO类
  public static class CreateTenantRequest {
    private String code;
    private String name;
    private String description;
    private String logoUrl;
    private String contactEmail;
    private String contactPhone;
    private String address;
    private Tenant.TenantStatus status;
    private Tenant.SubscriptionPlan subscriptionPlan;
    private LocalDateTime subscriptionExpiresAt;

    // Getters and Setters
    public String getCode() {
      return code;
    }

    public void setCode(String code) {
      this.code = code;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getDescription() {
      return description;
    }

    public void setDescription(String description) {
      this.description = description;
    }

    public String getLogoUrl() {
      return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
      this.logoUrl = logoUrl;
    }

    public String getContactEmail() {
      return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
      this.contactEmail = contactEmail;
    }

    public String getContactPhone() {
      return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
      this.contactPhone = contactPhone;
    }

    public String getAddress() {
      return address;
    }

    public void setAddress(String address) {
      this.address = address;
    }

    public Tenant.TenantStatus getStatus() {
      return status;
    }

    public void setStatus(Tenant.TenantStatus status) {
      this.status = status;
    }

    public Tenant.SubscriptionPlan getSubscriptionPlan() {
      return subscriptionPlan;
    }

    public void setSubscriptionPlan(Tenant.SubscriptionPlan subscriptionPlan) {
      this.subscriptionPlan = subscriptionPlan;
    }

    public LocalDateTime getSubscriptionExpiresAt() {
      return subscriptionExpiresAt;
    }

    public void setSubscriptionExpiresAt(LocalDateTime subscriptionExpiresAt) {
      this.subscriptionExpiresAt = subscriptionExpiresAt;
    }
  }

  public static class UpdateTenantRequest {
    private String name;
    private String description;
    private String logoUrl;
    private String contactEmail;
    private String contactPhone;
    private String address;
    private Tenant.TenantStatus status;
    private Tenant.SubscriptionPlan subscriptionPlan;
    private LocalDateTime subscriptionExpiresAt;

    // Getters and Setters
    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getDescription() {
      return description;
    }

    public void setDescription(String description) {
      this.description = description;
    }

    public String getLogoUrl() {
      return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
      this.logoUrl = logoUrl;
    }

    public String getContactEmail() {
      return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
      this.contactEmail = contactEmail;
    }

    public String getContactPhone() {
      return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
      this.contactPhone = contactPhone;
    }

    public String getAddress() {
      return address;
    }

    public void setAddress(String address) {
      this.address = address;
    }

    public Tenant.TenantStatus getStatus() {
      return status;
    }

    public void setStatus(Tenant.TenantStatus status) {
      this.status = status;
    }

    public Tenant.SubscriptionPlan getSubscriptionPlan() {
      return subscriptionPlan;
    }

    public void setSubscriptionPlan(Tenant.SubscriptionPlan subscriptionPlan) {
      this.subscriptionPlan = subscriptionPlan;
    }

    public LocalDateTime getSubscriptionExpiresAt() {
      return subscriptionExpiresAt;
    }

    public void setSubscriptionExpiresAt(LocalDateTime subscriptionExpiresAt) {
      this.subscriptionExpiresAt = subscriptionExpiresAt;
    }
  }

  public static class ToggleTenantRequest {
    private boolean isActive;

    public boolean isActive() {
      return isActive;
    }

    public void setActive(boolean active) {
      isActive = active;
    }
  }
}
