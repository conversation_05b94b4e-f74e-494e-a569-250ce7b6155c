package com.assessment.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(
    origins = {
      "http://localhost:5273",
      "http://localhost:5274",
      "${app.frontend.urls:http://localhost:3000}"
    })
public class TestController {

  @GetMapping("/public")
  public ResponseEntity<String> publicEndpoint() {
    return ResponseEntity.ok("Public endpoint works!");
  }

  @PostMapping("/public-post")
  public ResponseEntity<String> publicPostEndpoint(@RequestBody Object data) {
    return ResponseEntity.ok("Public POST endpoint works! Data: " + data);
  }
}
