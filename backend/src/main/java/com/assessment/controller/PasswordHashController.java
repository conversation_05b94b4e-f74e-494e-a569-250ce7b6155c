package com.assessment.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/hash")
@CrossOrigin(
    origins = {
      "http://localhost:5273",
      "http://localhost:5274",
      "${app.frontend.urls:http://localhost:3000}"
    })
public class PasswordHashController {

  @Autowired private PasswordEncoder passwordEncoder;

  @GetMapping("/password")
  public String hashPassword(@RequestParam String password) {
    return passwordEncoder.encode(password);
  }
}
