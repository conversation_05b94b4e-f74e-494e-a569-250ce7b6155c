package com.assessment.controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class IndexController {

  @GetMapping("/")
  public Map<String, Object> index() {
    Map<String, Object> response = new HashMap<>();
    response.put("status", "running");
    response.put("name", "智慧养老评估平台");
    response.put("version", "1.0.0");
    response.put("timestamp", LocalDateTime.now());
    response.put("message", "欢迎使用智慧养老评估平台API");
    response.put("documentation", "/swagger-ui.html");
    response.put("health", "/api/health");
    return response;
  }
}
