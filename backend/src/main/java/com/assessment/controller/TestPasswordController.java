package com.assessment.controller;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
public class TestPasswordController {

  private final PasswordEncoder passwordEncoder;

  @PostMapping("/generate-hash")
  public Map<String, String> generateHash(@RequestBody Map<String, String> request) {
    String password = request.getOrDefault("password", "password123");
    String hash = passwordEncoder.encode(password);

    return Map.of(
        "password", password,
        "hash", hash,
        "verified", String.valueOf(passwordEncoder.matches(password, hash)));
  }

  @PostMapping("/verify-hash")
  public Map<String, String> verifyHash(@RequestBody Map<String, String> request) {
    String password = request.get("password");
    String hash = request.get("hash");

    boolean matches = passwordEncoder.matches(password, hash);

    return Map.of(
        "password", password,
        "hash", hash,
        "matches", String.valueOf(matches));
  }
}
