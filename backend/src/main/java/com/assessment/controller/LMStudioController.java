package com.assessment.controller;

import com.assessment.config.LMStudioConfig;
import com.assessment.dto.ApiResponse;
import com.assessment.service.LMStudioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** LM Studio管理控制器 提供LM Studio服务和模型的管理接口 */
@RestController
@RequestMapping("/api/lm-studio")
@Slf4j
@Tag(name = "LM Studio Management", description = "LM Studio服务和模型管理接口")
public class LMStudioController {

  private final LMStudioService lmStudioService;
  private final LMStudioConfig lmStudioConfig;

  public LMStudioController(LMStudioService lmStudioService, LMStudioConfig lmStudioConfig) {
    this.lmStudioService = lmStudioService;
    this.lmStudioConfig = new LMStudioConfig(lmStudioConfig);
  }

  /** 获取当前LM Studio服务状态 */
  @GetMapping("/status")
  @Operation(summary = "获取LM Studio服务状态")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getServiceStatus() {
    try {
      Map<String, Object> status =
          Map.of(
              "available", lmStudioService.isServiceAvailable(),
              "currentServer", lmStudioService.getCurrentServerUrl(),
              "currentModel",
                  lmStudioService.getCurrentModel() != null
                      ? lmStudioService.getCurrentModel().getDisplayName()
                      : "未选择模型",
              "lastCheck", System.currentTimeMillis());

      return ResponseEntity.ok(ApiResponse.success(status));
    } catch (Exception e) {
      log.error("获取LM Studio服务状态失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取服务状态失败: " + e.getMessage()));
    }
  }

  /** 获取当前模型详细信息 */
  @GetMapping("/model/current")
  @Operation(summary = "获取当前模型详细信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentModel() {
    try {
      Map<String, Object> modelInfo = lmStudioService.getCurrentModelInfo();
      return ResponseEntity.ok(ApiResponse.success(modelInfo));
    } catch (Exception e) {
      log.error("获取当前模型信息失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取模型信息失败: " + e.getMessage()));
    }
  }

  /** 获取可用模型列表 */
  @GetMapping("/models")
  @Operation(summary = "获取可用模型列表")
  public ResponseEntity<ApiResponse<List<LMStudioConfig.ModelInfo>>> getAvailableModels() {
    try {
      List<LMStudioConfig.ModelInfo> models = lmStudioService.getAvailableModels();
      return ResponseEntity.ok(ApiResponse.success(models));
    } catch (Exception e) {
      log.error("获取可用模型列表失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取模型列表失败: " + e.getMessage()));
    }
  }

  /** 刷新可用模型列表 */
  @PostMapping("/models/refresh")
  @Operation(summary = "刷新可用模型列表")
  public ResponseEntity<ApiResponse<List<LMStudioConfig.ModelInfo>>> refreshModels() {
    try {
      List<LMStudioConfig.ModelInfo> models = lmStudioService.refreshAvailableModels();
      return ResponseEntity.ok(ApiResponse.success(models));
    } catch (Exception e) {
      log.error("刷新模型列表失败", e);
      return ResponseEntity.ok(ApiResponse.error("刷新模型列表失败: " + e.getMessage()));
    }
  }

  /** 切换到指定模型 */
  @PostMapping("/model/switch/{modelId}")
  @Operation(summary = "切换到指定模型")
  public ResponseEntity<ApiResponse<String>> switchModel(@PathVariable String modelId) {
    try {
      boolean success = lmStudioService.switchToModel(modelId);
      if (success) {
        return ResponseEntity.ok(ApiResponse.success("已切换到模型: " + modelId));
      } else {
        return ResponseEntity.ok(ApiResponse.error("切换模型失败，模型可能不存在或不可用"));
      }
    } catch (Exception e) {
      log.error("切换模型失败: {}", modelId, e);
      return ResponseEntity.ok(ApiResponse.error("切换模型失败: " + e.getMessage()));
    }
  }

  /** 切换到指定服务器 */
  @PostMapping("/server/switch")
  @Operation(summary = "切换到指定服务器")
  public ResponseEntity<ApiResponse<String>> switchServer(
      @RequestBody Map<String, String> request) {
    try {
      String serverUrl = request.get("serverUrl");
      if (serverUrl == null || serverUrl.trim().isEmpty()) {
        return ResponseEntity.badRequest().body(ApiResponse.error("服务器地址不能为空"));
      }

      boolean success = lmStudioService.switchToServer(serverUrl.trim());
      if (success) {
        return ResponseEntity.ok(ApiResponse.success("已切换到服务器: " + serverUrl));
      } else {
        return ResponseEntity.ok(ApiResponse.error("切换服务器失败，服务器可能不健康"));
      }
    } catch (Exception e) {
      log.error("切换服务器失败", e);
      return ResponseEntity.ok(ApiResponse.error("切换服务器失败: " + e.getMessage()));
    }
  }

  /** 自动切换到最佳可用服务器 */
  @PostMapping("/server/auto-switch")
  @Operation(summary = "自动切换到最佳可用服务器")
  public ResponseEntity<ApiResponse<String>> autoSwitchServer() {
    try {
      boolean success = lmStudioService.autoSwitchToBestServer();
      if (success) {
        return ResponseEntity.ok(
            ApiResponse.success("已自动切换到最佳服务器: " + lmStudioService.getCurrentServerUrl()));
      } else {
        return ResponseEntity.ok(ApiResponse.error("自动切换失败，没有找到健康的服务器"));
      }
    } catch (Exception e) {
      log.error("自动切换服务器失败", e);
      return ResponseEntity.ok(ApiResponse.error("自动切换失败: " + e.getMessage()));
    }
  }

  /** 获取配置信息 */
  @GetMapping("/config")
  @Operation(summary = "获取LM Studio配置信息")
  public ResponseEntity<ApiResponse<LMStudioConfig>> getConfig() {
    try {
      return ResponseEntity.ok(ApiResponse.success(lmStudioConfig));
    } catch (Exception e) {
      log.error("获取配置信息失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取配置信息失败: " + e.getMessage()));
    }
  }

  /** 获取模型选择配置 */
  @GetMapping("/models/selection-config")
  @Operation(summary = "获取模型选择配置")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getModelSelectionConfig() {
    try {
      Map<String, Object> config =
          Map.of(
              "preferredPatterns", lmStudioConfig.getModels().getSelection().getPreferredPatterns(),
              "excludedPatterns", lmStudioConfig.getModels().getExcludedPatterns(),
              "capabilityInference",
                  lmStudioConfig.getModels().getSelection().getCapabilityInference(),
              "currentModel",
                  lmStudioService.getCurrentModel() != null
                      ? lmStudioService.getCurrentModel()
                      : "未选择模型");

      return ResponseEntity.ok(ApiResponse.success(config));
    } catch (Exception e) {
      log.error("获取模型选择配置失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取模型选择配置失败: " + e.getMessage()));
    }
  }

  /** 获取自动切换配置信息 */
  @GetMapping("/config/auto-switch")
  @Operation(summary = "获取自动切换配置信息")
  public ResponseEntity<ApiResponse<Map<String, Object>>> getAutoSwitchConfig() {
    try {
      Map<String, Object> config =
          Map.of(
              "autoSwitch",
              lmStudioConfig.getAutoSwitch(),
              "note",
              "所有模型参数（temperature、max_tokens等）由LM Studio管理");

      return ResponseEntity.ok(ApiResponse.success(config));
    } catch (Exception e) {
      log.error("获取自动切换配置信息失败", e);
      return ResponseEntity.ok(ApiResponse.error("获取配置信息失败: " + e.getMessage()));
    }
  }
}
