package com.assessment.pdf;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/** PDF内容数据结构 */
@Data
@Builder
public class PDFContent {

  /** 提取的文本内容 */
  private String text;

  /** 提取的表格数据 */
  @lombok.Singular private List<AssessmentTable> tables;

  /** 提取的图像 */
  @lombok.Singular private List<BufferedImage> images;

  /** PDF页面数量 */
  private int pageCount;

  // 手动提供getter和setter方法以实现防御性拷贝
  public List<AssessmentTable> getTables() {
    return tables == null ? null : new ArrayList<>(tables);
  }

  public void setTables(List<AssessmentTable> tables) {
    this.tables = tables == null ? null : new ArrayList<>(tables);
  }

  public List<BufferedImage> getImages() {
    return images == null ? null : new ArrayList<>(images);
  }

  public void setImages(List<BufferedImage> images) {
    this.images = images == null ? null : new ArrayList<>(images);
  }
}
