package com.assessment.pdf;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/** 评估表格数据结构 */
@Data
@Builder
public class AssessmentTable {

  /** 表格类型 */
  private TableType type;

  /** 表格标题 */
  private String title;

  /** 表头 */
  @lombok.Singular private List<String> headers;

  /** 表格数据 */
  @lombok.Singular("dataRow")
  private List<List<String>> data;

  // 手动提供getter和setter方法以实现防御性拷贝
  public List<String> getHeaders() {
    return headers == null ? null : new ArrayList<>(headers);
  }

  public void setHeaders(List<String> headers) {
    this.headers = headers == null ? null : new ArrayList<>(headers);
  }

  public List<List<String>> getData() {
    return data == null ? null : new ArrayList<>(data);
  }

  public void setData(List<List<String>> data) {
    this.data = data == null ? null : new ArrayList<>(data);
  }

  /** 表格在PDF中的位置 */
  private int pageNumber;

  /** 表格类型枚举 */
  public enum TableType {
    SCORE_CRITERIA("评分标准表"),
    QUESTION_OPTIONS("问题选项表"),
    SCORING_RULES("评分规则表"),
    ASSESSMENT_ITEMS("评估项目表"),
    UNKNOWN("未知类型");

    private final String displayName;

    TableType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }
}
