package com.assessment.pdf;

import com.assessment.dto.AssessmentScaleDTO;
import com.assessment.exception.PDFParsingException;
import com.assessment.service.DoclingService;
import com.assessment.service.ScaleAnalysisService;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/** PDF解析核心服务 负责将PDF评估标准文件转换为结构化数据 */
@Service
@Slf4j
public class PDFParserService {

  @Autowired private DoclingService doclingService;

  @Autowired private ScaleAnalysisService scaleAnalysisService;

  private static final String PDF_UPLOAD_DIR = "data/pdf-uploads";

  /** 解析PDF文件并提取评估标准 */
  public AssessmentScaleDTO parsePDFToAssessmentScale(MultipartFile pdfFile) {
    try {
      log.info("开始解析PDF文件: {}", pdfFile.getOriginalFilename());

      // 1. 保存PDF文件
      String filePath = storePDFFile(pdfFile);
      log.debug("PDF文件已保存到: {}", filePath);

      // 2. 使用Docling转换PDF为Markdown
      File file = new File(filePath);
      String markdown = doclingService.convertPdfToMarkdown(file);
      log.debug("PDF转换为Markdown完成，长度: {} 字符", markdown.length());

      // 3. 解析评估标准结构
      AssessmentStructure structure =
          scaleAnalysisService.analyzeMarkdownToStructure(markdown, pdfFile.getOriginalFilename());
      log.debug("评估结构解析完成，章节数量: {}", structure.getSections().size());

      // 4. 生成JSON Schema
      JsonNode schema = scaleAnalysisService.generateJsonSchema(structure);
      log.debug("JSON Schema生成完成");

      // 5. 生成评分规则
      JsonNode scoringRules = scaleAnalysisService.generateScoringRulesJson(structure);
      log.debug("评分规则生成完成");

      // 6. 创建评估量表DTO
      AssessmentScaleDTO scaleDTO =
          createAssessmentScaleDTO(structure, schema, scoringRules, filePath);

      log.info("PDF解析完成: {} -> {}", pdfFile.getOriginalFilename(), scaleDTO.getName());
      return scaleDTO;

    } catch (IOException | IllegalArgumentException e) {
      log.error("PDF解析失败: {}", e.getMessage(), e);
      throw new PDFParsingException("PDF解析失败: " + e.getMessage(), e);
    } catch (RuntimeException e) {
      log.error("PDF解析过程中发生运行时错误: {}", e.getMessage(), e);
      throw new PDFParsingException("PDF解析过程中发生运行时错误: " + e.getMessage(), e);
    }
  }

  /** 保存PDF文件到服务器 */
  private String storePDFFile(MultipartFile pdfFile) throws IOException {
    // 创建上传目录
    Path uploadDir = Paths.get(PDF_UPLOAD_DIR);
    if (!Files.exists(uploadDir)) {
      Files.createDirectories(uploadDir);
    }

    // 生成唯一文件名
    String originalFilename = pdfFile.getOriginalFilename();
    if (originalFilename == null || originalFilename.isEmpty()) {
      throw new IllegalArgumentException("文件名不能为空");
    }

    int lastDotIndex = originalFilename.lastIndexOf(".");
    if (lastDotIndex == -1) {
      throw new IllegalArgumentException("文件必须有扩展名");
    }

    String fileExtension = originalFilename.substring(lastDotIndex);
    String uniqueFilename = UUID.randomUUID().toString() + fileExtension;

    // 保存文件
    Path filePath = uploadDir.resolve(uniqueFilename);
    Files.copy(pdfFile.getInputStream(), filePath);

    return filePath.toString();
  }

  /** 创建评估量表DTO */
  private AssessmentScaleDTO createAssessmentScaleDTO(
      AssessmentStructure structure, JsonNode schema, JsonNode scoringRules, String sourcePdfPath) {

    AssessmentMetadata metadata = structure.getMetadata();

    return AssessmentScaleDTO.builder()
        .name(metadata.getTitle())
        .code(generateScaleCode(metadata.getTitle()))
        .category(convertStringToCategory(metadata.getType()))
        .version(metadata.getVersion())
        .description(metadata.getDescription())
        .formSchema(schema)
        .scoringRules(scoringRules)
        .sourcePdfPath(sourcePdfPath)
        .estimatedDuration(estimateAssessmentDuration(structure))
        .maxScore(calculateMaxScore(structure))
        .minScore(calculateMinScore(structure))
        .build();
  }

  /** 生成量表代码 */
  private String generateScaleCode(String title) {
    // 简单的代码生成逻辑，可以根据需要优化
    String code =
        title.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "").toUpperCase(java.util.Locale.ROOT);
    return code.length() > 20 ? code.substring(0, 20) : code;
  }

  /** 估算评估时长 */
  private Integer estimateAssessmentDuration(AssessmentStructure structure) {
    int questionCount =
        structure.getSections().stream().mapToInt(section -> section.getQuestions().size()).sum();

    // 假设每个问题平均需要30秒
    return Math.max(5, questionCount * 30 / 60);
  }

  /** 计算最大得分 */
  private Integer calculateMaxScore(AssessmentStructure structure) {
    return structure.getScoringRules().getMaxPossibleScore();
  }

  /** 计算最小得分 */
  private Integer calculateMinScore(AssessmentStructure structure) {
    return structure.getScoringRules().getMinPossibleScore();
  }

  /** 将字符串转换为量表分类 */
  private String convertStringToCategory(String typeString) {
    if (typeString == null || typeString.trim().isEmpty()) {
      return "自定义量表";
    }

    // 映射常见的评估类型
    switch (typeString.toLowerCase()) {
      case "elderly":
      case "老年人":
        return "老年人评估";
      case "emotional":
      case "情绪":
        return "情绪评估";
      case "interrai":
        return "interRAI评估";
      case "care":
      case "护理":
        return "护理评估";
      default:
        return typeString;
    }
  }
}
