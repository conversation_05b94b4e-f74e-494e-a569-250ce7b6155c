package com.assessment.dto;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;

/** 评估量表数据传输对象 */
@Data
@Builder
public class AssessmentScaleDTO {

  private String id;
  private String name;
  private String code;
  private String category;
  private String version;
  private String description;
  private JsonNode formSchema;
  private JsonNode scoringRules;
  private JsonNode configOptions;
  private Boolean isActive;
  private Boolean isOfficial;
  private String applicableScope;
  private Integer estimatedDuration;
  private Integer maxScore;
  private Integer minScore;
  private String sourcePdfPath;
  private GlobalScaleRegistry.ScaleStatus status;
  private String parseError;
  private Long usageCount;
}
