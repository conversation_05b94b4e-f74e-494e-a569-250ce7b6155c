package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 统一API响应格式 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
  private boolean success;
  private String message;
  private T data;
  private String errorCode;
  private Long timestamp;

  public static <T> ApiResponse<T> success(T data) {
    ApiResponse<T> response = new ApiResponse<>();
    response.setSuccess(true);
    response.setMessage("操作成功");
    response.setData(data);
    response.setTimestamp(System.currentTimeMillis());
    return response;
  }

  public static <T> ApiResponse<T> success(T data, String message) {
    ApiResponse<T> response = new ApiResponse<>();
    response.setSuccess(true);
    response.setMessage(message);
    response.setData(data);
    response.setTimestamp(System.currentTimeMillis());
    return response;
  }

  public static <T> ApiResponse<T> error(String message) {
    ApiResponse<T> response = new ApiResponse<>();
    response.setSuccess(false);
    response.setMessage(message);
    response.setTimestamp(System.currentTimeMillis());
    return response;
  }

  public static <T> ApiResponse<T> error(String message, String errorCode) {
    ApiResponse<T> response = new ApiResponse<>();
    response.setSuccess(false);
    response.setMessage(message);
    response.setErrorCode(errorCode);
    response.setTimestamp(System.currentTimeMillis());
    return response;
  }
}
