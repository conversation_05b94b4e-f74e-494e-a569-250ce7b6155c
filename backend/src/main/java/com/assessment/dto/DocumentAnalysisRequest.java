package com.assessment.dto;

/** 文档分析请求DTO */
public class DocumentAnalysisRequest {

  /** Markdown格式的文档内容 */
  private String markdownContent;

  /** 文件名 */
  private String fileName;

  /** 自定义提示词 */
  private String customPrompt;

  /** 是否使用流式输出 */
  private Boolean useStream = false;

  /** 分析选项 */
  private AnalysisOptions options;

  public String getMarkdownContent() {
    return markdownContent;
  }

  public void setMarkdownContent(String markdownContent) {
    this.markdownContent = markdownContent;
  }

  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public String getCustomPrompt() {
    return customPrompt;
  }

  public void setCustomPrompt(String customPrompt) {
    this.customPrompt = customPrompt;
  }

  public Boolean getUseStream() {
    return useStream;
  }

  public void setUseStream(Boolean useStream) {
    this.useStream = useStream;
  }

  public AnalysisOptions getOptions() {
    return this.options == null ? null : new AnalysisOptions(this.options);
  }

  public void setOptions(AnalysisOptions options) {
    this.options = options == null ? null : new AnalysisOptions(options);
  }

  public static class AnalysisOptions {
    /** 是否生成详细字段注释 */
    private boolean generateDetailedComments = true;

    /** 是否自动推断字段类型 */
    private boolean autoInferFieldTypes = true;

    /** 最大字段数量限制 */
    private int maxFields = 50;

    /** 分析语言 */
    private String language = "zh-CN";

    public AnalysisOptions() {}

    public AnalysisOptions(AnalysisOptions other) {
      if (other != null) {
        this.generateDetailedComments = other.generateDetailedComments;
        this.autoInferFieldTypes = other.autoInferFieldTypes;
        this.maxFields = other.maxFields;
        this.language = other.language;
      }
    }

    public boolean isGenerateDetailedComments() {
      return generateDetailedComments;
    }

    public void setGenerateDetailedComments(boolean generateDetailedComments) {
      this.generateDetailedComments = generateDetailedComments;
    }

    public boolean isAutoInferFieldTypes() {
      return autoInferFieldTypes;
    }

    public void setAutoInferFieldTypes(boolean autoInferFieldTypes) {
      this.autoInferFieldTypes = autoInferFieldTypes;
    }

    public int getMaxFields() {
      return maxFields;
    }

    public void setMaxFields(int maxFields) {
      this.maxFields = maxFields;
    }

    public String getLanguage() {
      return language;
    }

    public void setLanguage(String language) {
      this.language = language;
    }
  }
}
