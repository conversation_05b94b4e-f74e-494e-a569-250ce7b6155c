package com.assessment.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/** 创建评估请求DTO */
@Data
public class CreateAssessmentRequest {

  @NotBlank(message = "被评估人ID不能为空")
  private String elderlyId;

  @NotBlank(message = "评估量表ID不能为空")
  private String scaleId;

  @NotBlank(message = "评估员ID不能为空")
  private String assessorId;

  private String taskId; // 可选，关联的评估任务ID

  @NotNull(message = "评估数据不能为空")
  private JsonNode formData; // 评估表单数据

  private String notes; // 备注
}
