package com.assessment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** 执行DDL请求DTO */
@Getter
@Setter
@Builder
@NoArgsConstructor
public class ExecuteDDLRequest {

  /** SQL语句 */
  private String sql;

  /** 表名 */
  private String tableName;

  /** 表注释 */
  private String tableComment;

  /** 是否覆盖已存在的表 */
  @Builder.Default private Boolean overwriteExisting = false;

  /** 执行选项 */
  private ExecutionOptions options;

  public ExecuteDDLRequest(
      String sql,
      String tableName,
      String tableComment,
      Boolean overwriteExisting,
      ExecutionOptions options) {
    this.sql = sql;
    this.tableName = tableName;
    this.tableComment = tableComment;
    this.overwriteExisting = overwriteExisting;
    this.setOptions(options); // Use safe setter for defensive copy
  }

  // --- Safe getter and setter for options ---
  public ExecutionOptions getOptions() {
    return this.options == null ? null : new ExecutionOptions(this.options);
  }

  public void setOptions(ExecutionOptions options) {
    this.options = options == null ? null : new ExecutionOptions(options);
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ExecutionOptions {
    /** 是否检查表存在性 */
    @Builder.Default private boolean checkTableExists = true;

    /** 是否备份已存在的表 */
    @Builder.Default private boolean backupExistingTable = false;

    /** 是否强制执行 */
    @Builder.Default private boolean forceExecution = false;

    // --- Copy Constructor ---
    public ExecutionOptions(ExecutionOptions other) {
      if (other != null) {
        this.checkTableExists = other.checkTableExists;
        this.backupExistingTable = other.backupExistingTable;
        this.forceExecution = other.forceExecution;
      }
    }
  }
}
