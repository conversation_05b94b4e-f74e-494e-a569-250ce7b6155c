package com.assessment.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 多租户登录响应DTO 包含用户信息、租户信息、权限等 */
@Data
@Builder
@NoArgsConstructor
public class MultiTenantLoginResponse {

  // 认证信息
  private String accessToken;
  private String refreshToken;
  @Builder.Default private String tokenType = "Bearer";
  private long expiresIn; // 过期时间（秒）

  // 用户基本信息
  private String userId;
  private String username;
  private String email;
  private String firstName;
  private String lastName;
  private String platformRole; // 平台级角色：admin, user

  // 租户信息
  private String tenantId;
  private String tenantCode;
  private String tenantName;
  private String tenantIndustry;
  private String subscriptionPlan;

  // 租户内角色和权限
  private String tenantRole; // 租户内角色：ADMIN, ASSESSOR, REVIEWER, VIEWER
  private String displayName; // 在租户中的显示名称
  private String department;
  private String professionalTitle;
  private List<String> permissions; // 具体权限列表

  // 状态信息
  private boolean isActive;
  private boolean isSuperAdmin; // 是否为超级管理员
  private LocalDateTime lastLoginAt;
  private LocalDateTime joinedAt; // 加入租户时间

  // 租户配置信息
  private TenantConfig tenantConfig;

  // --- 手动实现构造函数以进行防御性拷贝 ---
  @Builder(toBuilder = true)
  public MultiTenantLoginResponse(
      String accessToken,
      String refreshToken,
      String tokenType,
      long expiresIn,
      String userId,
      String username,
      String email,
      String firstName,
      String lastName,
      String platformRole,
      String tenantId,
      String tenantCode,
      String tenantName,
      String tenantIndustry,
      String subscriptionPlan,
      String tenantRole,
      String displayName,
      String department,
      String professionalTitle,
      List<String> permissions,
      boolean isActive,
      boolean isSuperAdmin,
      LocalDateTime lastLoginAt,
      LocalDateTime joinedAt,
      TenantConfig tenantConfig) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.tokenType = tokenType;
    this.expiresIn = expiresIn;
    this.userId = userId;
    this.username = username;
    this.email = email;
    this.firstName = firstName;
    this.lastName = lastName;
    this.platformRole = platformRole;
    this.tenantId = tenantId;
    this.tenantCode = tenantCode;
    this.tenantName = tenantName;
    this.tenantIndustry = tenantIndustry;
    this.subscriptionPlan = subscriptionPlan;
    this.tenantRole = tenantRole;
    this.displayName = displayName;
    this.department = department;
    this.professionalTitle = professionalTitle;
    this.setPermissions(permissions); // 使用安全的 setter
    this.isActive = isActive;
    this.isSuperAdmin = isSuperAdmin;
    this.lastLoginAt = lastLoginAt;
    this.joinedAt = joinedAt;
    this.setTenantConfig(tenantConfig); // 使用安全的 setter
  }

  // --- 重写 getter 和 setter 以进行防御性拷贝 ---

  public List<String> getPermissions() {
    return this.permissions == null ? null : new ArrayList<>(this.permissions);
  }

  public void setPermissions(List<String> permissions) {
    this.permissions = permissions == null ? null : new ArrayList<>(permissions);
  }

  public TenantConfig getTenantConfig() {
    return this.tenantConfig == null ? null : new TenantConfig(this.tenantConfig);
  }

  public void setTenantConfig(TenantConfig tenantConfig) {
    this.tenantConfig = tenantConfig == null ? null : new TenantConfig(tenantConfig);
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class TenantConfig {
    private int maxUsers;
    private int maxMonthlyAssessments;
    private int maxCustomScales;
    private boolean enableCustomScales;
    private boolean enableReports;
    private boolean enableAuditLogs;

    // --- 添加拷贝构造函数 ---
    public TenantConfig(TenantConfig other) {
      if (other != null) {
        this.maxUsers = other.maxUsers;
        this.maxMonthlyAssessments = other.maxMonthlyAssessments;
        this.maxCustomScales = other.maxCustomScales;
        this.enableCustomScales = other.enableCustomScales;
        this.enableReports = other.enableReports;
        this.enableAuditLogs = other.enableAuditLogs;
      }
    }
  }

  /** 检查是否有特定权限 */
  public boolean hasPermission(String permission) {
    return this.permissions != null && this.permissions.contains(permission);
  }

  /** 检查是否为租户管理员 */
  public boolean isTenantAdmin() {
    return "ADMIN".equals(tenantRole);
  }

  /** 检查是否可以进行评估 */
  public boolean canAssess() {
    return hasPermission("ASSESSMENT_CREATE")
        || hasPermission("ASSESSMENT_ALL")
        || "ADMIN".equals(tenantRole)
        || "ASSESSOR".equals(tenantRole);
  }

  /** 检查是否可以审核 */
  public boolean canReview() {
    return hasPermission("ASSESSMENT_REVIEW")
        || hasPermission("ASSESSMENT_ALL")
        || "ADMIN".equals(tenantRole)
        || "REVIEWER".equals(tenantRole);
  }
}
