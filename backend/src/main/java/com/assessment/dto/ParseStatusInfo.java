package com.assessment.dto;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 解析状态信息 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParseStatusInfo {
  private String scaleId;
  private String scaleName;
  private GlobalScaleRegistry.ScaleStatus parseStatus;
  private String parseError;
  private String sourcePdfPath;
  private int totalPages;
  private int errorCount;
  private String conversionStatus;
  private int processedPages;
  private int successfulPages;
  private int contentLength;

  // --- 添加拷贝构造函数 ---
  public ParseStatusInfo(ParseStatusInfo other) {
    if (other != null) {
      this.scaleId = other.scaleId;
      this.scaleName = other.scaleName;
      this.parseStatus = other.parseStatus;
      this.parseError = other.parseError;
      this.sourcePdfPath = other.sourcePdfPath;
      this.totalPages = other.totalPages;
      this.errorCount = other.errorCount;
      this.conversionStatus = other.conversionStatus;
      this.processedPages = other.processedPages;
      this.successfulPages = other.successfulPages;
      this.contentLength = other.contentLength;
    }
  }
}
