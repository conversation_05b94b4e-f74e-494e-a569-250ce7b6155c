package com.assessment.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/** DDL执行结果DTO */
@Builder
public class ExecuteDDLResult {

  /** 执行是否成功 */
  private Boolean success;

  /** 执行信息 */
  private String message;

  /** 创建的表名 */
  private String tableName;

  /** 执行的SQL语句 */
  private String executedSql;

  /** 执行耗时（毫秒） */
  private Long executionTimeMs;

  /** 执行时间 */
  private LocalDateTime executionTime;

  /** 影响的行数 */
  private Integer affectedRows;

  /** 错误信息列表 */
  private List<String> errors;

  /** 警告信息列表 */
  private List<String> warnings;

  /** 是否覆盖了已存在的表 */
  private Boolean overwrittenExistingTable;

  /** 备份表名（如果有备份） */
  private String backupTableName;

  public ExecuteDDLResult() {}

  public ExecuteDDLResult(
      Boolean success,
      String message,
      String tableName,
      String executedSql,
      Long executionTimeMs,
      LocalDateTime executionTime,
      Integer affectedRows,
      List<String> errors,
      List<String> warnings,
      Boolean overwrittenExistingTable,
      String backupTableName) {
    this.success = success;
    this.message = message;
    this.tableName = tableName;
    this.executedSql = executedSql;
    this.executionTimeMs = executionTimeMs;
    this.executionTime = executionTime;
    this.affectedRows = affectedRows;
    this.setErrors(errors);
    this.setWarnings(warnings);
    this.overwrittenExistingTable = overwrittenExistingTable;
    this.backupTableName = backupTableName;
  }

  // Getters and Setters

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public String getExecutedSql() {
    return executedSql;
  }

  public void setExecutedSql(String executedSql) {
    this.executedSql = executedSql;
  }

  public Long getExecutionTimeMs() {
    return executionTimeMs;
  }

  public void setExecutionTimeMs(Long executionTimeMs) {
    this.executionTimeMs = executionTimeMs;
  }

  public LocalDateTime getExecutionTime() {
    return executionTime;
  }

  public void setExecutionTime(LocalDateTime executionTime) {
    this.executionTime = executionTime;
  }

  public Integer getAffectedRows() {
    return affectedRows;
  }

  public void setAffectedRows(Integer affectedRows) {
    this.affectedRows = affectedRows;
  }

  public List<String> getErrors() {
    return errors == null ? null : new ArrayList<>(errors);
  }

  public void setErrors(List<String> errors) {
    this.errors = errors == null ? null : new ArrayList<>(errors);
  }

  public List<String> getWarnings() {
    return warnings == null ? null : new ArrayList<>(warnings);
  }

  public void setWarnings(List<String> warnings) {
    this.warnings = warnings == null ? null : new ArrayList<>(warnings);
  }

  public Boolean getOverwrittenExistingTable() {
    return overwrittenExistingTable;
  }

  public void setOverwrittenExistingTable(Boolean overwrittenExistingTable) {
    this.overwrittenExistingTable = overwrittenExistingTable;
  }

  public String getBackupTableName() {
    return backupTableName;
  }

  public void setBackupTableName(String backupTableName) {
    this.backupTableName = backupTableName;
  }
}
