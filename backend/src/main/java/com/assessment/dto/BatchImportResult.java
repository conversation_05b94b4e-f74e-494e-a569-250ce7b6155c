package com.assessment.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Singular;

/** 批量导入结果 */
@Builder
public class BatchImportResult {
  private final Integer totalFiles;
  private final Integer successCount;
  private final Integer failureCount;
  @Singular private final List<AssessmentScaleDTO> createdScales;

  public Integer getTotalFiles() {
    return totalFiles;
  }

  public Integer getSuccessCount() {
    return successCount;
  }

  public Integer getFailureCount() {
    return failureCount;
  }

  public List<AssessmentScaleDTO> getCreatedScales() {
    return this.createdScales == null ? null : new ArrayList<>(this.createdScales);
  }
}
