package com.assessment.dto;

import com.fasterxml.jackson.databind.JsonNode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/** 字段映射数据传输对象 用于PDF解析后的字段确认和调整 */
@Getter
@Setter
@Builder
public class FieldMappingDTO {

  /** 量表ID */
  private String scaleId;

  /** 量表名称 */
  private String scaleName;

  /** 解析出的字段列表 */
  private List<ParsedField> parsedFields;

  /** 建议的表单Schema */
  private JsonNode suggestedSchema;

  /** 建议的评分规则 */
  private JsonNode suggestedScoringRules;

  /** 置信度评分 (0-1) */
  private Double confidenceScore;

  // 安全的集合 getter/setter 方法
  public List<ParsedField> getParsedFields() {
    return parsedFields == null ? null : new ArrayList<>(parsedFields);
  }

  public void setParsedFields(List<ParsedField> parsedFields) {
    this.parsedFields = parsedFields == null ? null : new ArrayList<>(parsedFields);
  }

  /** 解析出的字段信息 */
  @Getter
  @Setter
  @Builder
  public static class ParsedField {

    /** 字段ID（唯一标识） */
    private String fieldId;

    /** 从PDF中提取的原始文本 */
    private String originalText;

    /** 建议的字段名称 */
    private String suggestedName;

    /** 建议的字段类型 */
    private FieldType suggestedType;

    /** 建议的字段描述 */
    private String suggestedDescription;

    /** 建议的选项（用于选择题） */
    private List<FieldOption> suggestedOptions;

    /** 建议的验证规则 */
    private FieldValidation suggestedValidation;

    /** 建议的评分权重 */
    private Double suggestedWeight;

    /** 字段在表格中的位置信息 */
    private FieldPosition position;

    /** 置信度 (0-1) */
    private Double confidence;

    /** 是否必填 */
    private Boolean required;

    /** 管理员确认状态 */
    private ConfirmationStatus confirmationStatus;

    /** 管理员备注 */
    private String adminNotes;

    // 安全的集合 getter/setter 方法
    public List<FieldOption> getSuggestedOptions() {
      return suggestedOptions == null ? null : new ArrayList<>(suggestedOptions);
    }

    public void setSuggestedOptions(List<FieldOption> suggestedOptions) {
      this.suggestedOptions = suggestedOptions == null ? null : new ArrayList<>(suggestedOptions);
    }

    // 安全的对象 getter/setter 方法
    public FieldValidation getSuggestedValidation() {
      return suggestedValidation == null ? null : copyFieldValidation(suggestedValidation);
    }

    public void setSuggestedValidation(FieldValidation suggestedValidation) {
      this.suggestedValidation =
          suggestedValidation == null ? null : copyFieldValidation(suggestedValidation);
    }

    public FieldPosition getPosition() {
      return position == null ? null : copyFieldPosition(position);
    }

    public void setPosition(FieldPosition position) {
      this.position = position == null ? null : copyFieldPosition(position);
    }

    // 复制 FieldValidation 对象
    private FieldValidation copyFieldValidation(FieldValidation original) {
      if (original == null) return null;
      return FieldValidation.builder()
          .minValue(original.getMinValue())
          .maxValue(original.getMaxValue())
          .minLength(original.getMinLength())
          .maxLength(original.getMaxLength())
          .pattern(original.getPattern())
          .customRules(original.getCustomRules()) // 这个方法已经返回副本
          .build();
    }

    // 复制 FieldPosition 对象
    private FieldPosition copyFieldPosition(FieldPosition original) {
      if (original == null) return null;
      return FieldPosition.builder()
          .pageNumber(original.getPageNumber())
          .tableIndex(original.getTableIndex())
          .rowIndex(original.getRowIndex())
          .columnIndex(original.getColumnIndex())
          .boundingBox(original.getBoundingBox()) // 这个方法已经返回副本
          .build();
    }
  }

  /** 字段选项 */
  @Getter
  @Setter
  @Builder
  public static class FieldOption {
    /** 选项值 */
    private String value;

    /** 选项显示文本 */
    private String label;

    /** 选项分值 */
    private Integer score;

    /** 选项描述 */
    private String description;
  }

  /** 字段验证规则 */
  @Getter
  @Setter
  @Builder
  public static class FieldValidation {
    /** 最小值（用于数字类型） */
    private Double minValue;

    /** 最大值（用于数字类型） */
    private Double maxValue;

    /** 最小长度（用于文本类型） */
    private Integer minLength;

    /** 最大长度（用于文本类型） */
    private Integer maxLength;

    /** 正则表达式模式 */
    private String pattern;

    /** 自定义验证规则 */
    private List<String> customRules;

    // 安全的集合 getter/setter 方法
    public List<String> getCustomRules() {
      return customRules == null ? null : new ArrayList<>(customRules);
    }

    public void setCustomRules(List<String> customRules) {
      this.customRules = customRules == null ? null : new ArrayList<>(customRules);
    }
  }

  /** 字段位置信息 */
  @Getter
  @Setter
  @Builder
  public static class FieldPosition {
    /** 页码 */
    private Integer pageNumber;

    /** 表格索引 */
    private Integer tableIndex;

    /** 行索引 */
    private Integer rowIndex;

    /** 列索引 */
    private Integer columnIndex;

    /** 边界框坐标 */
    private Map<String, Double> boundingBox;

    // 安全的映射 getter/setter 方法
    public Map<String, Double> getBoundingBox() {
      return boundingBox == null ? null : new HashMap<>(boundingBox);
    }

    public void setBoundingBox(Map<String, Double> boundingBox) {
      this.boundingBox = boundingBox == null ? null : new HashMap<>(boundingBox);
    }
  }

  /** 字段类型枚举 */
  public enum FieldType {
    TEXT("文本"),
    NUMBER("数字"),
    INTEGER("整数"),
    BOOLEAN("布尔值"),
    SELECT("单选"),
    MULTI_SELECT("多选"),
    DATE("日期"),
    TIME("时间"),
    EMAIL("邮箱"),
    PHONE("电话"),
    URL("链接"),
    TEXTAREA("长文本"),
    RATING("评分"),
    SLIDER("滑块"),
    CHECKBOX("复选框"),
    RADIO("单选按钮"),
    DROPDOWN("下拉框"),
    FILE("文件"),
    IMAGE("图片");

    private final String displayName;

    FieldType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  /** 确认状态枚举 */
  public enum ConfirmationStatus {
    PENDING("待确认"),
    CONFIRMED("已确认"),
    MODIFIED("已修改"),
    REJECTED("已拒绝");

    private final String displayName;

    ConfirmationStatus(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }
}
