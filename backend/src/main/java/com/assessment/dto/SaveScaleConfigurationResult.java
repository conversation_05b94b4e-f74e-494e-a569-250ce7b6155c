package com.assessment.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/** 保存量表配置结果DTO */
@Builder
public class SaveScaleConfigurationResult {

  /** 配置ID */
  private String configurationId;

  /** 量表名称 */
  private String scaleName;

  /** 对应的数据库表名 */
  private String tableName;

  /** 保存是否成功 */
  private Boolean success;

  /** 保存信息 */
  private String message;

  /** 保存的字段数量 */
  private Integer savedFieldCount;

  /** 保存时间 */
  private LocalDateTime savedAt;

  /** 保存耗时（毫秒） */
  private Long saveTimeMs;

  /** 是否覆盖了已存在的配置 */
  private Boolean overwrittenExisting;

  /** 警告信息列表 */
  private List<String> warnings;

  /** 错误信息列表 */
  private List<String> errors;

  /** 配置版本 */
  private String version;

  /** 配置状态 */
  private String status;

  public SaveScaleConfigurationResult() {}

  public SaveScaleConfigurationResult(
      String configurationId,
      String scaleName,
      String tableName,
      Boolean success,
      String message,
      Integer savedFieldCount,
      LocalDateTime savedAt,
      Long saveTimeMs,
      Boolean overwrittenExisting,
      List<String> warnings,
      List<String> errors,
      String version,
      String status) {
    this.configurationId = configurationId;
    this.scaleName = scaleName;
    this.tableName = tableName;
    this.success = success;
    this.message = message;
    this.savedFieldCount = savedFieldCount;
    this.savedAt = savedAt;
    this.saveTimeMs = saveTimeMs;
    this.overwrittenExisting = overwrittenExisting;
    this.setWarnings(warnings);
    this.setErrors(errors);
    this.version = version;
    this.status = status;
  }

  // Getters and Setters

  public String getConfigurationId() {
    return configurationId;
  }

  public void setConfigurationId(String configurationId) {
    this.configurationId = configurationId;
  }

  public String getScaleName() {
    return scaleName;
  }

  public void setScaleName(String scaleName) {
    this.scaleName = scaleName;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public Integer getSavedFieldCount() {
    return savedFieldCount;
  }

  public void setSavedFieldCount(Integer savedFieldCount) {
    this.savedFieldCount = savedFieldCount;
  }

  public LocalDateTime getSavedAt() {
    return savedAt;
  }

  public void setSavedAt(LocalDateTime savedAt) {
    this.savedAt = savedAt;
  }

  public Long getSaveTimeMs() {
    return saveTimeMs;
  }

  public void setSaveTimeMs(Long saveTimeMs) {
    this.saveTimeMs = saveTimeMs;
  }

  public Boolean getOverwrittenExisting() {
    return overwrittenExisting;
  }

  public void setOverwrittenExisting(Boolean overwrittenExisting) {
    this.overwrittenExisting = overwrittenExisting;
  }

  public List<String> getWarnings() {
    return warnings == null ? null : new ArrayList<>(warnings);
  }

  public void setWarnings(List<String> warnings) {
    this.warnings = warnings == null ? null : new ArrayList<>(warnings);
  }

  public List<String> getErrors() {
    return errors == null ? null : new ArrayList<>(errors);
  }

  public void setErrors(List<String> errors) {
    this.errors = errors == null ? null : new ArrayList<>(errors);
  }

  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }
}
