package com.assessment.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * DTO Builder模式测试
 * 测试使用Lombok @Builder注解的DTO类
 */
@DisplayName("DTO Builder模式测试")
class DTOBuilderTest {

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Test
    @DisplayName("测试BatchImportResult的Builder模式")
    void testBatchImportResultBuilder() {
        // Arrange
        List<AssessmentScaleDTO> createdScales = new ArrayList<>();
        AssessmentScaleDTO scale = AssessmentScaleDTO.builder()
            .id("scale-001")
            .name("测试量表")
            .code("TEST_SCALE")
            .build();
        createdScales.add(scale);

        // Act
        BatchImportResult result = BatchImportResult.builder()
            .totalFiles(10)
            .successCount(8)
            .failureCount(2)
            .createdScales(createdScales)
            .build();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getTotalFiles()).isEqualTo(10);
        assertThat(result.getSuccessCount()).isEqualTo(8);
        assertThat(result.getFailureCount()).isEqualTo(2);
        assertThat(result.getCreatedScales()).hasSize(1);
        assertThat(result.getCreatedScales().get(0).getName()).isEqualTo("测试量表");
    }

    @Test
    @DisplayName("测试BatchImportResult的空列表处理")
    void testBatchImportResultWithEmptyList() {
        // Act
        BatchImportResult result = BatchImportResult.builder()
            .totalFiles(5)
            .successCount(5)
            .failureCount(0)
            .build();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getTotalFiles()).isEqualTo(5);
        assertThat(result.getSuccessCount()).isEqualTo(5);
        assertThat(result.getFailureCount()).isEqualTo(0);
        assertThat(result.getCreatedScales()).isEmpty();
    }

    @Test
    @DisplayName("测试AssessmentScaleDTO的Builder模式")
    void testAssessmentScaleDTOBuilder() throws Exception {
        // Arrange
        JsonNode formSchema = objectMapper.readTree("{\"type\":\"object\",\"properties\":{}}");
        JsonNode scoringRules = objectMapper.readTree("{\"maxScore\":100,\"minScore\":0}");

        // Act
        AssessmentScaleDTO dto = AssessmentScaleDTO.builder()
            .id("scale-123")
            .name("老年人能力评估量表")
            .code("ELDERLY_ASSESSMENT")
            .category("老年人评估")
            .version("1.0")
            .description("用于评估老年人日常生活能力的标准化量表")
            .formSchema(formSchema)
            .scoringRules(scoringRules)
            .isActive(true)
            .isOfficial(false)
            .estimatedDuration(30)
            .maxScore(100)
            .minScore(0)
            .sourcePdfPath("/path/to/source.pdf")
            .usageCount(100L)
            .build();

        // Assert
        assertThat(dto).isNotNull();
        assertThat(dto.getId()).isEqualTo("scale-123");
        assertThat(dto.getName()).isEqualTo("老年人能力评估量表");
        assertThat(dto.getCode()).isEqualTo("ELDERLY_ASSESSMENT");
        assertThat(dto.getCategory()).isEqualTo("老年人评估");
        assertThat(dto.getVersion()).isEqualTo("1.0");
        assertThat(dto.getDescription()).isEqualTo("用于评估老年人日常生活能力的标准化量表");
        assertThat(dto.getFormSchema()).isEqualTo(formSchema);
        assertThat(dto.getScoringRules()).isEqualTo(scoringRules);
        assertThat(dto.getIsActive()).isTrue();
        assertThat(dto.getIsOfficial()).isFalse();
        assertThat(dto.getEstimatedDuration()).isEqualTo(30);
        assertThat(dto.getMaxScore()).isEqualTo(100);
        assertThat(dto.getMinScore()).isEqualTo(0);
        assertThat(dto.getSourcePdfPath()).isEqualTo("/path/to/source.pdf");
        assertThat(dto.getUsageCount()).isEqualTo(100L);
    }

    @Test
    @DisplayName("测试AssessmentScaleDTO的最小字段构建")
    void testAssessmentScaleDTOMinimalBuilder() {
        // Act
        AssessmentScaleDTO dto = AssessmentScaleDTO.builder()
            .name("简单量表")
            .code("SIMPLE_SCALE")
            .build();

        // Assert
        assertThat(dto).isNotNull();
        assertThat(dto.getName()).isEqualTo("简单量表");
        assertThat(dto.getCode()).isEqualTo("SIMPLE_SCALE");
        assertThat(dto.getId()).isNull();
        assertThat(dto.getCategory()).isNull();
        assertThat(dto.getVersion()).isNull();
        assertThat(dto.getDescription()).isNull();
        assertThat(dto.getFormSchema()).isNull();
        assertThat(dto.getScoringRules()).isNull();
        assertThat(dto.getIsActive()).isNull();
        assertThat(dto.getIsOfficial()).isNull();
    }

    @Test
    @DisplayName("测试AssessmentScaleDTO的JSON字段")
    void testAssessmentScaleDTOJsonFields() throws Exception {
        // Arrange
        JsonNode complexFormSchema = objectMapper.readTree("""
            {
                "type": "object",
                "properties": {
                    "personalInfo": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "age": {"type": "integer"}
                        }
                    },
                    "assessmentItems": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "question": {"type": "string"},
                                "score": {"type": "integer"}
                            }
                        }
                    }
                }
            }
            """);

        JsonNode complexScoringRules = objectMapper.readTree("""
            {
                "totalScore": {
                    "calculation": "sum",
                    "weightedItems": [
                        {"item": "mobility", "weight": 0.3},
                        {"item": "cognition", "weight": 0.4},
                        {"item": "selfCare", "weight": 0.3}
                    ]
                },
                "gradeMapping": {
                    "0-30": "重度依赖",
                    "31-60": "中度依赖", 
                    "61-85": "轻度依赖",
                    "86-100": "基本自理"
                }
            }
            """);

        // Act
        AssessmentScaleDTO dto = AssessmentScaleDTO.builder()
            .name("复杂评估量表")
            .code("COMPLEX_SCALE")
            .formSchema(complexFormSchema)
            .scoringRules(complexScoringRules)
            .build();

        // Assert
        assertThat(dto).isNotNull();
        assertThat(dto.getFormSchema()).isNotNull();
        assertThat(dto.getScoringRules()).isNotNull();
        
        // 验证JSON结构
        JsonNode formProperties = dto.getFormSchema().get("properties");
        assertThat(formProperties).isNotNull();
        assertThat(formProperties.has("personalInfo")).isTrue();
        assertThat(formProperties.has("assessmentItems")).isTrue();
        
        JsonNode scoringTotal = dto.getScoringRules().get("totalScore");
        assertThat(scoringTotal).isNotNull();
        assertThat(scoringTotal.get("calculation").asText()).isEqualTo("sum");
    }

    @Test
    @DisplayName("测试BatchImportResult的Singular注解")
    void testBatchImportResultSingularBuilder() {
        // Act - 使用singular方法逐个添加
        BatchImportResult result = BatchImportResult.builder()
            .totalFiles(3)
            .successCount(3)
            .failureCount(0)
            .createdScale(AssessmentScaleDTO.builder()
                .name("量表1")
                .code("SCALE_1")
                .build())
            .createdScale(AssessmentScaleDTO.builder()
                .name("量表2")
                .code("SCALE_2")
                .build())
            .createdScale(AssessmentScaleDTO.builder()
                .name("量表3")
                .code("SCALE_3")
                .build())
            .build();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getCreatedScales()).hasSize(3);
        assertThat(result.getCreatedScales().get(0).getName()).isEqualTo("量表1");
        assertThat(result.getCreatedScales().get(1).getName()).isEqualTo("量表2");
        assertThat(result.getCreatedScales().get(2).getName()).isEqualTo("量表3");
    }

    @Test
    @DisplayName("测试DTO对象的toString方法")
    void testDTOToStringMethods() {
        // Arrange
        AssessmentScaleDTO scaleDTO = AssessmentScaleDTO.builder()
            .name("测试量表")
            .code("TEST")
            .build();

        BatchImportResult result = BatchImportResult.builder()
            .totalFiles(1)
            .successCount(1)
            .failureCount(0)
            .createdScale(scaleDTO)
            .build();

        // Act & Assert
        assertThat(scaleDTO.toString()).isNotNull();
        assertThat(scaleDTO.toString()).contains("AssessmentScaleDTO");
        assertThat(scaleDTO.toString()).contains("测试量表");

        assertThat(result.toString()).isNotNull();
        assertThat(result.toString()).contains("BatchImportResult");
    }

    @Test
    @DisplayName("测试DTO对象的equals和hashCode")
    void testDTOEqualsAndHashCode() {
        // Arrange
        AssessmentScaleDTO dto1 = AssessmentScaleDTO.builder()
            .id("same-id")
            .name("相同量表")
            .code("SAME_CODE")
            .build();

        AssessmentScaleDTO dto2 = AssessmentScaleDTO.builder()
            .id("same-id")
            .name("相同量表")
            .code("SAME_CODE")
            .build();

        AssessmentScaleDTO dto3 = AssessmentScaleDTO.builder()
            .id("different-id")
            .name("不同量表")
            .code("DIFFERENT_CODE")
            .build();

        // Assert - Lombok生成的equals和hashCode
        assertThat(dto1).isEqualTo(dto2);
        assertThat(dto1.hashCode()).isEqualTo(dto2.hashCode());
        assertThat(dto1).isNotEqualTo(dto3);
        assertThat(dto1.hashCode()).isNotEqualTo(dto3.hashCode());

        // 自反性
        assertThat(dto1).isEqualTo(dto1);
        assertThat(dto1.hashCode()).isEqualTo(dto1.hashCode());
    }

    @Test
    @DisplayName("测试BatchImportResult的不可变性")
    void testBatchImportResultImmutability() {
        // Arrange
        List<AssessmentScaleDTO> originalList = new ArrayList<>();
        originalList.add(AssessmentScaleDTO.builder().name("原始量表").build());

        BatchImportResult result = BatchImportResult.builder()
            .totalFiles(1)
            .createdScales(originalList)
            .build();

        // Act - 修改原始列表
        originalList.add(AssessmentScaleDTO.builder().name("新增量表").build());

        // Assert - result中的列表不应受影响
        assertThat(result.getCreatedScales()).hasSize(1);
        assertThat(result.getCreatedScales().get(0).getName()).isEqualTo("原始量表");

        // Act - 尝试修改返回的列表
        List<AssessmentScaleDTO> returnedList = result.getCreatedScales();
        
        // Assert - 返回的是新的ArrayList，可以修改
        returnedList.add(AssessmentScaleDTO.builder().name("测试添加").build());
        assertThat(returnedList).hasSize(2);
        
        // 但原始result不受影响
        assertThat(result.getCreatedScales()).hasSize(1);
    }

    @Test
    @DisplayName("测试DTO Builder的链式调用")
    void testDTOBuilderChaining() {
        // Act - 测试Builder的链式调用
        AssessmentScaleDTO dto = AssessmentScaleDTO.builder()
            .id("chain-test")
            .name("链式调用测试")
            .code("CHAIN_TEST")
            .category("测试分类")
            .version("1.0")
            .description("测试Builder链式调用")
            .isActive(true)
            .isOfficial(false)
            .estimatedDuration(15)
            .maxScore(50)
            .minScore(0)
            .usageCount(10L)
            .build();

        // Assert
        assertThat(dto.getId()).isEqualTo("chain-test");
        assertThat(dto.getName()).isEqualTo("链式调用测试");
        assertThat(dto.getCode()).isEqualTo("CHAIN_TEST");
        assertThat(dto.getCategory()).isEqualTo("测试分类");
        assertThat(dto.getVersion()).isEqualTo("1.0");
        assertThat(dto.getDescription()).isEqualTo("测试Builder链式调用");
        assertThat(dto.getIsActive()).isTrue();
        assertThat(dto.getIsOfficial()).isFalse();
        assertThat(dto.getEstimatedDuration()).isEqualTo(15);
        assertThat(dto.getMaxScore()).isEqualTo(50);
        assertThat(dto.getMinScore()).isEqualTo(0);
        assertThat(dto.getUsageCount()).isEqualTo(10L);
    }
}