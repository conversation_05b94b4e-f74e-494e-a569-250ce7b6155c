package com.assessment.dto;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * 简单DTO测试
 * 测试有无参构造函数的DTO类
 */
@DisplayName("简单DTO测试")
class SimpleDTOTest {

    @Test
    @DisplayName("测试LoginResponse的基本功能")
    void testLoginResponseBasicFunctionality() {
        // Act - 使用无参构造函数
        LoginResponse response = new LoginResponse();
        
        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getToken()).isNull();
        assertThat(response.getRefreshToken()).isNull();
        assertThat(response.getUser()).isNull();
        assertThat(response.getExpiresIn()).isEqualTo(0);
    }

    @Test
    @DisplayName("测试LoginResponse的属性设置")
    void testLoginResponseProperties() {
        // Arrange
        LoginResponse response = new LoginResponse();
        
        // Act
        response.setToken("jwt-token-123");
        response.setRefreshToken("refresh-token-456");
        response.setExpiresIn(3600L);
        
        // Assert
        assertThat(response.getToken()).isEqualTo("jwt-token-123");
        assertThat(response.getRefreshToken()).isEqualTo("refresh-token-456");
        assertThat(response.getExpiresIn()).isEqualTo(3600L);
    }

    @Test
    @DisplayName("测试LoginResponse的工厂方法")
    void testLoginResponseFactoryMethod() {
        // Act
        LoginResponse response = LoginResponse.of("token", "refresh", null);
        
        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getToken()).isEqualTo("token");
        assertThat(response.getRefreshToken()).isEqualTo("refresh");
        assertThat(response.getUser()).isNull();
        assertThat(response.getExpiresIn()).isEqualTo(0);
    }

    @Test
    @DisplayName("测试DocumentAnalysisRequest的基本功能")
    void testDocumentAnalysisRequestBasicFunctionality() {
        // Act
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        
        // Assert
        assertThat(request).isNotNull();
        assertThat(request.getClass().getSimpleName()).isEqualTo("DocumentAnalysisRequest");
    }

    @Test
    @DisplayName("测试DocumentAnalysisResult的基本功能")
    void testDocumentAnalysisResultBasicFunctionality() {
        // Act
        DocumentAnalysisResult result = new DocumentAnalysisResult();
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getClass().getSimpleName()).isEqualTo("DocumentAnalysisResult");
    }

    @Test
    @DisplayName("测试ExecuteDDLRequest的基本功能")
    void testExecuteDDLRequestBasicFunctionality() {
        // Act
        ExecuteDDLRequest request = new ExecuteDDLRequest();
        
        // Assert
        assertThat(request).isNotNull();
        assertThat(request.getClass().getSimpleName()).isEqualTo("ExecuteDDLRequest");
    }

    @Test
    @DisplayName("测试ExecuteDDLResult的基本功能")
    void testExecuteDDLResultBasicFunctionality() {
        // Act
        ExecuteDDLResult result = new ExecuteDDLResult();
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getClass().getSimpleName()).isEqualTo("ExecuteDDLResult");
    }

    @Test
    @DisplayName("测试PDFFormatInfo的基本功能")
    void testPDFFormatInfoBasicFunctionality() {
        // Act
        PDFFormatInfo info = new PDFFormatInfo();
        
        // Assert
        assertThat(info).isNotNull();
        assertThat(info.getClass().getSimpleName()).isEqualTo("PDFFormatInfo");
    }

    @Test
    @DisplayName("测试ParseStatusInfo的基本功能")
    void testParseStatusInfoBasicFunctionality() {
        // Act
        ParseStatusInfo info = new ParseStatusInfo();
        
        // Assert
        assertThat(info).isNotNull();
        assertThat(info.getClass().getSimpleName()).isEqualTo("ParseStatusInfo");
    }

    @Test
    @DisplayName("测试SaveScaleConfigurationRequest的基本功能")
    void testSaveScaleConfigurationRequestBasicFunctionality() {
        // Act
        SaveScaleConfigurationRequest request = new SaveScaleConfigurationRequest();
        
        // Assert
        assertThat(request).isNotNull();
        assertThat(request.getClass().getSimpleName()).isEqualTo("SaveScaleConfigurationRequest");
    }

    @Test
    @DisplayName("测试SaveScaleConfigurationResult的基本功能")
    void testSaveScaleConfigurationResultBasicFunctionality() {
        // Act
        SaveScaleConfigurationResult result = new SaveScaleConfigurationResult();
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getClass().getSimpleName()).isEqualTo("SaveScaleConfigurationResult");
    }

    @Test
    @DisplayName("测试MultiTenantLoginRequest的基本功能")
    void testMultiTenantLoginRequestBasicFunctionality() {
        // Act
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        
        // Assert
        assertThat(request).isNotNull();
        assertThat(request.getClass().getSimpleName()).isEqualTo("MultiTenantLoginRequest");
    }

    @Test
    @DisplayName("测试MultiTenantLoginResponse的基本功能")
    void testMultiTenantLoginResponseBasicFunctionality() {
        // Act
        MultiTenantLoginResponse response = new MultiTenantLoginResponse();
        
        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getClass().getSimpleName()).isEqualTo("MultiTenantLoginResponse");
    }

    @Test
    @DisplayName("测试DTO类的包名")
    void testDTOClassPackages() {
        // Act & Assert
        assertThat(new LoginResponse().getClass().getPackage().getName())
            .isEqualTo("com.assessment.dto");
        assertThat(new DocumentAnalysisRequest().getClass().getPackage().getName())
            .isEqualTo("com.assessment.dto");
        assertThat(new DocumentAnalysisResult().getClass().getPackage().getName())
            .isEqualTo("com.assessment.dto");
        assertThat(new ExecuteDDLRequest().getClass().getPackage().getName())
            .isEqualTo("com.assessment.dto");
        assertThat(new ExecuteDDLResult().getClass().getPackage().getName())
            .isEqualTo("com.assessment.dto");
    }

    @Test
    @DisplayName("测试DTO类的实例化")
    void testDTOClassInstantiation() {
        // 测试所有DTO类都可以正常实例化
        assertThatCode(() -> new LoginResponse()).doesNotThrowAnyException();
        assertThatCode(() -> new DocumentAnalysisRequest()).doesNotThrowAnyException();
        assertThatCode(() -> new DocumentAnalysisResult()).doesNotThrowAnyException();
        assertThatCode(() -> new ExecuteDDLRequest()).doesNotThrowAnyException();
        assertThatCode(() -> new ExecuteDDLResult()).doesNotThrowAnyException();
        assertThatCode(() -> new PDFFormatInfo()).doesNotThrowAnyException();
        assertThatCode(() -> new ParseStatusInfo()).doesNotThrowAnyException();
        assertThatCode(() -> new SaveScaleConfigurationRequest()).doesNotThrowAnyException();
        assertThatCode(() -> new SaveScaleConfigurationResult()).doesNotThrowAnyException();
        assertThatCode(() -> new MultiTenantLoginRequest()).doesNotThrowAnyException();
        assertThatCode(() -> new MultiTenantLoginResponse()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试DTO类的toString方法")
    void testDTOClassToString() {
        // Arrange
        LoginResponse response = new LoginResponse();
        response.setToken("test-token");
        
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        ExecuteDDLRequest ddlRequest = new ExecuteDDLRequest();
        
        // Assert
        assertThat(response.toString()).isNotNull();
        assertThat(request.toString()).isNotNull();
        assertThat(ddlRequest.toString()).isNotNull();
    }

    @Test
    @DisplayName("测试DTO类的相等性")
    void testDTOClassEquality() {
        // 测试不同实例的相等性
        LoginResponse response1 = new LoginResponse();
        response1.setToken("same-token");
        
        LoginResponse response2 = new LoginResponse();
        response2.setToken("same-token");
        
        LoginResponse response3 = new LoginResponse();
        response3.setToken("different-token");
        
        // 不同的实例应该不相等（除非重写了equals）
        assertThat(response1).isNotSameAs(response2);
        
        // 但自己应该等于自己
        assertThat(response1).isEqualTo(response1);
    }

    @Test
    @DisplayName("测试DTO类的类型检查")
    void testDTOClassTypeChecking() {
        // Act
        Object response = new LoginResponse();
        Object request = new DocumentAnalysisRequest();
        Object ddlRequest = new ExecuteDDLRequest();
        Object info = new PDFFormatInfo();
        
        // Assert
        assertThat(response).isInstanceOf(LoginResponse.class);
        assertThat(request).isInstanceOf(DocumentAnalysisRequest.class);
        assertThat(ddlRequest).isInstanceOf(ExecuteDDLRequest.class);
        assertThat(info).isInstanceOf(PDFFormatInfo.class);
        
        // 交叉检查类型
        assertThat(response).isNotInstanceOf(DocumentAnalysisRequest.class);
        assertThat(request).isNotInstanceOf(LoginResponse.class);
        assertThat(ddlRequest).isNotInstanceOf(PDFFormatInfo.class);
    }

    @Test
    @DisplayName("测试LoginResponse的空值处理")
    void testLoginResponseNullHandling() {
        // Arrange
        LoginResponse response = new LoginResponse();
        
        // Act & Assert - null值
        response.setToken(null);
        assertThat(response.getToken()).isNull();
        
        response.setRefreshToken(null);
        assertThat(response.getRefreshToken()).isNull();
        
        // 空字符串
        response.setToken("");
        assertThat(response.getToken()).isEmpty();
        
        response.setRefreshToken("");
        assertThat(response.getRefreshToken()).isEmpty();
        
        // 负数过期时间
        response.setExpiresIn(-1);
        assertThat(response.getExpiresIn()).isEqualTo(-1);
    }

    @Test
    @DisplayName("测试DTO类的构造函数数量")
    void testDTOClassConstructorCount() {
        // 检查DTO类的构造函数
        Class<?> loginResponseClass = LoginResponse.class;
        Class<?> requestClass = DocumentAnalysisRequest.class;
        Class<?> resultClass = DocumentAnalysisResult.class;
        
        // 这些类都应该有构造函数
        assertThat(loginResponseClass.getConstructors().length).isGreaterThan(0);
        assertThat(requestClass.getConstructors().length).isGreaterThan(0);
        assertThat(resultClass.getConstructors().length).isGreaterThan(0);
    }

    @Test
    @DisplayName("测试DTO类的方法数量")
    void testDTOClassMethodCount() {
        // 检查DTO类的公共方法数量
        Class<?> loginResponseClass = LoginResponse.class;
        Class<?> requestClass = DocumentAnalysisRequest.class;
        Class<?> resultClass = DocumentAnalysisResult.class;
        
        // 这些类应该都有一些公共方法
        assertThat(loginResponseClass.getMethods().length).isGreaterThan(0);
        assertThat(requestClass.getMethods().length).isGreaterThan(0);
        assertThat(resultClass.getMethods().length).isGreaterThan(0);
        
        // LoginResponse应该有getter和setter方法
        assertThat(loginResponseClass.getDeclaredMethods().length).isGreaterThan(5);
    }
}