package com.assessment.exception;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 异常类基础测试
 * 测试自定义异常类的构造和基本功能
 */
@DisplayName("异常类基础测试")
class ExceptionClassesTest {

    @Test
    @DisplayName("AssessmentProcessingException - 基础构造函数")
    void testAssessmentProcessingException_BasicConstructor() {
        // Arrange
        String message = "评估处理失败";
        
        // Act
        AssessmentProcessingException exception = new AssessmentProcessingException(message);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(exception.getCause()).isNull();
    }

    @Test
    @DisplayName("AssessmentProcessingException - 带原因构造函数")
    void testAssessmentProcessingException_WithCause() {
        // Arrange
        String message = "评估处理失败";
        Throwable cause = new IllegalArgumentException("参数错误");
        
        // Act
        AssessmentProcessingException exception = new AssessmentProcessingException(message, cause);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("FieldMappingException - 基础构造函数")
    void testFieldMappingException_BasicConstructor() throws Exception {
        // Arrange
        String message = "字段映射失败";
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.FieldMappingException");
        Object exception = exceptionClass.getConstructor(String.class).newInstance(message);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
    }

    @Test
    @DisplayName("FieldMappingException - 带原因构造函数")
    void testFieldMappingException_WithCause() throws Exception {
        // Arrange
        String message = "字段映射失败";
        Throwable cause = new IllegalArgumentException("参数错误");
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.FieldMappingException");
        Object exception = exceptionClass.getConstructor(String.class, Throwable.class).newInstance(message, cause);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
        assertThat(((Exception) exception).getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("PDFParsingException - 基础构造函数")
    void testPDFParsingException_BasicConstructor() throws Exception {
        // Arrange
        String message = "PDF解析失败";
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.PDFParsingException");
        Object exception = exceptionClass.getConstructor(String.class).newInstance(message);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
    }

    @Test
    @DisplayName("PDFParsingException - 带原因构造函数")
    void testPDFParsingException_WithCause() throws Exception {
        // Arrange
        String message = "PDF解析失败";
        Throwable cause = new IllegalArgumentException("文件格式错误");
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.PDFParsingException");
        Object exception = exceptionClass.getConstructor(String.class, Throwable.class).newInstance(message, cause);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
        assertThat(((Exception) exception).getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("MarkdownAnalysisException - 基础构造函数")
    void testMarkdownAnalysisException_BasicConstructor() throws Exception {
        // Arrange
        String message = "Markdown分析失败";
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.MarkdownAnalysisException");
        Object exception = exceptionClass.getConstructor(String.class).newInstance(message);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
    }

    @Test
    @DisplayName("MarkdownAnalysisException - 带原因构造函数")
    void testMarkdownAnalysisException_WithCause() throws Exception {
        // Arrange
        String message = "Markdown分析失败";
        Throwable cause = new IllegalArgumentException("格式错误");
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.MarkdownAnalysisException");
        Object exception = exceptionClass.getConstructor(String.class, Throwable.class).newInstance(message, cause);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
        assertThat(((Exception) exception).getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("SchemaGenerationException - 基础构造函数")
    void testSchemaGenerationException_BasicConstructor() throws Exception {
        // Arrange
        String message = "Schema生成失败";
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.SchemaGenerationException");
        Object exception = exceptionClass.getConstructor(String.class).newInstance(message);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
    }

    @Test
    @DisplayName("SchemaGenerationException - 带原因构造函数")
    void testSchemaGenerationException_WithCause() throws Exception {
        // Arrange
        String message = "Schema生成失败";
        Throwable cause = new IllegalArgumentException("配置错误");
        
        // Act
        Class<?> exceptionClass = Class.forName("com.assessment.exception.SchemaGenerationException");
        Object exception = exceptionClass.getConstructor(String.class, Throwable.class).newInstance(message, cause);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(((Exception) exception).getMessage()).isEqualTo(message);
        assertThat(((Exception) exception).getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("异常类继承关系")
    void testExceptionInheritance() {
        // Act & Assert
        assertThat(AssessmentProcessingException.class.getSuperclass()).isEqualTo(RuntimeException.class);
        
        // 验证其他异常类也继承自RuntimeException
        assertThat(RuntimeException.class.isAssignableFrom(AssessmentProcessingException.class)).isTrue();
    }

    @Test
    @DisplayName("异常消息空值处理")
    void testExceptionWithNullMessage() {
        // Act
        AssessmentProcessingException exception = new AssessmentProcessingException(null);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception.getMessage()).isNull();
    }

    @Test
    @DisplayName("异常消息空字符串处理")
    void testExceptionWithEmptyMessage() {
        // Act
        AssessmentProcessingException exception = new AssessmentProcessingException("");
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception.getMessage()).isEmpty();
    }

    @Test
    @DisplayName("异常原因空值处理")
    void testExceptionWithNullCause() {
        // Arrange
        String message = "测试异常";
        
        // Act
        AssessmentProcessingException exception = new AssessmentProcessingException(message, null);
        
        // Assert
        assertThat(exception).isNotNull();
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isNull();
    }
}