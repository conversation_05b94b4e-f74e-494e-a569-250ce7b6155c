package com.assessment.exception;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * 异常类测试
 * 测试各种自定义异常的功能
 */
@DisplayName("异常类测试")
class ExceptionTest {

    @Test
    @DisplayName("PDFParsingException应该正确创建和处理")
    void pdfParsingExceptionShouldCreateAndHandleCorrectly() {
        // Test message constructor
        String message = "PDF解析失败";
        PDFParsingException exception1 = new PDFParsingException(message);
        
        assertThat(exception1.getMessage()).isEqualTo(message);
        assertThat(exception1).isInstanceOf(RuntimeException.class);
        
        // Test message and cause constructor
        RuntimeException cause = new RuntimeException("底层错误");
        PDFParsingException exception2 = new PDFParsingException(message, cause);
        
        assertThat(exception2.getMessage()).isEqualTo(message);
        assertThat(exception2.getCause()).isEqualTo(cause);
        
        // Test throwable
        assertThatThrownBy(() -> {
            throw new PDFParsingException("测试异常");
        }).isInstanceOf(PDFParsingException.class)
          .hasMessage("测试异常");
    }

    @Test
    @DisplayName("SchemaGenerationException应该正确创建和处理")
    void schemaGenerationExceptionShouldCreateAndHandleCorrectly() {
        // Test message constructor
        String message = "Schema生成失败";
        SchemaGenerationException exception1 = new SchemaGenerationException(message);
        
        assertThat(exception1.getMessage()).isEqualTo(message);
        assertThat(exception1).isInstanceOf(RuntimeException.class);
        
        // Test message and cause constructor
        Exception cause = new Exception("配置错误");
        SchemaGenerationException exception2 = new SchemaGenerationException(message, cause);
        
        assertThat(exception2.getMessage()).isEqualTo(message);
        assertThat(exception2.getCause()).isEqualTo(cause);
        
        // Test throwable
        assertThatThrownBy(() -> {
            throw new SchemaGenerationException("Schema创建失败");
        }).isInstanceOf(SchemaGenerationException.class)
          .hasMessage("Schema创建失败");
    }

    @Test
    @DisplayName("AssessmentProcessingException应该正确创建和处理")
    void assessmentProcessingExceptionShouldCreateAndHandleCorrectly() {
        // Test message constructor
        String message = "评估处理失败";
        AssessmentProcessingException exception1 = new AssessmentProcessingException(message);
        
        assertThat(exception1.getMessage()).isEqualTo(message);
        assertThat(exception1).isInstanceOf(RuntimeException.class);
        
        // Test message and cause constructor
        IllegalArgumentException cause = new IllegalArgumentException("参数无效");
        AssessmentProcessingException exception2 = new AssessmentProcessingException(message, cause);
        
        assertThat(exception2.getMessage()).isEqualTo(message);
        assertThat(exception2.getCause()).isEqualTo(cause);
        
        // Test throwable
        assertThatThrownBy(() -> {
            throw new AssessmentProcessingException("评估失败");
        }).isInstanceOf(AssessmentProcessingException.class)
          .hasMessage("评估失败");
    }

    @Test
    @DisplayName("FieldMappingException应该正确创建和处理")
    void fieldMappingExceptionShouldCreateAndHandleCorrectly() {
        // Test message constructor
        String message = "字段映射失败";
        FieldMappingException exception1 = new FieldMappingException(message);
        
        assertThat(exception1.getMessage()).isEqualTo(message);
        assertThat(exception1).isInstanceOf(RuntimeException.class);
        
        // Test message and cause constructor
        NullPointerException cause = new NullPointerException("空指针错误");
        FieldMappingException exception2 = new FieldMappingException(message, cause);
        
        assertThat(exception2.getMessage()).isEqualTo(message);
        assertThat(exception2.getCause()).isEqualTo(cause);
        
        // Test throwable
        assertThatThrownBy(() -> {
            throw new FieldMappingException("映射错误");
        }).isInstanceOf(FieldMappingException.class)
          .hasMessage("映射错误");
    }

    @Test
    @DisplayName("MarkdownAnalysisException应该正确创建和处理")
    void markdownAnalysisExceptionShouldCreateAndHandleCorrectly() {
        // Test message and cause constructor (only available constructor)
        String message = "Markdown分析失败";
        IllegalStateException cause = new IllegalStateException("状态错误");
        MarkdownAnalysisException exception = new MarkdownAnalysisException(message, cause);
        
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception).isInstanceOf(RuntimeException.class);
        
        // Test throwable
        assertThatThrownBy(() -> {
            throw new MarkdownAnalysisException("分析失败", new RuntimeException("原因"));
        }).isInstanceOf(MarkdownAnalysisException.class)
          .hasMessage("分析失败");
    }

    @Test
    @DisplayName("测试异常的继承关系")
    void testExceptionInheritanceHierarchy() {
        // 所有自定义异常都应该继承自RuntimeException
        assertThat(new PDFParsingException("test")).isInstanceOf(RuntimeException.class);
        assertThat(new SchemaGenerationException("test")).isInstanceOf(RuntimeException.class);
        assertThat(new AssessmentProcessingException("test")).isInstanceOf(RuntimeException.class);
        assertThat(new FieldMappingException("test")).isInstanceOf(RuntimeException.class);
        assertThat(new MarkdownAnalysisException("test", null)).isInstanceOf(RuntimeException.class);
        
        // 都应该是Throwable的实例
        assertThat(new PDFParsingException("test")).isInstanceOf(Throwable.class);
        assertThat(new SchemaGenerationException("test")).isInstanceOf(Throwable.class);
        assertThat(new AssessmentProcessingException("test")).isInstanceOf(Throwable.class);
        assertThat(new FieldMappingException("test")).isInstanceOf(Throwable.class);
        assertThat(new MarkdownAnalysisException("test", null)).isInstanceOf(Throwable.class);
    }

    @Test
    @DisplayName("测试异常链的传播")
    void testExceptionChainPropagation() {
        // 创建异常链
        RuntimeException rootCause = new RuntimeException("根本原因");
        IllegalArgumentException middleCause = new IllegalArgumentException("中间原因", rootCause);
        PDFParsingException finalException = new PDFParsingException("最终异常", middleCause);
        
        // 验证异常链
        assertThat(finalException.getCause()).isEqualTo(middleCause);
        assertThat(finalException.getCause().getCause()).isEqualTo(rootCause);
        assertThat(finalException.getCause().getCause().getCause()).isNull();
        
        // 验证消息
        assertThat(finalException.getMessage()).isEqualTo("最终异常");
        assertThat(finalException.getCause().getMessage()).isEqualTo("中间原因");
        assertThat(finalException.getCause().getCause().getMessage()).isEqualTo("根本原因");
    }

    @Test
    @DisplayName("测试空消息和null原因的处理")
    void testNullAndEmptyMessageHandling() {
        // 测试空消息
        PDFParsingException exception1 = new PDFParsingException("");
        assertThat(exception1.getMessage()).isEmpty();
        
        // 测试null消息
        PDFParsingException exception2 = new PDFParsingException(null);
        assertThat(exception2.getMessage()).isNull();
        
        // 测试null原因
        PDFParsingException exception3 = new PDFParsingException("消息", null);
        assertThat(exception3.getMessage()).isEqualTo("消息");
        assertThat(exception3.getCause()).isNull();
    }

    @Test
    @DisplayName("测试异常的toString方法")
    void testExceptionToStringMethod() {
        String message = "测试异常消息";
        PDFParsingException exception = new PDFParsingException(message);
        
        String toString = exception.toString();
        assertThat(toString).contains("PDFParsingException");
        assertThat(toString).contains(message);
    }

    @Test
    @DisplayName("测试异常堆栈跟踪")
    void testExceptionStackTrace() {
        PDFParsingException exception = new PDFParsingException("堆栈测试");
        
        StackTraceElement[] stackTrace = exception.getStackTrace();
        assertThat(stackTrace).isNotNull();
        assertThat(stackTrace.length).isGreaterThan(0);
        
        // 第一个元素应该是当前方法
        StackTraceElement firstElement = stackTrace[0];
        assertThat(firstElement.getMethodName()).isEqualTo("testExceptionStackTrace");
        assertThat(firstElement.getClassName()).contains("ExceptionTest");
    }
}