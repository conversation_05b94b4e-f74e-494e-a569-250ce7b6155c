package com.assessment.exception;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.util.List;

/**
 * GlobalExceptionHandler 测试类
 * 测试全局异常处理器的异常处理逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("全局异常处理器测试")
class GlobalExceptionHandlerTest {

    private GlobalExceptionHandler exceptionHandler;

    @Mock
    private MethodArgumentNotValidException methodArgumentNotValidException;

    @Mock
    private BindingResult bindingResult;

    @Mock
    private ObjectError objectError;

    @BeforeEach
    void setUp() {
        exceptionHandler = new GlobalExceptionHandler();
    }

    @Test
    @DisplayName("处理RuntimeException - 基础业务异常")
    void testHandleRuntimeException_BasicBusinessException() {
        // Arrange
        String errorMessage = "业务处理失败";
        RuntimeException exception = new RuntimeException(errorMessage);

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleRuntimeException(exception);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo(errorMessage);
        assertThat(response.getBody().getErrorCode()).isEqualTo("BAD_REQUEST");
        assertThat(response.getBody().getData()).isNull();
    }

    @Test
    @DisplayName("处理RuntimeException - 自定义业务异常")
    void testHandleRuntimeException_CustomBusinessException() {
        // Arrange
        String errorMessage = "评估处理异常";
        AssessmentProcessingException exception = new AssessmentProcessingException(errorMessage);

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleRuntimeException(exception);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo(errorMessage);
        assertThat(response.getBody().getErrorCode()).isEqualTo("BAD_REQUEST");
    }

    @Test
    @DisplayName("处理RuntimeException - 空消息异常")
    void testHandleRuntimeException_NullMessage() {
        // Arrange
        RuntimeException exception = new RuntimeException((String) null);

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleRuntimeException(exception);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isNull();
        assertThat(response.getBody().getErrorCode()).isEqualTo("BAD_REQUEST");
    }

    @Test
    @DisplayName("处理MethodArgumentNotValidException - 参数校验失败")
    void testHandleValidationException_WithValidationErrors() {
        // Arrange
        String errorMessage = "用户名不能为空";
        when(methodArgumentNotValidException.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getAllErrors()).thenReturn(List.of(objectError));
        when(objectError.getDefaultMessage()).thenReturn(errorMessage);

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleValidationException(methodArgumentNotValidException);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo(errorMessage);
        assertThat(response.getBody().getErrorCode()).isEqualTo("VALIDATION_ERROR");
    }

    @Test
    @DisplayName("处理MethodArgumentNotValidException - 无错误消息")
    void testHandleValidationException_NoErrorMessage() {
        // Arrange
        when(methodArgumentNotValidException.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getAllErrors()).thenReturn(List.of());

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleValidationException(methodArgumentNotValidException);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("参数校验失败");
        assertThat(response.getBody().getErrorCode()).isEqualTo("VALIDATION_ERROR");
    }

    @Test
    @DisplayName("处理MethodArgumentNotValidException - 错误消息为null")
    void testHandleValidationException_NullErrorMessage() {
        // Arrange
        when(methodArgumentNotValidException.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getAllErrors()).thenReturn(List.of(objectError));
        when(objectError.getDefaultMessage()).thenReturn(null);

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleValidationException(methodArgumentNotValidException);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("参数校验失败");
        assertThat(response.getBody().getErrorCode()).isEqualTo("VALIDATION_ERROR");
    }

    @Test
    @DisplayName("处理Exception - 系统级异常")
    void testHandleException_SystemException() {
        // Arrange
        Exception exception = new Exception("系统内部错误");

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleException(exception);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("系统内部错误，请联系管理员");
        assertThat(response.getBody().getErrorCode()).isEqualTo("INTERNAL_ERROR");
        assertThat(response.getBody().getData()).isNull();
    }

    @Test
    @DisplayName("处理Exception - NullPointerException")
    void testHandleException_NullPointerException() {
        // Arrange
        NullPointerException exception = new NullPointerException("空指针异常");

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleException(exception);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("系统内部错误，请联系管理员");
        assertThat(response.getBody().getErrorCode()).isEqualTo("INTERNAL_ERROR");
    }

    @Test
    @DisplayName("处理Exception - IOException")
    void testHandleException_IOException() {
        // Arrange
        java.io.IOException exception = new java.io.IOException("文件读取失败");

        // Act
        ResponseEntity<ApiResponse<Void>> response = exceptionHandler.handleException(exception);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("系统内部错误，请联系管理员");
        assertThat(response.getBody().getErrorCode()).isEqualTo("INTERNAL_ERROR");
    }

    @Test
    @DisplayName("异常处理器基本属性")
    void testExceptionHandlerBasicProperties() {
        // Assert
        assertThat(exceptionHandler).isNotNull();
        assertThat(exceptionHandler.getClass().getSimpleName()).isEqualTo("GlobalExceptionHandler");
        assertThat(exceptionHandler.getClass().getPackage().getName()).isEqualTo("com.assessment.exception");
    }

    @Test
    @DisplayName("异常处理器注解存在性")
    void testExceptionHandlerAnnotations() {
        // Assert
        assertThat(exceptionHandler.getClass().isAnnotationPresent(org.springframework.web.bind.annotation.RestControllerAdvice.class)).isTrue();
    }
}