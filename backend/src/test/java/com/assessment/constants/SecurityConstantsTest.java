package com.assessment.constants;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/** SecurityConstants单元测试 - 验证安全常量的正确性和工具类的不可实例化特性 */
@DisplayName("安全常量测试")
class SecurityConstantsTest {

  @Test
  @DisplayName("工具类不应被实例化 - 患者安全关键")
  void securityConstants_ShouldNotBeInstantiable() {
    // When & Then: 尝试通过反射实例化应该抛出异常
    assertThatThrownBy(
            () -> {
              var constructor = SecurityConstants.class.getDeclaredConstructor();
              constructor.setAccessible(true);
              constructor.newInstance();
            })
        .cause()
        .isInstanceOf(UnsupportedOperationException.class)
        .hasMessageContaining("Utility class cannot be instantiated");
  }

  @Test
  @DisplayName("JWT过期时间常量应为24小时")
  void jwtExpirationTime_ShouldBe24Hours() {
    // Then: 24小时 = 24 * 60 * 60 * 1000 = 86400000毫秒
    assertThat(SecurityConstants.JWT_EXPIRATION_TIME).isEqualTo(86400000L);
  }

  @Test
  @DisplayName("JWT刷新Token过期时间常量应为7天")
  void jwtRefreshExpirationTime_ShouldBe7Days() {
    // Then: 7天 = 7 * 24 * 60 * 60 * 1000 = 604800000毫秒
    assertThat(SecurityConstants.JWT_REFRESH_EXPIRATION_TIME).isEqualTo(604800000L);
  }

  @Test
  @DisplayName("Bearer前缀长度应为7")
  void bearerPrefixLength_ShouldBe7() {
    // Then: "Bearer " 的长度为7
    assertThat(SecurityConstants.BEARER_PREFIX_LENGTH).isEqualTo(7);
  }

  @Test
  @DisplayName("CORS最大缓存时间应为1小时")
  void corsMaxAge_ShouldBe1Hour() {
    // Then: 1小时 = 3600秒
    assertThat(SecurityConstants.CORS_MAX_AGE_SECONDS).isEqualTo(3600L);
  }

  @Test
  @DisplayName("密码最小长度应为8")
  void passwordMinLength_ShouldBe8() {
    assertThat(SecurityConstants.PASSWORD_MIN_LENGTH).isEqualTo(8);
  }

  @Test
  @DisplayName("最大登录尝试次数应为5")
  void maxLoginAttempts_ShouldBe5() {
    assertThat(SecurityConstants.MAX_LOGIN_ATTEMPTS).isEqualTo(5);
  }

  @Test
  @DisplayName("账户锁定时间应为30分钟")
  void accountLockDuration_ShouldBe30Minutes() {
    // Then: 30分钟 = 30 * 60 = 1800秒
    assertThat(SecurityConstants.ACCOUNT_LOCK_DURATION_SECONDS).isEqualTo(1800);
  }

  @Test
  @DisplayName("文件上传大小限制应为10MB")
  void maxUploadFileSize_ShouldBe10MB() {
    // Then: 10MB = 10 * 1024 * 1024 = ********字节
    assertThat(SecurityConstants.MAX_UPLOAD_FILE_SIZE_BYTES).isEqualTo(********L);
  }

  @Test
  @DisplayName("所有常量都应为正数")
  void allConstants_ShouldBePositive() {
    assertThat(SecurityConstants.JWT_EXPIRATION_TIME).isPositive();
    assertThat(SecurityConstants.JWT_REFRESH_EXPIRATION_TIME).isPositive();
    assertThat(SecurityConstants.BEARER_PREFIX_LENGTH).isPositive();
    assertThat(SecurityConstants.CORS_MAX_AGE_SECONDS).isPositive();
    assertThat(SecurityConstants.PASSWORD_MIN_LENGTH).isPositive();
    assertThat(SecurityConstants.MAX_LOGIN_ATTEMPTS).isPositive();
    assertThat(SecurityConstants.ACCOUNT_LOCK_DURATION_SECONDS).isPositive();
    assertThat(SecurityConstants.MAX_UPLOAD_FILE_SIZE_BYTES).isPositive();
  }
}
