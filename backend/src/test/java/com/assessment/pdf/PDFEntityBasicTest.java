package com.assessment.pdf;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

import java.awt.image.BufferedImage;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * PDF相关实体类基础单元测试
 * 测试PDF处理相关的数据结构和实体类
 */
@DisplayName("PDF相关实体类基础单元测试")
class PDFEntityBasicTest {

    @Test
    @DisplayName("测试PDFContent实体类")
    void testPDFContentEntity() {
        // Arrange
        String text = "测试PDF内容";
        List<AssessmentTable> tables = Arrays.asList(
            AssessmentTable.builder().title("表格1").build(),
            AssessmentTable.builder().title("表格2").build()
        );
        List<BufferedImage> images = Arrays.asList(
            new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB)
        );
        int pageCount = 5;
        
        // Act
        PDFContent pdfContent = PDFContent.builder()
            .text(text)
            .tables(tables)
            .images(images)
            .pageCount(pageCount)
            .build();
        
        // Assert
        assertThat(pdfContent).isNotNull();
        assertThat(pdfContent.getText()).isEqualTo(text);
        assertThat(pdfContent.getTables()).hasSize(2);
        assertThat(pdfContent.getImages()).hasSize(1);
        assertThat(pdfContent.getPageCount()).isEqualTo(pageCount);
        
        // 测试防御性拷贝
        assertThat(pdfContent.getTables()).isNotSameAs(tables);
        assertThat(pdfContent.getImages()).isNotSameAs(images);
    }

    @Test
    @DisplayName("测试PDFContent实体类 - 空值处理")
    void testPDFContentEntity_NullValues() {
        // Act
        PDFContent pdfContent = PDFContent.builder()
            .text(null)
            .pageCount(0)
            .build();
        
        // Assert
        assertThat(pdfContent).isNotNull();
        assertThat(pdfContent.getText()).isNull();
        assertThat(pdfContent.getTables()).isEmpty();
        assertThat(pdfContent.getImages()).isEmpty();
        assertThat(pdfContent.getPageCount()).isEqualTo(0);
    }

    @Test
    @DisplayName("测试PDFContent setter方法")
    void testPDFContentSetters() {
        // Arrange
        PDFContent pdfContent = PDFContent.builder().build();
        List<AssessmentTable> tables = Arrays.asList(
            AssessmentTable.builder().title("表格1").type(AssessmentTable.TableType.UNKNOWN).build()
        );
        List<BufferedImage> images = Arrays.asList(
            new BufferedImage(50, 50, BufferedImage.TYPE_INT_RGB)
        );
        
        // Act
        pdfContent.setText("新文本");
        pdfContent.setTables(tables);
        pdfContent.setImages(images);
        pdfContent.setPageCount(3);
        
        // Assert
        assertThat(pdfContent.getText()).isEqualTo("新文本");
        assertThat(pdfContent.getTables()).hasSize(1);
        assertThat(pdfContent.getImages()).hasSize(1);
        assertThat(pdfContent.getPageCount()).isEqualTo(3);
        
        // 测试防御性拷贝
        assertThat(pdfContent.getTables()).isNotSameAs(tables);
        assertThat(pdfContent.getImages()).isNotSameAs(images);
    }

    @Test
    @DisplayName("测试AssessmentTable实体类")
    void testAssessmentTableEntity() {
        // Arrange
        List<String> headers = Arrays.asList("列1", "列2", "列3");
        List<List<String>> data = Arrays.asList(
            Arrays.asList("数据1", "数据2", "数据3"),
            Arrays.asList("数据4", "数据5", "数据6")
        );
        
        // Act
        AssessmentTable table = AssessmentTable.builder()
            .type(AssessmentTable.TableType.SCORE_CRITERIA)
            .title("测试表格")
            .headers(headers)
            .data(data)
            .pageNumber(1)
            .build();
        
        // Assert
        assertThat(table).isNotNull();
        assertThat(table.getType()).isEqualTo(AssessmentTable.TableType.SCORE_CRITERIA);
        assertThat(table.getTitle()).isEqualTo("测试表格");
        assertThat(table.getHeaders()).hasSize(3);
        assertThat(table.getData()).hasSize(2);
        assertThat(table.getPageNumber()).isEqualTo(1);
        
        // 测试防御性拷贝
        assertThat(table.getHeaders()).isNotSameAs(headers);
        assertThat(table.getData()).isNotSameAs(data);
    }

    @Test
    @DisplayName("测试AssessmentTable TableType枚举")
    void testAssessmentTableTableType() {
        // Act & Assert
        assertThat(AssessmentTable.TableType.SCORE_CRITERIA.getDisplayName()).isEqualTo("评分标准表");
        assertThat(AssessmentTable.TableType.QUESTION_OPTIONS.getDisplayName()).isEqualTo("问题选项表");
        assertThat(AssessmentTable.TableType.SCORING_RULES.getDisplayName()).isEqualTo("评分规则表");
        assertThat(AssessmentTable.TableType.ASSESSMENT_ITEMS.getDisplayName()).isEqualTo("评估项目表");
        assertThat(AssessmentTable.TableType.UNKNOWN.getDisplayName()).isEqualTo("未知类型");
        
        // 验证枚举值
        assertThat(AssessmentTable.TableType.values()).hasSize(5);
        assertThat(AssessmentTable.TableType.valueOf("SCORE_CRITERIA"))
            .isEqualTo(AssessmentTable.TableType.SCORE_CRITERIA);
    }

    @Test
    @DisplayName("测试AssessmentTable setter方法")
    void testAssessmentTableSetters() {
        // Arrange
        AssessmentTable table = AssessmentTable.builder().build();
        List<String> headers = Arrays.asList("新列1", "新列2");
        List<List<String>> data = Arrays.asList(Arrays.asList("新数据1", "新数据2"));
        
        // Act
        table.setType(AssessmentTable.TableType.QUESTION_OPTIONS);
        table.setTitle("新表格");
        table.setHeaders(headers);
        table.setData(data);
        table.setPageNumber(2);
        
        // Assert
        assertThat(table.getType()).isEqualTo(AssessmentTable.TableType.QUESTION_OPTIONS);
        assertThat(table.getTitle()).isEqualTo("新表格");
        assertThat(table.getHeaders()).hasSize(2);
        assertThat(table.getData()).hasSize(1);
        assertThat(table.getPageNumber()).isEqualTo(2);
        
        // 测试防御性拷贝
        assertThat(table.getHeaders()).isNotSameAs(headers);
        assertThat(table.getData()).isNotSameAs(data);
    }

    @Test
    @DisplayName("测试AssessmentMetadata实体类")
    void testAssessmentMetadataEntity() {
        // Act
        AssessmentMetadata metadata = AssessmentMetadata.builder()
            .title("测试量表")
            .version("1.0")
            .description("测试描述")
            .type("elderly")
            .author("测试作者")
            .build();
        
        // Assert
        assertThat(metadata).isNotNull();
        assertThat(metadata.getTitle()).isEqualTo("测试量表");
        assertThat(metadata.getVersion()).isEqualTo("1.0");
        assertThat(metadata.getDescription()).isEqualTo("测试描述");
        assertThat(metadata.getType()).isEqualTo("elderly");
        assertThat(metadata.getAuthor()).isEqualTo("测试作者");
    }

    @Test
    @DisplayName("测试ScoringRules实体类")
    void testScoringRulesEntity() {
        // Act
        ScoringRules scoringRules = ScoringRules.builder()
            .maxPossibleScore(100)
            .minPossibleScore(0)
            .passScore(60)
            .scoringMethod("sum")
            .algorithm("weighted")
            .build();
        
        // Assert
        assertThat(scoringRules).isNotNull();
        assertThat(scoringRules.getMaxPossibleScore()).isEqualTo(100);
        assertThat(scoringRules.getMinPossibleScore()).isEqualTo(0);
        assertThat(scoringRules.getPassScore()).isEqualTo(60);
        assertThat(scoringRules.getScoringMethod()).isEqualTo("sum");
        assertThat(scoringRules.getAlgorithm()).isEqualTo("weighted");
    }

    @Test
    @DisplayName("测试AssessmentSection实体类")
    void testAssessmentSectionEntity() {
        // Arrange
        List<AssessmentQuestion> questions = Arrays.asList(
            AssessmentQuestion.builder().text("问题1").build(),
            AssessmentQuestion.builder().text("问题2").build()
        );
        
        // Act
        AssessmentSection section = AssessmentSection.builder()
            .id("section1")
            .title("第一部分")
            .description("第一部分描述")
            .order(1)
            .questions(questions)
            .build();
        
        // Assert
        assertThat(section).isNotNull();
        assertThat(section.getId()).isEqualTo("section1");
        assertThat(section.getTitle()).isEqualTo("第一部分");
        assertThat(section.getDescription()).isEqualTo("第一部分描述");
        assertThat(section.getOrder()).isEqualTo(1);
        assertThat(section.getQuestions()).hasSize(2);
        
        // 测试防御性拷贝
        assertThat(section.getQuestions()).isNotSameAs(questions);
    }

    @Test
    @DisplayName("测试AssessmentSection addQuestion方法")
    void testAssessmentSectionAddQuestion() {
        // Arrange
        AssessmentSection section = AssessmentSection.builder()
            .title("测试部分")
            .build();
        AssessmentQuestion question = AssessmentQuestion.builder()
            .text("新问题")
            .build();
        
        // Act
        section.addQuestion(question);
        
        // Assert
        assertThat(section.getQuestions()).hasSize(1);
        assertThat(section.getQuestions().get(0).getText()).isEqualTo("新问题");
    }

    @Test
    @DisplayName("测试AssessmentSection addQuestion方法 - null questions列表")
    void testAssessmentSectionAddQuestion_NullQuestions() {
        // Arrange
        AssessmentSection section = AssessmentSection.builder().build();
        section.setQuestions(null);
        AssessmentQuestion question = AssessmentQuestion.builder()
            .text("新问题")
            .build();
        
        // Act
        section.addQuestion(question);
        
        // Assert
        assertThat(section.getQuestions()).hasSize(1);
        assertThat(section.getQuestions().get(0).getText()).isEqualTo("新问题");
    }

    @Test
    @DisplayName("测试AssessmentQuestion实体类")
    void testAssessmentQuestionEntity() {
        // Arrange
        List<String> options = Arrays.asList("选项A", "选项B", "选项C");
        
        // Act
        AssessmentQuestion question = AssessmentQuestion.builder()
            .id("q1")
            .title("问题标题")
            .text("问题文本")
            .type("single_choice")
            .required(true)
            .order(1)
            .options(options)
            .build();
        
        // Assert
        assertThat(question).isNotNull();
        assertThat(question.getId()).isEqualTo("q1");
        assertThat(question.getTitle()).isEqualTo("问题标题");
        assertThat(question.getText()).isEqualTo("问题文本");
        assertThat(question.getType()).isEqualTo("single_choice");
        assertThat(question.isRequired()).isTrue();
        assertThat(question.getOrder()).isEqualTo(1);
        assertThat(question.getOptions()).hasSize(3);
    }

    @Test
    @DisplayName("测试AssessmentQuestion getText方法 - fallback to title")
    void testAssessmentQuestionGetText_FallbackToTitle() {
        // Act
        AssessmentQuestion question = AssessmentQuestion.builder()
            .title("问题标题")
            .text(null)
            .build();
        
        // Assert
        assertThat(question.getText()).isEqualTo("问题标题");
    }

    @Test
    @DisplayName("测试AssessmentQuestion addOption方法")
    void testAssessmentQuestionAddOption() {
        // Arrange
        AssessmentQuestion question = AssessmentQuestion.builder()
            .text("测试问题")
            .build();
        
        // Act
        question.addOption("新选项1");
        question.addOption("新选项2");
        
        // Assert
        assertThat(question.getOptions()).hasSize(2);
        assertThat(question.getOptions()).containsExactly("新选项1", "新选项2");
    }

    @Test
    @DisplayName("测试AssessmentQuestion addOption方法 - null options列表")
    void testAssessmentQuestionAddOption_NullOptions() {
        // Arrange
        AssessmentQuestion question = AssessmentQuestion.builder().build();
        question.setOptions(null);
        
        // Act
        question.addOption("新选项");
        
        // Assert
        assertThat(question.getOptions()).hasSize(1);
        assertThat(question.getOptions().get(0)).isEqualTo("新选项");
    }

    @Test
    @DisplayName("测试AssessmentStructure实体类")
    void testAssessmentStructureEntity() {
        // Arrange
        AssessmentMetadata metadata = AssessmentMetadata.builder()
            .title("测试量表")
            .build();
        List<AssessmentSection> sections = Arrays.asList(
            AssessmentSection.builder().title("部分1").build(),
            AssessmentSection.builder().title("部分2").build()
        );
        ScoringRules scoringRules = ScoringRules.builder()
            .maxPossibleScore(100)
            .build();
        
        // Act
        AssessmentStructure structure = AssessmentStructure.builder()
            .metadata(metadata)
            .sections(sections)
            .scoringRules(scoringRules)
            .build();
        
        // Assert
        assertThat(structure).isNotNull();
        assertThat(structure.getMetadata()).isEqualTo(metadata);
        assertThat(structure.getSections()).hasSize(2);
        assertThat(structure.getScoringRules()).isEqualTo(scoringRules);
        
        // 测试防御性拷贝
        assertThat(structure.getSections()).isNotSameAs(sections);
    }

    @Test
    @DisplayName("测试QuestionType枚举")
    void testQuestionTypeEnum() {
        // Act & Assert
        assertThat(QuestionType.SINGLE_CHOICE.getDisplayName()).isEqualTo("单选");
        assertThat(QuestionType.MULTIPLE_CHOICE.getDisplayName()).isEqualTo("多选");
        assertThat(QuestionType.TEXT_INPUT.getDisplayName()).isEqualTo("文本输入");
        assertThat(QuestionType.NUMBER_INPUT.getDisplayName()).isEqualTo("数字输入");
        assertThat(QuestionType.DATE_INPUT.getDisplayName()).isEqualTo("日期输入");
        
        // 验证枚举值
        assertThat(QuestionType.values()).hasSize(5);
        assertThat(QuestionType.valueOf("SINGLE_CHOICE")).isEqualTo(QuestionType.SINGLE_CHOICE);
    }

    @Test
    @DisplayName("测试实体类实例化")
    void testEntityInstantiation() {
        // Act & Assert
        assertThatCode(() -> PDFContent.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> AssessmentTable.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> AssessmentMetadata.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> ScoringRules.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> AssessmentSection.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> AssessmentQuestion.builder().build()).doesNotThrowAnyException();
        assertThatCode(() -> AssessmentStructure.builder().build()).doesNotThrowAnyException();
        
        // 验证实例
        assertThat(PDFContent.builder().build()).isInstanceOf(PDFContent.class);
        assertThat(AssessmentTable.builder().build()).isInstanceOf(AssessmentTable.class);
        assertThat(AssessmentMetadata.builder().build()).isInstanceOf(AssessmentMetadata.class);
        assertThat(ScoringRules.builder().build()).isInstanceOf(ScoringRules.class);
        assertThat(AssessmentSection.builder().build()).isInstanceOf(AssessmentSection.class);
        assertThat(AssessmentQuestion.builder().build()).isInstanceOf(AssessmentQuestion.class);
        assertThat(AssessmentStructure.builder().build()).isInstanceOf(AssessmentStructure.class);
    }
}