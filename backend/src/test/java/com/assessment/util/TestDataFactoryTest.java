package com.assessment.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.TenantUserMembership;
import java.time.LocalDate;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * TestDataFactory单元测试
 * 验证测试数据工厂方法的正确性
 */
@DisplayName("测试数据工厂测试")
class TestDataFactoryTest {

    @Test
    @DisplayName("创建测试租户")
    void createTestTenant_ShouldReturnValidTenant() {
        // Arrange
        String code = "TEST001";
        String name = "测试医院";
        
        // Act
        Tenant tenant = TestDataFactory.createTestTenant(code, name);
        
        // Assert
        assertThat(tenant).isNotNull();
        assertThat(tenant.getCode()).isEqualTo(code);
        assertThat(tenant.getName()).isEqualTo(name);
        assertThat(tenant.getIndustry()).isEqualTo("healthcare");
        assertThat(tenant.getContactPerson()).isEqualTo("Test Contact");
        assertThat(tenant.getContactEmail()).isEqualTo("test@" + code + ".com");
        assertThat(tenant.getContactPhone()).isEqualTo("**********");
        assertThat(tenant.getSubscriptionPlan()).isEqualTo(Tenant.SubscriptionPlan.STANDARD);
        assertThat(tenant.getSubscriptionStatus()).isEqualTo(Tenant.SubscriptionStatus.ACTIVE);
        assertThat(tenant.getSubscriptionStartDate()).isEqualTo(LocalDate.now());
        assertThat(tenant.getSubscriptionEndDate()).isEqualTo(LocalDate.now().plusYears(1));
        assertThat(tenant.getMaxUsers()).isEqualTo(100);
        assertThat(tenant.getMaxMonthlyAssessments()).isEqualTo(5000);
        assertThat(tenant.getMaxCustomScales()).isEqualTo(20);
        assertThat(tenant.getMaxStorageMb()).isEqualTo(2048);
        assertThat(tenant.getStatus()).isEqualTo(Tenant.TenantStatus.ACTIVE);
        assertThat(tenant.getIsTrial()).isFalse();
        assertThat(tenant.getId()).isNotNull();
    }

    @Test
    @DisplayName("创建默认测试租户")
    void createDefaultTestTenant_ShouldReturnValidDefaultTenant() {
        // Act
        Tenant tenant = TestDataFactory.createDefaultTestTenant();
        
        // Assert
        assertThat(tenant).isNotNull();
        assertThat(tenant.getCode()).isEqualTo("TEST001");
        assertThat(tenant.getName()).isEqualTo("Test Hospital");
        assertThat(tenant.getIndustry()).isEqualTo("healthcare");
    }

    @Test
    @DisplayName("创建测试用户")
    void createTestUser_ShouldReturnValidUser() {
        // Arrange
        String username = "testuser";
        String email = "<EMAIL>";
        
        // Act
        PlatformUser user = TestDataFactory.createTestUser(username, email);
        
        // Assert
        assertThat(user).isNotNull();
        assertThat(user.getUsername()).isEqualTo(username);
        assertThat(user.getEmail()).isEqualTo(email);
        assertThat(user.getPasswordHash()).isEqualTo("$2a$04$test.password.hash");
        assertThat(user.getFirstName()).isEqualTo("Test");
        assertThat(user.getLastName()).isEqualTo("User");
        assertThat(user.getPhone()).isEqualTo("**********");
        assertThat(user.getPlatformRole()).isEqualTo(PlatformUser.PlatformRole.USER);
        assertThat(user.getIsActive()).isTrue();
        assertThat(user.getEmailVerified()).isTrue();
        assertThat(user.getId()).isNotNull();
    }

    @Test
    @DisplayName("创建默认测试用户")
    void createDefaultTestUser_ShouldReturnValidDefaultUser() {
        // Act
        PlatformUser user = TestDataFactory.createDefaultTestUser();
        
        // Assert
        assertThat(user).isNotNull();
        assertThat(user.getUsername()).isEqualTo("testuser");
        assertThat(user.getEmail()).isEqualTo("<EMAIL>");
        assertThat(user.getPlatformRole()).isEqualTo(PlatformUser.PlatformRole.USER);
    }

    @Test
    @DisplayName("创建租户用户关联")
    void createTenantMembership_ShouldReturnValidMembership() {
        // Arrange
        UUID tenantId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        String role = "ADMIN";
        
        // Act
        TenantUserMembership membership = TestDataFactory.createTenantMembership(tenantId, userId, role);
        
        // Assert
        assertThat(membership).isNotNull();
        assertThat(membership.getTenantId()).isEqualTo(tenantId.toString());
        assertThat(membership.getUserId()).isEqualTo(userId.toString());
        assertThat(membership.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.ADMIN);
        assertThat(membership.getDisplayName()).isEqualTo("Test User");
        assertThat(membership.getProfessionalTitle()).isEqualTo("Test Assessor");
        assertThat(membership.getDepartment()).isEqualTo("Testing Department");
        assertThat(membership.getStatus()).isEqualTo(TenantUserMembership.MembershipStatus.ACTIVE);
        assertThat(membership.getId()).isNotNull();
    }

    @Test
    @DisplayName("创建管理员成员关系")
    void createAdminMembership_ShouldReturnAdminMembership() {
        // Arrange
        UUID tenantId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        
        // Act
        TenantUserMembership membership = TestDataFactory.createAdminMembership(tenantId, userId);
        
        // Assert
        assertThat(membership).isNotNull();
        assertThat(membership.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.ADMIN);
        assertThat(membership.getTenantId()).isEqualTo(tenantId.toString());
        assertThat(membership.getUserId()).isEqualTo(userId.toString());
    }

    @Test
    @DisplayName("创建评估师成员关系")
    void createAssessorMembership_ShouldReturnAssessorMembership() {
        // Arrange
        UUID tenantId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        
        // Act
        TenantUserMembership membership = TestDataFactory.createAssessorMembership(tenantId, userId);
        
        // Assert
        assertThat(membership).isNotNull();
        assertThat(membership.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.ASSESSOR);
        assertThat(membership.getTenantId()).isEqualTo(tenantId.toString());
        assertThat(membership.getUserId()).isEqualTo(userId.toString());
    }

    @Test
    @DisplayName("创建审核员成员关系")
    void createReviewerMembership_ShouldReturnReviewerMembership() {
        // Arrange
        UUID tenantId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        
        // Act
        TenantUserMembership membership = TestDataFactory.createReviewerMembership(tenantId, userId);
        
        // Assert
        assertThat(membership).isNotNull();
        assertThat(membership.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.REVIEWER);
        assertThat(membership.getTenantId()).isEqualTo(tenantId.toString());
        assertThat(membership.getUserId()).isEqualTo(userId.toString());
    }

    @Test
    @DisplayName("创建查看者成员关系")
    void createViewerMembership_ShouldReturnViewerMembership() {
        // Arrange
        UUID tenantId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        
        // Act
        TenantUserMembership membership = TestDataFactory.createViewerMembership(tenantId, userId);
        
        // Assert
        assertThat(membership).isNotNull();
        assertThat(membership.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.VIEWER);
        assertThat(membership.getTenantId()).isEqualTo(tenantId.toString());
        assertThat(membership.getUserId()).isEqualTo(userId.toString());
    }

    @Test
    @DisplayName("验证数据一致性")
    void testDataConsistency() {
        // Act
        Tenant tenant = TestDataFactory.createDefaultTestTenant();
        PlatformUser user = TestDataFactory.createDefaultTestUser();
        TenantUserMembership membership = TestDataFactory.createAdminMembership(
            tenant.getId(), user.getId()
        );
        
        // Assert
        assertThat(membership.getTenantId()).isEqualTo(tenant.getId().toString());
        assertThat(membership.getUserId()).isEqualTo(user.getId().toString());
    }

    @Test
    @DisplayName("验证不同实例的唯一性")
    void testInstanceUniqueness() {
        // Act
        Tenant tenant1 = TestDataFactory.createDefaultTestTenant();
        Tenant tenant2 = TestDataFactory.createDefaultTestTenant();
        PlatformUser user1 = TestDataFactory.createDefaultTestUser();
        PlatformUser user2 = TestDataFactory.createDefaultTestUser();
        
        // Assert - 虽然使用相同的工厂方法，但应该生成不同的ID
        assertThat(tenant1.getId()).isNotEqualTo(tenant2.getId());
        assertThat(user1.getId()).isNotEqualTo(user2.getId());
        
        // 但基本属性应该相同
        assertThat(tenant1.getCode()).isEqualTo(tenant2.getCode());
        assertThat(user1.getUsername()).isEqualTo(user2.getUsername());
    }

    @Test
    @DisplayName("验证所有角色类型")
    void testAllRoleTypes() {
        // Arrange
        UUID tenantId = UUID.randomUUID();
        UUID userId = UUID.randomUUID();
        
        // Act & Assert
        TenantUserMembership admin = TestDataFactory.createAdminMembership(tenantId, userId);
        assertThat(admin.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.ADMIN);
        
        TenantUserMembership assessor = TestDataFactory.createAssessorMembership(tenantId, userId);
        assertThat(assessor.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.ASSESSOR);
        
        TenantUserMembership reviewer = TestDataFactory.createReviewerMembership(tenantId, userId);
        assertThat(reviewer.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.REVIEWER);
        
        TenantUserMembership viewer = TestDataFactory.createViewerMembership(tenantId, userId);
        assertThat(viewer.getTenantRole()).isEqualTo(TenantUserMembership.TenantRole.VIEWER);
    }
}