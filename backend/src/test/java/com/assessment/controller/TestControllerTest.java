package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器单元测试
 * 直接测试控制器方法，不依赖Spring上下文
 */
@DisplayName("测试控制器单元测试")
class TestControllerTest {

    @Test
    @DisplayName("测试公开GET端点")
    void testPublicEndpoint() {
        // Arrange
        TestController testController = new TestController();

        // Act
        ResponseEntity<String> result = testController.publicEndpoint();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo("Public endpoint works!");
    }

    @Test
    @DisplayName("测试公开POST端点")
    void testPublicPostEndpoint() {
        // Arrange
        TestController testController = new TestController();
        Map<String, Object> testData = new HashMap<>();
        testData.put("test", "data");

        // Act
        ResponseEntity<String> result = testController.publicPostEndpoint(testData);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).contains("Public POST endpoint works!");
        assertThat(result.getBody()).contains("Data:");
    }

    @Test
    @DisplayName("测试POST端点处理null数据")
    void testPublicPostEndpointWithNull() {
        // Arrange
        TestController testController = new TestController();

        // Act
        ResponseEntity<String> result = testController.publicPostEndpoint(null);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).contains("Public POST endpoint works!");
        assertThat(result.getBody()).contains("null");
    }
}