package com.assessment.controller;

import com.assessment.controller.config.ControllerTestConfig;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemDashboardController 基础测试类
 */
@WebMvcTest(SystemDashboardController.class)
@Import(ControllerTestConfig.class)
@DisplayName("系统监控面板控制器基础测试")
@SuppressWarnings("removal")
class SystemDashboardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private PlatformUserRepository userRepository;

    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @MockBean
    private TenantAssessmentRecordRepository assessmentRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取系统概览统计 - 基础HTTP测试")
    void testGetSystemOverview_HttpEndpoint() throws Exception {
        // 准备数据
        when(tenantRepository.count()).thenReturn(50L);
        when(tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE)).thenReturn(45L);
        when(userRepository.count()).thenReturn(200L);
        when(userRepository.countByIsActive(true)).thenReturn(180L);
        when(scaleRepository.count()).thenReturn(30L);
        when(scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(25L);
        when(assessmentRepository.count()).thenReturn(1000L);
        when(assessmentRepository.countByTenantIdAndAssessmentDateAfter(anyString(), any(LocalDateTime.class)))
            .thenReturn(10L, 50L, 200L); // todayAssessments, weekAssessments, monthAssessments

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/overview"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalTenants").value(50))
                .andExpect(jsonPath("$.data.activeTenants").value(45))
                .andExpect(jsonPath("$.data.totalUsers").value(200))
                .andExpect(jsonPath("$.data.activeUsers").value(180))
                .andExpect(jsonPath("$.data.totalScales").value(30))
                .andExpect(jsonPath("$.data.activeScales").value(25))
                .andExpect(jsonPath("$.data.totalAssessments").value(1000));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户统计 - 成功")
    void testGetTenantStats_Success() throws Exception {
        // 准备数据
        when(tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE)).thenReturn(40L);
        when(tenantRepository.countByStatus(Tenant.TenantStatus.INACTIVE)).thenReturn(8L);
        when(tenantRepository.countByStatus(Tenant.TenantStatus.SUSPENDED)).thenReturn(2L);
        
        List<Tenant> mockTenants = Arrays.asList(
            createMockTenant("tenant1", "租户1"),
            createMockTenant("tenant2", "租户2")
        );
        Page<Tenant> tenantsPage = new PageImpl<>(mockTenants);
        when(tenantRepository.findAll(any(Pageable.class))).thenReturn(tenantsPage);

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/tenant-stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.statusDistribution.ACTIVE").value(40))
                .andExpect(jsonPath("$.data.statusDistribution.INACTIVE").value(8))
                .andExpect(jsonPath("$.data.statusDistribution.SUSPENDED").value(2));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取用户统计 - 成功")
    void testGetUserStats_Success() throws Exception {
        // 准备数据
        when(userRepository.count()).thenReturn(200L);
        when(userRepository.countByIsActive(true)).thenReturn(180L);
        when(userRepository.countByPlatformRole(PlatformUser.PlatformRole.ADMIN)).thenReturn(10L);

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/user-stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalUsers").value(200))
                .andExpect(jsonPath("$.data.activeUsers").value(180))
                .andExpect(jsonPath("$.data.inactiveUsers").value(20))
                .andExpect(jsonPath("$.data.adminUsers").value(10))
                .andExpect(jsonPath("$.data.regularUsers").value(190));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估统计 - 成功")
    void testGetAssessmentStats_Success() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(1500L);

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/assessment-stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalAssessments").value(1500));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表统计 - 成功")
    void testGetScaleStats_Success() throws Exception {
        // 准备数据
        when(scaleRepository.count()).thenReturn(30L);
        when(scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(25L);
        when(scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.INACTIVE)).thenReturn(3L);
        when(scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.DEPRECATED)).thenReturn(2L);
        when(scaleRepository.countByCategory()).thenReturn(new ArrayList<>());
        when(scaleRepository.countByPublisherType()).thenReturn(new ArrayList<>());
        when(scaleRepository.findTop10ByStatusOrderByUsageCountDesc("ACTIVE")).thenReturn(new ArrayList<>());

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/scale-stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalScales").value(30))
                .andExpect(jsonPath("$.data.statusDistribution.ACTIVE").value(25))
                .andExpect(jsonPath("$.data.statusDistribution.INACTIVE").value(3))
                .andExpect(jsonPath("$.data.statusDistribution.DEPRECATED").value(2));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取系统性能指标 - 成功")
    void testGetSystemPerformance_Success() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/performance"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.memory").exists())
                .andExpect(jsonPath("$.data.system").exists())
                .andExpect(jsonPath("$.data.database").exists())
                .andExpect(jsonPath("$.data.responseTime").exists());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取实时活动 - 成功")
    void testGetRecentActivities_Success() throws Exception {
        // 准备数据
        List<TenantAssessmentRecord> mockRecords = Arrays.asList(
            createMockAssessmentRecord("record1", "REC001"),
            createMockAssessmentRecord("record2", "REC002")
        );
        Page<TenantAssessmentRecord> recordsPage = new PageImpl<>(mockRecords);
        when(assessmentRepository.findAll(any(Pageable.class))).thenReturn(recordsPage);

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/activities"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取实时活动 - 自定义限制")
    void testGetRecentActivities_WithCustomLimit() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/activities")
                .param("limit", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问概览 - 返回403")
    void testAccessOverviewWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/overview"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问租户统计 - 返回403")
    void testAccessTenantStatsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/tenant-stats"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问用户统计 - 返回403")
    void testAccessUserStatsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/user-stats"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问评估统计 - 返回403")
    void testAccessAssessmentStatsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/assessment-stats"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问量表统计 - 返回403")
    void testAccessScaleStatsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/scale-stats"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问性能指标 - 返回403")
    void testAccessPerformanceWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/performance"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问实时活动 - 返回403")
    void testAccessActivitiesWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/activities"))
                .andExpect(status().isForbidden());
    }

    // 辅助方法
    private Tenant createMockTenant(String id, String name) {
        return Tenant.builder()
            .id(java.util.UUID.fromString("550e8400-e29b-41d4-a716-************"))
            .name(name)
            .code("T" + id.toUpperCase())
            .status(Tenant.TenantStatus.ACTIVE)
            .build();
    }

    private TenantAssessmentRecord createMockAssessmentRecord(String id, String recordNumber) {
        return TenantAssessmentRecord.builder()
            .id(id)
            .recordNumber(recordNumber)
            .tenantId("tenant1")
            .assessorId("user1")
            .status(TenantAssessmentRecord.RecordStatus.DRAFT)
            .build();
    }
}