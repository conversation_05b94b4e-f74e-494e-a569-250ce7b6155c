package com.assessment.controller;

import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemDashboardController 基础测试类
 */
@WebMvcTest(SystemDashboardController.class)
@DisplayName("系统监控面板控制器基础测试")
@SuppressWarnings("removal")
class SystemDashboardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private PlatformUserRepository userRepository;

    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @MockBean
    private TenantAssessmentRecordRepository assessmentRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取系统概览统计 - 基础HTTP测试")
    void testGetSystemOverview_HttpEndpoint() throws Exception {
        // 准备数据
        when(tenantRepository.count()).thenReturn(50L);
        when(userRepository.count()).thenReturn(200L);
        when(scaleRepository.count()).thenReturn(30L);
        when(assessmentRepository.count()).thenReturn(1000L);

        // 执行请求
        mockMvc.perform(get("/api/system/dashboard/overview"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/dashboard/overview"))
                .andExpect(status().isForbidden());
    }
}