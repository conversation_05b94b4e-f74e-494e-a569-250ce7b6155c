package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.config.LMStudioConfig;
import com.assessment.dto.ApiResponse;
import com.assessment.service.LMStudioService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

/**
 * LM Studio控制器单元测试
 * 使用Mock对象测试控制器逻辑
 */
@DisplayName("LM Studio控制器单元测试")
class LMStudioControllerUnitTest {

    private LMStudioController lmStudioController;
    private LMStudioService mockLMStudioService;
    private LMStudioConfig mockLMStudioConfig;

    @BeforeEach
    void setUp() {
        mockLMStudioService = mock(LMStudioService.class);
        mockLMStudioConfig = mock(LMStudioConfig.class);
        lmStudioController = new LMStudioController(mockLMStudioService, mockLMStudioConfig);
    }

    @Test
    @DisplayName("测试获取服务状态 - 可用")
    void testGetServiceStatus_Available() {
        // Arrange
        LMStudioConfig.ModelInfo mockModel = mock(LMStudioConfig.ModelInfo.class);
        when(mockModel.getDisplayName()).thenReturn("deepseek-r1-0528-qwen3-8b");
        
        when(mockLMStudioService.isServiceAvailable()).thenReturn(true);
        when(mockLMStudioService.getCurrentServerUrl()).thenReturn("192.168.1.231:1234");
        when(mockLMStudioService.getCurrentModel()).thenReturn(mockModel);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            lmStudioController.getServiceStatus();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("available")).isEqualTo(true);
        assertThat(data.get("currentServer")).isEqualTo("192.168.1.231:1234");
        assertThat(data.get("currentModel")).isEqualTo("deepseek-r1-0528-qwen3-8b");
    }

    @Test
    @DisplayName("测试获取服务状态 - 不可用")
    void testGetServiceStatus_Unavailable() {
        // Arrange
        when(mockLMStudioService.isServiceAvailable()).thenReturn(false);
        when(mockLMStudioService.getCurrentServerUrl()).thenReturn("192.168.1.231:1234");
        when(mockLMStudioService.getCurrentModel()).thenReturn(null);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            lmStudioController.getServiceStatus();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("available")).isEqualTo(false);
        assertThat(data.get("currentModel")).isEqualTo("未选择模型");
    }

    @Test
    @DisplayName("测试获取当前模型信息 - 成功")
    void testGetCurrentModel_Success() {
        // Arrange
        Map<String, Object> expectedModelInfo = Map.of(
            "modelName", "deepseek-r1-0528-qwen3-8b",
            "serverUrl", "192.168.1.231:1234",
            "status", "active"
        );
        when(mockLMStudioService.getCurrentModelInfo()).thenReturn(expectedModelInfo);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            lmStudioController.getCurrentModel();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedModelInfo);
    }

    @Test
    @DisplayName("测试获取可用模型列表 - 成功")
    void testGetAvailableModels_Success() {
        // Arrange
        LMStudioConfig.ModelInfo model1 = mock(LMStudioConfig.ModelInfo.class);
        LMStudioConfig.ModelInfo model2 = mock(LMStudioConfig.ModelInfo.class);
        List<LMStudioConfig.ModelInfo> expectedModels = Arrays.asList(model1, model2);
        
        when(mockLMStudioService.getAvailableModels()).thenReturn(expectedModels);

        // Act
        ResponseEntity<ApiResponse<List<LMStudioConfig.ModelInfo>>> response = 
            lmStudioController.getAvailableModels();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedModels);
        assertThat(response.getBody().getData()).hasSize(2);
    }

    @Test
    @DisplayName("测试刷新模型列表 - 成功")
    void testRefreshModels_Success() {
        // Arrange
        LMStudioConfig.ModelInfo model = mock(LMStudioConfig.ModelInfo.class);
        List<LMStudioConfig.ModelInfo> expectedModels = Arrays.asList(model);
        
        when(mockLMStudioService.refreshAvailableModels()).thenReturn(expectedModels);

        // Act
        ResponseEntity<ApiResponse<List<LMStudioConfig.ModelInfo>>> response = 
            lmStudioController.refreshModels();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedModels);
    }

    @Test
    @DisplayName("测试切换模型 - 成功")
    void testSwitchModel_Success() {
        // Arrange
        String modelId = "deepseek-r1-0528-qwen3-8b";
        when(mockLMStudioService.switchToModel(modelId)).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.switchModel(modelId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo("已切换到模型: " + modelId);
    }

    @Test
    @DisplayName("测试切换模型 - 失败")
    void testSwitchModel_Failure() {
        // Arrange
        String modelId = "invalid-model";
        when(mockLMStudioService.switchToModel(modelId)).thenReturn(false);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.switchModel(modelId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("切换模型失败，模型可能不存在或不可用");
    }

    @Test
    @DisplayName("测试切换服务器 - 成功")
    void testSwitchServer_Success() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        String serverUrl = "192.168.1.232:1234";
        request.put("serverUrl", serverUrl);
        
        when(mockLMStudioService.switchToServer(serverUrl)).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.switchServer(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo("已切换到服务器: " + serverUrl);
    }

    @Test
    @DisplayName("测试切换服务器 - 地址为空")
    void testSwitchServer_EmptyUrl() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        request.put("serverUrl", "");

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.switchServer(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("服务器地址不能为空");
    }

    @Test
    @DisplayName("测试切换服务器 - 失败")
    void testSwitchServer_Failure() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        String serverUrl = "invalid-server:1234";
        request.put("serverUrl", serverUrl);
        
        when(mockLMStudioService.switchToServer(serverUrl)).thenReturn(false);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.switchServer(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("切换服务器失败，服务器可能不健康");
    }

    @Test
    @DisplayName("测试自动切换服务器 - 成功")
    void testAutoSwitchServer_Success() {
        // Arrange
        String bestServerUrl = "192.168.1.231:1234";
        when(mockLMStudioService.autoSwitchToBestServer()).thenReturn(true);
        when(mockLMStudioService.getCurrentServerUrl()).thenReturn(bestServerUrl);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.autoSwitchServer();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo("已自动切换到最佳服务器: " + bestServerUrl);
    }

    @Test
    @DisplayName("测试自动切换服务器 - 失败")
    void testAutoSwitchServer_Failure() {
        // Arrange
        when(mockLMStudioService.autoSwitchToBestServer()).thenReturn(false);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            lmStudioController.autoSwitchServer();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("自动切换失败，没有找到健康的服务器");
    }

    @Test
    @DisplayName("测试获取配置信息 - 成功")
    void testGetConfig_Success() {
        // Act
        ResponseEntity<ApiResponse<LMStudioConfig>> response = 
            lmStudioController.getConfig();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isNotNull();
    }

    @Test
    @DisplayName("测试获取模型选择配置 - 成功")
    void testGetModelSelectionConfig_Success() {
        // Arrange
        LMStudioConfig.ModelInfo mockModel = mock(LMStudioConfig.ModelInfo.class);
        LMStudioConfig.ModelsConfig mockModels = mock(LMStudioConfig.ModelsConfig.class);
        LMStudioConfig.SelectionConfig mockSelection = mock(LMStudioConfig.SelectionConfig.class);
        Map<String, List<String>> mockCapabilityInference = new HashMap<>();
        mockCapabilityInference.put("code", Arrays.asList("coder", "programming"));
        
        when(mockLMStudioConfig.getModels()).thenReturn(mockModels);
        when(mockModels.getSelection()).thenReturn(mockSelection);
        when(mockModels.getExcludedPatterns()).thenReturn(Arrays.asList("test-*"));
        when(mockSelection.getPreferredPatterns()).thenReturn(Arrays.asList("deepseek-*"));
        when(mockSelection.getCapabilityInference()).thenReturn(mockCapabilityInference);
        when(mockLMStudioService.getCurrentModel()).thenReturn(mockModel);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            lmStudioController.getModelSelectionConfig();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("preferredPatterns");
        assertThat(data).containsKey("excludedPatterns");
        assertThat(data).containsKey("capabilityInference");
        assertThat(data).containsKey("currentModel");
    }

    @Test
    @DisplayName("测试获取自动切换配置 - 成功")
    void testGetAutoSwitchConfig_Success() {
        // Arrange
        LMStudioConfig.AutoSwitchConfig mockAutoSwitch = mock(LMStudioConfig.AutoSwitchConfig.class);
        when(mockLMStudioConfig.getAutoSwitch()).thenReturn(mockAutoSwitch);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            lmStudioController.getAutoSwitchConfig();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("autoSwitch");
        assertThat(data).containsKey("note");
        assertThat(data.get("note")).isEqualTo("所有模型参数（temperature、max_tokens等）由LM Studio管理");
    }
}