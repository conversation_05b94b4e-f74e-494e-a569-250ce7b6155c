package com.assessment.controller;

import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.assessment.entity.multitenant.TenantAssessmentRecord;

/**
 * SystemAssessmentController 基础测试类
 * 测试系统级评估记录管理功能的HTTP端点
 */
@WebMvcTest(SystemAssessmentController.class)
@DisplayName("系统评估控制器基础测试")
@SuppressWarnings("removal")
class SystemAssessmentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TenantAssessmentRecordRepository assessmentRepository;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @MockBean
    private PlatformUserRepository userRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估记录列表 - 基础HTTP测试")
    void testGetAssessments_HttpEndpoint() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估统计 - 基础HTTP测试")
    void testGetAssessmentStats_HttpEndpoint() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(100L);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/assessments"))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("未认证访问 - 返回401")
    void testUnauthenticatedAccess_Unauthorized() throws Exception {
        mockMvc.perform(get("/api/system/assessments"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按租户查询评估记录 - 基础HTTP测试")
    void testGetAssessmentsByTenant_HttpEndpoint() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findByTenantId(anyString(), any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("tenantId", "test-tenant-id")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估概览 - 基础HTTP测试")
    void testGetAssessmentOverview_HttpEndpoint() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(1000L);
        when(tenantRepository.count()).thenReturn(50L);
        when(scaleRepository.count()).thenReturn(30L);
        when(userRepository.count()).thenReturn(200L);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/overview"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
}