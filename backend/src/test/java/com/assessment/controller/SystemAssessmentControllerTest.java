package com.assessment.controller;

import com.assessment.controller.config.ControllerTestConfig;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemAssessmentController 基础测试类
 * 测试系统级评估记录管理功能的HTTP端点
 */
@WebMvcTest(SystemAssessmentController.class)
@Import(ControllerTestConfig.class)
@DisplayName("系统评估控制器基础测试")
@SuppressWarnings("removal")
class SystemAssessmentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private TenantAssessmentRecordRepository assessmentRepository;

    @MockBean
    private TenantRepository tenantRepository;

    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @MockBean
    private PlatformUserRepository userRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估记录列表 - 基础HTTP测试")
    void testGetAssessments_HttpEndpoint() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估统计 - 基础HTTP测试")
    void testGetAssessmentStats_HttpEndpoint() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(100L);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/assessments"))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("未认证访问 - 返回401")
    void testUnauthenticatedAccess_Unauthorized() throws Exception {
        mockMvc.perform(get("/api/system/assessments"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按租户查询评估记录 - 基础HTTP测试")
    void testGetAssessmentsByTenant_HttpEndpoint() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findByTenantId(anyString(), any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("tenantId", "test-tenant-id")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估详情 - 成功")
    void testGetAssessment_Success() throws Exception {
        // 准备数据
        String recordId = "record-123";
        TenantAssessmentRecord record = createMockAssessmentRecord(recordId, "REC001");
        when(assessmentRepository.findById(recordId)).thenReturn(Optional.of(record));

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/" + recordId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(recordId));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估详情 - 记录不存在")
    void testGetAssessment_NotFound() throws Exception {
        // 准备数据
        String recordId = "nonexistent";
        when(assessmentRepository.findById(recordId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/" + recordId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("审核评估记录 - 通过")
    void testReviewAssessment_Approve() throws Exception {
        // 准备数据
        String recordId = "record-123";
        TenantAssessmentRecord record = createMockAssessmentRecord(recordId, "REC001");
        when(assessmentRepository.findById(recordId)).thenReturn(Optional.of(record));
        when(assessmentRepository.save(any(TenantAssessmentRecord.class))).thenReturn(record);

        String requestBody = objectMapper.writeValueAsString(Map.of(
                "approved", true,
                "reviewNotes", "审核通过",
                "reviewerId", "reviewer-123"
        ));

        // 执行请求
        mockMvc.perform(post("/api/system/assessments/" + recordId + "/review")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("批量审核评估记录 - 成功")
    void testBatchReviewAssessments_Success() throws Exception {
        // 准备数据
        List<String> recordIds = Arrays.asList("record1", "record2", "record3");
        List<TenantAssessmentRecord> records = recordIds.stream()
                .map(id -> createMockAssessmentRecord(id, "REC" + id))
                .toList();
        
        when(assessmentRepository.findAllById(recordIds)).thenReturn(records);
        when(assessmentRepository.saveAll(anyList())).thenReturn(records);

        String requestBody = objectMapper.writeValueAsString(Map.of(
                "recordIds", recordIds,
                "approved", true,
                "reviewNotes", "批量审核通过",
                "reviewerId", "reviewer-123"
        ));

        // 执行请求
        mockMvc.perform(post("/api/system/assessments/batch-review")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("批量审核评估记录 - 记录ID列表为空")
    void testBatchReviewAssessments_EmptyIds() throws Exception {
        String requestBody = objectMapper.writeValueAsString(Map.of(
                "recordIds", List.of(),
                "approved", true,
                "reviewNotes", "批量审核",
                "reviewerId", "reviewer-123"
        ));

        // 执行请求
        mockMvc.perform(post("/api/system/assessments/batch-review")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("归档评估记录 - 成功")
    void testArchiveAssessment_Success() throws Exception {
        // 准备数据
        String recordId = "record-123";
        TenantAssessmentRecord record = createMockAssessmentRecord(recordId, "REC001");
        when(assessmentRepository.findById(recordId)).thenReturn(Optional.of(record));
        when(assessmentRepository.save(any(TenantAssessmentRecord.class))).thenReturn(record);

        // 执行请求
        mockMvc.perform(post("/api/system/assessments/" + recordId + "/archive"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("删除评估记录 - 成功")
    void testDeleteAssessment_Success() throws Exception {
        // 准备数据
        String recordId = "record-123";
        when(assessmentRepository.existsById(recordId)).thenReturn(true);

        // 执行请求
        mockMvc.perform(delete("/api/system/assessments/" + recordId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("删除评估记录 - 记录不存在")
    void testDeleteAssessment_NotFound() throws Exception {
        // 准备数据
        String recordId = "nonexistent";
        when(assessmentRepository.existsById(recordId)).thenReturn(false);

        // 执行请求
        mockMvc.perform(delete("/api/system/assessments/" + recordId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按状态查询评估记录 - 成功")
    void testGetAssessments_WithStatus() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findByTenantIdAndStatus(anyString(), anyString(), any(Pageable.class)))
            .thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("tenantId", "tenant-123")
                .param("status", "SUBMITTED")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按评估师查询评估记录 - 成功")
    void testGetAssessments_WithAssessor() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findByTenantIdAndAssessorId(anyString(), anyString(), any(Pageable.class)))
            .thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("tenantId", "tenant-123")
                .param("assessorId", "assessor-123")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按量表查询评估记录 - 成功")
    void testGetAssessments_WithScale() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findByTenantIdAndScaleId(anyString(), anyString(), any(Pageable.class)))
            .thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("tenantId", "tenant-123")
                .param("scaleId", "scale-123")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("自定义排序 - 成功")
    void testGetAssessments_WithCustomSort() throws Exception {
        // 准备数据
        Page<TenantAssessmentRecord> emptyPage = new PageImpl<>(new ArrayList<>());
        when(assessmentRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments")
                .param("sortField", "assessmentDate")
                .param("sortOrder", "ASC")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估统计 - 全局统计")
    void testGetAssessmentStats_Global() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(1000L);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估统计 - 租户统计")
    void testGetAssessmentStats_Tenant() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(100L);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/stats")
                .param("tenantId", "tenant-123"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取评估统计 - 时间范围")
    void testGetAssessmentStats_WithDateRange() throws Exception {
        // 准备数据
        when(assessmentRepository.count()).thenReturn(50L);

        // 执行请求
        mockMvc.perform(get("/api/system/assessments/stats")
                .param("startDate", "2024-01-01T00:00:00")
                .param("endDate", "2024-12-31T23:59:59"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问详情 - 返回403")
    void testAccessDetailsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/assessments/record-123"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员审核记录 - 返回403")
    void testReviewAssessmentWithoutAdminRole_Forbidden() throws Exception {
        String requestBody = objectMapper.writeValueAsString(Map.of(
                "approved", true,
                "reviewNotes", "审核通过",
                "reviewerId", "reviewer-123"
        ));

        mockMvc.perform(post("/api/system/assessments/record-123/review")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员删除记录 - 返回403")
    void testDeleteAssessmentWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(delete("/api/system/assessments/record-123"))
                .andExpect(status().isForbidden());
    }

    // 辅助方法
    private TenantAssessmentRecord createMockAssessmentRecord(String id, String recordNumber) {
        return TenantAssessmentRecord.builder()
                .id(id)
                .recordNumber(recordNumber)
                .tenantId("tenant-123")
                .subjectId("subject-123")
                .scaleId("scale-123")
                .scaleType(TenantAssessmentRecord.ScaleType.GLOBAL)
                .assessorId("assessor-123")
                .assessmentDate(LocalDateTime.now())
                .assessmentType(TenantAssessmentRecord.AssessmentType.REGULAR)
                .status(TenantAssessmentRecord.RecordStatus.DRAFT)
                .build();
    }
}