package com.assessment.controller;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.*;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * MultiTenantScaleController 简化测试类
 * 测试多租户量表控制器的主要功能
 */
@WebMvcTest(MultiTenantScaleController.class)
@DisplayName("多租户量表控制器测试")
@SuppressWarnings("removal")
class MultiTenantScaleControllerSimpleTest {

    @Autowired
    private MockMvc mockMvc;


    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @Test
    @DisplayName("获取公开量表 - 基本功能")
    @WithMockUser
    void testGetPublicScales_Basic() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> scales = createMockScales();
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, PageRequest.of(0, 20), scales.size());

        when(scaleRepository.findByVisibilityAndStatus(
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/public"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content.length()").value(3));
    }

    @Test
    @DisplayName("获取公开量表 - 搜索功能")
    @WithMockUser
    void testGetPublicScales_Search() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> scales = List.of(createMockScale("老年人", "老年人认知能力评估"));
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, PageRequest.of(0, 20), scales.size());

        when(scaleRepository.findByNameContainingAndVisibilityAndStatus(
                eq("老年人"),
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/public")
                .param("search", "老年人"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(1));
    }

    @Test
    @DisplayName("获取公开量表 - 分类筛选")
    @WithMockUser
    void testGetPublicScales_Category() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> scales = List.of(createMockScale("认知评估", "认知能力测试"));
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, PageRequest.of(0, 20), scales.size());

        when(scaleRepository.findByCategoryAndVisibilityAndStatus(
                eq("认知评估"),
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/public")
                .param("category", "认知评估"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(1));
    }

    @Test
    @DisplayName("获取公开量表 - 异常处理")
    @WithMockUser
    void testGetPublicScales_Exception() throws Exception {
        // 模拟异常
        when(scaleRepository.findByVisibilityAndStatus(
                any(GlobalScaleRegistry.Visibility.class),
                any(GlobalScaleRegistry.ScaleStatus.class),
                any(Pageable.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/public"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("获取量表列表失败: 数据库连接失败"));
    }

    @Test
    @DisplayName("获取量表详情 - 成功")
    @WithMockUser
    void testGetScaleDetails_Success() throws Exception {
        // 准备数据
        GlobalScaleRegistry scale = createMockScale("scale-123", "老年人能力评估");
        when(scaleRepository.findById("scale-123")).thenReturn(Optional.of(scale));

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/scale-123"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("scale-123"))
                .andExpect(jsonPath("$.name").value("老年人能力评估"));
    }

    @Test
    @DisplayName("获取量表详情 - 不存在")
    @WithMockUser
    void testGetScaleDetails_NotFound() throws Exception {
        // 模拟量表不存在
        when(scaleRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/nonexistent"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("获取量表详情 - 私有量表")
    @WithMockUser
    void testGetScaleDetails_PrivateScale() throws Exception {
        // 准备私有量表数据
        GlobalScaleRegistry privateScale = createMockScale("private-scale", "私有量表");
        privateScale.setVisibility(GlobalScaleRegistry.Visibility.PRIVATE);
        when(scaleRepository.findById("private-scale")).thenReturn(Optional.of(privateScale));

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/private-scale"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("获取量表分类 - 成功")
    @WithMockUser
    void testGetCategories_Success() throws Exception {
        // 准备数据
        List<String> categories = Arrays.asList("老年人评估", "认知评估", "心理评估");
        when(scaleRepository.findDistinctCategories()).thenReturn(categories);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3));
    }

    @Test
    @DisplayName("获取量表分类 - 异常处理")
    @WithMockUser
    void testGetCategories_Exception() throws Exception {
        // 模拟异常
        when(scaleRepository.findDistinctCategories())
                .thenThrow(new RuntimeException("查询分类失败"));

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/categories"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("增加量表使用次数 - 成功")
    @WithMockUser
    void testIncrementUsage_Success() throws Exception {
        // 准备数据
        GlobalScaleRegistry scale = createMockScale("usage-scale", "使用测试量表");
        when(scaleRepository.findById("usage-scale")).thenReturn(Optional.of(scale));
        when(scaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(scale);

        // 执行请求
        mockMvc.perform(post("/api/multi-tenant/scales/usage-scale/usage"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("使用次数已更新"));
    }

    @Test
    @DisplayName("增加量表使用次数 - 量表不存在")
    @WithMockUser
    void testIncrementUsage_NotFound() throws Exception {
        // 模拟量表不存在
        when(scaleRepository.findById("nonexistent")).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(post("/api/multi-tenant/scales/nonexistent/usage"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("获取热门量表 - 成功")
    @WithMockUser
    void testGetPopularScales_Success() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> popularScales = createMockScales();
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(popularScales, PageRequest.of(0, 10), popularScales.size());

        when(scaleRepository.findByVisibilityAndStatus(
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/popular"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3));
    }

    @Test
    @DisplayName("获取热门量表 - 自定义限制")
    @WithMockUser
    void testGetPopularScales_CustomLimit() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> popularScales = createMockScales().subList(0, 2);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(popularScales, PageRequest.of(0, 5), popularScales.size());

        when(scaleRepository.findByVisibilityAndStatus(
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/popular")
                .param("limit", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2));
    }

    @Test
    @DisplayName("获取热门量表 - 异常处理")
    @WithMockUser
    void testGetPopularScales_Exception() throws Exception {
        // 模拟异常
        when(scaleRepository.findByVisibilityAndStatus(
                any(GlobalScaleRegistry.Visibility.class),
                any(GlobalScaleRegistry.ScaleStatus.class),
                any(Pageable.class)))
                .thenThrow(new RuntimeException("查询热门量表失败"));

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/popular"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("分页功能测试")
    @WithMockUser
    void testPagination() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> scales = createMockScales().subList(0, 2);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, PageRequest.of(1, 2), 5);

        when(scaleRepository.findByVisibilityAndStatus(
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/public")
                .param("page", "1")
                .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.size").value(2))
                .andExpect(jsonPath("$.number").value(1))
                .andExpect(jsonPath("$.totalElements").value(5));
    }

    @Test
    @DisplayName("边界情况 - 空搜索词")
    @WithMockUser
    void testEmptySearch() throws Exception {
        // 准备数据
        List<GlobalScaleRegistry> scales = createMockScales();
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, PageRequest.of(0, 20), scales.size());

        when(scaleRepository.findByVisibilityAndStatus(
                eq(GlobalScaleRegistry.Visibility.PUBLIC),
                eq(GlobalScaleRegistry.ScaleStatus.ACTIVE),
                any(Pageable.class)))
                .thenReturn(scalesPage);

        // 执行请求 - 空字符串搜索
        mockMvc.perform(get("/api/multi-tenant/scales/public")
                .param("search", ""))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(3));
    }

    @Test
    @DisplayName("路径参数验证")
    @WithMockUser
    void testPathVariables() throws Exception {
        // 测试特殊字符ID
        String specialId = "scale-123_test";
        when(scaleRepository.findById(specialId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/multi-tenant/scales/" + specialId))
                .andExpect(status().isNotFound());

        mockMvc.perform(post("/api/multi-tenant/scales/" + specialId + "/usage"))
                .andExpect(status().isNotFound());
    }

    // 辅助方法
    private List<GlobalScaleRegistry> createMockScales() {
        return Arrays.asList(
                createMockScale("scale1", "老年人能力评估"),
                createMockScale("scale2", "认知功能测试"),
                createMockScale("scale3", "日常生活能力评估")
        );
    }

    private GlobalScaleRegistry createMockScale(String id, String name) {
        GlobalScaleRegistry scale = new GlobalScaleRegistry();
        scale.setId(id);
        scale.setName(name);
        scale.setCode("SCALE_" + id.toUpperCase());
        scale.setCategory("老年人评估");
        scale.setVersion("1.0");
        scale.setPublisherType(GlobalScaleRegistry.PublisherType.TENANT);
        scale.setPublisherId("tenant-123");
        scale.setVisibility(GlobalScaleRegistry.Visibility.PUBLIC);
        scale.setStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        scale.setUsageCount(100L);
        scale.setCreatedAt(LocalDateTime.now());
        scale.setUpdatedAt(LocalDateTime.now());
        return scale;
    }
}