package com.assessment.controller;

import com.assessment.controller.config.ControllerTestConfig;
import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.service.MultiTenantAuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * MultiTenantAuthController 全面测试类
 * 测试多租户认证控制器的所有HTTP端点
 */
@WebMvcTest(MultiTenantAuthController.class)
@Import(ControllerTestConfig.class)
@DisplayName("多租户认证控制器全面测试")
@SuppressWarnings("removal")
class MultiTenantAuthControllerComprehensiveTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private MultiTenantAuthService authService;

    @Test
    @DisplayName("多租户登录 - 成功")
    void testLogin_Success() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("doctor001");
        request.setPassword("password123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "hospital", "doctor001", "ASSESSOR", "jwt-token-123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("jwt-token-123"))
                .andExpect(jsonPath("$.username").value("doctor001"))
                .andExpect(jsonPath("$.tenantCode").value("hospital"))
                .andExpect(jsonPath("$.tenantRole").value("ASSESSOR"))
                .andExpect(jsonPath("$.permissions").isArray());
    }

    @Test
    @DisplayName("多租户登录 - 租户代码为空")
    void testLogin_EmptyTenantCode() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("");
        request.setUsername("user001");
        request.setPassword("password123");

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 用户名为空")
    void testLogin_EmptyUsername() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("");
        request.setPassword("password123");

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 密码为空")
    void testLogin_EmptyPassword() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("user001");
        request.setPassword("");

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 认证失败")
    void testLogin_AuthenticationFailure() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("wronguser");
        request.setPassword("wrongpassword");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenThrow(new BadCredentialsException("用户名或密码错误"));

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 租户不存在")
    void testLogin_TenantNotFound() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("nonexistent");
        request.setUsername("user001");
        request.setPassword("password123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenThrow(new IllegalArgumentException("租户不存在"));

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 租户被禁用")
    void testLogin_TenantDisabled() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("disabled_tenant");
        request.setUsername("user001");
        request.setPassword("password123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenThrow(new IllegalStateException("租户已被禁用"));

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 服务器内部错误")
    void testLogin_ServerError() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("user001");
        request.setPassword("password123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("多租户登录 - 无效JSON格式")
    void testLogin_InvalidJson() throws Exception {
        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json format}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("超级管理员登录 - 成功")
    void testSuperAdminLogin_Success() throws Exception {
        // 准备数据
        Map<String, String> request = new HashMap<>();
        request.put("username", "superadmin");
        request.put("password", "admin123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "platform", "superadmin", "SUPER_ADMIN", "admin-jwt-token-123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/superadmin/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("admin-jwt-token-123"))
                .andExpect(jsonPath("$.username").value("superadmin"))
                .andExpect(jsonPath("$.tenantCode").value("platform"))
                .andExpect(jsonPath("$.tenantRole").value("SUPER_ADMIN"));
    }

    @Test
    @DisplayName("超级管理员登录 - 认证失败")
    void testSuperAdminLogin_AuthenticationFailure() throws Exception {
        // 准备数据
        Map<String, String> request = new HashMap<>();
        request.put("username", "wrongadmin");
        request.put("password", "wrongpassword");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenThrow(new BadCredentialsException("超级管理员认证失败"));

        // 执行请求
        mockMvc.perform(post("/api/auth/superadmin/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("超级管理员登录 - 缺少用户名")
    void testSuperAdminLogin_MissingUsername() throws Exception {
        // 准备数据
        Map<String, String> request = new HashMap<>();
        request.put("password", "admin123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "platform", null, "SUPER_ADMIN", "admin-jwt-token-123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/superadmin/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("超级管理员登录 - 缺少密码")
    void testSuperAdminLogin_MissingPassword() throws Exception {
        // 准备数据
        Map<String, String> request = new HashMap<>();
        request.put("username", "superadmin");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "platform", "superadmin", "SUPER_ADMIN", "admin-jwt-token-123");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/superadmin/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value("superadmin"));
    }

    @Test
    @DisplayName("获取当前用户信息 - 成功")
    void testGetCurrentUser_Success() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/auth/me"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("需要从JWT Token中解析用户信息"));
    }

    @Test
    @DisplayName("刷新Token - 成功")
    void testRefreshToken_Success() throws Exception {
        // 准备数据
        Map<String, String> request = new HashMap<>();
        request.put("refreshToken", "refresh-token-123");

        // 执行请求
        mockMvc.perform(post("/api/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Token刷新功能待实现"))
                .andExpect(jsonPath("$.refreshToken").value("refresh-token-123"));
    }

    @Test
    @DisplayName("刷新Token - 空请求体")
    void testRefreshToken_EmptyRequest() throws Exception {
        // 准备数据
        Map<String, String> request = new HashMap<>();

        // 执行请求
        mockMvc.perform(post("/api/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Token刷新功能待实现"));
    }

    @Test
    @DisplayName("用户登出 - 成功")
    void testLogout_Success() throws Exception {
        // 执行请求
        mockMvc.perform(post("/api/auth/logout"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("登出成功"));
    }

    @Test
    @DisplayName("获取登录配置 - 成功")
    void testGetLoginConfig_Success() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/auth/config"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.systemName").value("智能评估平台"))
                .andExpect(jsonPath("$.version").value("2.0.0"))
                .andExpect(jsonPath("$.loginType").value("multi-tenant"))
                .andExpect(jsonPath("$.loginHints").isMap())
                .andExpect(jsonPath("$.loginHints.tenantCode").value("请输入您的机构代码"))
                .andExpect(jsonPath("$.loginHints.username").value("请输入用户名"))
                .andExpect(jsonPath("$.loginHints.password").value("请输入密码"))
                .andExpect(jsonPath("$.demoAccounts").isMap())
                .andExpect(jsonPath("$.demoAccounts.hospital").isMap())
                .andExpect(jsonPath("$.demoAccounts.nursing").isMap())
                .andExpect(jsonPath("$.demoAccounts.superadmin").isMap());
    }

    @Test
    @DisplayName("获取登录配置 - 详细内容验证")
    void testGetLoginConfig_DetailedContent() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/auth/config"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.demoAccounts.hospital.tenantCode").value("demo_hospital"))
                .andExpect(jsonPath("$.demoAccounts.nursing.tenantCode").value("demo_nursing"))
                .andExpect(jsonPath("$.demoAccounts.superadmin.username").value("superadmin"))
                .andExpect(jsonPath("$.demoAccounts.superadmin.password").value("password123"));
    }

    @Test
    @DisplayName("不同角色登录 - ADMIN角色")
    void testLogin_AdminRole() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("admin001");
        request.setPassword("password123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "hospital", "admin001", "ADMIN", "admin-jwt-token");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tenantRole").value("ADMIN"));
    }

    @Test
    @DisplayName("不同角色登录 - REVIEWER角色")
    void testLogin_ReviewerRole() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("nursing");
        request.setUsername("reviewer001");
        request.setPassword("password123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "nursing", "reviewer001", "REVIEWER", "reviewer-jwt-token");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tenantRole").value("REVIEWER"));
    }

    @Test
    @DisplayName("不同角色登录 - VIEWER角色")
    void testLogin_ViewerRole() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("research");
        request.setUsername("viewer001");
        request.setPassword("password123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "research", "viewer001", "VIEWER", "viewer-jwt-token");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tenantRole").value("VIEWER"));
    }

    @Test
    @DisplayName("多租户登录 - 长用户名和密码")
    void testLogin_LongCredentials() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("very_long_username_with_special_characters_12345");
        request.setPassword("very_long_password_with_special_characters_!@#$%^&*()_+");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "hospital", request.getUsername(), "ASSESSOR", "long-jwt-token");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value(request.getUsername()));
    }

    @Test
    @DisplayName("多租户登录 - 特殊字符在租户代码中")
    void testLogin_SpecialCharactersInTenantCode() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("tenant-with-dashes_and_underscores");
        request.setUsername("user001");
        request.setPassword("password123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                request.getTenantCode(), "user001", "ASSESSOR", "special-jwt-token");

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tenantCode").value(request.getTenantCode()));
    }

    @Test
    @DisplayName("多租户登录 - 完整权限列表")
    void testLogin_CompletePermissionsList() throws Exception {
        // 准备数据
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("hospital");
        request.setUsername("fullaccess001");
        request.setPassword("password123");

        MultiTenantLoginResponse response = createMockLoginResponse(
                "hospital", "fullaccess001", "ADMIN", "full-access-jwt-token");
        response.setPermissions(Arrays.asList(
                "USER_READ", "USER_WRITE", "USER_DELETE",
                "ASSESSMENT_READ", "ASSESSMENT_WRITE", "ASSESSMENT_DELETE",
                "SCALE_READ", "SCALE_WRITE", "SCALE_DELETE",
                "REPORT_READ", "REPORT_WRITE", "REPORT_EXPORT",
                "SYSTEM_ADMIN", "TENANT_ADMIN"
        ));

        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
                .thenReturn(response);

        // 执行请求
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.permissions").isArray())
                .andExpect(jsonPath("$.permissions.length()").value(14))
                .andExpect(jsonPath("$.permissions[0]").value("USER_READ"))
                .andExpect(jsonPath("$.permissions[13]").value("TENANT_ADMIN"));
    }

    // 辅助方法
    private MultiTenantLoginResponse createMockLoginResponse(
            String tenantCode, String username, String role, String token) {
        MultiTenantLoginResponse response = new MultiTenantLoginResponse();
        response.setTenantCode(tenantCode);
        response.setUsername(username);
        response.setTenantRole(role);
        response.setAccessToken(token);
        response.setTenantId("tenant-id-" + tenantCode);
        response.setUserId("user-id-" + username);
        response.setTenantName(tenantCode + "租户");
        response.setExpiresIn(7200L);
        response.setPermissions(Arrays.asList("USER_READ", "ASSESSMENT_READ", "SCALE_READ"));
        return response;
    }
}