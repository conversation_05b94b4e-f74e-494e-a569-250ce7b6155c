package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import com.assessment.service.DatabaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 数据库控制器单元测试
 * 使用Mock对象测试控制器逻辑
 */
@DisplayName("数据库控制器单元测试")
class DatabaseControllerUnitTest {

    private DatabaseController databaseController;
    private DatabaseService mockDatabaseService;

    @BeforeEach
    void setUp() {
        mockDatabaseService = mock(DatabaseService.class);
        databaseController = new DatabaseController(mockDatabaseService);
    }

    @Test
    @DisplayName("测试执行DDL - 成功场景")
    void testExecuteDDL_Success() {
        // Arrange
        ExecuteDDLRequest request = new ExecuteDDLRequest();
        request.setTableName("test_table");
        request.setSql("CREATE TABLE test_table (id SERIAL PRIMARY KEY)");

        ExecuteDDLResult expectedResult = new ExecuteDDLResult();
        expectedResult.setSuccess(true);
        expectedResult.setTableName("test_table");
        expectedResult.setMessage("表创建成功");

        when(mockDatabaseService.executeDDL(any(ExecuteDDLRequest.class)))
            .thenReturn(expectedResult);

        // Act
        ResponseEntity<ApiResponse<ExecuteDDLResult>> response = 
            databaseController.executeDDL(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedResult);
    }

    @Test
    @DisplayName("测试执行DDL - SQL为空")
    void testExecuteDDL_EmptySql() {
        // Arrange
        ExecuteDDLRequest request = new ExecuteDDLRequest();
        request.setTableName("test_table");
        request.setSql("");

        // Act
        ResponseEntity<ApiResponse<ExecuteDDLResult>> response = 
            databaseController.executeDDL(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("SQL语句不能为空");
    }

    @Test
    @DisplayName("测试执行DDL - 表名为空")
    void testExecuteDDL_EmptyTableName() {
        // Arrange
        ExecuteDDLRequest request = new ExecuteDDLRequest();
        request.setTableName("");
        request.setSql("CREATE TABLE test_table (id SERIAL PRIMARY KEY)");

        // Act
        ResponseEntity<ApiResponse<ExecuteDDLResult>> response = 
            databaseController.executeDDL(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("表名不能为空");
    }

    @Test
    @DisplayName("测试执行DDL - null请求")
    void testExecuteDDL_NullRequest() {
        // Arrange
        ExecuteDDLRequest request = new ExecuteDDLRequest();
        request.setTableName(null);
        request.setSql(null);

        // Act
        ResponseEntity<ApiResponse<ExecuteDDLResult>> response = 
            databaseController.executeDDL(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("SQL语句不能为空");
    }
}