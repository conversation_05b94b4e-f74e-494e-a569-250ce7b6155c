package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * 简单控制器单元测试
 * 直接测试控制器方法，不依赖Spring上下文
 */
@DisplayName("简单控制器单元测试")
class SimpleControllerTest {

    @Test
    @DisplayName("测试IndexController首页方法")
    void testIndexControllerIndex() {
        // Arrange
        IndexController indexController = new IndexController();

        // Act
        Map<String, Object> result = indexController.index();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.get("status")).isEqualTo("running");
        assertThat(result.get("name")).isEqualTo("智慧养老评估平台");
        assertThat(result.get("version")).isEqualTo("1.0.0");
        assertThat(result.get("message")).isEqualTo("欢迎使用智慧养老评估平台API");
        assertThat(result.get("documentation")).isEqualTo("/swagger-ui.html");
        assertThat(result.get("health")).isEqualTo("/api/health");
        assertThat(result.get("timestamp")).isNotNull();
    }

    @Test
    @DisplayName("测试HealthController健康检查方法")
    void testHealthControllerHealth() {
        // Arrange
        HealthController healthController = new HealthController();

        // Act
        Map<String, Object> result = healthController.health();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.get("status")).isEqualTo("OK");
        assertThat(result.get("message")).isEqualTo("智慧养老评估平台后端服务运行正常");
        assertThat(result.get("timestamp")).isNotNull();
    }

    @Test
    @DisplayName("测试HealthController系统信息方法")
    void testHealthControllerInfo() {
        // Arrange
        HealthController healthController = new HealthController();

        // Act
        Map<String, Object> result = healthController.info();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.get("name")).isEqualTo("Elderly Assessment Platform");
        assertThat(result.get("version")).isEqualTo("1.0.0-SNAPSHOT");
        assertThat(result.get("description")).isEqualTo("智慧养老评估平台后端服务");
        assertThat(result.get("java.version")).isNotNull();
    }
}