package com.assessment.controller;

import com.assessment.controller.config.ControllerTestConfig;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PasswordHashController 测试类
 */
@WebMvcTest(PasswordHashController.class)
@Import(ControllerTestConfig.class)
@DisplayName("密码哈希控制器测试")
class PasswordHashControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    @DisplayName("测试密码哈希生成 - 普通密码")
    @WithMockUser
    void testHashPassword_NormalPassword() throws Exception {
        // Arrange
        String password = "testPassword123";

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.not(password))) // 哈希不应该等于原密码
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$"))); // BCrypt哈希格式
    }

    @Test
    @DisplayName("测试密码哈希生成 - 空密码")
    @WithMockUser
    void testHashPassword_EmptyPassword() throws Exception {
        // Arrange
        String password = "";

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$"))); // 即使空密码也应该有有效的BCrypt格式
    }

    @Test
    @DisplayName("测试密码哈希生成 - 长密码")
    @WithMockUser
    void testHashPassword_LongPassword() throws Exception {
        // Arrange - BCrypt限制在72字节以内
        String password = "a".repeat(70); // 保持在BCrypt限制内

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.not(password)))
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$")));
    }

    @Test
    @DisplayName("测试密码哈希生成 - 特殊字符")
    @WithMockUser
    void testHashPassword_SpecialCharacters() throws Exception {
        // Arrange
        String password = "!@#$%^&*()_+-=[]{}|;:'\",.<>?/`~";

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.not(password)))
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$")));
    }

    @Test
    @DisplayName("测试密码哈希生成 - Unicode字符")
    @WithMockUser
    void testHashPassword_UnicodeCharacters() throws Exception {
        // Arrange
        String password = "测试密码123";

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.not(password)))
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$")));
    }

    @Test
    @DisplayName("测试密码哈希生成 - 数字密码")
    @WithMockUser
    void testHashPassword_NumericPassword() throws Exception {
        // Arrange
        String password = "123456789";

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.not(password)))
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$")));
    }

    @Test
    @DisplayName("测试密码哈希生成 - 缺少密码参数")
    @WithMockUser
    void testHashPassword_MissingParameter() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/hash/password"))
                .andExpect(status().isInternalServerError()); // 缺少必需参数导致500错误
    }

    @Test
    @DisplayName("测试密码哈希生成 - 空白密码")
    @WithMockUser
    void testHashPassword_WhitespacePassword() throws Exception {
        // Arrange
        String password = "   ";

        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.not(password)))
                .andExpect(content().string(org.hamcrest.Matchers.startsWith("$2a$")));
    }

    @Test
    @DisplayName("测试密码哈希生成 - 每次调用产生不同哈希")
    @WithMockUser
    void testHashPassword_DifferentHashEachTime() throws Exception {
        // Arrange
        String password = "samePassword";

        // Act - 执行两次相同的请求
        String result1 = mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        String result2 = mockMvc.perform(get("/api/hash/password")
                .param("password", password))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // Assert - 两次哈希结果应该不同（BCrypt使用随机盐）
        org.assertj.core.api.Assertions.assertThat(result1).isNotEqualTo(result2);
        org.assertj.core.api.Assertions.assertThat(result1).startsWith("$2a$");
        org.assertj.core.api.Assertions.assertThat(result2).startsWith("$2a$");
    }

    @Test
    @DisplayName("测试CORS配置 - 预检请求")
    @WithMockUser
    void testCorsConfiguration() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/hash/password")
                .param("password", "test")
                .header("Origin", "http://localhost:5273"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "http://localhost:5273"));
    }
}