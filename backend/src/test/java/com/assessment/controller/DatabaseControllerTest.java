package com.assessment.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import com.assessment.service.DatabaseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据库控制器测试
 */
@WebMvcTest(controllers = DatabaseController.class)
@DisplayName("数据库控制器测试")
@SuppressWarnings("removal")  // MockBean在Spring Boot 3.4+中被标记为弃用，但仍可使用
class DatabaseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DatabaseService databaseService;

    @Autowired
    private ObjectMapper objectMapper;

    private ExecuteDDLRequest validRequest;
    private ExecuteDDLResult successResult;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        validRequest = new ExecuteDDLRequest();
        validRequest.setTableName("test_assessment");
        validRequest.setSql("CREATE TABLE test_assessment (id SERIAL PRIMARY KEY, name VARCHAR(100))");
        validRequest.setOverwriteExisting(true);
        
        // 设置执行选项
        ExecuteDDLRequest.ExecutionOptions options = ExecuteDDLRequest.ExecutionOptions.builder()
            .checkTableExists(true)
            .backupExistingTable(true)
            .forceExecution(false)
            .build();
        validRequest.setOptions(options);

        successResult = new ExecuteDDLResult();
        successResult.setSuccess(true);
        successResult.setMessage("DDL执行成功");
        successResult.setTableName("test_assessment");
        successResult.setExecutionTimeMs(100L);
    }

    @Test
    @DisplayName("测试执行DDL - 成功场景")
    @WithMockUser(roles = "ADMIN")
    void testExecuteDDLSuccess() throws Exception {
        // Arrange
        when(databaseService.executeDDL(any(ExecuteDDLRequest.class)))
            .thenReturn(successResult);

        // Act & Assert
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.message").value("DDL执行成功"))
                .andExpect(jsonPath("$.data.tableName").value("test_assessment"))
                .andExpect(jsonPath("$.data.executionTimeMs").value(100));
    }

    @Test
    @DisplayName("测试执行DDL - SQL为空")
    @WithMockUser(roles = "ADMIN")
    void testExecuteDDLWithEmptySQL() throws Exception {
        // Arrange
        validRequest.setSql("");

        // Act & Assert
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("SQL语句不能为空"));
    }

    @Test
    @DisplayName("测试执行DDL - 表名为空")
    @WithMockUser(roles = "ADMIN")
    void testExecuteDDLWithEmptyTableName() throws Exception {
        // Arrange
        validRequest.setTableName("");

        // Act & Assert
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("表名不能为空"));
    }

    @Test
    @DisplayName("测试执行DDL - 服务抛出异常")
    @WithMockUser(roles = "ADMIN")
    void testExecuteDDLWithServiceException() throws Exception {
        // Arrange
        when(databaseService.executeDDL(any(ExecuteDDLRequest.class)))
            .thenThrow(new RuntimeException("数据库连接失败"));

        // Act & Assert
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("DDL执行失败: 数据库连接失败"));
    }

    @Test
    @DisplayName("测试执行DDL - 强制执行模式")
    @WithMockUser(roles = "ADMIN")
    void testExecuteDDLForceExecution() throws Exception {
        // Arrange
        ExecuteDDLRequest.ExecutionOptions forceOptions = ExecuteDDLRequest.ExecutionOptions.builder()
            .checkTableExists(false)
            .backupExistingTable(false)
            .forceExecution(true)
            .build();
        validRequest.setOptions(forceOptions);
        
        ExecuteDDLResult forceResult = new ExecuteDDLResult();
        forceResult.setSuccess(true);
        forceResult.setMessage("强制执行模式: DDL执行成功");
        forceResult.setTableName("test_assessment");
        
        when(databaseService.executeDDL(any(ExecuteDDLRequest.class)))
            .thenReturn(forceResult);

        // Act & Assert
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.message").value("强制执行模式: DDL执行成功"));
    }

    @Test
    @DisplayName("测试检查表是否存在")
    @WithMockUser(roles = "ADMIN")
    void testCheckTableExists() throws Exception {
        // Arrange
        when(databaseService.tableExists("test_assessment")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(get("/api/database/table-exists/test_assessment")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    @DisplayName("测试获取表结构信息")
    @WithMockUser(roles = "ADMIN")
    void testGetTableStructure() throws Exception {
        // Arrange
        Map<String, Object> structure = new HashMap<>();
        structure.put("tableName", "test_assessment");
        structure.put("columns", 5);
        
        when(databaseService.getTableStructure("test_assessment")).thenReturn(structure);

        // Act & Assert
        mockMvc.perform(get("/api/database/table-structure/test_assessment")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.tableName").value("test_assessment"))
                .andExpect(jsonPath("$.data.columns").value(5));
    }

    @Test
    @DisplayName("测试未授权访问")
    void testUnauthorizedAccess() throws Exception {
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("测试权限不足")
    @WithMockUser(roles = "USER")
    void testInsufficientPermission() throws Exception {
        mockMvc.perform(post("/api/database/execute-ddl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isForbidden());
    }
}