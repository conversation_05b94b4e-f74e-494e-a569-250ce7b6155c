package com.assessment.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

/**
 * 健康检查控制器测试
 * 测试实际的HealthController实现
 */
@WebMvcTest(controllers = HealthController.class)
@DisplayName("健康检查控制器测试")
class HealthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    @DisplayName("测试健康检查接口 - 返回正常状态")
    void testHealth() throws Exception {
        mockMvc.perform(get("/api/health"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("OK"))
                .andExpect(jsonPath("$.message").value("智慧养老评估平台后端服务运行正常"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    @DisplayName("测试系统信息接口 - 返回应用详情")
    void testInfo() throws Exception {
        mockMvc.perform(get("/api/health/info"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("Elderly Assessment Platform"))
                .andExpect(jsonPath("$.version").value("1.0.0-SNAPSHOT"))
                .andExpect(jsonPath("$.description").value("智慧养老评估平台后端服务"))
                .andExpect(jsonPath("$.['java.version']").exists());
    }
}