package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Map;

/**
 * 系统仪表板控制器基础单元测试
 * 只测试已确认存在的方法
 */
@DisplayName("系统仪表板控制器基础单元测试")
class SystemDashboardControllerBasicTest {

    private SystemDashboardController systemDashboardController;
    private TenantRepository mockTenantRepository;
    private PlatformUserRepository mockUserRepository;
    private GlobalScaleRegistryRepository mockScaleRepository;
    private TenantAssessmentRecordRepository mockAssessmentRepository;

    @BeforeEach
    void setUp() {
        mockTenantRepository = mock(TenantRepository.class);
        mockUserRepository = mock(PlatformUserRepository.class);
        mockScaleRepository = mock(GlobalScaleRegistryRepository.class);
        mockAssessmentRepository = mock(TenantAssessmentRecordRepository.class);
        
        systemDashboardController = new SystemDashboardController(
            mockTenantRepository,
            mockUserRepository,
            mockScaleRepository,
            mockAssessmentRepository
        );
    }

    @Test
    @DisplayName("测试获取系统概览 - 基本统计")
    void testGetSystemOverview_BasicStats() {
        // Arrange
        when(mockTenantRepository.count()).thenReturn(100L);
        when(mockTenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE)).thenReturn(85L);
        when(mockUserRepository.count()).thenReturn(1000L);
        when(mockUserRepository.countByIsActive(true)).thenReturn(900L);
        when(mockScaleRepository.count()).thenReturn(50L);
        when(mockScaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(45L);
        when(mockAssessmentRepository.count()).thenReturn(10000L);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemDashboardController.getSystemOverview();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("totalTenants");
        assertThat(data).containsKey("activeTenants");
        assertThat(data).containsKey("totalUsers");
        assertThat(data).containsKey("activeUsers");
        assertThat(data).containsKey("totalScales");
        assertThat(data).containsKey("activeScales");
        assertThat(data).containsKey("totalAssessments");
        
        assertThat(data.get("totalTenants")).isEqualTo(100L);
        assertThat(data.get("activeTenants")).isEqualTo(85L);
        assertThat(data.get("totalUsers")).isEqualTo(1000L);
        assertThat(data.get("activeUsers")).isEqualTo(900L);
    }

    @Test
    @DisplayName("测试获取系统概览 - 空数据")
    void testGetSystemOverview_EmptyData() {
        // Arrange
        when(mockTenantRepository.count()).thenReturn(0L);
        when(mockTenantRepository.countByStatus(any())).thenReturn(0L);
        when(mockUserRepository.count()).thenReturn(0L);
        when(mockUserRepository.countByIsActive(any())).thenReturn(0L);
        when(mockScaleRepository.count()).thenReturn(0L);
        when(mockScaleRepository.countByStatus(any())).thenReturn(0L);
        when(mockAssessmentRepository.count()).thenReturn(0L);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemDashboardController.getSystemOverview();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalTenants")).isEqualTo(0L);
        assertThat(data.get("activeTenants")).isEqualTo(0L);
        assertThat(data.get("totalUsers")).isEqualTo(0L);
        assertThat(data.get("activeUsers")).isEqualTo(0L);
    }

    @Test
    @DisplayName("测试获取租户统计 - 基本功能")
    void testGetTenantStats_Basic() {
        // Arrange
        when(mockTenantRepository.count()).thenReturn(50L);
        when(mockTenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE)).thenReturn(45L);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemDashboardController.getTenantStats();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("totalTenants");
        assertThat(data).containsKey("activeTenants");
        assertThat(data.get("totalTenants")).isEqualTo(50L);
        assertThat(data.get("activeTenants")).isEqualTo(45L);
    }

    @Test
    @DisplayName("测试获取量表统计 - 基本功能")
    void testGetScaleStats_Basic() {
        // Arrange
        when(mockScaleRepository.count()).thenReturn(25L);
        when(mockScaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(22L);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemDashboardController.getScaleStats();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("totalScales");
        assertThat(data).containsKey("activeScales");
        assertThat(data.get("totalScales")).isEqualTo(25L);
        assertThat(data.get("activeScales")).isEqualTo(22L);
    }
}