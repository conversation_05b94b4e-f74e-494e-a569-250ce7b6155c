package com.assessment.controller;

import com.assessment.controller.config.ControllerTestConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TestPasswordController 测试类
 */
@WebMvcTest(TestPasswordController.class)
@Import(ControllerTestConfig.class)
@DisplayName("密码测试控制器测试")
class TestPasswordControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试生成密码哈希 - 默认密码")
    @WithMockUser
    void testGenerateHash_DefaultPassword() throws Exception {
        // Arrange
        Map<String, String> request = Map.of();

        // Act & Assert
        mockMvc.perform(post("/api/test/generate-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value("password123"))
                .andExpect(jsonPath("$.hash").exists())
                .andExpect(jsonPath("$.verified").value("true"));
    }

    @Test
    @DisplayName("测试生成密码哈希 - 自定义密码")
    @WithMockUser
    void testGenerateHash_CustomPassword() throws Exception {
        // Arrange
        String customPassword = "myCustomPassword456";
        Map<String, String> request = Map.of("password", customPassword);

        // Act & Assert
        mockMvc.perform(post("/api/test/generate-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(customPassword))
                .andExpect(jsonPath("$.hash").exists())
                .andExpect(jsonPath("$.verified").value("true"));
    }

    @Test
    @DisplayName("测试生成密码哈希 - 空密码")
    @WithMockUser
    void testGenerateHash_EmptyPassword() throws Exception {
        // Arrange
        Map<String, String> request = Map.of("password", "");

        // Act & Assert
        mockMvc.perform(post("/api/test/generate-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(""))
                .andExpect(jsonPath("$.hash").exists())
                .andExpect(jsonPath("$.verified").value("true"));
    }

    @Test
    @DisplayName("测试验证密码哈希 - 匹配的密码")
    @WithMockUser
    void testVerifyHash_MatchingPassword() throws Exception {
        // Arrange - 使用已知的BCrypt哈希
        String password = "password";
        String hash = "$2a$10$j/N0KmU6dQVm9q88vdZcyeAiL4ylPnboPx7YibJDAACipl27RJzdy"; // "password" 的BCrypt哈希
        Map<String, String> request = Map.of(
            "password", password,
            "hash", hash
        );

        // Act & Assert
        mockMvc.perform(post("/api/test/verify-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(password))
                .andExpect(jsonPath("$.hash").value(hash))
                .andExpect(jsonPath("$.matches").value("true"));
    }

    @Test
    @DisplayName("测试验证密码哈希 - 不匹配的密码")
    @WithMockUser
    void testVerifyHash_NonMatchingPassword() throws Exception {
        // Arrange
        String password = "wrongPassword";
        String hash = "$2a$10$j/N0KmU6dQVm9q88vdZcyeAiL4ylPnboPx7YibJDAACipl27RJzdy"; // "password" 的BCrypt哈希
        Map<String, String> request = Map.of(
            "password", password,
            "hash", hash
        );

        // Act & Assert
        mockMvc.perform(post("/api/test/verify-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(password))
                .andExpect(jsonPath("$.hash").value(hash))
                .andExpect(jsonPath("$.matches").value("false"));
    }

    @Test
    @DisplayName("测试验证密码哈希 - 空密码")
    @WithMockUser
    void testVerifyHash_EmptyPassword() throws Exception {
        // Arrange
        String password = "";
        String hash = "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.";
        Map<String, String> request = Map.of(
            "password", password,
            "hash", hash
        );

        // Act & Assert
        mockMvc.perform(post("/api/test/verify-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(""))
                .andExpect(jsonPath("$.hash").value(hash))
                .andExpect(jsonPath("$.matches").value("false"));
    }

    @Test
    @DisplayName("测试验证密码哈希 - 空哈希")
    @WithMockUser
    void testVerifyHash_EmptyHash() throws Exception {
        // Arrange
        String password = "testPassword";
        String hash = "";
        Map<String, String> request = Map.of(
            "password", password,
            "hash", hash
        );

        // Act & Assert
        mockMvc.perform(post("/api/test/verify-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(password))
                .andExpect(jsonPath("$.hash").value(""))
                .andExpect(jsonPath("$.matches").value("false"));
    }

    @Test
    @DisplayName("测试生成密码哈希 - 特殊字符密码")
    @WithMockUser
    void testGenerateHash_SpecialCharacters() throws Exception {
        // Arrange
        String specialPassword = "!@#$%^&*()_+-=[]{}|;:'\",.<>?/`~";
        Map<String, String> request = Map.of("password", specialPassword);

        // Act & Assert
        mockMvc.perform(post("/api/test/generate-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(specialPassword))
                .andExpect(jsonPath("$.hash").exists())
                .andExpect(jsonPath("$.verified").value("true"));
    }

    @Test
    @DisplayName("测试生成密码哈希 - Unicode字符")
    @WithMockUser
    void testGenerateHash_UnicodeCharacters() throws Exception {
        // Arrange
        String unicodePassword = "测试密码123";
        Map<String, String> request = Map.of("password", unicodePassword);

        // Act & Assert
        mockMvc.perform(post("/api/test/generate-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.password").value(unicodePassword))
                .andExpect(jsonPath("$.hash").exists())
                .andExpect(jsonPath("$.verified").value("true"));
    }

    @Test
    @DisplayName("测试请求体错误处理 - 无效JSON")
    @WithMockUser
    void testInvalidJson() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/api/test/generate-hash")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());
    }
}