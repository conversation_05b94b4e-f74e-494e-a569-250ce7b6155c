package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.service.MultiTenantAuthService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 多租户认证控制器单元测试
 * 使用Mock对象测试控制器逻辑
 */
@DisplayName("多租户认证控制器单元测试")
class MultiTenantAuthControllerUnitTest {

    private MultiTenantAuthController multiTenantAuthController;
    private MultiTenantAuthService mockAuthService;

    @BeforeEach
    void setUp() {
        mockAuthService = mock(MultiTenantAuthService.class);
        multiTenantAuthController = new MultiTenantAuthController(mockAuthService);
    }

    @Test
    @DisplayName("测试多租户登录 - 成功")
    void testLogin_Success() {
        // Arrange
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("test-tenant");
        request.setUsername("testuser");
        request.setPassword("password123");

        MultiTenantLoginResponse expectedResponse = new MultiTenantLoginResponse();
        expectedResponse.setTenantCode("test-tenant");
        expectedResponse.setUsername("testuser");
        expectedResponse.setTenantRole("USER");
        expectedResponse.setAccessToken("jwt-token-123");

        when(mockAuthService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<MultiTenantLoginResponse> response = 
            multiTenantAuthController.login(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getTenantCode()).isEqualTo("test-tenant");
        assertThat(response.getBody().getUsername()).isEqualTo("testuser");
        assertThat(response.getBody().getTenantRole()).isEqualTo("USER");
        assertThat(response.getBody().getAccessToken()).isEqualTo("jwt-token-123");
    }

    @Test
    @DisplayName("测试超级管理员登录 - 成功")
    void testSuperAdminLogin_Success() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        request.put("username", "superadmin");
        request.put("password", "admin123");

        MultiTenantLoginResponse expectedResponse = new MultiTenantLoginResponse();
        expectedResponse.setTenantCode("platform");
        expectedResponse.setUsername("superadmin");
        expectedResponse.setTenantRole("SUPER_ADMIN");
        expectedResponse.setAccessToken("admin-jwt-token-123");

        when(mockAuthService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<MultiTenantLoginResponse> response = 
            multiTenantAuthController.superAdminLogin(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getTenantCode()).isEqualTo("platform");
        assertThat(response.getBody().getUsername()).isEqualTo("superadmin");
        assertThat(response.getBody().getTenantRole()).isEqualTo("SUPER_ADMIN");
        assertThat(response.getBody().getAccessToken()).isEqualTo("admin-jwt-token-123");
    }

    @Test
    @DisplayName("测试超级管理员登录 - 缺少用户名")
    void testSuperAdminLogin_MissingUsername() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        request.put("password", "admin123");
        // Missing username

        MultiTenantLoginRequest expectedRequest = new MultiTenantLoginRequest();
        expectedRequest.setTenantCode("platform");
        expectedRequest.setUsername(null);
        expectedRequest.setPassword("admin123");

        MultiTenantLoginResponse expectedResponse = new MultiTenantLoginResponse();
        expectedResponse.setUsername(null);

        when(mockAuthService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<MultiTenantLoginResponse> response = 
            multiTenantAuthController.superAdminLogin(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getUsername()).isNull();
    }

    @Test
    @DisplayName("测试超级管理员登录 - 缺少密码")
    void testSuperAdminLogin_MissingPassword() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        request.put("username", "superadmin");
        // Missing password

        MultiTenantLoginResponse expectedResponse = new MultiTenantLoginResponse();
        expectedResponse.setUsername("superadmin");

        when(mockAuthService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<MultiTenantLoginResponse> response = 
            multiTenantAuthController.superAdminLogin(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getUsername()).isEqualTo("superadmin");
    }

    @Test
    @DisplayName("测试多租户登录 - 验证请求对象传递")
    void testLogin_RequestObjectPassing() {
        // Arrange
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        request.setTenantCode("another-tenant");
        request.setUsername("anotheruser");
        request.setPassword("password456");

        MultiTenantLoginResponse expectedResponse = new MultiTenantLoginResponse();
        expectedResponse.setTenantCode("another-tenant");
        expectedResponse.setUsername("anotheruser");
        expectedResponse.setTenantRole("ADMIN");

        when(mockAuthService.authenticate(request)).thenReturn(expectedResponse);

        // Act
        ResponseEntity<MultiTenantLoginResponse> response = 
            multiTenantAuthController.login(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(expectedResponse);
    }
}