package com.assessment.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.service.MultiTenantAuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

/**
 * 多租户认证控制器测试
 */
@WebMvcTest(controllers = MultiTenantAuthController.class)
@DisplayName("多租户认证控制器测试")
@SuppressWarnings("removal")  // MockBean在Spring Boot 3.4+中被标记为弃用，但仍可使用
class MultiTenantAuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MultiTenantAuthService authService;

    @Autowired
    private ObjectMapper objectMapper;

    private MultiTenantLoginRequest validLoginRequest;
    private MultiTenantLoginResponse successResponse;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        validLoginRequest = new MultiTenantLoginRequest();
        validLoginRequest.setTenantCode("default");
        validLoginRequest.setUsername("admin");
        validLoginRequest.setPassword("password123");

        successResponse = new MultiTenantLoginResponse();
        successResponse.setAccessToken("jwt-token-123");
        successResponse.setUsername("admin");
        successResponse.setTenantCode("default");
        successResponse.setTenantName("默认租户");
        successResponse.setTenantRole("ADMIN");
        successResponse.setPermissions(Arrays.asList("USER_READ", "USER_WRITE", "ASSESSMENT_MANAGE"));
        successResponse.setUserId("user-123");
        successResponse.setTenantId("tenant-123");
        successResponse.setExpiresIn(7200L);
    }

    @Test
    @DisplayName("测试多租户登录 - 成功场景")
    void testLoginSuccess() throws Exception {
        // Arrange
        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("jwt-token-123"))
                .andExpect(jsonPath("$.username").value("admin"))
                .andExpect(jsonPath("$.tenantCode").value("default"))
                .andExpect(jsonPath("$.tenantName").value("默认租户"))
                .andExpect(jsonPath("$.tenantRole").value("ADMIN"))
                .andExpect(jsonPath("$.permissions").isArray())
                .andExpect(jsonPath("$.permissions[0]").value("USER_READ"));
    }

    @Test
    @DisplayName("测试多租户登录 - 租户代码为空")
    void testLoginWithEmptyTenantCode() throws Exception {
        // Arrange
        validLoginRequest.setTenantCode("");

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试多租户登录 - 用户名为空")
    void testLoginWithEmptyUsername() throws Exception {
        // Arrange
        validLoginRequest.setUsername("");

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试多租户登录 - 密码为空")
    void testLoginWithEmptyPassword() throws Exception {
        // Arrange
        validLoginRequest.setPassword("");

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试多租户登录 - 认证失败")
    void testLoginAuthenticationFailure() throws Exception {
        // Arrange
        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenThrow(new BadCredentialsException("用户名或密码错误"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 用户名或密码错误"));
    }

    @Test
    @DisplayName("测试多租户登录 - 租户不存在")
    void testLoginTenantNotFound() throws Exception {
        // Arrange
        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenThrow(new IllegalArgumentException("租户不存在"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 租户不存在"));
    }

    @Test
    @DisplayName("测试多租户登录 - 租户被禁用")
    void testLoginTenantDisabled() throws Exception {
        // Arrange
        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenThrow(new IllegalStateException("租户已被禁用"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 租户已被禁用"));
    }

    @Test
    @DisplayName("测试多租户登录 - 用户被锁定")
    void testLoginUserLocked() throws Exception {
        // Arrange
        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenThrow(new IllegalStateException("用户账号已被锁定"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 用户账号已被锁定"));
    }

    @Test
    @DisplayName("测试多租户登录 - 服务器内部错误")
    void testLoginServerError() throws Exception {
        // Arrange
        when(authService.authenticate(any(MultiTenantLoginRequest.class)))
            .thenThrow(new RuntimeException("数据库连接失败"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 数据库连接失败"));
    }

    @Test
    @DisplayName("测试多租户登录 - 无效JSON格式")
    void testLoginInvalidJson() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json}"))
                .andExpect(status().isBadRequest());
    }
}