package com.assessment.controller;

import com.assessment.controller.config.ControllerTestConfig;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.repository.multitenant.TenantRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemTenantController 全面测试类
 * 测试系统级租户管理功能的HTTP端点
 */
@WebMvcTest(SystemTenantController.class)
@Import(ControllerTestConfig.class)
@DisplayName("系统租户控制器全面测试")
@SuppressWarnings("removal")
class SystemTenantControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private TenantRepository tenantRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户列表 - 无条件查询")
    void testGetTenants_NoFilters() throws Exception {
        // 准备数据
        List<Tenant> tenants = Arrays.asList(
            createMockTenant("T001", "租户1"),
            createMockTenant("T002", "租户2")
        );
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);
        when(tenantRepository.findAll(any(Pageable.class))).thenReturn(tenantsPage);

        // 执行请求
        mockMvc.perform(get("/api/system/tenants")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalElements").value(2))
                .andExpect(jsonPath("$.data.content[0].code").value("T001"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户列表 - 搜索功能")
    void testGetTenants_WithSearch() throws Exception {
        // 准备数据
        List<Tenant> tenants = Arrays.asList(createMockTenant("MED001", "医疗中心"));
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);
        when(tenantRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
                eq("医疗"), eq("医疗"), any(Pageable.class))).thenReturn(tenantsPage);

        // 执行请求
        mockMvc.perform(get("/api/system/tenants")
                .param("search", "医疗"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalElements").value(1));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户列表 - 状态筛选")
    void testGetTenants_WithStatusFilter() throws Exception {
        // 准备数据
        List<Tenant> tenants = Arrays.asList(createMockTenant("ACT001", "活跃租户"));
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);
        when(tenantRepository.findByStatus(eq(Tenant.TenantStatus.ACTIVE), any(Pageable.class)))
            .thenReturn(tenantsPage);

        // 执行请求
        mockMvc.perform(get("/api/system/tenants")
                .param("status", "ACTIVE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalElements").value(1));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户列表 - 搜索和状态组合筛选")
    void testGetTenants_WithSearchAndStatus() throws Exception {
        // 准备数据
        List<Tenant> tenants = Arrays.asList(createMockTenant("MED002", "医疗机构"));
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);
        when(tenantRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCaseAndStatus(
                eq("医疗"), eq("医疗"), eq(Tenant.TenantStatus.ACTIVE), any(Pageable.class)))
            .thenReturn(tenantsPage);

        // 执行请求
        mockMvc.perform(get("/api/system/tenants")
                .param("search", "医疗")
                .param("status", "ACTIVE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalElements").value(1));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户列表 - 自定义排序")
    void testGetTenants_WithCustomSort() throws Exception {
        // 准备数据
        Page<Tenant> emptyPage = new PageImpl<>(new ArrayList<>());
        when(tenantRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/tenants")
                .param("sortField", "name")
                .param("sortOrder", "ASC"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户详情 - 成功")
    void testGetTenant_Success() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        Tenant tenant = createMockTenant("T001", "测试租户");
        tenant.setId(tenantId);
        when(tenantRepository.findById(tenantId)).thenReturn(Optional.of(tenant));

        // 执行请求
        mockMvc.perform(get("/api/system/tenants/" + tenantId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.code").value("T001"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户详情 - 租户不存在")
    void testGetTenant_NotFound() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        when(tenantRepository.findById(tenantId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/system/tenants/" + tenantId.toString()))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取租户详情失败: 租户不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户详情 - 无效ID格式")
    void testGetTenant_InvalidIdFormat() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/system/tenants/invalid-uuid"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("无效的租户ID格式"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建租户 - 成功")
    void testCreateTenant_Success() throws Exception {
        // 准备数据
        Tenant newTenant = createMockTenant("NEW001", "新租户");
        newTenant.setId(UUID.randomUUID());

        when(tenantRepository.existsByCode("NEW001")).thenReturn(false);
        when(tenantRepository.save(any(Tenant.class))).thenReturn(newTenant);

        Map<String, Object> request = Map.of(
                "code", "new001",
                "name", "新租户",
                "description", "测试租户",
                "contactEmail", "<EMAIL>"
        );

        // 执行请求
        mockMvc.perform(post("/api/system/tenants")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.code").value("NEW001"))
                .andExpect(jsonPath("$.message").value("租户创建成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建租户 - 代码已存在")
    void testCreateTenant_CodeExists() throws Exception {
        // 准备数据 - 使用原始小写代码进行检查
        when(tenantRepository.existsByCode("existing")).thenReturn(true);

        Map<String, Object> request = Map.of(
                "code", "existing",
                "name", "已存在租户"
        );

        // 执行请求
        mockMvc.perform(post("/api/system/tenants")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("租户代码已存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建租户 - 带完整字段")
    void testCreateTenant_WithAllFields() throws Exception {
        // 准备数据
        Tenant fullTenant = createMockTenant("FULL001", "完整租户");
        fullTenant.setId(UUID.randomUUID());

        when(tenantRepository.existsByCode("FULL001")).thenReturn(false);
        when(tenantRepository.save(any(Tenant.class))).thenReturn(fullTenant);

        Map<String, Object> request = Map.of(
                "code", "full001",
                "name", "完整租户",
                "description", "包含所有字段的租户",
                "logoUrl", "https://example.com/logo.png",
                "contactEmail", "<EMAIL>",
                "contactPhone", "13800138000",
                "address", "北京市朝阳区",
                "status", "ACTIVE",
                "subscriptionPlan", "PREMIUM"
        );

        // 执行请求
        mockMvc.perform(post("/api/system/tenants")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新租户 - 成功")
    void testUpdateTenant_Success() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        Tenant existingTenant = createMockTenant("UPD001", "原租户名");
        existingTenant.setId(tenantId);

        Tenant updatedTenant = createMockTenant("UPD001", "更新后租户名");
        updatedTenant.setId(tenantId);

        when(tenantRepository.findById(tenantId)).thenReturn(Optional.of(existingTenant));
        when(tenantRepository.save(any(Tenant.class))).thenReturn(updatedTenant);

        Map<String, Object> request = Map.of(
                "name", "更新后租户名",
                "description", "更新后的描述",
                "contactEmail", "<EMAIL>"
        );

        // 执行请求
        mockMvc.perform(put("/api/system/tenants/" + tenantId.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("租户更新成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新租户 - 租户不存在")
    void testUpdateTenant_NotFound() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        when(tenantRepository.findById(tenantId)).thenReturn(Optional.empty());

        Map<String, Object> request = Map.of("name", "更新名称");

        // 执行请求
        mockMvc.perform(put("/api/system/tenants/" + tenantId.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("更新租户失败: 租户不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新租户 - 无效ID格式")
    void testUpdateTenant_InvalidIdFormat() throws Exception {
        Map<String, Object> request = Map.of("name", "更新名称");

        // 执行请求
        mockMvc.perform(put("/api/system/tenants/invalid-uuid")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("无效的租户ID格式"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("切换租户状态 - 启用")
    void testToggleTenant_Activate() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        Tenant tenant = createMockTenant("TOG001", "切换租户");
        tenant.setId(tenantId);
        tenant.setStatus(Tenant.TenantStatus.SUSPENDED);

        when(tenantRepository.findById(tenantId)).thenReturn(Optional.of(tenant));
        // 使用ArgumentCaptor验证save被调用时对象状态是正确的
        when(tenantRepository.save(any(Tenant.class))).thenAnswer(invocation -> {
            Tenant savedTenant = invocation.getArgument(0);
            return savedTenant; // 返回修改后的对象
        });

        Map<String, Object> request = Map.of("active", true);

        // 执行请求
        mockMvc.perform(patch("/api/system/tenants/" + tenantId.toString() + "/toggle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("租户启用成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("切换租户状态 - 暂停")
    void testToggleTenant_Suspend() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        Tenant tenant = createMockTenant("TOG002", "切换租户");
        tenant.setId(tenantId);
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);

        Tenant updatedTenant = createMockTenant("TOG002", "切换租户");
        updatedTenant.setId(tenantId);
        updatedTenant.setStatus(Tenant.TenantStatus.SUSPENDED);

        when(tenantRepository.findById(tenantId)).thenReturn(Optional.of(tenant));
        when(tenantRepository.save(any(Tenant.class))).thenReturn(updatedTenant);

        Map<String, Object> request = Map.of("active", false);

        // 执行请求
        mockMvc.perform(patch("/api/system/tenants/" + tenantId.toString() + "/toggle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("租户暂停成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("切换租户状态 - 租户不存在")
    void testToggleTenant_NotFound() throws Exception {
        // 准备数据
        UUID tenantId = UUID.randomUUID();
        when(tenantRepository.findById(tenantId)).thenReturn(Optional.empty());

        Map<String, Object> request = Map.of("active", true);

        // 执行请求
        mockMvc.perform(patch("/api/system/tenants/" + tenantId.toString() + "/toggle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("操作失败: 租户不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取租户统计 - 成功")
    void testGetTenantStats_Success() throws Exception {
        // 准备数据
        when(tenantRepository.count()).thenReturn(100L);
        when(tenantRepository.countByStatus(Tenant.TenantStatus.ACTIVE)).thenReturn(80L);
        when(tenantRepository.countByStatus(Tenant.TenantStatus.SUSPENDED)).thenReturn(15L);
        when(tenantRepository.countByStatus(Tenant.TenantStatus.DISABLED)).thenReturn(5L);

        // 执行请求
        mockMvc.perform(get("/api/system/tenants/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalTenants").value(100))
                .andExpect(jsonPath("$.data.activeTenants").value(80))
                .andExpect(jsonPath("$.data.suspendedTenants").value(15))
                .andExpect(jsonPath("$.data.disabledTenants").value(5));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问租户列表 - 返回403")
    void testAccessTenantsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/tenants"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问租户详情 - 返回403")
    void testAccessTenantDetailsWithoutAdminRole_Forbidden() throws Exception {
        UUID tenantId = UUID.randomUUID();
        mockMvc.perform(get("/api/system/tenants/" + tenantId.toString()))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员创建租户 - 返回403")
    void testCreateTenantWithoutAdminRole_Forbidden() throws Exception {
        Map<String, Object> request = Map.of("code", "test", "name", "测试");

        mockMvc.perform(post("/api/system/tenants")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员更新租户 - 返回403")
    void testUpdateTenantWithoutAdminRole_Forbidden() throws Exception {
        UUID tenantId = UUID.randomUUID();
        Map<String, Object> request = Map.of("name", "更新名称");

        mockMvc.perform(put("/api/system/tenants/" + tenantId.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员切换租户状态 - 返回403")
    void testToggleTenantWithoutAdminRole_Forbidden() throws Exception {
        UUID tenantId = UUID.randomUUID();
        Map<String, Object> request = Map.of("active", true);

        mockMvc.perform(patch("/api/system/tenants/" + tenantId.toString() + "/toggle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问租户统计 - 返回403")
    void testAccessTenantStatsWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/tenants/stats"))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("未认证访问 - 返回403")
    void testUnauthenticatedAccess_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/tenants"))
                .andExpect(status().isForbidden());
    }

    // 辅助方法
    private Tenant createMockTenant(String code, String name) {
        Tenant tenant = Tenant.builder()
                .id(UUID.randomUUID())
                .code(code)
                .name(name)
                .description("测试租户描述")
                .status(Tenant.TenantStatus.ACTIVE)
                .subscriptionPlan(Tenant.SubscriptionPlan.STANDARD)
                .contactEmail("<EMAIL>")
                .contactPhone("13800138000")
                .address("测试地址")
                .build();
        // 手动设置审计字段（因为它们在BaseEntity中，不在builder中）
        tenant.setCreatedAt(LocalDateTime.now());
        tenant.setUpdatedAt(LocalDateTime.now());
        return tenant;
    }
}