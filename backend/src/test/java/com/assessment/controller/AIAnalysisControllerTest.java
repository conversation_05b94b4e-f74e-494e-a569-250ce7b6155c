package com.assessment.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;

import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.assessment.service.AIAnalysisService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * AI分析控制器测试
 */
@WebMvcTest(controllers = AIAnalysisController.class)
@ContextConfiguration(classes = {com.assessment.TestApplication.class})
@DisplayName("AI分析控制器测试")
@SuppressWarnings("removal")  // MockBean在Spring Boot 3.4+中被标记为弃用，但仍可使用
class AIAnalysisControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AIAnalysisService aiAnalysisService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试文档分析接口")
    @WithMockUser(roles = "ADMIN")
    void testAnalyzeDocument() throws Exception {
        // Arrange
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setMarkdownContent("老年人能力评估量表");
        request.setFileName("elderly_assessment.md");
        
        DocumentAnalysisResult result = new DocumentAnalysisResult();
        result.setSuccess(true);
        result.setMessage("分析成功");
        result.setScaleType("老年人能力评估");
        result.setTableName("elderly_assessment");
        result.setFieldCount(10);
        result.setConfidence(95);
        
        when(aiAnalysisService.analyzeDocument(any(DocumentAnalysisRequest.class)))
            .thenReturn(result);

        // Act & Assert
        mockMvc.perform(post("/api/ai/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("分析成功"))
                .andExpect(jsonPath("$.scaleType").value("老年人能力评估"))
                .andExpect(jsonPath("$.tableName").value("elderly_assessment"))
                .andExpect(jsonPath("$.fieldCount").value(10))
                .andExpect(jsonPath("$.confidence").value(95));
    }

    @Test
    @DisplayName("测试无权限访问")
    void testUnauthorizedAccess() throws Exception {
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setMarkdownContent("测试内容");
        
        mockMvc.perform(post("/api/ai/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("测试无效请求参数")
    @WithMockUser(roles = "ADMIN")
    void testInvalidRequest() throws Exception {
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        // 空内容
        
        mockMvc.perform(post("/api/ai/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}