package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 多租户量表控制器单元测试
 * 使用Mock对象测试控制器逻辑
 */
@DisplayName("多租户量表控制器单元测试")
class MultiTenantScaleControllerUnitTest {

    private MultiTenantScaleController multiTenantScaleController;
    private GlobalScaleRegistryRepository mockScaleRepository;

    @BeforeEach
    void setUp() {
        mockScaleRepository = mock(GlobalScaleRegistryRepository.class);
        multiTenantScaleController = new MultiTenantScaleController(mockScaleRepository);
    }

    @Test
    @DisplayName("测试获取公开量表 - 无筛选条件")
    void testGetPublicScales_NoFilter() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("量表1", "category1");
        GlobalScaleRegistry scale2 = createMockScale("量表2", "category2");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1, scale2);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
        assertThat(responseBody.get("totalElements")).isEqualTo(2L);
        assertThat(responseBody.get("totalPages")).isEqualTo(1);
        assertThat(responseBody.get("size")).isEqualTo(2);
        assertThat(responseBody.get("number")).isEqualTo(0);
    }

    @Test
    @DisplayName("测试获取公开量表 - 按类别筛选")
    void testGetPublicScales_FilterByCategory() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("老年评估量表", "elderly");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByCategoryAndVisibilityAndStatus(
            anyString(),
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, "elderly", null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
        assertThat(responseBody.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取公开量表 - 按搜索关键词筛选")
    void testGetPublicScales_FilterBySearch() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("老年能力评估", "elderly");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByNameContainingAndVisibilityAndStatus(
            anyString(),
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, "老年");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
        assertThat(responseBody.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取公开量表 - 空搜索关键词")
    void testGetPublicScales_EmptySearch() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("量表1", "category1");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, "");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
    }

    @Test
    @DisplayName("测试获取公开量表 - 空类别")
    void testGetPublicScales_EmptyCategory() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("量表1", "category1");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, "", null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
    }

    @Test
    @DisplayName("测试获取公开量表 - 分页参数")
    void testGetPublicScales_PaginationParameters() {
        // Arrange
        List<GlobalScaleRegistry> scales = Arrays.asList();
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, 
            org.springframework.data.domain.PageRequest.of(1, 10), 25);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(1, 10, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("totalElements")).isEqualTo(25L);
        assertThat(responseBody.get("totalPages")).isEqualTo(3);
        assertThat(responseBody.get("size")).isEqualTo(10); // Pageable的size（请求的页面大小）
        assertThat(responseBody.get("number")).isEqualTo(1);
    }

    @Test
    @DisplayName("测试获取公开量表 - 异常处理")
    void testGetPublicScales_ExceptionHandling() {
        // Arrange
        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenThrow(new RuntimeException("数据库连接失败"));

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("error")).isEqualTo("获取量表列表失败: 数据库连接失败");
    }

    @Test
    @DisplayName("测试获取量表详情 - 成功")
    void testGetScaleDetails_Success() {
        // Arrange
        String scaleId = "scale-123";
        GlobalScaleRegistry scale = createMockScale("老年人能力评估", "elderly");
        when(scale.getId()).thenReturn(scaleId);
        when(mockScaleRepository.findById(scaleId)).thenReturn(java.util.Optional.of(scale));

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.getScaleDetails(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(scale);
    }

    @Test
    @DisplayName("测试获取量表详情 - 量表不存在")
    void testGetScaleDetails_NotFound() {
        // Arrange
        String scaleId = "nonexistent";
        when(mockScaleRepository.findById(scaleId)).thenReturn(java.util.Optional.empty());

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.getScaleDetails(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }

    @Test
    @DisplayName("测试获取量表详情 - 私有量表")
    void testGetScaleDetails_PrivateScale() {
        // Arrange
        String scaleId = "private-scale";
        GlobalScaleRegistry scale = mock(GlobalScaleRegistry.class);
        when(scale.getVisibility()).thenReturn(GlobalScaleRegistry.Visibility.PRIVATE);
        when(mockScaleRepository.findById(scaleId)).thenReturn(java.util.Optional.of(scale));

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.getScaleDetails(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }

    @Test
    @DisplayName("测试获取量表详情 - 异常处理")
    void testGetScaleDetails_Exception() {
        // Arrange
        String scaleId = "error-scale";
        when(mockScaleRepository.findById(scaleId)).thenThrow(new RuntimeException("数据访问错误"));

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.getScaleDetails(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertThat(body.get("error")).isEqualTo("获取量表详情失败: 数据访问错误");
    }

    @Test
    @DisplayName("测试获取量表分类 - 成功")
    void testGetCategories_Success() {
        // Arrange
        List<String> categories = Arrays.asList("老年人评估", "认知评估", "心理评估");
        when(mockScaleRepository.findDistinctCategories()).thenReturn(categories);

        // Act
        ResponseEntity<List<String>> response = multiTenantScaleController.getCategories();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(categories);
        assertThat(response.getBody()).hasSize(3);
    }

    @Test
    @DisplayName("测试获取量表分类 - 异常处理")
    void testGetCategories_Exception() {
        // Arrange
        when(mockScaleRepository.findDistinctCategories()).thenThrow(new RuntimeException("查询失败"));

        // Act
        ResponseEntity<List<String>> response = multiTenantScaleController.getCategories();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
    }

    @Test
    @DisplayName("测试增加使用次数 - 成功")
    void testIncrementUsage_Success() {
        // Arrange
        String scaleId = "usage-scale";
        GlobalScaleRegistry scale = mock(GlobalScaleRegistry.class);
        when(mockScaleRepository.findById(scaleId)).thenReturn(java.util.Optional.of(scale));
        when(mockScaleRepository.save(scale)).thenReturn(scale);

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.incrementUsage(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertThat(body.get("message")).isEqualTo("使用次数已更新");
    }

    @Test
    @DisplayName("测试增加使用次数 - 量表不存在")
    void testIncrementUsage_NotFound() {
        // Arrange
        String scaleId = "nonexistent";
        when(mockScaleRepository.findById(scaleId)).thenReturn(java.util.Optional.empty());

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.incrementUsage(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }

    @Test
    @DisplayName("测试增加使用次数 - 异常处理")
    void testIncrementUsage_Exception() {
        // Arrange
        String scaleId = "error-scale";
        GlobalScaleRegistry scale = mock(GlobalScaleRegistry.class);
        when(mockScaleRepository.findById(scaleId)).thenReturn(java.util.Optional.of(scale));
        when(mockScaleRepository.save(scale)).thenThrow(new RuntimeException("保存失败"));

        // Act
        ResponseEntity<Object> response = multiTenantScaleController.incrementUsage(scaleId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertThat(body.get("error")).isEqualTo("更新使用次数失败: 保存失败");
    }

    @Test
    @DisplayName("测试获取热门量表 - 成功")
    void testGetPopularScales_Success() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("热门量表1", "category1");
        GlobalScaleRegistry scale2 = createMockScale("热门量表2", "category2");
        List<GlobalScaleRegistry> popularScales = Arrays.asList(scale1, scale2);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(popularScales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<List<GlobalScaleRegistry>> response = multiTenantScaleController.getPopularScales(10);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(popularScales);
        assertThat(response.getBody()).hasSize(2);
    }

    @Test
    @DisplayName("测试获取热门量表 - 异常处理")
    void testGetPopularScales_Exception() {
        // Arrange
        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenThrow(new RuntimeException("查询失败"));

        // Act
        ResponseEntity<List<GlobalScaleRegistry>> response = multiTenantScaleController.getPopularScales(10);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
    }

    /**
     * 创建模拟量表对象
     */
    private GlobalScaleRegistry createMockScale(String name, String category) {
        GlobalScaleRegistry scale = mock(GlobalScaleRegistry.class);
        when(scale.getName()).thenReturn(name);
        when(scale.getCategory()).thenReturn(category);
        when(scale.getVisibility()).thenReturn(GlobalScaleRegistry.Visibility.PUBLIC);
        when(scale.getStatus()).thenReturn(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        return scale;
    }
}