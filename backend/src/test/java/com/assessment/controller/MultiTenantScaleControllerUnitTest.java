package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 多租户量表控制器单元测试
 * 使用Mock对象测试控制器逻辑
 */
@DisplayName("多租户量表控制器单元测试")
class MultiTenantScaleControllerUnitTest {

    private MultiTenantScaleController multiTenantScaleController;
    private GlobalScaleRegistryRepository mockScaleRepository;

    @BeforeEach
    void setUp() {
        mockScaleRepository = mock(GlobalScaleRegistryRepository.class);
        multiTenantScaleController = new MultiTenantScaleController(mockScaleRepository);
    }

    @Test
    @DisplayName("测试获取公开量表 - 无筛选条件")
    void testGetPublicScales_NoFilter() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("量表1", "category1");
        GlobalScaleRegistry scale2 = createMockScale("量表2", "category2");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1, scale2);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
        assertThat(responseBody.get("totalElements")).isEqualTo(2L);
        assertThat(responseBody.get("totalPages")).isEqualTo(1);
        assertThat(responseBody.get("size")).isEqualTo(2);
        assertThat(responseBody.get("number")).isEqualTo(0);
    }

    @Test
    @DisplayName("测试获取公开量表 - 按类别筛选")
    void testGetPublicScales_FilterByCategory() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("老年评估量表", "elderly");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByCategoryAndVisibilityAndStatus(
            anyString(),
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, "elderly", null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
        assertThat(responseBody.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取公开量表 - 按搜索关键词筛选")
    void testGetPublicScales_FilterBySearch() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("老年能力评估", "elderly");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByNameContainingAndVisibilityAndStatus(
            anyString(),
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, "老年");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
        assertThat(responseBody.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取公开量表 - 空搜索关键词")
    void testGetPublicScales_EmptySearch() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("量表1", "category1");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, null, "");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
    }

    @Test
    @DisplayName("测试获取公开量表 - 空类别")
    void testGetPublicScales_EmptyCategory() {
        // Arrange
        GlobalScaleRegistry scale1 = createMockScale("量表1", "category1");
        List<GlobalScaleRegistry> scales = Arrays.asList(scale1);
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(0, 20, "", null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("content")).isEqualTo(scales);
    }

    @Test
    @DisplayName("测试获取公开量表 - 分页参数")
    void testGetPublicScales_PaginationParameters() {
        // Arrange
        List<GlobalScaleRegistry> scales = Arrays.asList();
        Page<GlobalScaleRegistry> scalesPage = new PageImpl<>(scales, 
            org.springframework.data.domain.PageRequest.of(1, 10), 25);

        when(mockScaleRepository.findByVisibilityAndStatus(
            any(GlobalScaleRegistry.Visibility.class),
            any(GlobalScaleRegistry.ScaleStatus.class),
            any(Pageable.class)
        )).thenReturn(scalesPage);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            multiTenantScaleController.getPublicScales(1, 10, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> responseBody = response.getBody();
        assertThat(responseBody.get("totalElements")).isEqualTo(25L);
        assertThat(responseBody.get("totalPages")).isEqualTo(3);
        assertThat(responseBody.get("size")).isEqualTo(10); // Pageable的size（请求的页面大小）
        assertThat(responseBody.get("number")).isEqualTo(1);
    }

    /**
     * 创建模拟量表对象
     */
    private GlobalScaleRegistry createMockScale(String name, String category) {
        GlobalScaleRegistry scale = mock(GlobalScaleRegistry.class);
        when(scale.getName()).thenReturn(name);
        when(scale.getCategory()).thenReturn(category);
        when(scale.getVisibility()).thenReturn(GlobalScaleRegistry.Visibility.PUBLIC);
        when(scale.getStatus()).thenReturn(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        return scale;
    }
}