package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.assessment.service.AIAnalysisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;

/**
 * AI分析控制器单元测试
 * 使用Mock对象测试控制器逻辑
 */
@DisplayName("AI分析控制器单元测试")
class AIAnalysisControllerUnitTest {

    private AIAnalysisController aiAnalysisController;
    private AIAnalysisService mockAIAnalysisService;

    @BeforeEach
    void setUp() {
        mockAIAnalysisService = mock(AIAnalysisService.class);
        aiAnalysisController = new AIAnalysisController(mockAIAnalysisService);
    }

    @Test
    @DisplayName("测试文档结构分析 - 成功场景")
    void testAnalyzeDocumentStructure_Success() {
        // Arrange
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setFileName("test.pdf");
        request.setMarkdownContent("# 测试量表\n## 基本信息\n姓名：\n年龄：");

        DocumentAnalysisResult expectedResult = new DocumentAnalysisResult();
        expectedResult.setTableName("test_table");
        expectedResult.setConfidence(95);
        
        // Create proper DatabaseField objects
        DocumentAnalysisResult.DatabaseField field1 = new DocumentAnalysisResult.DatabaseField();
        field1.setName("name");
        field1.setType("VARCHAR(100)");
        
        DocumentAnalysisResult.DatabaseField field2 = new DocumentAnalysisResult.DatabaseField();
        field2.setName("age");
        field2.setType("INTEGER");
        
        expectedResult.setFields(Arrays.asList(field1, field2));

        when(mockAIAnalysisService.analyzeDocument(any(DocumentAnalysisRequest.class)))
            .thenReturn(expectedResult);

        // Act
        ResponseEntity<ApiResponse<DocumentAnalysisResult>> response = 
            aiAnalysisController.analyzeDocumentStructure(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedResult);
    }

    @Test
    @DisplayName("测试文档结构分析 - 内容为空")
    void testAnalyzeDocumentStructure_EmptyContent() {
        // Arrange
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setFileName("test.pdf");
        request.setMarkdownContent("");

        // Act
        ResponseEntity<ApiResponse<DocumentAnalysisResult>> response = 
            aiAnalysisController.analyzeDocumentStructure(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("文档内容不能为空");
    }

    @Test
    @DisplayName("测试文档结构分析 - 内容过长")
    void testAnalyzeDocumentStructure_ContentTooLong() {
        // Arrange
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setFileName("test.pdf");
        // 创建超过100KB的内容
        request.setMarkdownContent("x".repeat(100001));

        // Act
        ResponseEntity<ApiResponse<DocumentAnalysisResult>> response = 
            aiAnalysisController.analyzeDocumentStructure(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("文档内容过长，请限制在100KB以内");
    }

    @Test
    @DisplayName("测试获取AI服务状态 - 可用")
    void testGetAIStatus_Available() {
        // Arrange
        when(mockAIAnalysisService.isAIServiceAvailable()).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<String>> response = aiAnalysisController.getAIStatus();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo("AI服务正常");
    }

    @Test
    @DisplayName("测试获取AI服务状态 - 不可用")
    void testGetAIStatus_Unavailable() {
        // Arrange
        when(mockAIAnalysisService.isAIServiceAvailable()).thenReturn(false);

        // Act
        ResponseEntity<ApiResponse<String>> response = aiAnalysisController.getAIStatus();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo("AI服务不可用");
    }

    @Test
    @DisplayName("测试获取模型信息 - 成功")
    void testGetModelInfo_Success() {
        // Arrange
        Map<String, Object> expectedModelInfo = Map.of(
            "modelName", "deepseek-r1-0528-qwen3-8b",
            "serverUrl", "192.168.1.231:1234",
            "status", "online"
        );
        when(mockAIAnalysisService.getCurrentModelInfo()).thenReturn(expectedModelInfo);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            aiAnalysisController.getModelInfo();

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedModelInfo);
    }

    @Test
    @DisplayName("测试AI对话 - 成功")
    void testChatWithAI_Success() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        request.put("message", "这个量表包含哪些字段？");
        request.put("context", "# 测试量表\n姓名：\n年龄：");

        String expectedResponse = "这个量表包含姓名和年龄两个字段。";
        when(mockAIAnalysisService.chatWithAI(anyString(), anyString()))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            aiAnalysisController.chatWithAI(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(expectedResponse);
    }

    @Test
    @DisplayName("测试AI对话 - 消息为空")
    void testChatWithAI_EmptyMessage() {
        // Arrange
        Map<String, String> request = new HashMap<>();
        request.put("message", "");
        request.put("context", "测试上下文");

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            aiAnalysisController.chatWithAI(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("消息内容不能为空");
    }

    @Test
    @DisplayName("测试流式文档分析 - 内容为空")
    void testAnalyzeDocumentStructureStream_EmptyContent() {
        // Arrange
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setFileName("test.pdf");
        request.setMarkdownContent("");

        // Act
        SseEmitter emitter = aiAnalysisController.analyzeDocumentStructureStream(request);

        // Assert
        assertThat(emitter).isNotNull();
        assertThat(emitter.getTimeout()).isEqualTo(600000L);
    }

    @Test
    @DisplayName("测试流式文档分析 - 内容过长")
    void testAnalyzeDocumentStructureStream_ContentTooLong() {
        // Arrange
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setFileName("test.pdf");
        request.setMarkdownContent("x".repeat(100001));

        // Act
        SseEmitter emitter = aiAnalysisController.analyzeDocumentStructureStream(request);

        // Assert
        assertThat(emitter).isNotNull();
        assertThat(emitter.getTimeout()).isEqualTo(600000L);
    }

    @Test
    @DisplayName("测试SSE功能")
    void testSSE() {
        // Act
        SseEmitter emitter = aiAnalysisController.testSSE();

        // Assert
        assertThat(emitter).isNotNull();
        assertThat(emitter.getTimeout()).isEqualTo(60000L);
    }
}