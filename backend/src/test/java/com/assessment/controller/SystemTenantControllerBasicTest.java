package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.repository.multitenant.TenantRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 系统租户控制器基础单元测试
 * 专注于测试控制器的核心逻辑
 */
@DisplayName("系统租户控制器基础单元测试")
class SystemTenantControllerBasicTest {

    private SystemTenantController systemTenantController;
    private TenantRepository mockTenantRepository;

    @BeforeEach
    void setUp() {
        mockTenantRepository = mock(TenantRepository.class);
        systemTenantController = new SystemTenantController(mockTenantRepository);
    }

    @Test
    @DisplayName("测试获取租户列表 - 基本功能")
    void testGetTenants_Basic() {
        // Arrange
        List<Tenant> tenants = Arrays.asList(
            createSimpleMockTenant("tenant1", "企业A"),
            createSimpleMockTenant("tenant2", "企业B")
        );
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);

        when(mockTenantRepository.findAll(any(Pageable.class)))
            .thenReturn(tenantsPage);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemTenantController.getTenants(0, 20, null, null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("content");
        assertThat(data).containsKey("totalElements");
        assertThat(data.get("totalElements")).isEqualTo(2L);
    }

    @Test
    @DisplayName("测试获取租户列表 - 带搜索条件")
    void testGetTenants_WithSearch() {
        // Arrange
        List<Tenant> tenants = Arrays.asList(
            createSimpleMockTenant("medical-center", "医疗中心")
        );
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);

        when(mockTenantRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
                anyString(), anyString(), any(Pageable.class)))
            .thenReturn(tenantsPage);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemTenantController.getTenants(0, 20, "医疗", null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取租户列表 - 按状态筛选")
    void testGetTenants_FilterByStatus() {
        // Arrange
        List<Tenant> tenants = Arrays.asList(
            createSimpleMockTenant("active-tenant", "活跃租户")
        );
        Page<Tenant> tenantsPage = new PageImpl<>(tenants);

        when(mockTenantRepository.findByStatus(
                any(Tenant.TenantStatus.class), any(Pageable.class)))
            .thenReturn(tenantsPage);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemTenantController.getTenants(0, 20, null, "ACTIVE", null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取租户详情 - 成功")
    void testGetTenant_Success() {
        // Arrange
        String tenantId = UUID.randomUUID().toString();
        Tenant tenant = createSimpleMockTenant("test-tenant", "测试租户");
        tenant.setId(UUID.fromString(tenantId));

        when(mockTenantRepository.findById(UUID.fromString(tenantId)))
            .thenReturn(Optional.of(tenant));

        // Act
        ResponseEntity<ApiResponse<Tenant>> response = 
            systemTenantController.getTenant(tenantId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(tenant);
    }

    @Test
    @DisplayName("测试获取租户详情 - 无效ID格式")
    void testGetTenant_InvalidIdFormat() {
        // Act
        ResponseEntity<ApiResponse<Tenant>> response = 
            systemTenantController.getTenant("invalid-uuid");

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("无效的租户ID格式");
    }

    @Test
    @DisplayName("测试创建租户 - 租户代码已存在")
    void testCreateTenant_CodeExists() {
        // Arrange
        SystemTenantController.CreateTenantRequest request = 
            new SystemTenantController.CreateTenantRequest();
        request.setCode("existing-code");
        request.setName("新租户");

        when(mockTenantRepository.existsByCode("existing-code")).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse<Tenant>> response = 
            systemTenantController.createTenant(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).isEqualTo("租户代码已存在");
    }

    @Test
    @DisplayName("测试创建租户 - 成功")
    void testCreateTenant_Success() {
        // Arrange
        SystemTenantController.CreateTenantRequest request = 
            new SystemTenantController.CreateTenantRequest();
        request.setCode("new-tenant");
        request.setName("新租户");

        Tenant savedTenant = createSimpleMockTenant("new-tenant", "新租户");
        savedTenant.setId(UUID.randomUUID());

        when(mockTenantRepository.existsByCode("new-tenant")).thenReturn(false);
        when(mockTenantRepository.save(any(Tenant.class))).thenReturn(savedTenant);

        // Act
        ResponseEntity<ApiResponse<Tenant>> response = 
            systemTenantController.createTenant(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isEqualTo(savedTenant);
    }

    @Test
    @DisplayName("测试更新租户 - 租户不存在")
    void testUpdateTenant_NotFound() {
        // Arrange
        String tenantId = UUID.randomUUID().toString();
        SystemTenantController.UpdateTenantRequest request = 
            new SystemTenantController.UpdateTenantRequest();
        request.setName("更新名称");

        when(mockTenantRepository.findById(UUID.fromString(tenantId)))
            .thenReturn(Optional.empty());

        // Act
        ResponseEntity<ApiResponse<Tenant>> response = 
            systemTenantController.updateTenant(tenantId, request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("租户不存在");
    }

    /**
     * 创建简单的模拟租户对象
     */
    private Tenant createSimpleMockTenant(String code, String name) {
        Tenant tenant = new Tenant();
        tenant.setId(UUID.randomUUID());
        tenant.setCode(code);
        tenant.setName(name);
        tenant.setIndustry("测试行业");
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        tenant.setSubscriptionPlan(Tenant.SubscriptionPlan.STANDARD);
        tenant.setCreatedAt(LocalDateTime.now());
        tenant.setUpdatedAt(LocalDateTime.now());
        return tenant;
    }
}