package com.assessment.controller;

import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.assessment.entity.multitenant.GlobalScaleRegistry;

/**
 * SystemScaleController 基础测试类
 */
@WebMvcTest(SystemScaleController.class)
@DisplayName("系统量表控制器基础测试")
@SuppressWarnings("removal")
class SystemScaleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表列表 - 基础HTTP测试")
    void testGetScales_HttpEndpoint() throws Exception {
        // 准备数据
        Page<GlobalScaleRegistry> emptyPage = new PageImpl<>(new ArrayList<>());
        when(scaleRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/scales")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表统计 - 基础HTTP测试")
    void testGetScaleStats_HttpEndpoint() throws Exception {
        // 准备数据
        when(scaleRepository.count()).thenReturn(30L);

        // 执行请求
        mockMvc.perform(get("/api/system/scales/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表分类 - 基础HTTP测试")
    void testGetCategories_HttpEndpoint() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/system/scales/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/scales"))
                .andExpect(status().isForbidden());
    }
}