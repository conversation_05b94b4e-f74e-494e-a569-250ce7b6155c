package com.assessment.controller;

import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.controller.config.ControllerTestConfig;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemScaleController 全面测试类
 * 测试系统级量表管理功能的HTTP端点
 */
@WebMvcTest(SystemScaleController.class)
@Import(ControllerTestConfig.class)
@DisplayName("系统量表控制器全面测试")
@SuppressWarnings("removal")
class SystemScaleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private GlobalScaleRegistryRepository scaleRepository;

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表列表 - 基础HTTP测试")
    void testGetScales_HttpEndpoint() throws Exception {
        // 准备数据
        Page<GlobalScaleRegistry> emptyPage = new PageImpl<>(new ArrayList<>());
        when(scaleRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/scales")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表统计 - 基础HTTP测试")
    void testGetScaleStats_HttpEndpoint() throws Exception {
        // 准备数据
        when(scaleRepository.count()).thenReturn(30L);
        when(scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(25L);
        when(scaleRepository.countByVisibility(GlobalScaleRegistry.Visibility.PUBLIC)).thenReturn(20L);
        when(scaleRepository.countByIsOfficial(true)).thenReturn(15L);
        when(scaleRepository.countByCategory()).thenReturn(new ArrayList<>());
        when(scaleRepository.countByPublisherType()).thenReturn(new ArrayList<>());

        // 执行请求
        mockMvc.perform(get("/api/system/scales/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表分类 - 基础HTTP测试")
    void testGetCategories_HttpEndpoint() throws Exception {
        // 准备数据
        when(scaleRepository.findDistinctCategories()).thenReturn(Arrays.asList("health", "education"));

        // 执行请求
        mockMvc.perform(get("/api/system/scales/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表详情 - 成功")
    void testGetScale_Success() throws Exception {
        // 准备数据
        String scaleId = "scale-123";
        GlobalScaleRegistry scale = createMockScale("TEST_SCALE", "测试量表");
        scale.setId(scaleId);
        
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.of(scale));

        // 执行请求
        mockMvc.perform(get("/api/system/scales/" + scaleId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.code").value("TEST_SCALE"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表详情 - 量表不存在")
    void testGetScale_NotFound() throws Exception {
        // 准备数据
        String scaleId = "nonexistent";
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(get("/api/system/scales/" + scaleId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按条件搜索量表 - 成功")
    void testGetScales_WithSearch() throws Exception {
        // 准备数据
        Page<GlobalScaleRegistry> emptyPage = new PageImpl<>(new ArrayList<>());
        when(scaleRepository.findByNameContainingIgnoreCaseOrCodeContainingIgnoreCase(
            eq("老年"), eq("老年"), any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/scales")
                .param("search", "老年")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按分类筛选量表 - 成功")
    void testGetScales_WithCategory() throws Exception {
        // 准备数据
        Page<GlobalScaleRegistry> emptyPage = new PageImpl<>(new ArrayList<>());
        when(scaleRepository.findByCategory(eq("health"), any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/scales")
                .param("category", "health")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("按状态筛选量表 - 成功")
    void testGetScales_WithStatus() throws Exception {
        // 准备数据
        Page<GlobalScaleRegistry> emptyPage = new PageImpl<>(new ArrayList<>());
        when(scaleRepository.findByStatus(any(GlobalScaleRegistry.ScaleStatus.class), any(Pageable.class)))
            .thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/scales")
                .param("status", "ACTIVE")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("自定义排序 - 成功")
    void testGetScales_WithCustomSort() throws Exception {
        // 准备数据
        Page<GlobalScaleRegistry> emptyPage = new PageImpl<>(new ArrayList<>());
        when(scaleRepository.findAll(any(Pageable.class))).thenReturn(emptyPage);

        // 执行请求
        mockMvc.perform(get("/api/system/scales")
                .param("sortField", "name")
                .param("sortOrder", "ASC")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建量表 - 成功")
    void testCreateScale_Success() throws Exception {
        // 准备数据
        GlobalScaleRegistry savedScale = createMockScale("ELDERLY_001", "老年人能力评估量表");
        
        when(scaleRepository.existsByCode("ELDERLY_001")).thenReturn(false);
        when(scaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(savedScale);

        Map<String, Object> request = Map.of(
                "code", "ELDERLY_001",
                "name", "老年人能力评估量表",
                "version", "1.0.0",
                "category", "health",
                "formSchema", "{\"fields\":[]}",
                "scoringRules", "{\"rules\":[]}"
        );

        // 执行请求
        mockMvc.perform(post("/api/system/scales")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("量表创建成功"))
                .andExpect(jsonPath("$.data.code").value("ELDERLY_001"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建量表 - 代码已存在")
    void testCreateScale_CodeExists() throws Exception {
        // 准备数据
        when(scaleRepository.existsByCode("EXISTING_CODE")).thenReturn(true);

        Map<String, Object> request = Map.of(
                "code", "EXISTING_CODE",
                "name", "重复代码量表",
                "formSchema", "{\"fields\":[]}",
                "scoringRules", "{\"rules\":[]}"
        );

        // 执行请求
        mockMvc.perform(post("/api/system/scales")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("量表代码已存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("创建量表 - 带完整字段")
    void testCreateScale_WithAllFields() throws Exception {
        // 准备数据
        GlobalScaleRegistry savedScale = createMockScale("COMPLETE_001", "完整量表");
        
        when(scaleRepository.existsByCode("COMPLETE_001")).thenReturn(false);
        when(scaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(savedScale);

        Map<String, Object> request = new HashMap<>();
        request.put("code", "COMPLETE_001");
        request.put("name", "完整量表");
        request.put("version", "2.0.0");
        request.put("category", "comprehensive");
        request.put("industryTags", new String[]{"healthcare", "elderly"});
        request.put("keywords", new String[]{"assessment", "evaluation"});
        request.put("formSchema", "{\"fields\":[{\"type\":\"text\"}]}");
        request.put("scoringRules", "{\"rules\":[{\"weight\":1.0}]}");
        request.put("validationRules", "{\"validations\":[]}");
        request.put("reportTemplate", "{\"template\":\"basic\"}");
        request.put("publisherType", "PLATFORM");
        request.put("publisherId", "platform-001");
        request.put("visibility", "PUBLIC");
        request.put("price", 99.99);
        request.put("isOfficial", true);

        // 执行请求
        mockMvc.perform(post("/api/system/scales")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("量表创建成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新量表 - 成功")
    void testUpdateScale_Success() throws Exception {
        // 准备数据
        String scaleId = "scale-001";
        GlobalScaleRegistry existingScale = createMockScale("UPDATE_001", "原量表名");
        existingScale.setId(scaleId);
        
        GlobalScaleRegistry updatedScale = createMockScale("UPDATE_001", "更新后量表名");
        updatedScale.setId(scaleId);
        
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.of(existingScale));
        when(scaleRepository.existsByCode("UPDATE_001")).thenReturn(false);
        when(scaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(updatedScale);

        Map<String, Object> request = Map.of(
                "code", "UPDATE_001",
                "name", "更新后量表名",
                "version", "1.1.0",
                "category", "updated_category",
                "formSchema", "{\"fields\":[{\"updated\":true}]}",
                "scoringRules", "{\"rules\":[{\"updated\":true}]}"
        );

        // 执行请求
        mockMvc.perform(put("/api/system/scales/" + scaleId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("量表更新成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新量表 - 量表不存在")
    void testUpdateScale_NotFound() throws Exception {
        // 准备数据
        String scaleId = "nonexistent";
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.empty());

        Map<String, Object> request = Map.of(
                "code", "NOT_FOUND",
                "name", "不存在的量表",
                "formSchema", "{}",
                "scoringRules", "{}"
        );

        // 执行请求
        mockMvc.perform(put("/api/system/scales/" + scaleId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("更新量表失败: 量表不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("更新量表 - 代码冲突")
    void testUpdateScale_CodeConflict() throws Exception {
        // 准备数据
        String scaleId = "scale-001";
        GlobalScaleRegistry existingScale = createMockScale("OLD_CODE", "原量表");
        existingScale.setId(scaleId);
        
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.of(existingScale));
        when(scaleRepository.existsByCode("CONFLICT_CODE")).thenReturn(true);

        Map<String, Object> request = Map.of(
                "code", "CONFLICT_CODE",
                "name", "代码冲突量表",
                "formSchema", "{}",
                "scoringRules", "{}"
        );

        // 执行请求
        mockMvc.perform(put("/api/system/scales/" + scaleId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("量表代码已被其他量表使用"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("删除量表 - 成功")
    void testDeleteScale_Success() throws Exception {
        // 准备数据
        String scaleId = "scale-001";
        GlobalScaleRegistry scale = createMockScale("DELETE_001", "待删除量表");
        scale.setId(scaleId);
        
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.of(scale));

        // 执行请求
        mockMvc.perform(delete("/api/system/scales/" + scaleId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("量表删除成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("删除量表 - 量表不存在")
    void testDeleteScale_NotFound() throws Exception {
        // 准备数据
        String scaleId = "nonexistent";
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(delete("/api/system/scales/" + scaleId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("删除量表失败: 量表不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("发布量表 - 成功")
    void testPublishScale_Success() throws Exception {
        // 准备数据
        String scaleId = "scale-001";
        GlobalScaleRegistry scale = createMockScale("PUBLISH_001", "待发布量表");
        scale.setId(scaleId);
        scale.setStatus(GlobalScaleRegistry.ScaleStatus.INACTIVE);
        
        GlobalScaleRegistry publishedScale = createMockScale("PUBLISH_001", "已发布量表");
        publishedScale.setId(scaleId);
        publishedScale.setStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.of(scale));
        when(scaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(publishedScale);

        // 执行请求
        mockMvc.perform(post("/api/system/scales/" + scaleId + "/publish"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("量表发布成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("发布量表 - 量表不存在")
    void testPublishScale_NotFound() throws Exception {
        // 准备数据
        String scaleId = "nonexistent";
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.empty());

        // 执行请求
        mockMvc.perform(post("/api/system/scales/" + scaleId + "/publish"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("发布量表失败: 量表不存在"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("弃用量表 - 成功")
    void testDeprecateScale_Success() throws Exception {
        // 准备数据
        String scaleId = "scale-001";
        GlobalScaleRegistry scale = createMockScale("DEPRECATE_001", "待弃用量表");
        scale.setId(scaleId);
        scale.setStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        
        GlobalScaleRegistry deprecatedScale = createMockScale("DEPRECATE_001", "已弃用量表");
        deprecatedScale.setId(scaleId);
        deprecatedScale.setStatus(GlobalScaleRegistry.ScaleStatus.DEPRECATED);
        
        when(scaleRepository.findById(scaleId)).thenReturn(Optional.of(scale));
        when(scaleRepository.save(any(GlobalScaleRegistry.class))).thenReturn(deprecatedScale);

        // 执行请求
        mockMvc.perform(post("/api/system/scales/" + scaleId + "/deprecate"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("量表弃用成功"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表统计 - 详细测试")
    void testGetScaleStats_Detailed() throws Exception {
        // 准备数据
        when(scaleRepository.count()).thenReturn(100L);
        when(scaleRepository.countByStatus(GlobalScaleRegistry.ScaleStatus.ACTIVE)).thenReturn(80L);
        when(scaleRepository.countByVisibility(GlobalScaleRegistry.Visibility.PUBLIC)).thenReturn(60L);
        when(scaleRepository.countByIsOfficial(true)).thenReturn(30L);
        
        List<Object[]> categoryStats = Arrays.asList(
            new Object[]{"health", 40L},
            new Object[]{"education", 30L},
            new Object[]{"psychology", 30L}
        );
        List<Object[]> publisherStats = Arrays.asList(
            new Object[]{"PLATFORM", 50L},
            new Object[]{"THIRD_PARTY", 30L},
            new Object[]{"USER", 20L}
        );
        
        when(scaleRepository.countByCategory()).thenReturn(categoryStats);
        when(scaleRepository.countByPublisherType()).thenReturn(publisherStats);

        // 执行请求
        mockMvc.perform(get("/api/system/scales/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalScales").value(100))
                .andExpect(jsonPath("$.data.activeScales").value(80))
                .andExpect(jsonPath("$.data.publicScales").value(60))
                .andExpect(jsonPath("$.data.officialScales").value(30))
                .andExpect(jsonPath("$.data.categoryStats").isArray())
                .andExpect(jsonPath("$.data.publisherStats").isArray());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("获取量表分类 - 成功")
    void testGetCategories_Success() throws Exception {
        // 准备数据
        List<String> categories = Arrays.asList("health", "education", "psychology", "business");
        when(scaleRepository.findDistinctCategories()).thenReturn(categories);

        // 执行请求
        mockMvc.perform(get("/api/system/scales/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(4));
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员访问 - 返回403")
    void testAccessWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/scales"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员创建量表 - 返回403")
    void testCreateScaleWithoutAdminRole_Forbidden() throws Exception {
        Map<String, Object> request = Map.of(
                "code", "TEST",
                "name", "测试量表",
                "formSchema", "{}",
                "scoringRules", "{}"
        );

        mockMvc.perform(post("/api/system/scales")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员更新量表 - 返回403")
    void testUpdateScaleWithoutAdminRole_Forbidden() throws Exception {
        Map<String, Object> request = Map.of(
                "code", "TEST",
                "name", "测试量表",
                "formSchema", "{}",
                "scoringRules", "{}"
        );

        mockMvc.perform(put("/api/system/scales/scale-001")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER")
    @DisplayName("非管理员删除量表 - 返回403")
    void testDeleteScaleWithoutAdminRole_Forbidden() throws Exception {
        mockMvc.perform(delete("/api/system/scales/scale-001"))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("未认证访问 - 返回403")
    void testUnauthenticatedAccess_Forbidden() throws Exception {
        mockMvc.perform(get("/api/system/scales"))
                .andExpect(status().isForbidden());
    }

    // 辅助方法
    private GlobalScaleRegistry createMockScale(String code, String name) {
        return GlobalScaleRegistry.builder()
                .id("scale-" + code.toLowerCase())
                .code(code)
                .name(name)
                .version("1.0.0")
                .category("health")
                .publisherType(GlobalScaleRegistry.PublisherType.PLATFORM)
                .publisherId("platform-001")
                .visibility(GlobalScaleRegistry.Visibility.PUBLIC)
                .price(BigDecimal.ZERO)
                .isOfficial(true)
                .isVerified(true)
                .status(GlobalScaleRegistry.ScaleStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .publishedAt(LocalDateTime.now())
                .build();
    }
}