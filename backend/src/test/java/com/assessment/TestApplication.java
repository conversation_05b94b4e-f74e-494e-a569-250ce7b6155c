package com.assessment;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 测试专用配置类
 * 
 * 注意：使用@TestConfiguration而不是@SpringBootApplication避免配置冲突
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-13
 */
@TestConfiguration
@EnableAutoConfiguration(
    exclude = {RedisAutoConfiguration.class, RedisRepositoriesAutoConfiguration.class})
@ComponentScan("com.assessment")
public class TestApplication {
}
