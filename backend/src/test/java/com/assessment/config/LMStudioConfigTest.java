package com.assessment.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * LMStudioConfig 测试类
 * 测试LM Studio配置的各种功能
 */
@DisplayName("LM Studio配置测试")
class LMStudioConfigTest {

    private LMStudioConfig config;

    @BeforeEach
    void setUp() {
        config = new LMStudioConfig();
    }

    @Test
    @DisplayName("当创建配置时，应该有默认值")
    void whenCreateConfig_thenHaveDefaultValues() {
        // Assert
        assertThat(config.getServer()).isNotNull();
        assertThat(config.getModels()).isNotNull();
        assertThat(config.getAutoSwitch()).isNotNull();
        
        // 检查服务器默认配置
        assertThat(config.getServer().getPrimaryUrl()).isEqualTo("http://192.168.1.231:1234");
        assertThat(config.getServer().getBackupUrls()).isNotEmpty();
        assertThat(config.getServer().getConnectionTimeout()).isEqualTo(10000);
        assertThat(config.getServer().getReadTimeout()).isEqualTo(60000);
        assertThat(config.getServer().getMaxRetries()).isEqualTo(3);
        assertThat(config.getServer().getHealthCheckInterval()).isEqualTo(30);
        
        // 检查自动切换默认配置
        assertThat(config.getAutoSwitch().getEnabled()).isTrue();
        assertThat(config.getAutoSwitch().getFallbackEnabled()).isTrue();
        assertThat(config.getAutoSwitch().getServerFallbackEnabled()).isTrue();
        assertThat(config.getAutoSwitch().getResponseTimeThreshold()).isEqualTo(30000L);
    }

    @Test
    @DisplayName("当复制构造时，应该创建深拷贝")
    void whenCopyConstruct_thenCreateDeepCopy() {
        // Arrange
        LMStudioConfig original = new LMStudioConfig();
        
        // Act
        LMStudioConfig copy = new LMStudioConfig(original);
        
        // Assert
        assertThat(copy).isNotSameAs(original);
        assertThat(copy.getServer()).isNotSameAs(original.getServer());
        assertThat(copy.getModels()).isNotSameAs(original.getModels());
        assertThat(copy.getAutoSwitch()).isNotSameAs(original.getAutoSwitch());
        
        // 但内容应该相同
        assertThat(copy.getServer().getPrimaryUrl()).isEqualTo(original.getServer().getPrimaryUrl());
        assertThat(copy.getAutoSwitch().getEnabled()).isEqualTo(original.getAutoSwitch().getEnabled());
    }

    @Test
    @DisplayName("当获取模型优先级时，应该根据模式匹配")
    void whenGetModelPriority_thenMatchByPattern() {
        // Act & Assert
        assertThat(config.getModelPriority("deepseek-r1-qwen-instruct")).isEqualTo(1); // 最高优先级
        assertThat(config.getModelPriority("deepseek-coder-v2")).isEqualTo(2); // 第二优先级
        assertThat(config.getModelPriority("qwen2.5-8b-instruct")).isEqualTo(3); // 第三优先级
        assertThat(config.getModelPriority("some-random-model")).isEqualTo(999); // 默认优先级
    }

    @Test
    @DisplayName("当推断模型能力时，应该根据关键词匹配")
    void whenInferModelCapabilities_thenMatchByKeywords() {
        // Act & Assert
        List<String> coderCapabilities = config.inferModelCapabilities("deepseek-coder-v2");
        assertThat(coderCapabilities).contains("code", "chat");
        
        List<String> qwenCapabilities = config.inferModelCapabilities("qwen2.5-7b-instruct");
        assertThat(qwenCapabilities).contains("chinese", "chat");
        
        List<String> reasoningCapabilities = config.inferModelCapabilities("gpt-4-reasoning");
        assertThat(reasoningCapabilities).contains("reasoning", "chat");
        
        List<String> genericCapabilities = config.inferModelCapabilities("generic-model");
        assertThat(genericCapabilities).contains("chat"); // 所有模型都应该有chat能力
    }

    @Test
    @DisplayName("当检查模型是否被排除时，应该根据排除模式匹配")
    void whenCheckModelExclusion_thenMatchByExclusionPattern() {
        // Act & Assert
        assertThat(config.isModelExcluded("text-embedding-ada-002")).isTrue(); // 包含embedding
        assertThat(config.isModelExcluded("whisper-large-v3")).isTrue(); // 包含whisper
        assertThat(config.isModelExcluded("tts-1-hd")).isTrue(); // 包含tts
        assertThat(config.isModelExcluded("gpt-4-vision-preview")).isTrue(); // 包含vision
        assertThat(config.isModelExcluded("translate-model")).isTrue(); // 包含translate
        
        assertThat(config.isModelExcluded("qwen2.5-7b-instruct")).isFalse(); // 正常模型
        assertThat(config.isModelExcluded("deepseek-coder")).isFalse(); // 正常模型
    }

    @Test
    @DisplayName("当生成显示名称时，应该格式化模型ID")
    void whenGenerateDisplayName_thenFormatModelId() {
        // Act & Assert
        assertThat(config.generateDisplayName("deepseek-coder-v2-instruct"))
            .isEqualTo("Deepseek Coder V2 Instruct");
        
        assertThat(config.generateDisplayName("microsoft/DialoGPT-medium"))
            .isEqualTo("Dialogpt Medium");
        
        assertThat(config.generateDisplayName("qwen2.5_7b_instruct"))
            .isEqualTo("Qwen2.5 7b Instruct");
        
        assertThat(config.generateDisplayName("simple-model"))
            .isEqualTo("Simple Model");
    }

    @Test
    @DisplayName("当ModelInfo创建时，应该设置所有属性")
    void whenCreateModelInfo_thenSetAllProperties() {
        // Arrange
        List<String> capabilities = List.of("chat", "code");
        
        // Act
        LMStudioConfig.ModelInfo modelInfo = new LMStudioConfig.ModelInfo(
            "test-model", "Test Model", "测试模型", 1, capabilities);
        
        // Assert
        assertThat(modelInfo.getId()).isEqualTo("test-model");
        assertThat(modelInfo.getDisplayName()).isEqualTo("Test Model");
        assertThat(modelInfo.getDescription()).isEqualTo("测试模型");
        assertThat(modelInfo.getPriority()).isEqualTo(1);
        assertThat(modelInfo.getCapabilities()).containsExactly("chat", "code");
        assertThat(modelInfo.getCapabilities()).isNotSameAs(capabilities); // 应该是副本
    }

    @Test
    @DisplayName("当ModelInfo复制构造时，应该创建深拷贝")
    void whenCopyConstructModelInfo_thenCreateDeepCopy() {
        // Arrange
        LMStudioConfig.ModelInfo original = new LMStudioConfig.ModelInfo(
            "original", "Original", "原始", 1, List.of("chat"));
        
        // Act
        LMStudioConfig.ModelInfo copy = new LMStudioConfig.ModelInfo(original);
        
        // Assert
        assertThat(copy).isNotSameAs(original);
        assertThat(copy.getId()).isEqualTo(original.getId());
        assertThat(copy.getCapabilities()).isNotSameAs(original.getCapabilities());
        assertThat(copy.getCapabilities()).containsExactlyElementsOf(original.getCapabilities());
    }

    @Test
    @DisplayName("当设置ServerConfig时，应该创建防御性拷贝")
    void whenSetServerConfig_thenCreateDefensiveCopy() {
        // Arrange
        LMStudioConfig.ServerConfig serverConfig = new LMStudioConfig.ServerConfig();
        serverConfig.setPrimaryUrl("http://test:1234");
        
        // Act
        config.setServer(serverConfig);
        
        // Assert
        assertThat(config.getServer()).isNotSameAs(serverConfig);
        assertThat(config.getServer().getPrimaryUrl()).isEqualTo("http://test:1234");
    }

    @Test
    @DisplayName("当设置null值时，应该正确处理")
    void whenSetNullValues_thenHandleCorrectly() {
        // Act
        config.setServer(null);
        config.setModels(null);
        config.setAutoSwitch(null);
        
        // Assert
        assertThat(config.getServer()).isNull();
        assertThat(config.getModels()).isNull();
        assertThat(config.getAutoSwitch()).isNull();
    }
}