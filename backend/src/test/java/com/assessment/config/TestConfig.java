package com.assessment.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;

/**
 * 测试配置类
 * 为测试环境提供专用的Bean配置
 */
@TestConfiguration
@ActiveProfiles("test")
public class TestConfig {

    /**
     * 测试环境使用较低强度的密码编码器，提升测试速度
     */
    @Bean
    @Primary
    public PasswordEncoder testPasswordEncoder() {
        return new BCryptPasswordEncoder(4); // 降低强度，提升测试速度
    }
}