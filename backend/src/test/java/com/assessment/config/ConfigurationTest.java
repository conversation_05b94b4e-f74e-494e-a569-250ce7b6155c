package com.assessment.config;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * 配置类综合测试
 * 测试各种配置类的基本功能和属性
 */
@DisplayName("配置类综合测试")
class ConfigurationTest {

    @Test
    @DisplayName("测试DoclingProperties的基本功能")
    void testDoclingPropertiesBasicFunctionality() {
        // Arrange & Act
        DoclingProperties properties = new DoclingProperties();
        
        // Assert
        assertThat(properties).isNotNull();
        assertThat(properties.getClass().getSimpleName()).isEqualTo("DoclingProperties");
        assertThat(properties.toString()).isNotNull();
        assertThat(properties.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试AIProperties的基本功能")
    void testAIPropertiesBasicFunctionality() {
        // Arrange & Act
        AIProperties properties = new AIProperties();
        
        // Assert
        assertThat(properties).isNotNull();
        assertThat(properties.getClass().getSimpleName()).isEqualTo("AIProperties");
        assertThat(properties.toString()).isNotNull();
        assertThat(properties.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试AssessmentProperties的基本功能")
    void testAssessmentPropertiesBasicFunctionality() {
        // Arrange & Act
        AssessmentProperties properties = new AssessmentProperties();
        
        // Assert
        assertThat(properties).isNotNull();
        assertThat(properties.getClass().getSimpleName()).isEqualTo("AssessmentProperties");
        assertThat(properties.toString()).isNotNull();
        assertThat(properties.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试配置类的包名")
    void testConfigurationClassPackages() {
        // Act & Assert
        assertThat(new DoclingProperties().getClass().getPackage().getName())
            .isEqualTo("com.assessment.config");
        assertThat(new AIProperties().getClass().getPackage().getName())
            .isEqualTo("com.assessment.config");
        assertThat(new AssessmentProperties().getClass().getPackage().getName())
            .isEqualTo("com.assessment.config");
        assertThat(new LMStudioConfig().getClass().getPackage().getName())
            .isEqualTo("com.assessment.config");
    }

    @Test
    @DisplayName("测试配置类的实例化")
    void testConfigurationClassInstantiation() {
        // 测试所有配置类都可以正常实例化
        assertThatCode(() -> new DoclingProperties()).doesNotThrowAnyException();
        assertThatCode(() -> new AIProperties()).doesNotThrowAnyException();
        assertThatCode(() -> new AssessmentProperties()).doesNotThrowAnyException();
        assertThatCode(() -> new LMStudioConfig()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试配置类的相等性")
    void testConfigurationClassEquality() {
        // 测试不同实例的相等性
        DoclingProperties docling1 = new DoclingProperties();
        DoclingProperties docling2 = new DoclingProperties();
        
        AIProperties ai1 = new AIProperties();
        AIProperties ai2 = new AIProperties();
        
        AssessmentProperties assessment1 = new AssessmentProperties();
        AssessmentProperties assessment2 = new AssessmentProperties();
        
        // 不同的实例应该不相等（除非重写了equals方法）
        assertThat(docling1).isNotSameAs(docling2);
        assertThat(ai1).isNotSameAs(ai2);
        assertThat(assessment1).isNotSameAs(assessment2);
        
        // 但自己应该等于自己
        assertThat(docling1).isEqualTo(docling1);
        assertThat(ai1).isEqualTo(ai1);
        assertThat(assessment1).isEqualTo(assessment1);
    }

    @Test
    @DisplayName("测试配置类的类型检查")
    void testConfigurationClassTypeChecking() {
        // Act
        Object docling = new DoclingProperties();
        Object ai = new AIProperties();
        Object assessment = new AssessmentProperties();
        Object lmstudio = new LMStudioConfig();
        
        // Assert
        assertThat(docling).isInstanceOf(DoclingProperties.class);
        assertThat(ai).isInstanceOf(AIProperties.class);
        assertThat(assessment).isInstanceOf(AssessmentProperties.class);
        assertThat(lmstudio).isInstanceOf(LMStudioConfig.class);
        
        // 交叉检查类型
        assertThat(docling).isNotInstanceOf(AIProperties.class);
        assertThat(ai).isNotInstanceOf(DoclingProperties.class);
        assertThat(assessment).isNotInstanceOf(LMStudioConfig.class);
    }

    @Test
    @DisplayName("测试配置类的注解")
    void testConfigurationClassAnnotations() {
        // 检查LMStudioConfig的注解
        Class<?> lmStudioClass = LMStudioConfig.class;
        assertThat(lmStudioClass.isAnnotationPresent(org.springframework.context.annotation.Configuration.class))
            .isTrue();
        assertThat(lmStudioClass.isAnnotationPresent(
            org.springframework.boot.context.properties.ConfigurationProperties.class))
            .isTrue();
    }

    @Test
    @DisplayName("测试配置类的修饰符")
    void testConfigurationClassModifiers() {
        // 检查类的修饰符
        Class<?> doclingClass = DoclingProperties.class;
        Class<?> aiClass = AIProperties.class;
        Class<?> assessmentClass = AssessmentProperties.class;
        Class<?> lmStudioClass = LMStudioConfig.class;
        
        assertThat(java.lang.reflect.Modifier.isPublic(doclingClass.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPublic(aiClass.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPublic(assessmentClass.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPublic(lmStudioClass.getModifiers())).isTrue();
        
        // 都不应该是final类
        assertThat(java.lang.reflect.Modifier.isFinal(doclingClass.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isFinal(aiClass.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isFinal(assessmentClass.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isFinal(lmStudioClass.getModifiers())).isFalse();
    }

    @Test
    @DisplayName("测试配置类的字段数量")
    void testConfigurationClassFieldCount() {
        // 检查配置类的字段数量（包括私有字段）
        Class<?> doclingClass = DoclingProperties.class;
        Class<?> aiClass = AIProperties.class;
        Class<?> assessmentClass = AssessmentProperties.class;
        Class<?> lmStudioClass = LMStudioConfig.class;
        
        // 这些类应该都有一些字段
        assertThat(doclingClass.getDeclaredFields().length).isGreaterThan(0);
        assertThat(aiClass.getDeclaredFields().length).isGreaterThan(0);
        assertThat(assessmentClass.getDeclaredFields().length).isGreaterThan(0);
        assertThat(lmStudioClass.getDeclaredFields().length).isGreaterThan(0);
    }
}