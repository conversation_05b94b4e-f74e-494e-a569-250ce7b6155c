package com.assessment.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.service.JwtTokenService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 多租户认证流程集成测试
 * 
 * 测试覆盖：
 * 1. 完整的多租户登录流程
 * 2. JWT Token生成与验证
 * 3. 租户隔离验证
 * 4. 权限检查机制
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@DisplayName("多租户认证流程集成测试")
class MultiTenantAuthenticationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PlatformUserRepository platformUserRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private TenantUserMembershipRepository membershipRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenService jwtTokenService;

    private Tenant testTenant;
    private PlatformUser testUser;
    private String testPassword = "testPassword123";

    @BeforeEach
    void setUp() {
        // 创建测试租户
        testTenant = createTestTenant();
        
        // 创建测试用户
        testUser = createTestUser();
        
        // 创建租户用户关系
        createTenantUserMembership();
    }

    @Test
    @DisplayName("完整的多租户登录流程 - 成功场景")
    void testCompleteMultiTenantLoginFlow() throws Exception {
        // Arrange
        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode(testTenant.getCode());
        loginRequest.setUsername(testUser.getUsername());
        loginRequest.setPassword(testPassword);

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.username").value(testUser.getUsername()))
                .andExpect(jsonPath("$.tenantCode").value(testTenant.getCode()))
                .andExpect(jsonPath("$.tenantName").value(testTenant.getName()))
                .andExpect(jsonPath("$.tenantRole").exists())
                .andExpect(jsonPath("$.permissions").isArray())
                .andExpect(jsonPath("$.userId").value(testUser.getId().toString()))
                .andExpect(jsonPath("$.tenantId").value(testTenant.getId().toString()))
                .andExpect(jsonPath("$.expiresIn").exists());
    }

    @Test
    @DisplayName("跨租户访问阻止测试")
    void testCrossTenantAccessPrevention() throws Exception {
        // Arrange - 创建另一个租户
        Tenant anotherTenant = createAnotherTenant();
        
        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode(anotherTenant.getCode());
        loginRequest.setUsername(testUser.getUsername()); // 使用原租户的用户
        loginRequest.setPassword(testPassword);

        // Act & Assert - 应该失败，因为用户不属于这个租户
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 用户不属于该租户"));
    }

    @Test
    @DisplayName("JWT Token验证流程测试")
    void testJwtTokenValidationFlow() throws Exception {
        // Arrange - 先登录获取Token
        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode(testTenant.getCode());
        loginRequest.setUsername(testUser.getUsername());
        loginRequest.setPassword(testPassword);

        String response = mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 解析获取Token
        var responseData = objectMapper.readTree(response);
        String accessToken = responseData.get("accessToken").asText();

        // Act & Assert - 验证Token有效性
        assertThat(accessToken).isNotBlank();
        
        // 使用JwtTokenService验证Token
        boolean isValid = jwtTokenService.validateToken(accessToken);
        assertThat(isValid).isTrue();

    }

    @Test
    @DisplayName("租户被禁用时的登录阻止测试")
    void testDisabledTenantLoginPrevention() throws Exception {
        // Arrange - 禁用租户
        testTenant.setStatus(Tenant.TenantStatus.DISABLED);
        tenantRepository.save(testTenant);

        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode(testTenant.getCode());
        loginRequest.setUsername(testUser.getUsername());
        loginRequest.setPassword(testPassword);

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 租户已禁用"));
    }

    @Test
    @DisplayName("用户被禁用时的登录阻止测试")
    void testDisabledUserLoginPrevention() throws Exception {
        // Arrange - 禁用用户
        testUser.setIsActive(false);
        platformUserRepository.save(testUser);

        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode(testTenant.getCode());
        loginRequest.setUsername(testUser.getUsername());
        loginRequest.setPassword(testPassword);

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 用户账户已禁用"));
    }

    @Test
    @DisplayName("错误密码登录阻止测试")
    void testWrongPasswordLoginPrevention() throws Exception {
        // Arrange
        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode(testTenant.getCode());
        loginRequest.setUsername(testUser.getUsername());
        loginRequest.setPassword("wrongPassword");

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 用户名或密码错误"));
    }

    @Test
    @DisplayName("不存在的租户登录阻止测试")
    void testNonExistentTenantLoginPrevention() throws Exception {
        // Arrange
        MultiTenantLoginRequest loginRequest = new MultiTenantLoginRequest();
        loginRequest.setTenantCode("NON_EXISTENT_TENANT");
        loginRequest.setUsername(testUser.getUsername());
        loginRequest.setPassword(testPassword);

        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("认证失败: 租户不存在"));
    }

    // Helper Methods

    private Tenant createTestTenant() {
        Tenant tenant = new Tenant();
        tenant.setId(UUID.randomUUID());
        tenant.setCode("TEST_HOSPITAL_001");
        tenant.setName("测试医院001");
        tenant.setIndustry("HOSPITAL");
        tenant.setContactEmail("<EMAIL>");
        tenant.setContactPhone("13800138001");
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        tenant.setCreatedAt(LocalDateTime.now());
        tenant.setUpdatedAt(LocalDateTime.now());
        return tenantRepository.save(tenant);
    }

    private Tenant createAnotherTenant() {
        Tenant tenant = new Tenant();
        tenant.setId(UUID.randomUUID());
        tenant.setCode("TEST_HOSPITAL_002");
        tenant.setName("测试医院002");
        tenant.setIndustry("HOSPITAL");
        tenant.setContactEmail("<EMAIL>");
        tenant.setContactPhone("13800138002");
        tenant.setStatus(Tenant.TenantStatus.ACTIVE);
        tenant.setCreatedAt(LocalDateTime.now());
        tenant.setUpdatedAt(LocalDateTime.now());
        return tenantRepository.save(tenant);
    }

    private PlatformUser createTestUser() {
        PlatformUser user = new PlatformUser();
        user.setId(UUID.randomUUID());
        user.setUsername("test_doctor");
        user.setEmail("<EMAIL>");
        user.setPasswordHash(passwordEncoder.encode(testPassword));
        user.setFullName("测试医生");
        user.setPlatformRole(PlatformUser.PlatformRole.USER);
        user.setIsActive(true);
        user.setEmailVerified(true);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        return platformUserRepository.save(user);
    }

    private void createTenantUserMembership() {
        TenantUserMembership membership = new TenantUserMembership();
        membership.setId(UUID.randomUUID().toString());
        membership.setTenantId(testTenant.getId().toString());
        membership.setUserId(testUser.getId().toString());
        membership.setTenantRole(TenantUserMembership.TenantRole.ASSESSOR);
        membership.setStatus(TenantUserMembership.MembershipStatus.ACTIVE);
        membership.setJoinedAt(LocalDateTime.now());
        membershipRepository.save(membership);
    }
}