package com.assessment.integration;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import com.assessment.dto.DocumentAnalysisRequest;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * API集成测试
 * 测试完整的API流程和集成
 */
@SpringBootTest(classes = com.assessment.TestApplication.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("API集成测试")
class APIIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("测试健康检查API集成")
    void testHealthCheckIntegration() throws Exception {
        mockMvc.perform(get("/api/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    @DisplayName("测试数据库连接API")
    @WithMockUser(roles = "ADMIN")
    void testDatabaseConnectionAPI() throws Exception {
        mockMvc.perform(get("/api/database/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.connected").value(true));
    }

    @Test
    @DisplayName("测试认证流程")
    void testAuthenticationFlow() throws Exception {
        // 测试未认证访问
        mockMvc.perform(get("/api/database/status"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("测试AI分析API集成")
    @WithMockUser(roles = "ADMIN")
    void testAIAnalysisIntegration() throws Exception {
        DocumentAnalysisRequest request = new DocumentAnalysisRequest();
        request.setMarkdownContent("# 老年人能力评估量表\n\n## 基本信息\n1. 姓名\n2. 年龄");
        request.setFileName("test_scale.md");
        
        mockMvc.perform(post("/api/ai/analyze")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());
    }

    @Test
    @DisplayName("测试系统信息API")
    void testSystemInfoAPI() throws Exception {
        mockMvc.perform(get("/api/health/info"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.application").exists())
                .andExpect(jsonPath("$.version").exists())
                .andExpect(jsonPath("$.java.version").exists());
    }

    @Test
    @DisplayName("测试CORS配置")
    void testCORSConfiguration() throws Exception {
        mockMvc.perform(get("/api/health")
                .header("Origin", "http://localhost:5274"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试错误处理")
    @WithMockUser(roles = "ADMIN")
    void testErrorHandling() throws Exception {
        // 测试无效的API端点
        mockMvc.perform(get("/api/nonexistent"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("测试内容类型验证")
    @WithMockUser(roles = "ADMIN")
    void testContentTypeValidation() throws Exception {
        // 测试错误的Content-Type
        mockMvc.perform(post("/api/ai/analyze")
                .contentType(MediaType.TEXT_PLAIN)
                .content("invalid content"))
                .andExpect(status().isBadRequest());
    }
}