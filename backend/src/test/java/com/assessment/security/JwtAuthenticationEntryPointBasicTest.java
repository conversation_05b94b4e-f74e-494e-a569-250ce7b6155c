package com.assessment.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.AuthenticationException;

/**
 * JWT认证入口点基础单元测试
 * 测试认证异常的处理逻辑
 */
@DisplayName("JWT认证入口点基础单元测试")
class JwtAuthenticationEntryPointBasicTest {

    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private HttpServletRequest mockRequest;
    private HttpServletResponse mockResponse;
    private AuthenticationException mockAuthException;

    @BeforeEach
    void setUp() {
        jwtAuthenticationEntryPoint = new JwtAuthenticationEntryPoint();
        mockRequest = mock(HttpServletRequest.class);
        mockResponse = mock(HttpServletResponse.class);
        mockAuthException = mock(AuthenticationException.class);
    }

    @Test
    @DisplayName("测试认证异常处理")
    void testCommenceWithAuthenticationException() throws IOException, ServletException {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockAuthException.getMessage()).thenReturn("Authentication failed");
        
        // Act
        jwtAuthenticationEntryPoint.commence(mockRequest, mockResponse, mockAuthException);
        
        // Assert
        verify(mockResponse).sendError(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "Unauthorized: Authentication failed"
        );
    }

    @Test
    @DisplayName("测试空异常消息处理")
    void testCommenceWithNullExceptionMessage() throws IOException, ServletException {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        when(mockAuthException.getMessage()).thenReturn(null);
        
        // Act
        jwtAuthenticationEntryPoint.commence(mockRequest, mockResponse, mockAuthException);
        
        // Assert
        verify(mockResponse).sendError(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "Unauthorized: null"
        );
    }

    @Test
    @DisplayName("测试不同请求路径的异常处理")
    void testCommenceWithDifferentRequestPaths() throws IOException, ServletException {
        String[] paths = {
            "/api/users",
            "/api/admin/dashboard",
            "/api/reports",
            "/secure/data"
        };
        
        for (int i = 0; i < paths.length; i++) {
            // Arrange - 为每个路径创建新的mock以避免验证冲突
            HttpServletRequest newMockRequest = mock(HttpServletRequest.class);
            HttpServletResponse newMockResponse = mock(HttpServletResponse.class);
            AuthenticationException newMockException = mock(AuthenticationException.class);
            
            when(newMockRequest.getRequestURI()).thenReturn(paths[i]);
            when(newMockException.getMessage()).thenReturn("Access denied");
            
            // Act
            jwtAuthenticationEntryPoint.commence(newMockRequest, newMockResponse, newMockException);
            
            // Assert
            verify(newMockResponse).sendError(
                HttpServletResponse.SC_UNAUTHORIZED, 
                "Unauthorized: Access denied"
            );
        }
    }

    @Test
    @DisplayName("测试长异常消息处理")
    void testCommenceWithLongExceptionMessage() throws IOException, ServletException {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/protected");
        String longMessage = "This is a very long authentication error message that might occur " +
                           "when there are detailed validation errors or complex authentication " +
                           "failures in the system";
        when(mockAuthException.getMessage()).thenReturn(longMessage);
        
        // Act
        jwtAuthenticationEntryPoint.commence(mockRequest, mockResponse, mockAuthException);
        
        // Assert
        verify(mockResponse).sendError(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "Unauthorized: " + longMessage
        );
    }

    @Test
    @DisplayName("测试特殊字符异常消息处理")
    void testCommenceWithSpecialCharacterExceptionMessage() throws IOException, ServletException {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("/api/test");
        String specialMessage = "Error: <script>alert('test')</script> & other chars: 中文测试";
        when(mockAuthException.getMessage()).thenReturn(specialMessage);
        
        // Act
        jwtAuthenticationEntryPoint.commence(mockRequest, mockResponse, mockAuthException);
        
        // Assert
        verify(mockResponse).sendError(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "Unauthorized: " + specialMessage
        );
    }

    @Test
    @DisplayName("测试入口点类的基本属性")
    void testJwtAuthenticationEntryPointBasicProperties() {
        // Act & Assert
        assertThat(jwtAuthenticationEntryPoint).isNotNull();
        assertThat(jwtAuthenticationEntryPoint.getClass().getSimpleName())
            .isEqualTo("JwtAuthenticationEntryPoint");
        assertThat(jwtAuthenticationEntryPoint.getClass().getPackage().getName())
            .isEqualTo("com.assessment.security");
        
        // 验证Component注解
        assertThat(jwtAuthenticationEntryPoint.getClass().isAnnotationPresent(
            org.springframework.stereotype.Component.class)).isTrue();
    }

    @Test
    @DisplayName("测试接口实现")
    void testAuthenticationEntryPointImplementation() {
        // Act & Assert
        assertThat(jwtAuthenticationEntryPoint).isInstanceOf(
            org.springframework.security.web.AuthenticationEntryPoint.class);
    }

    @Test
    @DisplayName("测试字符串表示和哈希码")
    void testStringRepresentationAndHashCode() {
        // Act & Assert
        assertThat(jwtAuthenticationEntryPoint.toString()).isNotNull();
        assertThat(jwtAuthenticationEntryPoint.toString())
            .contains("JwtAuthenticationEntryPoint");
        assertThat(jwtAuthenticationEntryPoint.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试实例化")
    void testInstantiation() {
        // Act & Assert
        assertThatCode(() -> new JwtAuthenticationEntryPoint()).doesNotThrowAnyException();
        
        JwtAuthenticationEntryPoint newEntryPoint = new JwtAuthenticationEntryPoint();
        assertThat(newEntryPoint).isNotNull();
        assertThat(newEntryPoint).isInstanceOf(JwtAuthenticationEntryPoint.class);
    }

    @Test
    @DisplayName("测试相等性和哈希码一致性")
    void testEqualityAndHashCodeConsistency() {
        // Arrange
        JwtAuthenticationEntryPoint anotherEntryPoint = new JwtAuthenticationEntryPoint();
        
        // Act & Assert
        assertThat(jwtAuthenticationEntryPoint).isEqualTo(jwtAuthenticationEntryPoint);
        assertThat(jwtAuthenticationEntryPoint).isNotSameAs(anotherEntryPoint);
        assertThat(jwtAuthenticationEntryPoint.hashCode())
            .isEqualTo(jwtAuthenticationEntryPoint.hashCode());
    }

    @Test
    @DisplayName("测试方法存在性")
    void testMethodExistence() {
        // Act & Assert
        Class<?> entryPointClass = JwtAuthenticationEntryPoint.class;
        
        // 验证commence方法存在
        assertThat(entryPointClass.getMethods()).anyMatch(
            method -> method.getName().equals("commence") && 
                     method.getParameterCount() == 3
        );
    }

    @Test
    @DisplayName("测试类型安全性")
    void testTypeSafety() {
        // Act & Assert
        Object entryPoint = jwtAuthenticationEntryPoint;
        
        assertThat(entryPoint).isInstanceOf(JwtAuthenticationEntryPoint.class);
        assertThat(entryPoint).isInstanceOf(
            org.springframework.security.web.AuthenticationEntryPoint.class);
        
        // 验证不是其他类型
        assertThat(entryPoint).isNotInstanceOf(String.class);
        assertThat(entryPoint).isNotInstanceOf(SecurityConfig.class);
    }

    @Test
    @DisplayName("测试空请求URI处理")
    void testCommenceWithNullRequestURI() throws IOException, ServletException {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn(null);
        when(mockAuthException.getMessage()).thenReturn("Test error");
        
        // Act
        jwtAuthenticationEntryPoint.commence(mockRequest, mockResponse, mockAuthException);
        
        // Assert
        verify(mockResponse).sendError(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "Unauthorized: Test error"
        );
    }

    @Test
    @DisplayName("测试空字符串请求URI处理")
    void testCommenceWithEmptyRequestURI() throws IOException, ServletException {
        // Arrange
        when(mockRequest.getRequestURI()).thenReturn("");
        when(mockAuthException.getMessage()).thenReturn("Empty URI error");
        
        // Act
        jwtAuthenticationEntryPoint.commence(mockRequest, mockResponse, mockAuthException);
        
        // Assert
        verify(mockResponse).sendError(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "Unauthorized: Empty URI error"
        );
    }
}