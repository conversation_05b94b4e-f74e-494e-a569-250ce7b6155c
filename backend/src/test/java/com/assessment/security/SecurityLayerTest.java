package com.assessment.security;

import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 安全配置测试
 * 验证安全相关的核心配置是否正确。
 */
@DisplayName("安全配置测试")
class SecurityLayerTest {

    @Test
    @DisplayName("应该成功创建密码编码器并且是BCrypt类型")
    void shouldCreatePasswordEncoderAsBCrypt() {
        // Arrange
        SecurityConfig securityConfig = new SecurityConfig();

        // Act
        PasswordEncoder passwordEncoder = securityConfig.passwordEncoder();

        // Assert
        assertThat(passwordEncoder).isNotNull();
        assertThat(passwordEncoder).isInstanceOf(BCryptPasswordEncoder.class);
    }

    @Test
    @DisplayName("密码编码器应该能正确编码和匹配密码")
    void passwordEncoderShouldEncodeAndMatchPasswords() {
        // Arrange
        SecurityConfig securityConfig = new SecurityConfig();
        PasswordEncoder passwordEncoder = securityConfig.passwordEncoder();
        String rawPassword = "mySecurePassword123";

        // Act
        String encodedPassword = passwordEncoder.encode(rawPassword);

        // Assert
        assertThat(encodedPassword).isNotNull();
        assertThat(encodedPassword).isNotEqualTo(rawPassword);
        assertThat(passwordEncoder.matches(rawPassword, encodedPassword)).isTrue();
        assertThat(passwordEncoder.matches("wrongPassword", encodedPassword)).isFalse();
    }
}