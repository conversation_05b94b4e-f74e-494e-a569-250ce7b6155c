package com.assessment.security;

import com.assessment.constants.SecurityConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * SecurityConfig 单元测试类
 * 测试Security配置类的Bean创建和配置方法
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SecurityConfig单元测试")
class SecurityConfigUnitTest {

    @InjectMocks
    private SecurityConfig securityConfig;

    @Mock
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Mock
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Mock
    private AuthenticationConfiguration authenticationConfiguration;

    @Mock
    private AuthenticationManager authenticationManager;

    @BeforeEach
    void setUp() {
        // 初始化模拟对象
    }

    @Test
    @DisplayName("测试密码编码器Bean创建")
    void testPasswordEncoderBean() {
        // Act
        PasswordEncoder passwordEncoder = securityConfig.passwordEncoder();

        // Assert
        assertThat(passwordEncoder).isNotNull();
        assertThat(passwordEncoder.getClass().getSimpleName()).isEqualTo("BCryptPasswordEncoder");

        // 测试实际功能
        String rawPassword = "testPassword123";
        String encodedPassword = passwordEncoder.encode(rawPassword);

        assertThat(encodedPassword).isNotNull();
        assertThat(encodedPassword).isNotEqualTo(rawPassword);
        assertThat(passwordEncoder.matches(rawPassword, encodedPassword)).isTrue();
        assertThat(passwordEncoder.matches("wrongPassword", encodedPassword)).isFalse();
    }

    @Test
    @DisplayName("测试认证管理器Bean创建")
    void testAuthenticationManagerBean() throws Exception {
        // Arrange
        when(authenticationConfiguration.getAuthenticationManager()).thenReturn(authenticationManager);

        // Act
        AuthenticationManager result = securityConfig.authenticationManager(authenticationConfiguration);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(authenticationManager);
    }

    @Test
    @DisplayName("测试CORS配置源Bean创建")
    void testCorsConfigurationSourceBean() {
        // Act
        UrlBasedCorsConfigurationSource corsSource = securityConfig.corsConfigurationSource();

        // Assert
        assertThat(corsSource).isNotNull();

        // 验证CORS配置
        CorsConfiguration corsConfig = corsSource.getCorsConfigurations().get("/**");
        assertThat(corsConfig).isNotNull();

        // 验证允许的Origin模式
        assertThat(corsConfig.getAllowedOriginPatterns())
            .isNotNull()
            .contains("*");

        // 验证允许的HTTP方法
        assertThat(corsConfig.getAllowedMethods())
            .isNotNull()
            .containsExactlyInAnyOrder("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH");

        // 验证允许的Headers
        assertThat(corsConfig.getAllowedHeaders())
            .isNotNull()
            .contains("*");

        // 验证凭据支持
        assertThat(corsConfig.getAllowCredentials()).isTrue();

        // 验证缓存时间
        assertThat(corsConfig.getMaxAge()).isEqualTo(SecurityConstants.CORS_MAX_AGE_SECONDS);
    }

    @Test
    @DisplayName("测试CORS配置 - 默认值验证")
    void testCorsConfiguration_DefaultValues() {
        // Act
        UrlBasedCorsConfigurationSource corsSource = securityConfig.corsConfigurationSource();
        CorsConfiguration corsConfig = corsSource.getCorsConfigurations().get("/**");

        // Assert - 验证具体配置值
        assertThat(corsConfig.getAllowedOriginPatterns()).hasSize(1);
        assertThat(corsConfig.getAllowedMethods()).hasSize(6);
        assertThat(corsConfig.getAllowedHeaders()).hasSize(1);
        assertThat(corsConfig.getAllowCredentials()).isTrue();
    }

    @Test
    @DisplayName("测试CORS配置 - 路径注册")
    void testCorsConfiguration_PathRegistration() {
        // Act
        UrlBasedCorsConfigurationSource corsSource = securityConfig.corsConfigurationSource();

        // Assert - 验证路径配置
        assertThat(corsSource.getCorsConfigurations()).containsKey("/**");
        assertThat(corsSource.getCorsConfigurations().get("/**")).isNotNull();
    }

    @Test
    @DisplayName("测试密码编码器 - 多次编码结果不同")
    void testPasswordEncoder_DifferentResultsEachTime() {
        // Arrange
        PasswordEncoder passwordEncoder = securityConfig.passwordEncoder();
        String rawPassword = "samePassword";

        // Act
        String encoded1 = passwordEncoder.encode(rawPassword);
        String encoded2 = passwordEncoder.encode(rawPassword);

        // Assert - BCrypt每次编码结果应该不同（因为随机盐）
        assertThat(encoded1).isNotEqualTo(encoded2);
        assertThat(passwordEncoder.matches(rawPassword, encoded1)).isTrue();
        assertThat(passwordEncoder.matches(rawPassword, encoded2)).isTrue();
    }

    @Test
    @DisplayName("测试密码编码器 - 边界情况")
    void testPasswordEncoder_EdgeCases() {
        // Arrange
        PasswordEncoder passwordEncoder = securityConfig.passwordEncoder();

        // Test empty password
        String emptyPassword = "";
        String encodedEmpty = passwordEncoder.encode(emptyPassword);
        assertThat(passwordEncoder.matches(emptyPassword, encodedEmpty)).isTrue();

        // Test long password (BCrypt has 72 byte limit)
        String longPassword = "a".repeat(70); // Keep under 72 bytes
        String encodedLong = passwordEncoder.encode(longPassword);
        assertThat(passwordEncoder.matches(longPassword, encodedLong)).isTrue();

        // Test special characters
        String specialPassword = "!@#$%^&*()_+-=[]{}|;:'\",.<>?/`~";
        String encodedSpecial = passwordEncoder.encode(specialPassword);
        assertThat(passwordEncoder.matches(specialPassword, encodedSpecial)).isTrue();

        // Test unicode
        String unicodePassword = "测试密码123";
        String encodedUnicode = passwordEncoder.encode(unicodePassword);
        assertThat(passwordEncoder.matches(unicodePassword, encodedUnicode)).isTrue();
    }

    @Test
    @DisplayName("测试CORS配置 - 方法验证")
    void testCorsConfiguration_MethodValidation() {
        // Act
        UrlBasedCorsConfigurationSource corsSource = securityConfig.corsConfigurationSource();
        CorsConfiguration corsConfig = corsSource.getCorsConfigurations().get("/**");

        // Assert - 验证每个HTTP方法都被允许
        assertThat(corsConfig.getAllowedMethods()).contains("GET");
        assertThat(corsConfig.getAllowedMethods()).contains("POST");
        assertThat(corsConfig.getAllowedMethods()).contains("PUT");
        assertThat(corsConfig.getAllowedMethods()).contains("DELETE");
        assertThat(corsConfig.getAllowedMethods()).contains("OPTIONS");
        assertThat(corsConfig.getAllowedMethods()).contains("PATCH");
    }

    @Test
    @DisplayName("测试CORS配置 - Headers验证")
    void testCorsConfiguration_HeadersValidation() {
        // Act
        UrlBasedCorsConfigurationSource corsSource = securityConfig.corsConfigurationSource();
        CorsConfiguration corsConfig = corsSource.getCorsConfigurations().get("/**");

        // Assert - 验证通配符header配置
        assertThat(corsConfig.getAllowedHeaders()).contains("*");
        
        // 验证这意味着允许所有headers
        assertThat(corsConfig.getAllowedHeaders()).hasSize(1);
    }

    @Test
    @DisplayName("测试SecurityConfig Bean实例化")
    void testSecurityConfigInstantiation() {
        // Assert
        assertThat(securityConfig).isNotNull();
        
        // 验证依赖注入的字段
        assertThat(securityConfig).hasFieldOrProperty("jwtAuthenticationFilter");
        assertThat(securityConfig).hasFieldOrProperty("unauthorizedHandler");
    }
}