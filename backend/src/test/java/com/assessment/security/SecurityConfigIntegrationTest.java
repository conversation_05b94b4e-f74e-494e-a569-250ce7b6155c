package com.assessment.security;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.anonymous;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SecurityConfig 集成测试类
 * 测试Spring Security配置的实际行为
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@DisplayName("Spring Security配置集成测试")
class SecurityConfigIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    // 测试基本的安全端点配置
    @Test
    @DisplayName("测试健康检查端点 - 公开访问")
    void testHealthEndpoint_PublicAccess() throws Exception {
        mockMvc.perform(get("/api/health/status")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试认证端点 - 公开访问")
    void testAuthEndpoint_PublicAccess() throws Exception {
        mockMvc.perform(post("/api/auth/login")
                .with(anonymous())
                .contentType("application/json")
                .content("{}"))
                .andExpect(status().isBadRequest()); // 400表示可以访问但参数错误
    }

    @Test
    @DisplayName("测试系统端点 - 需要认证")
    void testSystemEndpoint_RequiresAuth() throws Exception {
        mockMvc.perform(get("/api/system/users")
                .with(anonymous()))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("测试系统端点 - 认证用户可访问")
    @WithMockUser(roles = "ADMIN")
    void testSystemEndpoint_AuthenticatedAccess() throws Exception {
        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试OPTIONS请求 - 始终允许")
    void testOptionsRequest_AlwaysAllowed() throws Exception {
        mockMvc.perform(options("/api/any-path")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试Swagger UI - 公开访问")
    void testSwaggerUI_PublicAccess() throws Exception {
        mockMvc.perform(get("/swagger-ui/index.html")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试Actuator - 公开访问")
    void testActuator_PublicAccess() throws Exception {
        mockMvc.perform(get("/actuator/health")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试API文档 - 公开访问")
    void testApiDocs_PublicAccess() throws Exception {
        mockMvc.perform(get("/v3/api-docs")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试多租户认证 - 公开访问")
    void testMultiTenantAuth_PublicAccess() throws Exception {
        mockMvc.perform(post("/api/multi-tenant/auth/register")
                .with(anonymous())
                .contentType("application/json")
                .content("{}"))
                .andExpect(status().isBadRequest()); // 400表示可以访问但参数错误
    }

    @Test
    @DisplayName("测试CORS预检请求")
    void testCorsPreflightRequest() throws Exception {
        mockMvc.perform(options("/api/system/users")
                .header("Origin", "http://localhost:3000")
                .header("Access-Control-Request-Method", "POST")
                .header("Access-Control-Request-Headers", "Content-Type,Authorization"))
                .andExpect(status().isOk())
                .andExpect(header().exists("Access-Control-Allow-Origin"))
                .andExpect(header().exists("Access-Control-Allow-Methods"))
                .andExpect(header().exists("Access-Control-Allow-Headers"));
    }

    @Test
    @DisplayName("测试WebJars资源 - 公开访问")
    void testWebJarsResources_PublicAccess() throws Exception {
        mockMvc.perform(get("/webjars/swagger-ui/4.15.5/swagger-ui.js")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试根路径 - 公开访问")
    void testRootPath_PublicAccess() throws Exception {
        mockMvc.perform(get("/")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试错误页面 - 公开访问")
    void testErrorPage_PublicAccess() throws Exception {
        mockMvc.perform(get("/error")
                .with(anonymous()))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试多种HTTP方法在公开端点")
    void testMultipleHttpMethods_PublicEndpoints() throws Exception {
        String publicPath = "/api/auth/test";
        
        // GET
        mockMvc.perform(get(publicPath).with(anonymous()))
                .andExpect(status().isNotFound()); // 404表示路由可达但端点不存在

        // POST
        mockMvc.perform(post(publicPath).with(anonymous()).contentType("application/json").content("{}"))
                .andExpect(status().isNotFound());

        // PUT
        mockMvc.perform(put(publicPath).with(anonymous()).contentType("application/json").content("{}"))
                .andExpect(status().isNotFound());

        // DELETE
        mockMvc.perform(delete(publicPath).with(anonymous()))
                .andExpect(status().isNotFound());

        // OPTIONS
        mockMvc.perform(options(publicPath).with(anonymous()))
                .andExpect(status().isOk()); // OPTIONS请求被明确允许
    }

    @Test
    @DisplayName("测试受保护端点的不同认证级别")
    void testProtectedEndpoints_DifferentAuthLevels() throws Exception {
        // 匿名用户 - 应该被拒绝
        mockMvc.perform(get("/api/system/dashboard")
                .with(anonymous()))
                .andExpect(status().isUnauthorized());

        // 普通用户角色 - 对系统API可能被拒绝
        mockMvc.perform(get("/api/system/dashboard"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("测试管理员权限端点")
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testAdminEndpoints_WithAdminRole() throws Exception {
        mockMvc.perform(get("/api/system/dashboard"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试普通用户权限限制")
    @WithMockUser(username = "user", roles = {"USER"})
    void testUserEndpoints_WithUserRole() throws Exception {
        // 普通用户访问系统管理API应该被拒绝或返回404
        mockMvc.perform(get("/api/system/users"))
                .andExpect(status().isNotFound()); // 或者可能是403
    }

    @Test
    @DisplayName("测试深层路径匹配")
    void testDeepPathMatching() throws Exception {
        // 测试深层公开路径
        mockMvc.perform(get("/api/auth/deep/nested/endpoint")
                .with(anonymous()))
                .andExpect(status().isNotFound()); // 404表示路由可达

        // 测试深层受保护路径
        mockMvc.perform(get("/api/system/deep/nested/admin")
                .with(anonymous()))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("测试安全头部配置")
    void testSecurityHeaders() throws Exception {
        mockMvc.perform(get("/"))
                .andExpect(header().exists("X-Content-Type-Options"))
                .andExpect(header().exists("X-Frame-Options"));
    }
}