package com.assessment.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * HealthCheckService 测试类
 * 测试健康检查服务的基本功能
 */
@DisplayName("健康检查服务测试")
class HealthCheckServiceTest {

    private HealthCheckService healthCheckService;

    @BeforeEach
    void setUp() {
        healthCheckService = new HealthCheckService();
    }

    @Test
    @DisplayName("当检查应用健康状态时，应该返回健康")
    void whenCheckApplicationHealth_thenReturnHealthy() {
        // Act
        boolean healthy = healthCheckService.isHealthy();
        
        // Assert
        assertThat(healthy).isTrue();
    }

    @Test
    @DisplayName("当获取状态信息时，应该返回正常运行消息")
    void whenGetStatus_thenReturnNormalRunningMessage() {
        // Act
        String status = healthCheckService.getStatus();
        
        // Assert
        assertThat(status).isEqualTo("应用程序运行正常");
        assertThat(status).isNotNull();
        assertThat(status).isNotEmpty();
    }

    @Test
    @DisplayName("当获取版本信息时，应该返回版本号")
    void whenGetVersion_thenReturnVersionNumber() {
        // Act
        String version = healthCheckService.getVersion();
        
        // Assert
        assertThat(version).isEqualTo("1.0.0-SNAPSHOT");
        assertThat(version).matches("\\d+\\.\\d+\\.\\d+.*");
        assertThat(version).isNotNull();
        assertThat(version).isNotEmpty();
    }

    @Test
    @DisplayName("多次调用健康检查，应该始终返回一致结果")
    void whenMultipleHealthChecks_thenAlwaysReturnConsistentResult() {
        // Act & Assert
        assertThat(healthCheckService.isHealthy()).isTrue();
        assertThat(healthCheckService.isHealthy()).isTrue();
        assertThat(healthCheckService.isHealthy()).isTrue();
    }

    @Test
    @DisplayName("多次获取状态信息，应该返回相同内容")
    void whenMultipleStatusRequests_thenReturnSameContent() {
        // Act
        String status1 = healthCheckService.getStatus();
        String status2 = healthCheckService.getStatus();
        String status3 = healthCheckService.getStatus();
        
        // Assert
        assertThat(status1).isEqualTo(status2);
        assertThat(status2).isEqualTo(status3);
        assertThat(status1).isEqualTo(status3);
    }

    @Test
    @DisplayName("多次获取版本信息，应该返回相同版本")
    void whenMultipleVersionRequests_thenReturnSameVersion() {
        // Act
        String version1 = healthCheckService.getVersion();
        String version2 = healthCheckService.getVersion();
        String version3 = healthCheckService.getVersion();
        
        // Assert
        assertThat(version1).isEqualTo(version2);
        assertThat(version2).isEqualTo(version3);
        assertThat(version1).isEqualTo(version3);
    }
}