package com.assessment.service;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MultiTenantAuthService 单元测试
 * 覆盖多租户认证的核心业务逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("多租户认证服务测试")
class MultiTenantAuthServiceTest {

    @Mock
    private PlatformUserRepository userRepository;
    
    @Mock
    private TenantRepository tenantRepository;
    
    @Mock
    private TenantUserMembershipRepository membershipRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private JwtTokenService jwtTokenService;
    
    @InjectMocks
    private MultiTenantAuthService authService;
    
    private Tenant testTenant;
    private PlatformUser testUser;
    private TenantUserMembership testMembership;
    private MultiTenantLoginRequest validRequest;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testTenant = TestDataFactory.createTestTenant("HOSPITAL01", "测试医院");
        testUser = TestDataFactory.createTestUser("doctor1", "<EMAIL>");
        testMembership = TestDataFactory.createAssessorMembership(
            testTenant.getId(), testUser.getId());
        
        // 创建有效的登录请求
        validRequest = new MultiTenantLoginRequest();
        validRequest.setTenantCode(testTenant.getCode());
        validRequest.setUsername(testUser.getUsername());
        validRequest.setPassword("test123");
    }

    @Nested
    @DisplayName("正常认证流程测试")
    class NormalAuthenticationTests {

        @Test
        @DisplayName("给定有效凭据，当用户认证时，应该返回访问令牌")
        void givenValidCredentials_whenAuthenticate_thenReturnTokens() {
            // Arrange
            mockSuccessfulAuthentication();
            
            // Act
            MultiTenantLoginResponse response = authService.authenticate(validRequest);
            
            // Assert
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("mock-access-token");
            assertThat(response.getRefreshToken()).isEqualTo("mock-refresh-token");
            assertThat(response.getTenantId()).isEqualTo(testTenant.getId().toString());
            assertThat(response.getUserId()).isEqualTo(testUser.getId().toString());
            assertThat(response.getTenantRole()).isEqualTo("ASSESSOR");
            
            // 验证方法调用
            verify(tenantRepository).findByCode(testTenant.getCode());
            verify(userRepository).findByUsername(validRequest.getFullUsername());
            verify(membershipRepository).findByTenantIdAndUserId(
                testTenant.getId().toString(), testUser.getId().toString());
            verify(passwordEncoder).matches(validRequest.getPassword(), testUser.getPasswordHash());
            verify(jwtTokenService).generateAccessToken(testUser, testTenant, testMembership);
            verify(jwtTokenService).generateRefreshToken(testUser.getId().toString());
        }

        @Test
        @DisplayName("给定管理员用户，当认证成功时，应该返回管理员角色")
        void givenAdminUser_whenAuthenticate_thenReturnAdminRole() {
            // Arrange
            TenantUserMembership adminMembership = TestDataFactory.createAdminMembership(
                testTenant.getId(), testUser.getId());
            
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.of(testTenant));
            when(userRepository.findByUsername(validRequest.getFullUsername()))
                .thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(validRequest.getPassword(), testUser.getPasswordHash()))
                .thenReturn(true);
            when(membershipRepository.findByTenantIdAndUserId(anyString(), anyString()))
                .thenReturn(Optional.of(adminMembership));
            when(jwtTokenService.generateAccessToken(any(), any(), any()))
                .thenReturn("admin-access-token");
            when(jwtTokenService.generateRefreshToken(anyString()))
                .thenReturn("admin-refresh-token");
            
            // Act
            MultiTenantLoginResponse response = authService.authenticate(validRequest);
            
            // Assert
            assertThat(response.getTenantRole()).isEqualTo("ADMIN");
        }
    }

    @Nested
    @DisplayName("异常场景测试")
    class ExceptionScenarioTests {

        @Test
        @DisplayName("给定不存在的租户代码，当认证时，应该抛出异常")
        void givenNonExistentTenant_whenAuthenticate_thenThrowException() {
            // Arrange
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.empty());
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
            
            verify(userRepository, never()).findByUsername(anyString());
        }

        @Test
        @DisplayName("给定非活跃租户，当认证时，应该抛出异常")
        void givenInactiveTenant_whenAuthenticate_thenThrowException() {
            // Arrange
            testTenant.setStatus(Tenant.TenantStatus.SUSPENDED);
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.of(testTenant));
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
        }

        @Test
        @DisplayName("给定不存在的用户，当认证时，应该抛出异常")
        void givenNonExistentUser_whenAuthenticate_thenThrowException() {
            // Arrange
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.of(testTenant));
            when(userRepository.findByUsername(validRequest.getFullUsername()))
                .thenReturn(Optional.empty());
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
        }

        @Test
        @DisplayName("给定错误密码，当认证时，应该抛出异常")
        void givenWrongPassword_whenAuthenticate_thenThrowException() {
            // Arrange
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.of(testTenant));
            when(userRepository.findByUsername(validRequest.getFullUsername()))
                .thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(validRequest.getPassword(), testUser.getPasswordHash()))
                .thenReturn(false);
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
        }

        @Test
        @DisplayName("给定非活跃用户，当认证时，应该抛出异常")
        void givenInactiveUser_whenAuthenticate_thenThrowException() {
            // Arrange
            testUser.setIsActive(false);
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.of(testTenant));
            when(userRepository.findByUsername(validRequest.getFullUsername()))
                .thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(validRequest.getPassword(), testUser.getPasswordHash()))
                .thenReturn(true);
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
        }

        @Test
        @DisplayName("给定用户不属于租户，当认证时，应该抛出异常")
        void givenUserNotInTenant_whenAuthenticate_thenThrowException() {
            // Arrange
            when(tenantRepository.findByCode(testTenant.getCode()))
                .thenReturn(Optional.of(testTenant));
            when(userRepository.findByUsername(validRequest.getFullUsername()))
                .thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(validRequest.getPassword(), testUser.getPasswordHash()))
                .thenReturn(true);
            when(membershipRepository.findByTenantIdAndUserId(anyString(), anyString()))
                .thenReturn(Optional.empty());
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
        }
    }

    @Nested
    @DisplayName("超级管理员认证测试")
    class SuperAdminAuthenticationTests {

        @Test
        @DisplayName("给定超级管理员凭据，当认证时，应该返回超级管理员令牌")
        void givenSuperAdminCredentials_whenAuthenticate_thenReturnSuperAdminToken() {
            // Arrange
            MultiTenantLoginRequest superAdminRequest = new MultiTenantLoginRequest();
            superAdminRequest.setTenantCode("platform");
            superAdminRequest.setUsername("admin");
            superAdminRequest.setPassword("admin123");
            
            PlatformUser superAdmin = TestDataFactory.createTestUser("admin", "<EMAIL>");
            superAdmin.setPlatformRole(PlatformUser.PlatformRole.ADMIN);
            
            when(userRepository.findByUsername("admin"))
                .thenReturn(Optional.of(superAdmin));
            when(passwordEncoder.matches("admin123", superAdmin.getPasswordHash()))
                .thenReturn(true);
            when(jwtTokenService.generateSuperAdminToken(superAdmin))
                .thenReturn("super-admin-token");
            when(jwtTokenService.generateRefreshToken(superAdmin.getId().toString()))
                .thenReturn("super-admin-refresh");
            
            // Act
            MultiTenantLoginResponse response = authService.authenticate(superAdminRequest);
            
            // Assert
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("super-admin-token");
            assertThat(response.getRefreshToken()).isEqualTo("super-admin-refresh");
            assertThat(response.getTenantId()).isNull(); // 超级管理员没有特定租户
            assertThat(response.getUserId()).isEqualTo(superAdmin.getId().toString());
        }

        @Test
        @DisplayName("给定普通用户尝试超级管理员登录，当认证时，应该抛出异常")
        void givenRegularUserWithSuperAdminUsername_whenAuthenticate_thenThrowException() {
            // Arrange
            MultiTenantLoginRequest superAdminRequest = new MultiTenantLoginRequest();
            superAdminRequest.setTenantCode("platform");
            superAdminRequest.setUsername("admin");
            superAdminRequest.setPassword("admin123");
            
            PlatformUser regularUser = TestDataFactory.createTestUser("admin", "<EMAIL>");
            regularUser.setPlatformRole(PlatformUser.PlatformRole.USER); // 普通用户角色
            
            when(userRepository.findByUsername("admin"))
                .thenReturn(Optional.of(regularUser));
            when(passwordEncoder.matches("admin123", regularUser.getPasswordHash()))
                .thenReturn(true);
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(superAdminRequest))
                .isInstanceOf(BadCredentialsException.class)
                .hasMessageContaining("登录失败");
        }
    }

    @Nested
    @DisplayName("边界值测试")
    class BoundaryValueTests {

        @Test
        @DisplayName("给定空租户代码，当认证时，应该抛出异常")
        void givenEmptyTenantCode_whenAuthenticate_thenThrowException() {
            // Arrange
            validRequest.setTenantCode("");
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class);
        }

        @Test
        @DisplayName("给定空用户名，当认证时，应该抛出异常")
        void givenEmptyUsername_whenAuthenticate_thenThrowException() {
            // Arrange
            validRequest.setUsername("");
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class);
        }

        @Test
        @DisplayName("给定空密码，当认证时，应该抛出异常")
        void givenEmptyPassword_whenAuthenticate_thenThrowException() {
            // Arrange
            validRequest.setPassword("");
            
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(validRequest))
                .isInstanceOf(BadCredentialsException.class);
        }

        @Test
        @DisplayName("给定null请求，当认证时，应该抛出异常")
        void givenNullRequest_whenAuthenticate_thenThrowException() {
            // Act & Assert
            assertThatThrownBy(() -> authService.authenticate(null))
                .isInstanceOf(Exception.class)
                .hasMessageContaining("request");
        }
    }

    /**
     * 模拟成功认证的所有依赖调用
     */
    private void mockSuccessfulAuthentication() {
        when(tenantRepository.findByCode(testTenant.getCode()))
            .thenReturn(Optional.of(testTenant));
        // 使用完整用户名（包含租户前缀）
        when(userRepository.findByUsername(validRequest.getFullUsername()))
            .thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(validRequest.getPassword(), testUser.getPasswordHash()))
            .thenReturn(true);
        when(membershipRepository.findByTenantIdAndUserId(
            testTenant.getId().toString(), testUser.getId().toString()))
            .thenReturn(Optional.of(testMembership));
        when(jwtTokenService.generateAccessToken(testUser, testTenant, testMembership))
            .thenReturn("mock-access-token");
        when(jwtTokenService.generateRefreshToken(testUser.getId().toString()))
            .thenReturn("mock-refresh-token");
    }
}