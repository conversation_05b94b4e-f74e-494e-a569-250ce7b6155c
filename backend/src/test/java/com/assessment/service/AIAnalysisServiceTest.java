package com.assessment.service;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;


/**
 * AI分析服务简单连通性测试
 * 测试地址：192.168.1.231:1234
 * 测试模型：deepseek-r1-0528-qwen3-8b
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AI分析服务连通性测试")
class AIAnalysisServiceTest {

    private static final String TEST_SERVER_URL = "http://192.168.1.231:1234";
    private static final String TEST_MODEL_ID = "deepseek-r1-0528-qwen3-8b";
    private static final String TEST_MODEL_DISPLAY_NAME = "DeepSeek R1 0528 Qwen3 8b";

    @Mock
    private LMStudioService lmStudioService;

    @Mock
    private RestTemplate restTemplate;

    private AIAnalysisService aiAnalysisService;

    private DocumentAnalysisRequest testRequest;

    @BeforeEach
    void setUp() {
        aiAnalysisService = new AIAnalysisService(lmStudioService, restTemplate);

        testRequest = new DocumentAnalysisRequest();
        testRequest.setFileName("elderly_assessment_scale.pdf");
        testRequest.setMarkdownContent("老年人能力评估量表\n1. 日常生活活动\n2. 认知功能\n3. 社交能力");

        // testMarkdownContent变量已不再使用，移除以消除警告
        
        // 设置模拟的LM Studio服务响应
        setupMockLMStudioService();
    }
    
    private void setupMockLMStudioService() {
        // 使用lenient()避免不必要的stubbing警告
        lenient().when(lmStudioService.getCurrentServerUrl()).thenReturn(TEST_SERVER_URL);
        lenient().when(lmStudioService.getCurrentModel()).thenReturn(
            new com.assessment.config.LMStudioConfig.ModelInfo(
                TEST_MODEL_ID, 
                TEST_MODEL_DISPLAY_NAME, 
                "专业的推理和中文对话模型", 
                100, 
                java.util.Arrays.asList("reasoning", "chinese")
            )
        );
        
        Map<String, Object> modelInfo = new HashMap<>();
        modelInfo.put("id", TEST_MODEL_ID);
        modelInfo.put("displayName", TEST_MODEL_DISPLAY_NAME);
        modelInfo.put("serverUrl", TEST_SERVER_URL);
        modelInfo.put("serverHealth", true);
        lenient().when(lmStudioService.getCurrentModelInfo()).thenReturn(modelInfo);
    }

    @Test
    @DisplayName("测试连接到指定服务器和模型 - 192.168.1.231:1234")
    void testConnectToSpecificServerAndModel() throws JsonProcessingException {
        // Arrange - 模拟与deepseek-r1-0528-qwen3-8b模型的交互
        String innerJsonContent = """
            {
              "tableName": "elderly_assessment",
              "tableComment": "老年人能力评估量表",
              "confidence": 90,
              "fields": [
                { 
                  "name": "daily_living_score", 
                  "type": "INT", 
                  "nullable": true,
                  "comment": "日常生活活动评分",
                  "importance": 95
                },
                { 
                  "name": "cognitive_score", 
                  "type": "INT", 
                  "nullable": true,
                  "comment": "认知功能评分",
                  "importance": 90
                },
                { 
                  "name": "social_score", 
                  "type": "INT", 
                  "nullable": true,
                  "comment": "社交能力评分",
                  "importance": 80
                }
              ]
            }
            """;

        String aiResponseContent = String.format("```json\n%s\n```", innerJsonContent);
        String fullApiResponse = String.format("""
            {
              "choices": [
                {
                  "message": {
                    "role": "assistant",
                    "content": %s
                  }
                }
              ]
            }
            """, new ObjectMapper().writeValueAsString(aiResponseContent));

        ResponseEntity<String> mockResponseEntity = new ResponseEntity<>(fullApiResponse, HttpStatus.OK);

        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(restTemplate.postForEntity(anyString(), any(), eq(String.class))).thenReturn(mockResponseEntity);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(testRequest);

        // Assert - 验证使用了指定的服务器和模型
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTableName()).isEqualTo("elderly_assessment");
        assertThat(result.getTableComment()).isEqualTo("老年人能力评估量表");
        assertThat(result.getConfidence()).isEqualTo(90);
        assertThat(result.getFields()).hasSize(6); // 3个业务字段 + 3个基础字段
        
        // 验证业务字段
        assertThat(result.getFields()).anyMatch(field -> 
            "daily_living_score".equals(field.getName()) && "日常生活活动评分".equals(field.getComment()));
        assertThat(result.getFields()).anyMatch(field -> 
            "cognitive_score".equals(field.getName()) && "认知功能评分".equals(field.getComment()));
        assertThat(result.getFields()).anyMatch(field -> 
            "social_score".equals(field.getName()) && "社交能力评分".equals(field.getComment()));
    }

    @Test
    @DisplayName("测试网络连接失败时的降级处理")
    void testAnalyzeDocument_NetworkFailureFallback() {
        // Arrange - 模拟无法连接到192.168.1.231:1234
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(restTemplate.postForEntity(anyString(), any(), eq(String.class)))
            .thenThrow(new RestClientException("Connection to 192.168.1.231:1234 failed"));

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(testRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue(); // 降级处理也算成功
        assertThat(result.getMessage()).contains("AI服务调用失败，切换到基础降级分析");
        assertThat(result.getTableName()).isEqualTo("assessment_elderly_assessment_scale");
        assertThat(result.getFields()).isNotEmpty(); // 应该有基础字段
    }

    @Test
    @DisplayName("测试文档结构分析 - 内容为空")
    void testAnalyzeDocumentEmptyContent() {
        // Arrange
        testRequest.setMarkdownContent("");

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(testRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).contains("内容为空，执行基础降级分析");
    }

    @Test
    @DisplayName("测试AI服务可用性检查")
    void testIsAIServiceAvailable() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);

        // Act
        boolean available = aiAnalysisService.isAIServiceAvailable();

        // Assert
        assertThat(available).isTrue();
    }

    @Test
    @DisplayName("测试与AI聊天连通性 - deepseek模型交互")
    void testChatWithAI_DeepSeekModelConnectivity() throws JsonProcessingException {
        // Arrange - 模拟deepseek模型的回复
        String aiChatResponse = "我是DeepSeek R1模型，可以帮您分析文档结构并生成数据库表设计。我具备强大的推理能力和中文理解能力。";
        String fullChatResponse = String.format("""
            {
              "choices": [
                {
                  "message": {
                    "role": "assistant",
                    "content": "%s"
                  }
                }
              ]
            }
            """, aiChatResponse);

        ResponseEntity<String> mockChatResponseEntity = new ResponseEntity<>(fullChatResponse, HttpStatus.OK);
        
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(restTemplate.postForEntity(anyString(), any(), eq(String.class))).thenReturn(mockChatResponseEntity);

        // Act
        String result = aiAnalysisService.chatWithAI("你好，请介绍一下你的能力", null);

        // Assert
        assertThat(result).isEqualTo(aiChatResponse);
        assertThat(result).contains("DeepSeek R1");
        assertThat(result).contains("推理能力");
        assertThat(result).contains("中文理解");
    }
    
    @Test
    @DisplayName("测试服务不可用时的聊天降级")
    void testChatWithAI_ServiceUnavailable() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(false);

        // Act
        String result = aiAnalysisService.chatWithAI("测试消息", null);

        // Assert
        assertThat(result).isEqualTo("AI服务当前不可用");
    }

    @Test
    @DisplayName("测试获取指定模型信息 - deepseek-r1-0528-qwen3-8b")
    void testGetSpecificModelInfo() {
        // Act
        Map<String, Object> result = aiAnalysisService.getCurrentModelInfo();

        // Assert - 验证返回指定模型的信息
        assertThat(result).isNotNull();
        assertThat(result.get("id")).isEqualTo(TEST_MODEL_ID);
        assertThat(result.get("displayName")).isEqualTo(TEST_MODEL_DISPLAY_NAME);
        assertThat(result.get("serverUrl")).isEqualTo(TEST_SERVER_URL);
        assertThat(result.get("serverHealth")).isEqualTo(true);
    }

    @Test
    @DisplayName("测试AI服务不可用时的降级")
    void testAIServiceUnavailableFallback() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(false);
        
        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(testRequest);
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).contains("AI服务不可用，切换到基础降级分析");
        assertThat(result.getTableName()).contains("assessment_elderly_assessment_scale");
        assertThat(result.getFields()).isNotEmpty();
    }

    @Test
    @DisplayName("测试服务可用性检查 - 验证192.168.1.231:1234连通性")
    void testServiceConnectivityCheck() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        
        // Act
        boolean isAvailable = aiAnalysisService.isAIServiceAvailable();
        
        // Assert
        assertThat(isAvailable).isTrue();
    }

    @Test
    @DisplayName("测试空内容的AI分析")
    void testAnalyzeDocumentWithEmptyContent() {
        // Arrange
        DocumentAnalysisRequest emptyRequest = new DocumentAnalysisRequest();
        emptyRequest.setFileName("empty.pdf");
        emptyRequest.setMarkdownContent("");
        
        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(emptyRequest);
        
        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue(); // Should return fallback result
        assertThat(result.getMessage()).contains("内容为空，执行基础降级分析");
    }

    @Test
    @DisplayName("测试简单流式分析文档功能")
    void testStreamAnalysisSimple() throws Exception {
        // Arrange - 准备流式分析的mock
        testRequest.setUseStream(true);
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        
        // Mock一个简单的AI响应用于流式处理
        String mockStreamResponse = """
            ```json
            {
              "tableName": "stream_test",
              "fields": []
            }
            ```
            """;
        String fullResponse = String.format("""
            {
              "choices": [
                {
                  "message": {
                    "content": %s
                  }
                }
              ]
            }
            """, new ObjectMapper().writeValueAsString(mockStreamResponse));
        
        ResponseEntity<String> mockResponseEntity = new ResponseEntity<>(fullResponse, HttpStatus.OK);
        when(restTemplate.postForEntity(anyString(), any(), eq(String.class))).thenReturn(mockResponseEntity);
        
        org.springframework.web.servlet.mvc.method.annotation.SseEmitter mockEmitter = 
            new org.springframework.web.servlet.mvc.method.annotation.SseEmitter();

        // Act & Assert - 验证流式分析不会抛出异常
        aiAnalysisService.analyzeDocumentWithStream(testRequest, mockEmitter);
        // 给异步操作一点时间执行
        Thread.sleep(200);
    }

    @Test
    @DisplayName("测试边界条件处理")
    void testEdgeCases() {
        // 测试null请求
        DocumentAnalysisResult result1 = aiAnalysisService.analyzeDocument(null);
        assertThat(result1).isNotNull();
        assertThat(result1.isSuccess()).isTrue();
        assertThat(result1.getMessage()).contains("请求无效");
        assertThat(result1.getTableName()).isEqualTo("invalid_request");
        
        // 测试空内容请求
        DocumentAnalysisRequest emptyRequest = new DocumentAnalysisRequest();
        emptyRequest.setFileName("empty_scale.pdf");
        emptyRequest.setMarkdownContent("");
        
        DocumentAnalysisResult result2 = aiAnalysisService.analyzeDocument(emptyRequest);
        assertThat(result2).isNotNull();
        assertThat(result2.isSuccess()).isTrue();
        assertThat(result2.getMessage()).contains("内容为空，执行基础降级分析");
        assertThat(result2.getTableName()).isEqualTo("assessment_empty_scale");
    }
}