package com.assessment.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.*;

/**
 * AssessmentService 测试类
 * 测试评估服务的基本功能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("评估服务测试")
class AssessmentServiceTest {

    @InjectMocks
    private AssessmentService assessmentService;

    @BeforeEach
    void setUp() {
        // 初始化操作
    }

    @Test
    @DisplayName("当创建评估服务时，应该正确初始化")
    void whenCreateAssessmentService_thenShouldInitializeCorrectly() {
        // Assert
        assertThat(assessmentService).isNotNull();
    }

    @Test
    @DisplayName("评估服务应该能够被正确实例化")
    void assessmentServiceShouldBeInstantiatedCorrectly() {
        // Arrange & Act
        AssessmentService service = new AssessmentService();
        
        // Assert
        assertThat(service).isNotNull();
        assertThat(service).isInstanceOf(AssessmentService.class);
    }

    @Test
    @DisplayName("测试评估服务的基本方法可用性")
    void testAssessmentServiceBasicMethodAvailability() {
        // 这个测试确保服务类的基本结构正确
        // 由于我们没有具体的业务方法实现，这里测试服务的存在性
        
        // Assert
        assertThat(assessmentService).isNotNull();
        assertThat(assessmentService.getClass().getSimpleName()).isEqualTo("AssessmentService");
    }

    @Test
    @DisplayName("测试服务类的包名和类型")
    void testServiceClassPackageAndType() {
        // Act
        String packageName = assessmentService.getClass().getPackage().getName();
        String className = assessmentService.getClass().getSimpleName();
        
        // Assert
        assertThat(packageName).isEqualTo("com.assessment.service");
        assertThat(className).isEqualTo("AssessmentService");
        assertThat(assessmentService.getClass().isAnnotationPresent(org.springframework.stereotype.Service.class)).isTrue();
    }

    @Test
    @DisplayName("测试服务的字符串表示")
    void testServiceStringRepresentation() {
        // Act
        String serviceString = assessmentService.toString();
        
        // Assert
        assertThat(serviceString).isNotNull();
        assertThat(serviceString).contains("AssessmentService");
    }

    @Test
    @DisplayName("测试服务的哈希码和相等性")
    void testServiceHashCodeAndEquality() {
        // Arrange
        AssessmentService anotherService = new AssessmentService();
        
        // Act & Assert
        assertThat(assessmentService.hashCode()).isNotNull();
        assertThat(assessmentService).isNotEqualTo(anotherService); // 不同的实例应该不相等
        assertThat(assessmentService).isEqualTo(assessmentService); // 自己应该等于自己
    }
}