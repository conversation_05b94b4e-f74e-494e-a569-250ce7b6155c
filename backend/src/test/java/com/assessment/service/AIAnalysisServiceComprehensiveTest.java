package com.assessment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.never;

import com.assessment.dto.DocumentAnalysisRequest;
import com.assessment.dto.DocumentAnalysisResult;
import com.assessment.config.LMStudioConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * AIAnalysisService 综合测试类
 * 通过模拟提升测试覆盖率
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AI文档分析服务综合测试")
class AIAnalysisServiceComprehensiveTest {

    @Mock
    private LMStudioService lmStudioService;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private AIAnalysisService aiAnalysisService;

    private DocumentAnalysisRequest mockRequest;
    private final String mockMarkdownContent = "# 测试文档\n\n这是一个测试文档内容。";
    private final String mockFileName = "test_document.md";

    @BeforeEach
    void setUp() {
        mockRequest = new DocumentAnalysisRequest();
        mockRequest.setFileName(mockFileName);
        mockRequest.setMarkdownContent(mockMarkdownContent);
        mockRequest.setUseStream(false);
    }

    @Test
    @DisplayName("分析文档 - AI服务可用的成功场景")
    void testAnalyzeDocument_Success() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentModel()).thenReturn(
            new LMStudioConfig.ModelInfo("test-model", "Test Model", "测试模型", 100, null)
        );
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://192.168.1.231:1234");

        String mockAIResponse = """
            <think>
            分析文档结构...
            </think>
            
            ### 数据库表结构设计
            
            ```sql
            CREATE TABLE test_table (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255)
            );
            ```
            
            ### 系统集成JSON格式
            
            ```json
            {
                "tableName": "test_table",
                "tableComment": "测试表",
                "confidence": 85,
                "fields": [
                    {
                        "name": "id",
                        "type": "SERIAL",
                        "nullable": false,
                        "isPrimaryKey": true,
                        "comment": "主键ID",
                        "importance": 100
                    },
                    {
                        "name": "name", 
                        "type": "VARCHAR",
                        "length": "255",
                        "nullable": true,
                        "comment": "名称",
                        "importance": 80
                    }
                ]
            }
            ```
            
            ### 设计说明
            
            这是一个简单的测试表设计。
            """;

        ResponseEntity<String> mockResponse = new ResponseEntity<>(
            "{\"choices\":[{\"message\":{\"content\":\"" + mockAIResponse.replace("\"", "\\\"").replace("\n", "\\n") + "\"}}]}", 
            HttpStatus.OK
        );
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(mockRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTableName()).isEqualTo("test_table");
        assertThat(result.getTableComment()).isEqualTo("测试表");
        assertThat(result.getConfidence()).isEqualTo(85);
        assertThat(result.getFields()).hasSize(4); // 包括自动添加的id, created_at和updated_at
        assertThat(result.getGeneratedSql()).contains("CREATE TABLE test_table");
        assertThat(result.getAiThinkingProcess()).contains("分析文档结构");
        assertThat(result.getDesignExplanation()).contains("简单的测试表设计");

        verify(lmStudioService).isServiceAvailable();
        verify(restTemplate).postForEntity(anyString(), any(HttpEntity.class), eq(String.class));
    }

    @Test
    @DisplayName("分析文档 - AI服务不可用时降级处理")
    void testAnalyzeDocument_AIServiceUnavailable() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(false);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(mockRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).contains("AI服务不可用");
        assertThat(result.getTableName()).isEqualTo("assessment_test_document");
        assertThat(result.getFields()).isNotEmpty();
        
        verify(lmStudioService).isServiceAvailable();
        verify(restTemplate, never()).postForEntity(anyString(), any(), eq(String.class));
    }

    @Test
    @DisplayName("分析文档 - 空内容降级处理")
    void testAnalyzeDocument_EmptyContent() {
        // Arrange
        mockRequest.setMarkdownContent("");

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(mockRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).contains("内容为空");
        assertThat(result.getTableName()).isEqualTo("assessment_test_document");
        
        verify(lmStudioService, never()).isServiceAvailable();
    }

    @Test
    @DisplayName("分析文档 - null请求处理")
    void testAnalyzeDocument_NullRequest() {
        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(null);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).contains("请求无效");
        assertThat(result.getTableName()).isEqualTo("invalid_request");
    }

    @Test
    @DisplayName("分析文档 - AI响应解析失败降级")
    void testAnalyzeDocument_ParseError() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentModel()).thenReturn(
            new LMStudioConfig.ModelInfo("test-model", "Test Model", "测试模型", 100, null)
        );
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://192.168.1.231:1234");

        ResponseEntity<String> mockResponse = new ResponseEntity<>(
            "{\"choices\":[{\"message\":{\"content\":\"invalid response\"}}]}", 
            HttpStatus.OK
        );
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(mockRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue(); // 降级分析也算成功
        assertThat(result.getMessage()).contains("解析AI响应失败");
    }

    @Test
    @DisplayName("与AI聊天 - 成功场景")
    void testChatWithAI_Success() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentModel()).thenReturn(
            new LMStudioConfig.ModelInfo("test-model", "Test Model", "测试模型", 100, null)
        );
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://192.168.1.231:1234");

        ResponseEntity<String> mockResponse = new ResponseEntity<>(
            "{\"choices\":[{\"message\":{\"content\":\"AI回复内容\"}}]}", 
            HttpStatus.OK
        );
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);

        // Act
        String result = aiAnalysisService.chatWithAI("测试消息", "测试上下文");

        // Assert
        assertThat(result).isEqualTo("AI回复内容");
        verify(lmStudioService).isServiceAvailable();
        verify(restTemplate).postForEntity(anyString(), any(HttpEntity.class), eq(String.class));
    }

    @Test
    @DisplayName("与AI聊天 - 服务不可用")
    void testChatWithAI_ServiceUnavailable() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(false);

        // Act
        String result = aiAnalysisService.chatWithAI("测试消息", null);

        // Assert
        assertThat(result).isEqualTo("AI服务当前不可用");
        verify(lmStudioService).isServiceAvailable();
        verify(restTemplate, never()).postForEntity(anyString(), any(), eq(String.class));
    }

    @Test
    @DisplayName("检查AI服务可用性")
    void testIsAIServiceAvailable() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);

        // Act
        boolean result = aiAnalysisService.isAIServiceAvailable();

        // Assert
        assertThat(result).isTrue();
        verify(lmStudioService).isServiceAvailable();
    }

    @Test
    @DisplayName("获取当前模型信息")
    void testGetCurrentModelInfo() {
        // Arrange
        Map<String, Object> mockInfo = Map.of(
            "id", "test-model",
            "displayName", "Test Model",
            "serverUrl", "http://192.168.1.231:1234"
        );
        when(lmStudioService.getCurrentModelInfo()).thenReturn(mockInfo);

        // Act
        Map<String, Object> result = aiAnalysisService.getCurrentModelInfo();

        // Assert
        assertThat(result).isEqualTo(mockInfo);
        verify(lmStudioService).getCurrentModelInfo();
    }

    @Test
    @DisplayName("流式分析文档 - 异步处理")
    void testAnalyzeDocumentWithStream() throws Exception {
        // Arrange
        SseEmitter emitter = new SseEmitter();
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentModel()).thenReturn(
            new LMStudioConfig.ModelInfo("test-model", "Test Model", "测试模型", 100, null)
        );
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://192.168.1.231:1234");

        // 模拟简单的AI响应
        ResponseEntity<String> mockResponse = new ResponseEntity<>(
            "{\"choices\":[{\"message\":{\"content\":\"```json\\n{\\\"tableName\\\":\\\"test\\\"}\\n```\"}}]}", 
            HttpStatus.OK
        );
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);

        // Act
        aiAnalysisService.analyzeDocumentWithStream(mockRequest, emitter);

        // Assert - 由于是异步方法，主要验证方法可以执行
        Thread.sleep(100); // 给异步方法一点执行时间
        verify(lmStudioService).getCurrentServerUrl();
    }

    @Test
    @DisplayName("使用自定义提示词分析")
    void testAnalyzeDocument_WithCustomPrompt() {
        // Arrange
        mockRequest.setCustomPrompt("请分析这个文档并生成表结构");
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentModel()).thenReturn(
            new LMStudioConfig.ModelInfo("test-model", "Test Model", "测试模型", 100, null)
        );
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://192.168.1.231:1234");

        ResponseEntity<String> mockResponse = new ResponseEntity<>(
            "{\"choices\":[{\"message\":{\"content\":\"```json\\n{\\\"tableName\\\":\\\"custom_table\\\",\\\"fields\\\":[]}\\n```\"}}]}", 
            HttpStatus.OK
        );
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(mockRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTableName()).isEqualTo("custom_table");
    }

    @Test
    @DisplayName("AI调用异常处理")
    void testAnalyzeDocument_AICallException() {
        // Arrange
        when(lmStudioService.isServiceAvailable()).thenReturn(true);
        when(lmStudioService.getCurrentModel()).thenReturn(
            new LMStudioConfig.ModelInfo("test-model", "Test Model", "测试模型", 100, null)
        );
        when(lmStudioService.getCurrentServerUrl()).thenReturn("http://192.168.1.231:1234");
        when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
            .thenThrow(new RuntimeException("网络错误"));

        // Act
        DocumentAnalysisResult result = aiAnalysisService.analyzeDocument(mockRequest);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).contains("AI服务调用失败");
        assertThat(result.getTableName()).contains("assessment_test_document");
    }
}