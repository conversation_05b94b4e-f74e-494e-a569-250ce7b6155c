package com.assessment.service;

import com.assessment.pdf.AssessmentMetadata;
import com.assessment.pdf.AssessmentQuestion;
import com.assessment.pdf.AssessmentSection;
import com.assessment.pdf.AssessmentStructure;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * ScaleAnalysisService 单元测试
 * 测试Markdown量表分析的核心功能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("量表分析服务测试")
class ScaleAnalysisServiceTest {

    @InjectMocks
    private ScaleAnalysisService scaleAnalysisService;

    private String simpleMarkdown;
    private String complexMarkdown;

    @BeforeEach
    void setUp() {
        // 简单测试Markdown
        simpleMarkdown = """
            # 老年人能力评估量表
            
            版本：2.0
            
            ## 简介
            这是一个用于评估老年人能力的标准化量表
            
            ## 基本信息
            
            1. 您的年龄是？
            - 60-69岁
            - 70-79岁
            - 80岁以上
            
            2. 您的性别是？
            - 男性
            - 女性
            """;

        // 复杂测试Markdown
        complexMarkdown = """
            # 老年人日常生活能力评估量表
            
            ## 第一部分：基本信息
            
            | 问题 | 选项 | 分值 |
            |------|------|------|
            | 年龄 | 60-70岁 | 1 |
            | 年龄 | 70-80岁 | 2 |
            
            ## 第二部分：日常活动能力
            
            A.1 您能独立进行洗澡吗？
            - 完全独立
            - 需要部分帮助
            - 完全需要帮助
            
            A.2 您能独立穿衣吗？
            - 完全独立
            - 需要部分帮助
            - 完全需要帮助
            
            ## 第三部分：认知能力
            
            B.1 您的记忆力如何？
            - 良好
            - 一般
            - 较差
            """;

        // Note: Additional test data for elderly-specific assessments can be added here in the future
    }

    @Nested
    @DisplayName("Markdown分析测试")
    class MarkdownAnalysisTests {

        @Test
        @DisplayName("给定简单Markdown，当分析时，应该返回完整结构")
        void givenSimpleMarkdown_whenAnalyze_thenReturnCompleteStructure() {
            // Act
            AssessmentStructure result = scaleAnalysisService.analyzeMarkdownToStructure(
                simpleMarkdown, "elderly_assessment.md");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getMetadata()).isNotNull();
            assertThat(result.getSections()).isNotEmpty();
            assertThat(result.getScoringRules()).isNotNull();

            // 验证元数据
            AssessmentMetadata metadata = result.getMetadata();
            assertThat(metadata.getTitle()).isEqualTo("老年人能力评估量表");
            assertThat(metadata.getVersion()).isEqualTo("2.0");
            assertThat(metadata.getType()).isEqualTo("ELDERLY_ABILITY");
            assertThat(metadata.getDescription()).contains("评估老年人能力");

            // 验证章节
            List<AssessmentSection> sections = result.getSections();
            assertThat(sections).hasSize(2);
            assertThat(sections.get(0).getTitle()).isEqualTo("简介");
            assertThat(sections.get(1).getTitle()).isEqualTo("基本信息");

            // 验证问题
            List<AssessmentQuestion> questions = sections.get(1).getQuestions();
            assertThat(questions).hasSize(2);
            assertThat(questions.get(0).getText()).contains("年龄");
            assertThat(questions.get(0).getOptions()).hasSize(3);
            assertThat(questions.get(1).getText()).contains("性别");
            assertThat(questions.get(1).getOptions()).hasSize(2);
        }

        @Test
        @DisplayName("给定复杂Markdown，当分析时，应该正确解析所有章节和问题")
        void givenComplexMarkdown_whenAnalyze_thenParseAllSectionsAndQuestions() {
            // Act
            AssessmentStructure result = scaleAnalysisService.analyzeMarkdownToStructure(
                complexMarkdown, "complex_assessment.md");

            // Assert
            assertThat(result.getSections()).isNotEmpty(); // 至少有一个章节
            
            // 基本结构验证
            if (result.getSections().size() > 0) {
                AssessmentSection firstSection = result.getSections().get(0);
                assertThat(firstSection.getTitle()).isNotNull();
                assertThat(firstSection.getId()).isNotNull();
            }
        }
    }

    @Nested
    @DisplayName("元数据提取测试")
    class MetadataExtractionTests {

        @Test
        @DisplayName("给定包含多种量表类型关键词的Markdown，当分析时，应该识别正确类型")
        void givenMarkdownWithVariousTypes_whenAnalyze_thenIdentifyCorrectTypes() {
            String emotionalMarkdown = "# 情绪评估量表\n\n这是情绪相关的评估";
            String interraiMarkdown = "# InterRAI评估工具\n\n基于InterRAI标准";
            String customMarkdown = "# 自定义评估\n\n这是一个自定义的评估工具";

            AssessmentStructure emotional = scaleAnalysisService.analyzeMarkdownToStructure(
                emotionalMarkdown, "emotional.md");
            assertThat(emotional.getMetadata().getType()).isEqualTo("EMOTIONAL");

            AssessmentStructure interrai = scaleAnalysisService.analyzeMarkdownToStructure(
                interraiMarkdown, "interrai.md");
            assertThat(interrai.getMetadata().getType()).isEqualTo("INTERRAI");

            AssessmentStructure custom = scaleAnalysisService.analyzeMarkdownToStructure(
                customMarkdown, "custom.md");
            assertThat(custom.getMetadata().getType()).isEqualTo("CUSTOM");
        }

        @Test
        @DisplayName("给定无标题的Markdown，当分析时，应该从文件名提取标题")
        void givenMarkdownWithoutTitle_whenAnalyze_thenExtractTitleFromFileName() {
            String noTitleMarkdown = "这是一个没有标题的评估内容";
            
            AssessmentStructure result = scaleAnalysisService.analyzeMarkdownToStructure(
                noTitleMarkdown, "my_assessment_scale.pdf");

            assertThat(result.getMetadata().getTitle()).isEqualTo("my assessment scale");
        }

        @Test
        @DisplayName("给定包含版本信息的Markdown，当分析时，应该正确提取版本")
        void givenMarkdownWithVersionInfo_whenAnalyze_thenExtractVersionCorrectly() {
            String versionMarkdown1 = "# 测试量表\n版本：3.2.1";
            String versionMarkdown2 = "# 测试量表\nV2.0";
            String versionMarkdown3 = "# 测试量表\nversion 1.5";

            AssessmentStructure result1 = scaleAnalysisService.analyzeMarkdownToStructure(
                versionMarkdown1, "test1.md");
            assertThat(result1.getMetadata().getVersion()).isEqualTo("3.2.1");

            AssessmentStructure result2 = scaleAnalysisService.analyzeMarkdownToStructure(
                versionMarkdown2, "test2.md");
            assertThat(result2.getMetadata().getVersion()).isEqualTo("2.0");

            AssessmentStructure result3 = scaleAnalysisService.analyzeMarkdownToStructure(
                versionMarkdown3, "test3.md");
            assertThat(result3.getMetadata().getVersion()).isEqualTo("1.5");
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("给定null Markdown，当分析时，应该抛出异常")
        void givenNullMarkdown_whenAnalyze_thenThrowException() {
            assertThatThrownBy(() -> 
                scaleAnalysisService.analyzeMarkdownToStructure(null, "null.md"))
                .isInstanceOf(Exception.class); // 可能是NullPointerException或MarkdownAnalysisException
        }

        @Test
        @DisplayName("给定格式错误的Markdown，当分析时，应该优雅处理")
        void givenMalformedMarkdown_whenAnalyze_thenHandleGracefully() {
            String malformedMarkdown = "###无效格式\n||||\n[]()";
            
            // 应该不抛出异常，而是返回基本结构
            assertThatCode(() -> 
                scaleAnalysisService.analyzeMarkdownToStructure(malformedMarkdown, "malformed.md"))
                .doesNotThrowAnyException();
        }
    }

    @Nested
    @DisplayName("边界值测试")
    class BoundaryValueTests {

        @Test
        @DisplayName("给定包含特殊字符的Markdown，当分析时，应该正确处理")
        void givenMarkdownWithSpecialCharacters_whenAnalyze_thenHandleCorrectly() {
            String specialCharMarkdown = """
                # 特殊字符测试
                
                ## 章节测试
                
                1. 这是一个包含特殊字符的问题？
                - 选项A
                - 选项B
                
                2. 包含数学符号的问题
                - 选项1
                - 选项2
                """;

            AssessmentStructure result = scaleAnalysisService.analyzeMarkdownToStructure(
                specialCharMarkdown, "special_chars.md");

            assertThat(result.getMetadata().getTitle()).contains("特殊字符测试");
            assertThat(result.getSections().get(0).getTitle()).contains("章节测试");
            assertThat(result.getSections().get(0).getQuestions().get(0).getText()).contains("特殊字符");
        }

        @Test
        @DisplayName("给定无问题的Markdown，当分析时，应该返回空问题列表")
        void givenMarkdownWithoutQuestions_whenAnalyze_thenReturnEmptyQuestions() {
            String noQuestionsMarkdown = """
                # 仅包含文本的文档
                
                ## 介绍
                这里只有描述性文本，没有任何问题。
                
                ## 说明
                更多的说明文字。
                """;

            AssessmentStructure result = scaleAnalysisService.analyzeMarkdownToStructure(
                noQuestionsMarkdown, "no_questions.md");

            assertThat(result.getSections()).hasSize(2);
            result.getSections().forEach(section -> 
                assertThat(section.getQuestions()).isEmpty());
        }
    }
}
