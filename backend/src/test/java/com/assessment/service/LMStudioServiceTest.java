package com.assessment.service;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assumptions.assumeTrue;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestTemplate;

import com.assessment.config.LMStudioConfig;

import lombok.extern.slf4j.Slf4j;

@DisplayName("LM Studio Service Connectivity Test")
@Slf4j
class LMStudioServiceTest {

  private static LMStudioService lmStudioService;
  private static final String HOST = "*************";
  private static final int PORT = 1234;
  private static final String SERVER_URL = "http://" + HOST + ":" + PORT;

  @BeforeAll
  static void setUp() {
    // Skip tests if the target server is not reachable
    try (Socket socket = new Socket()) {
      socket.connect(new InetSocketAddress(HOST, PORT), 1000); // 1-second timeout
    } catch (IOException e) {
      log.warn(
          "Skipping LMStudioService tests: Cannot connect to {}:{}. Error: {}",
          HOST,
          PORT,
          e.getMessage());
      assumeTrue(false, "LM Studio server is not reachable at " + SERVER_URL);
    }

    LMStudioConfig config = new LMStudioConfig();
    // Configure if necessary, but the service hardcodes the URL, so we might not need much config.
    LMStudioConfig.ServerConfig serverConfig = new LMStudioConfig.ServerConfig();
    serverConfig.setPrimaryUrl(SERVER_URL);
    config.setServer(serverConfig);

    LMStudioConfig.AutoSwitchConfig autoSwitchConfig = new LMStudioConfig.AutoSwitchConfig();
    autoSwitchConfig.setServerFallbackEnabled(
        false); // Disable fallback for this simple test
    config.setAutoSwitch(autoSwitchConfig);

    RestTemplate restTemplate = new RestTemplate();
    lmStudioService = new LMStudioService(config, restTemplate);
    lmStudioService.initialize();
  }

  @Test
  @DisplayName("测试LM Studio服务是否可用")
  void testServiceIsAvailable() {
    // The initialize() method already selects the best server and model.
    // This test just verifies that the service reports as available.
    assertTrue(
        lmStudioService.isServiceAvailable(),
        "LMStudioService should be available after successful initialization.");
  }
}