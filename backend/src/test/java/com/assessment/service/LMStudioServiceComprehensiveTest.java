package com.assessment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.lenient;

import com.assessment.config.LMStudioConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * LMStudioService 综合测试类
 * 专注于快速提升覆盖率的模拟测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LM Studio服务管理综合测试")
class LMStudioServiceComprehensiveTest {

    @Mock
    private LMStudioConfig lmStudioConfig;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private LMStudioConfig.ServerConfig serverConfig;

    @Mock
    private LMStudioConfig.AutoSwitchConfig autoSwitchConfig;

    private LMStudioService lmStudioService;
    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        // 设置基本的mock配置
        lenient().when(lmStudioConfig.getServer()).thenReturn(serverConfig);
        lenient().when(lmStudioConfig.getAutoSwitch()).thenReturn(autoSwitchConfig);
        lenient().when(serverConfig.getPrimaryUrl()).thenReturn("http://192.168.1.231:1234");
        lenient().when(serverConfig.getBackupUrls()).thenReturn(Arrays.asList("http://192.168.1.232:1234"));
        lenient().when(serverConfig.getHealthCheckInterval()).thenReturn(60);
        lenient().when(autoSwitchConfig.getServerFallbackEnabled()).thenReturn(true);
        
        // 设置模型相关的mock
        lenient().when(lmStudioConfig.isModelExcluded(anyString())).thenReturn(false);
        lenient().when(lmStudioConfig.isModelExcluded("excluded-model")).thenReturn(true);
        lenient().when(lmStudioConfig.getModelPriority(anyString())).thenReturn(100);
        lenient().when(lmStudioConfig.inferModelCapabilities(anyString()))
            .thenReturn(Arrays.asList("reasoning", "chinese"));
        lenient().when(lmStudioConfig.generateDisplayName("qwen2.5-14b"))
            .thenReturn("Qwen2.5 14b");
        lenient().when(lmStudioConfig.generateDisplayName("deepseek-r1"))
            .thenReturn("DeepSeek R1");
        lenient().when(lmStudioConfig.generateDisplayName("excluded-model"))
            .thenReturn("Excluded Model");

        lmStudioService = new LMStudioService(lmStudioConfig, restTemplate);
    }

    @Test
    @DisplayName("初始化服务 - 成功连接到主服务器")
    void testInitialize_Success() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();

        // Act
        lmStudioService.initialize();

        // Assert
        assertThat(lmStudioService.getCurrentServerUrl()).isEqualTo("http://192.168.1.231:1234");
        assertThat(lmStudioService.isServiceAvailable()).isTrue();
        // 初始化时会调用一次健康检查和一次获取模型列表
        verify(restTemplate).getForEntity(contains("models"), eq(String.class));
    }

    @Test
    @DisplayName("获取当前服务地址")
    void testGetCurrentServerUrl() {
        // Arrange
        lmStudioService.initialize();

        // Act
        String url = lmStudioService.getCurrentServerUrl();

        // Assert
        assertThat(url).isEqualTo("http://192.168.1.231:1234");
    }

    @Test
    @DisplayName("获取当前模型信息 - 有模型")
    void testGetCurrentModel_WithModel() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();
        lmStudioService.initialize();

        // Act
        LMStudioConfig.ModelInfo model = lmStudioService.getCurrentModel();

        // Assert
        assertThat(model).isNotNull();
        assertThat(model.getId()).isEqualTo("qwen2.5-14b");
        assertThat(model.getDisplayName()).isEqualTo("Qwen2.5 14b");
    }

    @Test
    @DisplayName("获取当前模型信息 - 无模型")
    void testGetCurrentModel_NoModel() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockEmptyModels();
        lmStudioService.initialize();

        // Act
        LMStudioConfig.ModelInfo model = lmStudioService.getCurrentModel();

        // Assert
        assertThat(model).isNull();
    }

    @Test
    @DisplayName("获取当前模型详细信息")
    void testGetCurrentModelInfo() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();
        lmStudioService.initialize();

        // Act
        Map<String, Object> info = lmStudioService.getCurrentModelInfo();

        // Assert
        assertThat(info).isNotNull();
        assertThat(info).containsKeys("id", "displayName", "serverUrl", "serverHealth");
        assertThat(info.get("serverUrl")).isEqualTo("http://192.168.1.231:1234");
        assertThat(info.get("serverHealth")).isEqualTo(true);
    }

    @Test
    @DisplayName("检查服务可用性 - 服务可用且有模型")
    void testIsServiceAvailable_Available() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();
        lmStudioService.initialize();

        // Act
        boolean available = lmStudioService.isServiceAvailable();

        // Assert
        assertThat(available).isTrue();
    }

    @Test
    @DisplayName("检查服务可用性 - 服务不健康")
    void testIsServiceAvailable_Unhealthy() {
        // Arrange
        mockUnhealthyServer("http://192.168.1.231:1234");
        lmStudioService.initialize();

        // Act
        boolean available = lmStudioService.isServiceAvailable();

        // Assert
        assertThat(available).isFalse();
    }

    @Test
    @DisplayName("刷新可用模型列表 - 成功")
    void testRefreshAvailableModels_Success() {
        // Arrange
        mockAvailableModels();

        // Act
        List<LMStudioConfig.ModelInfo> models = lmStudioService.refreshAvailableModels();

        // Assert
        assertThat(models).isNotEmpty();
        assertThat(models).hasSize(2);
        assertThat(models.get(0).getId()).isEqualTo("qwen2.5-14b");
        assertThat(models.get(1).getId()).isEqualTo("deepseek-r1");
    }

    @Test
    @DisplayName("刷新可用模型列表 - 网络错误")
    void testRefreshAvailableModels_NetworkError() {
        // Arrange
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
            .thenThrow(new RestClientException("Connection refused"));

        // Act
        List<LMStudioConfig.ModelInfo> models = lmStudioService.refreshAvailableModels();

        // Assert
        assertThat(models).isEmpty();
    }

    @Test
    @DisplayName("刷新可用模型列表 - 空响应")
    void testRefreshAvailableModels_NullResponse() {
        // Arrange
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
            .thenReturn(null);

        // Act
        List<LMStudioConfig.ModelInfo> models = lmStudioService.refreshAvailableModels();

        // Assert
        assertThat(models).isEmpty();
    }

    @Test
    @DisplayName("切换到指定模型 - 成功")
    void testSwitchToModel_Success() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();
        lmStudioService.initialize();

        // Act
        boolean result = lmStudioService.switchToModel("deepseek-r1");

        // Assert
        assertThat(result).isTrue();
        assertThat(lmStudioService.getCurrentModel().getId()).isEqualTo("deepseek-r1");
    }

    @Test
    @DisplayName("切换到指定模型 - 模型不存在")
    void testSwitchToModel_NotFound() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();
        lmStudioService.initialize();

        // Act
        boolean result = lmStudioService.switchToModel("non-existent-model");

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("切换服务器 - 成功")
    void testSwitchToServer_Success() {
        // Arrange
        String newServer = "http://192.168.1.232:1234";
        mockHealthyServer(newServer);
        mockAvailableModels();

        // Act
        boolean result = lmStudioService.switchToServer(newServer);

        // Assert
        assertThat(result).isTrue();
        assertThat(lmStudioService.getCurrentServerUrl()).isEqualTo(newServer);
    }

    @Test
    @DisplayName("切换服务器 - 服务器不健康")
    void testSwitchToServer_Unhealthy() {
        // Arrange
        String newServer = "http://192.168.1.232:1234";
        mockUnhealthyServer(newServer);

        // Act
        boolean result = lmStudioService.switchToServer(newServer);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("自动切换到最佳服务器 - 主服务器可用")
    void testAutoSwitchToBestServer_PrimaryHealthy() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();

        // Act
        boolean result = lmStudioService.autoSwitchToBestServer();

        // Assert
        assertThat(result).isTrue();
        assertThat(lmStudioService.getCurrentServerUrl()).isEqualTo("http://192.168.1.231:1234");
    }

    @Test
    @DisplayName("自动切换到最佳服务器 - 切换到备用服务器")
    void testAutoSwitchToBestServer_FallbackToBackup() {
        // Arrange
        when(restTemplate.getForEntity(contains("192.168.1.231"), eq(String.class)))
            .thenThrow(new RestClientException("Connection refused"));
        when(restTemplate.getForEntity(contains("192.168.1.232"), eq(String.class)))
            .thenReturn(new ResponseEntity<>("{\"data\":[]}", HttpStatus.OK));
        mockAvailableModels();

        // Act
        boolean result = lmStudioService.autoSwitchToBestServer();

        // Assert
        assertThat(result).isTrue();
        assertThat(lmStudioService.getCurrentServerUrl()).isEqualTo("http://192.168.1.232:1234");
    }

    @Test
    @DisplayName("自动切换到最佳服务器 - 所有服务器不可用")
    void testAutoSwitchToBestServer_AllUnavailable() {
        // Arrange
        mockUnhealthyServer("http://192.168.1.231:1234");
        mockUnhealthyServer("http://192.168.1.232:1234");

        // Act
        boolean result = lmStudioService.autoSwitchToBestServer();

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("获取可用模型列表 - 使用缓存")
    void testGetAvailableModels_FromCache() {
        // Arrange
        mockHealthyServer("http://192.168.1.231:1234");
        mockAvailableModels();
        lmStudioService.initialize();
        
        // 第一次调用填充缓存
        lmStudioService.getAvailableModels();

        // Act - 第二次调用应该使用缓存
        List<LMStudioConfig.ModelInfo> models = lmStudioService.getAvailableModels();

        // Assert
        assertThat(models).isNotEmpty();
        // 验证只调用了两次（初始化一次，第一次获取一次）
        verify(restTemplate, times(2)).exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class));
    }

    @Test
    @DisplayName("模型排除功能")
    void testModelExclusion() {
        // Arrange
        when(lmStudioConfig.isModelExcluded("excluded-model")).thenReturn(true);
        
        ObjectNode response = objectMapper.createObjectNode();
        ArrayNode data = response.putArray("data");
        data.addObject().put("id", "qwen2.5-14b").put("object", "model");
        data.addObject().put("id", "excluded-model").put("object", "model");
        
        ResponseEntity<String> mockResponse = new ResponseEntity<>(response.toString(), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);

        // Act
        List<LMStudioConfig.ModelInfo> models = lmStudioService.refreshAvailableModels();

        // Assert
        assertThat(models).hasSize(1);
        assertThat(models.get(0).getId()).isEqualTo("qwen2.5-14b");
    }

    // 辅助方法
    private void mockHealthyServer(String serverUrl) {
        ResponseEntity<String> response = new ResponseEntity<>("{\"data\":[]}", HttpStatus.OK);
        when(restTemplate.getForEntity(contains(serverUrl), eq(String.class))).thenReturn(response);
    }

    private void mockUnhealthyServer(String serverUrl) {
        when(restTemplate.getForEntity(contains(serverUrl), eq(String.class)))
            .thenThrow(new RestClientException("Connection refused"));
    }

    private void mockAvailableModels() {
        ObjectNode response = objectMapper.createObjectNode();
        ArrayNode data = response.putArray("data");
        data.addObject().put("id", "qwen2.5-14b").put("object", "model");
        data.addObject().put("id", "deepseek-r1").put("object", "model");
        
        ResponseEntity<String> mockResponse = new ResponseEntity<>(response.toString(), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);
    }

    private void mockEmptyModels() {
        ObjectNode response = objectMapper.createObjectNode();
        response.putArray("data");
        
        ResponseEntity<String> mockResponse = new ResponseEntity<>(response.toString(), HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
            .thenReturn(mockResponse);
    }
}