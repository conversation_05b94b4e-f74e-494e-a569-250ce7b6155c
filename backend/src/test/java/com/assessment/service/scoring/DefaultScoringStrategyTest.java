package com.assessment.service.scoring;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/** 单元测试：验证 {@link DefaultScoringStrategy} 的核心计分与等级判定逻辑。 */
class DefaultScoringStrategyTest {

  private final ObjectMapper mapper = new ObjectMapper();
  private DefaultScoringStrategy strategy;

  @BeforeEach
  void setUp() {
    strategy = new DefaultScoringStrategy();
  }

  @Test
  @DisplayName("应正确计算总分并判定等级（含权重）")
  void calculateScore_withWeights() throws Exception {
    String rulesJson =
        "{"
            + "\"questionScoring\":{\"section1\":{\"q1\":{\"optionScores\":{\"yes\":5,\"no\":0}}}},"
            + "\"sectionWeights\":{\"section1\":1},"
            + "\"scoringLevels\":[{"
            + "\"name\":\"低风险\",\"minScore\":0,\"maxScore\":3},{"
            + "\"name\":\"中风险\",\"minScore\":4,\"maxScore\":6},{"
            + "\"name\":\"高风险\",\"minScore\":7,\"maxScore\":10}]}";

    String formJson = "{\"section1\":{\"q1\":\"yes\"}}";

    JsonNode rules = mapper.readTree(rulesJson);
    JsonNode form = mapper.readTree(formJson);

    BigDecimal total = strategy.calculateTotalScore(form, rules);
    String level = strategy.determineResultLevel(total, rules);

    assertEquals(0, BigDecimal.valueOf(5).compareTo(total));
    assertEquals("中风险", level);
  }

  @Test
  @DisplayName("当缺少权重时应按题目得分求和")
  void calculateScore_withoutWeights() throws Exception {
    String rulesJson =
        "{"
            + "\"questionScoring\":{\"section1\":{\"q1\":{\"optionScores\":{\"a\":2,\"b\":3}}}},"
            + "\"scoringLevels\":[{\"name\":\"一般\",\"minScore\":0,\"maxScore\":4},{\"name\":\"优秀\",\"minScore\":5,\"maxScore\":10}]}";

    String formJson = "{\"section1\":{\"q1\":\"b\"}}";

    JsonNode rules = mapper.readTree(rulesJson);
    JsonNode form = mapper.readTree(formJson);

    BigDecimal total = strategy.calculateTotalScore(form, rules);
    String level = strategy.determineResultLevel(total, rules);

    assertEquals(0, BigDecimal.valueOf(3).compareTo(total));
    assertEquals("一般", level);
  }
}
