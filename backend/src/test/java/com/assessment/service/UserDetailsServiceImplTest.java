package com.assessment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.repository.multitenant.PlatformUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Optional;
import java.util.UUID;

/**
 * 用户详细信息服务测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户详细信息服务测试")
class UserDetailsServiceImplTest {

    @Mock
    private PlatformUserRepository userRepository;

    @InjectMocks
    private UserDetailsServiceImpl userDetailsService;

    private PlatformUser testUser;

    @BeforeEach
    void setUp() {
        testUser = new PlatformUser();
        testUser.setId(UUID.randomUUID());
        testUser.setUsername("testuser");
        testUser.setPasswordHash("hashed-password");
        testUser.setEmail("<EMAIL>");
        testUser.setIsActive(true);
        testUser.setPlatformRole(PlatformUser.PlatformRole.USER);
    }

    @Test
    @DisplayName("测试加载用户 - 成功场景")
    void testLoadUserByUsernameSuccess() {
        // Arrange
        when(userRepository.findByUsername("testuser"))
            .thenReturn(Optional.of(testUser));

        // Act
        UserDetails userDetails = userDetailsService.loadUserByUsername("testuser");

        // Assert
        assertThat(userDetails).isNotNull();
        assertThat(userDetails.getUsername()).isEqualTo("testuser");
        assertThat(userDetails.getPassword()).isEqualTo("hashed-password");
        assertThat(userDetails.isEnabled()).isTrue();
        assertThat(userDetails.isAccountNonExpired()).isTrue();
        assertThat(userDetails.isAccountNonLocked()).isTrue();
        assertThat(userDetails.isCredentialsNonExpired()).isTrue();
        assertThat(userDetails.getAuthorities()).hasSize(1);
        assertThat(userDetails.getAuthorities().iterator().next().getAuthority()).isEqualTo("ROLE_USER");
    }

    @Test
    @DisplayName("测试加载用户 - 用户不存在")
    void testLoadUserByUsernameNotFound() {
        // Arrange
        when(userRepository.findByUsername("nonexistent"))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("nonexistent"))
            .isInstanceOf(UsernameNotFoundException.class)
            .hasMessageContaining("用户不存在: nonexistent");
    }

    @Test
    @DisplayName("测试加载用户 - 用户被禁用")
    void testLoadUserByUsernameDisabledUser() {
        // Arrange
        testUser.setIsActive(false);
        when(userRepository.findByUsername("testuser"))
            .thenReturn(Optional.of(testUser));

        // Act
        UserDetails userDetails = userDetailsService.loadUserByUsername("testuser");

        // Assert
        assertThat(userDetails).isNotNull();
        assertThat(userDetails.isEnabled()).isFalse();
    }

    @Test
    @DisplayName("测试加载用户 - 管理员角色")
    void testLoadUserByUsernameAdminRole() {
        // Arrange
        testUser.setPlatformRole(PlatformUser.PlatformRole.ADMIN);
        when(userRepository.findByUsername("testuser"))
            .thenReturn(Optional.of(testUser));

        // Act
        UserDetails userDetails = userDetailsService.loadUserByUsername("testuser");

        // Assert
        assertThat(userDetails).isNotNull();
        assertThat(userDetails.getAuthorities()).hasSize(1);
        assertThat(userDetails.getAuthorities().iterator().next().getAuthority()).isEqualTo("ROLE_ADMIN");
    }

    @Test
    @DisplayName("测试加载用户 - 空用户名")
    void testLoadUserByUsernameEmptyUsername() {
        // Arrange
        when(userRepository.findByUsername(""))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername(""))
            .isInstanceOf(UsernameNotFoundException.class)
            .hasMessageContaining("用户不存在: ");
    }

    @Test
    @DisplayName("测试加载用户 - null用户名")
    void testLoadUserByUsernameNullUsername() {
        // Arrange
        when(userRepository.findByUsername(null))
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername(null))
            .isInstanceOf(UsernameNotFoundException.class)
            .hasMessageContaining("用户不存在: null");
    }

    @Test
    @DisplayName("测试加载用户 - 数据库异常")
    void testLoadUserByUsernameDatabaseException() {
        // Arrange
        when(userRepository.findByUsername(anyString()))
            .thenThrow(new RuntimeException("数据库连接失败"));

        // Act & Assert
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("testuser"))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("数据库连接失败");
    }

    @Test
    @DisplayName("测试加载用户 - 角色为null")
    void testLoadUserByUsernameNullRole() {
        // Arrange
        testUser.setPlatformRole(null);
        when(userRepository.findByUsername("testuser"))
            .thenReturn(Optional.of(testUser));

        // Act & Assert - This should cause an exception since platformRole is required
        assertThatThrownBy(() -> userDetailsService.loadUserByUsername("testuser"))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("测试加载用户 - 默认USER角色")
    void testLoadUserByUsernameDefaultRole() {
        // Arrange - Use the default USER role from PlatformUser.PlatformRole enum
        testUser.setPlatformRole(PlatformUser.PlatformRole.USER);
        when(userRepository.findByUsername("testuser"))
            .thenReturn(Optional.of(testUser));

        // Act
        UserDetails userDetails = userDetailsService.loadUserByUsername("testuser");

        // Assert
        assertThat(userDetails).isNotNull();
        assertThat(userDetails.getAuthorities()).hasSize(1);
        assertThat(userDetails.getAuthorities().iterator().next().getAuthority()).isEqualTo("ROLE_USER");
    }
}