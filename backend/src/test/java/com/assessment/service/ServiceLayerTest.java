package com.assessment.service;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * 服务层综合测试
 * 测试服务类的基本功能和属性
 */
@DisplayName("服务层综合测试")
class ServiceLayerTest {

    @Test
    @DisplayName("测试DoclingService的基本功能")
    void testDoclingServiceBasicFunctionality() {
        // Arrange & Act
        DoclingService service = new DoclingService();
        
        // Assert
        assertThat(service).isNotNull();
        assertThat(service.getClass().getSimpleName()).isEqualTo("DoclingService");
        assertThat(service.toString()).isNotNull();
        assertThat(service.hashCode()).isNotNull();
    }

    @Test
    @DisplayName("测试AIAnalysisService的基本功能")
    void testAIAnalysisServiceBasicFunctionality() {
        // 这个服务需要依赖注入，我们只测试类型检查
        Class<?> serviceClass = AIAnalysisService.class;
        
        // Assert
        assertThat(serviceClass).isNotNull();
        assertThat(serviceClass.getSimpleName()).isEqualTo("AIAnalysisService");
        assertThat(serviceClass.getPackage().getName()).isEqualTo("com.assessment.service");
    }

    @Test
    @DisplayName("测试DatabaseService的基本功能")
    void testDatabaseServiceBasicFunctionality() {
        // 测试类的基本属性
        Class<?> serviceClass = DatabaseService.class;
        
        // Assert
        assertThat(serviceClass).isNotNull();
        assertThat(serviceClass.getSimpleName()).isEqualTo("DatabaseService");
        assertThat(serviceClass.getPackage().getName()).isEqualTo("com.assessment.service");
    }

    @Test
    @DisplayName("测试服务类的包名")
    void testServiceClassPackages() {
        // Act & Assert
        assertThat(new DoclingService().getClass().getPackage().getName())
            .isEqualTo("com.assessment.service");
        assertThat(new HealthCheckService().getClass().getPackage().getName())
            .isEqualTo("com.assessment.service");
        assertThat(new AssessmentService().getClass().getPackage().getName())
            .isEqualTo("com.assessment.service");
    }

    @Test
    @DisplayName("测试服务类的实例化")
    void testServiceClassInstantiation() {
        // 测试无参构造函数的服务可以正常实例化
        assertThatCode(() -> new DoclingService()).doesNotThrowAnyException();
        assertThatCode(() -> new HealthCheckService()).doesNotThrowAnyException();
        assertThatCode(() -> new AssessmentService()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试服务类的相等性")
    void testServiceClassEquality() {
        // 测试不同实例的相等性
        DoclingService docling1 = new DoclingService();
        DoclingService docling2 = new DoclingService();
        
        HealthCheckService health1 = new HealthCheckService();
        HealthCheckService health2 = new HealthCheckService();
        
        AssessmentService assessment1 = new AssessmentService();
        AssessmentService assessment2 = new AssessmentService();
        
        // 不同的实例应该不相等
        assertThat(docling1).isNotSameAs(docling2);
        assertThat(health1).isNotSameAs(health2);
        assertThat(assessment1).isNotSameAs(assessment2);
        
        // 但自己应该等于自己
        assertThat(docling1).isEqualTo(docling1);
        assertThat(health1).isEqualTo(health1);
        assertThat(assessment1).isEqualTo(assessment1);
    }

    @Test
    @DisplayName("测试服务类的类型检查")
    void testServiceClassTypeChecking() {
        // Act
        Object docling = new DoclingService();
        Object health = new HealthCheckService();
        Object assessment = new AssessmentService();
        
        // Assert
        assertThat(docling).isInstanceOf(DoclingService.class);
        assertThat(health).isInstanceOf(HealthCheckService.class);
        assertThat(assessment).isInstanceOf(AssessmentService.class);
        
        // 交叉检查类型
        assertThat(docling).isNotInstanceOf(HealthCheckService.class);
        assertThat(health).isNotInstanceOf(DoclingService.class);
        assertThat(assessment).isNotInstanceOf(DoclingService.class);
    }

    @Test
    @DisplayName("测试服务类的注解")
    void testServiceClassAnnotations() {
        // 检查服务类的@Service注解
        Class<?> doclingClass = DoclingService.class;
        Class<?> healthClass = HealthCheckService.class;
        Class<?> assessmentClass = AssessmentService.class;
        
        assertThat(doclingClass.isAnnotationPresent(org.springframework.stereotype.Service.class))
            .isTrue();
        assertThat(healthClass.isAnnotationPresent(org.springframework.stereotype.Service.class))
            .isTrue();
        assertThat(assessmentClass.isAnnotationPresent(org.springframework.stereotype.Service.class))
            .isTrue();
    }

    @Test
    @DisplayName("测试服务类的修饰符")
    void testServiceClassModifiers() {
        // 检查类的修饰符
        Class<?> doclingClass = DoclingService.class;
        Class<?> healthClass = HealthCheckService.class;
        Class<?> assessmentClass = AssessmentService.class;
        
        assertThat(java.lang.reflect.Modifier.isPublic(doclingClass.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPublic(healthClass.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPublic(assessmentClass.getModifiers())).isTrue();
        
        // 都不应该是abstract类
        assertThat(java.lang.reflect.Modifier.isAbstract(doclingClass.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isAbstract(healthClass.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isAbstract(assessmentClass.getModifiers())).isFalse();
    }

    @Test
    @DisplayName("测试服务类的方法数量")
    void testServiceClassMethodCount() {
        // 检查服务类的公共方法数量
        Class<?> doclingClass = DoclingService.class;
        Class<?> healthClass = HealthCheckService.class;
        Class<?> assessmentClass = AssessmentService.class;
        
        // 这些类应该都有一些公共方法
        assertThat(doclingClass.getMethods().length).isGreaterThan(0);
        assertThat(healthClass.getMethods().length).isGreaterThan(0);
        assertThat(assessmentClass.getMethods().length).isGreaterThan(0);
        
        // 检查声明的方法（不包括继承的）
        assertThat(doclingClass.getDeclaredMethods().length).isGreaterThan(0);
        assertThat(healthClass.getDeclaredMethods().length).isGreaterThan(0);
        // AssessmentService可能没有声明方法，所以不检查
    }

    @Test
    @DisplayName("测试服务类的构造函数")
    void testServiceClassConstructors() {
        // 检查服务类的构造函数
        Class<?> doclingClass = DoclingService.class;
        Class<?> healthClass = HealthCheckService.class;
        Class<?> assessmentClass = AssessmentService.class;
        
        // 这些类都应该有构造函数
        assertThat(doclingClass.getConstructors().length).isGreaterThan(0);
        assertThat(healthClass.getConstructors().length).isGreaterThan(0);
        assertThat(assessmentClass.getConstructors().length).isGreaterThan(0);
    }

    @Test
    @DisplayName("测试服务类的字符串表示")
    void testServiceClassStringRepresentation() {
        // 测试toString方法
        DoclingService docling = new DoclingService();
        HealthCheckService health = new HealthCheckService();
        AssessmentService assessment = new AssessmentService();
        
        assertThat(docling.toString()).isNotNull();
        assertThat(health.toString()).isNotNull();
        assertThat(assessment.toString()).isNotNull();
        
        assertThat(docling.toString()).contains("DoclingService");
        assertThat(health.toString()).contains("HealthCheckService");
        assertThat(assessment.toString()).contains("AssessmentService");
    }
}