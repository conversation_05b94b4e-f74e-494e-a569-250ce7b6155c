package com.assessment.service;

import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.util.TestDataFactory;
import io.jsonwebtoken.Claims;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;

/**
 * JwtTokenService 单元测试
 * 测试JWT令牌生成、验证和信息提取功能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("JWT令牌服务测试")
class JwtTokenServiceTest {

    private JwtTokenService jwtTokenService;
    
    private Tenant testTenant;
    private PlatformUser testUser;
    private PlatformUser superAdmin;
    private TenantUserMembership testMembership;

    @BeforeEach
    void setUp() {
        // 创建服务实例并设置测试配置
        jwtTokenService = new JwtTokenService();
        ReflectionTestUtils.setField(jwtTokenService, "jwtSecret", "dGVzdFNlY3JldEtleUZvckpXVFRva2VuVGVzdGluZ1B1cnBvc2VzMTIzNDU2");
        ReflectionTestUtils.setField(jwtTokenService, "accessTokenExpiration", 3600); // 1小时
        ReflectionTestUtils.setField(jwtTokenService, "refreshTokenExpiration", 86400); // 24小时

        // 创建测试数据
        testTenant = TestDataFactory.createTestTenant("HOSPITAL01", "测试医院");
        testUser = TestDataFactory.createTestUser("doctor1", "<EMAIL>");
        testMembership = TestDataFactory.createAssessorMembership(testTenant.getId(), testUser.getId());
        
        // 创建超级管理员
        superAdmin = TestDataFactory.createTestUser("admin", "<EMAIL>");
        superAdmin.setPlatformRole(PlatformUser.PlatformRole.ADMIN);
    }

    @Nested
    @DisplayName("访问令牌生成测试")
    class AccessTokenGenerationTests {

        @Test
        @DisplayName("给定有效的用户和租户信息，当生成访问令牌时，应该返回有效令牌")
        void givenValidUserAndTenant_whenGenerateAccessToken_thenReturnValidToken() {
            // Act
            String token = jwtTokenService.generateAccessToken(testUser, testTenant, testMembership);

            // Assert
            assertThat(token).isNotNull().isNotEmpty();
            assertThat(jwtTokenService.validateToken(token)).isTrue();
            
            // 验证令牌内容
            Claims claims = jwtTokenService.extractClaims(token);
            assertThat(claims.getSubject()).isEqualTo(testUser.getUsername());
            assertThat(claims.get("userId")).isEqualTo(testUser.getId().toString());
            assertThat(claims.get("username")).isEqualTo(testUser.getUsername());
            assertThat(claims.get("email")).isEqualTo(testUser.getEmail());
            assertThat(claims.get("platformRole")).isEqualTo(testUser.getPlatformRole().name());
            assertThat(claims.get("tenantId")).isEqualTo(testTenant.getId().toString());
            assertThat(claims.get("tenantCode")).isEqualTo(testTenant.getCode());
            assertThat(claims.get("tenantName")).isEqualTo(testTenant.getName());
            assertThat(claims.get("tenantRole")).isEqualTo(testMembership.getTenantRole().name());
            assertThat(claims.get("displayName")).isEqualTo(testMembership.getDisplayName());
            assertThat(claims.get("tokenType")).isEqualTo("access");
            assertThat(claims.get("isSuperAdmin")).isEqualTo(false);
        }

        @Test
        @DisplayName("给定管理员角色，当生成访问令牌时，应该包含正确的权限信息")
        void givenAdminRole_whenGenerateAccessToken_thenIncludeCorrectPermissions() {
            // Arrange
            TenantUserMembership adminMembership = TestDataFactory.createAdminMembership(
                testTenant.getId(), testUser.getId());

            // Act
            String token = jwtTokenService.generateAccessToken(testUser, testTenant, adminMembership);

            // Assert
            Claims claims = jwtTokenService.extractClaims(token);
            assertThat(claims.get("tenantRole")).isEqualTo("ADMIN");
            assertThat(claims.get("permissions")).isNotNull();
        }

        @Test
        @DisplayName("给定不同租户角色，当生成令牌时，应该正确设置角色信息")
        void givenDifferentTenantRoles_whenGenerateToken_thenSetCorrectRoleInfo() {
            // Test REVIEWER role
            TenantUserMembership reviewerMembership = TestDataFactory.createReviewerMembership(
                testTenant.getId(), testUser.getId());
            
            String reviewerToken = jwtTokenService.generateAccessToken(testUser, testTenant, reviewerMembership);
            Claims reviewerClaims = jwtTokenService.extractClaims(reviewerToken);
            
            assertThat(reviewerClaims.get("tenantRole")).isEqualTo("REVIEWER");

            // Test VIEWER role
            TenantUserMembership viewerMembership = TestDataFactory.createViewerMembership(
                testTenant.getId(), testUser.getId());
            
            String viewerToken = jwtTokenService.generateAccessToken(testUser, testTenant, viewerMembership);
            Claims viewerClaims = jwtTokenService.extractClaims(viewerToken);
            
            assertThat(viewerClaims.get("tenantRole")).isEqualTo("VIEWER");
        }
    }

    @Nested
    @DisplayName("超级管理员令牌生成测试")
    class SuperAdminTokenGenerationTests {

        @Test
        @DisplayName("给定超级管理员用户，当生成令牌时，应该返回超级管理员令牌")
        void givenSuperAdminUser_whenGenerateToken_thenReturnSuperAdminToken() {
            // Act
            String token = jwtTokenService.generateSuperAdminToken(superAdmin);

            // Assert
            assertThat(token).isNotNull().isNotEmpty();
            assertThat(jwtTokenService.validateToken(token)).isTrue();
            
            // 验证超级管理员令牌内容
            Claims claims = jwtTokenService.extractClaims(token);
            assertThat(claims.getSubject()).isEqualTo(superAdmin.getUsername());
            assertThat(claims.get("userId")).isEqualTo(superAdmin.getId().toString());
            assertThat(claims.get("username")).isEqualTo(superAdmin.getUsername());
            assertThat(claims.get("email")).isEqualTo(superAdmin.getEmail());
            assertThat(claims.get("platformRole")).isEqualTo(superAdmin.getPlatformRole().name());
            assertThat(claims.get("isSuperAdmin")).isEqualTo(true);
            assertThat(claims.get("tenantId")).isEqualTo("PLATFORM");
            assertThat(claims.get("tenantCode")).isEqualTo("PLATFORM");
            assertThat(claims.get("tenantRole")).isEqualTo("SUPER_ADMIN");
            assertThat(claims.get("tokenType")).isEqualTo("access");
            
            // 验证权限数组
            Object permissions = claims.get("permissions");
            assertThat(permissions).isNotNull();
        }

        @Test
        @DisplayName("给定超级管理员令牌，当检查是否为超级管理员时，应该返回true")
        void givenSuperAdminToken_whenCheckIsSuperAdmin_thenReturnTrue() {
            // Arrange
            String token = jwtTokenService.generateSuperAdminToken(superAdmin);

            // Act & Assert
            assertThat(jwtTokenService.isSuperAdmin(token)).isTrue();
        }

        @Test
        @DisplayName("给定普通用户令牌，当检查是否为超级管理员时，应该返回false")
        void givenRegularUserToken_whenCheckIsSuperAdmin_thenReturnFalse() {
            // Arrange
            String token = jwtTokenService.generateAccessToken(testUser, testTenant, testMembership);

            // Act & Assert
            assertThat(jwtTokenService.isSuperAdmin(token)).isFalse();
        }
    }

    @Nested
    @DisplayName("刷新令牌生成测试")
    class RefreshTokenGenerationTests {

        @Test
        @DisplayName("给定用户ID，当生成刷新令牌时，应该返回有效刷新令牌")
        void givenUserId_whenGenerateRefreshToken_thenReturnValidRefreshToken() {
            // Arrange
            String userId = testUser.getId().toString();

            // Act
            String token = jwtTokenService.generateRefreshToken(userId);

            // Assert
            assertThat(token).isNotNull().isNotEmpty();
            assertThat(jwtTokenService.validateToken(token)).isTrue();
            
            // 验证刷新令牌内容
            Claims claims = jwtTokenService.extractClaims(token);
            assertThat(claims.getSubject()).isEqualTo(userId);
            assertThat(claims.get("userId")).isEqualTo(userId);
            assertThat(claims.get("tokenType")).isEqualTo("refresh");
        }

        @Test
        @DisplayName("给定刷新令牌，当获取令牌类型时，应该返回refresh")
        void givenRefreshToken_whenGetTokenType_thenReturnRefresh() {
            // Arrange
            String userId = testUser.getId().toString();
            String token = jwtTokenService.generateRefreshToken(userId);

            // Act & Assert
            assertThat(jwtTokenService.getTokenType(token)).isEqualTo("refresh");
        }
    }

    @Nested
    @DisplayName("令牌验证测试")
    class TokenValidationTests {

        @Test
        @DisplayName("给定有效令牌，当验证时，应该返回true")
        void givenValidToken_whenValidate_thenReturnTrue() {
            // Arrange
            String token = jwtTokenService.generateAccessToken(testUser, testTenant, testMembership);

            // Act & Assert
            assertThat(jwtTokenService.validateToken(token)).isTrue();
        }

        @Test
        @DisplayName("给定无效令牌，当验证时，应该返回false")
        void givenInvalidToken_whenValidate_thenReturnFalse() {
            // Act & Assert
            assertThat(jwtTokenService.validateToken("invalid.jwt.token")).isFalse();
        }

        @Test
        @DisplayName("给定格式错误的令牌，当验证时，应该返回false")
        void givenMalformedToken_whenValidate_thenReturnFalse() {
            // Act & Assert
            assertThat(jwtTokenService.validateToken("malformed-token")).isFalse();
        }

        @Test
        @DisplayName("给定空令牌，当验证时，应该返回false")
        void givenNullToken_whenValidate_thenReturnFalse() {
            // Act & Assert
            assertThat(jwtTokenService.validateToken(null)).isFalse();
            assertThat(jwtTokenService.validateToken("")).isFalse();
        }
    }

    @Nested
    @DisplayName("令牌信息提取测试")
    class TokenExtractionTests {

        private String testToken;

        @BeforeEach
        void setUpToken() {
            testToken = jwtTokenService.generateAccessToken(testUser, testTenant, testMembership);
        }

        @Test
        @DisplayName("给定有效令牌，当提取用户名时，应该返回正确用户名")
        void givenValidToken_whenExtractUsername_thenReturnCorrectUsername() {
            // Act & Assert
            assertThat(jwtTokenService.extractUsername(testToken))
                .isEqualTo(testUser.getUsername());
        }

        @Test
        @DisplayName("给定有效令牌，当提取用户ID时，应该返回正确用户ID")
        void givenValidToken_whenExtractUserId_thenReturnCorrectUserId() {
            // Act & Assert
            assertThat(jwtTokenService.extractUserId(testToken))
                .isEqualTo(testUser.getId().toString());
        }

        @Test
        @DisplayName("给定有效令牌，当提取租户ID时，应该返回正确租户ID")
        void givenValidToken_whenExtractTenantId_thenReturnCorrectTenantId() {
            // Act & Assert
            assertThat(jwtTokenService.extractTenantId(testToken))
                .isEqualTo(testTenant.getId().toString());
        }

        @Test
        @DisplayName("给定有效令牌，当提取租户代码时，应该返回正确租户代码")
        void givenValidToken_whenExtractTenantCode_thenReturnCorrectTenantCode() {
            // Act & Assert
            assertThat(jwtTokenService.extractTenantCode(testToken))
                .isEqualTo(testTenant.getCode());
        }

        @Test
        @DisplayName("给定有效令牌，当提取租户角色时，应该返回正确租户角色")
        void givenValidToken_whenExtractTenantRole_thenReturnCorrectTenantRole() {
            // Act & Assert
            assertThat(jwtTokenService.extractTenantRole(testToken))
                .isEqualTo(testMembership.getTenantRole().name());
        }

        @Test
        @DisplayName("给定无效令牌，当提取信息时，应该抛出异常")
        void givenInvalidToken_whenExtractInfo_thenThrowException() {
            // Act & Assert
            assertThatThrownBy(() -> jwtTokenService.extractUsername("invalid.token"))
                .isInstanceOf(Exception.class);
            
            assertThatThrownBy(() -> jwtTokenService.extractUserId("invalid.token"))
                .isInstanceOf(Exception.class);
        }
    }

    @Nested
    @DisplayName("令牌过期测试")
    class TokenExpirationTests {

        @Test
        @DisplayName("给定未过期令牌，当检查过期时间时，应该返回false")
        void givenNonExpiredToken_whenCheckExpiration_thenReturnFalse() {
            // Arrange
            String token = jwtTokenService.generateAccessToken(testUser, testTenant, testMembership);

            // Act & Assert
            assertThat(jwtTokenService.isTokenExpired(token)).isFalse();
        }

        @Test
        @DisplayName("给定已过期的令牌，当检查过期时间时，应该返回true")
        void givenExpiredToken_whenCheckExpiration_thenReturnTrue() {
            // Arrange - 创建一个过期时间为1秒的服务实例
            JwtTokenService shortExpiryService = new JwtTokenService();
            ReflectionTestUtils.setField(shortExpiryService, "jwtSecret", 
                "dGVzdFNlY3JldEtleUZvckpXVFRva2VuVGVzdGluZ1B1cnBvc2VzMTIzNDU2");
            ReflectionTestUtils.setField(shortExpiryService, "accessTokenExpiration", 1); // 1秒

            String token = shortExpiryService.generateAccessToken(testUser, testTenant, testMembership);

            // Act - 等待令牌过期
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // Assert - 验证令牌已过期但捕获异常
            assertThat(shortExpiryService.validateToken(token)).isFalse();
            
            // 直接检查过期状态可能会抛出异常，所以用try-catch
            try {
                boolean isExpired = shortExpiryService.isTokenExpired(token);
                assertThat(isExpired).isTrue();
            } catch (Exception e) {
                // 过期的令牌可能在解析时就抛出异常，这也是正确的行为
                assertThat(e.getMessage()).contains("expired");
            }
        }
    }

    @Nested
    @DisplayName("刷新令牌功能测试")
    class RefreshTokenFunctionalityTests {

        @Test
        @DisplayName("给定有效刷新令牌，当刷新访问令牌时，应该返回新令牌")
        void givenValidRefreshToken_whenRefreshAccessToken_thenReturnNewToken() {
            // Arrange
            String refreshToken = jwtTokenService.generateRefreshToken(testUser.getId().toString());

            // Act
            String newToken = jwtTokenService.refreshAccessToken(refreshToken);

            // Assert
            assertThat(newToken).isNotNull().isNotEmpty();
            // 注意: 当前实现是临时的，返回原令牌
            assertThat(newToken).isEqualTo(refreshToken);
        }

        @Test
        @DisplayName("给定无效刷新令牌，当刷新访问令牌时，应该抛出异常")
        void givenInvalidRefreshToken_whenRefreshAccessToken_thenThrowException() {
            // Act & Assert
            assertThatThrownBy(() -> jwtTokenService.refreshAccessToken("invalid.token"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的刷新Token");
        }

        @Test
        @DisplayName("给定访问令牌作为刷新令牌，当刷新时，应该抛出异常")
        void givenAccessTokenAsRefreshToken_whenRefresh_thenThrowException() {
            // Arrange
            String accessToken = jwtTokenService.generateAccessToken(testUser, testTenant, testMembership);

            // Act & Assert
            assertThatThrownBy(() -> jwtTokenService.refreshAccessToken(accessToken))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("不是刷新Token");
        }
    }

    @Nested
    @DisplayName("边界值和异常测试")
    class BoundaryAndExceptionTests {

        @Test
        @DisplayName("给定null用户参数，当生成令牌时，应该抛出异常")
        void givenNullUserParameters_whenGenerateToken_thenThrowException() {
            // Act & Assert
            assertThatThrownBy(() -> jwtTokenService.generateAccessToken(null, testTenant, testMembership))
                .isInstanceOf(Exception.class);
            
            assertThatThrownBy(() -> jwtTokenService.generateSuperAdminToken(null))
                .isInstanceOf(Exception.class);
        }

        @Test
        @DisplayName("给定空字符串用户ID，当生成刷新令牌时，应该正常处理")
        void givenEmptyUserId_whenGenerateRefreshToken_thenHandleGracefully() {
            // Act & Assert
            assertThatCode(() -> jwtTokenService.generateRefreshToken(""))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定特殊字符的用户信息，当生成令牌时，应该正确处理")
        void givenSpecialCharacterUserInfo_whenGenerateToken_thenHandleCorrectly() {
            // Arrange
            PlatformUser specialUser = TestDataFactory.createTestUser("user@#$%", "<EMAIL>");
            
            // Act & Assert
            assertThatCode(() -> 
                jwtTokenService.generateAccessToken(specialUser, testTenant, testMembership))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定超长租户名称，当生成令牌时，应该正确处理")
        void givenVeryLongTenantName_whenGenerateToken_thenHandleCorrectly() {
            // Arrange
            String longName = "A".repeat(1000); // 超长租户名称
            Tenant longNameTenant = TestDataFactory.createTestTenant("LONG01", longName);
            
            // Act & Assert
            assertThatCode(() -> 
                jwtTokenService.generateAccessToken(testUser, longNameTenant, testMembership))
                .doesNotThrowAnyException();
        }
    }

    @Nested
    @DisplayName("密钥处理测试")
    class SigningKeyTests {

        @Test
        @DisplayName("给定短密钥，当生成令牌时，应该自动扩展密钥长度")
        void givenShortKey_whenGenerateToken_thenAutoExtendKeyLength() {
            // Arrange
            JwtTokenService shortKeyService = new JwtTokenService();
            ReflectionTestUtils.setField(shortKeyService, "jwtSecret", "short");
            ReflectionTestUtils.setField(shortKeyService, "accessTokenExpiration", 3600);

            // Act & Assert
            assertThatCode(() -> 
                shortKeyService.generateAccessToken(testUser, testTenant, testMembership))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定非Base64密钥，当生成令牌时，应该使用字符串字节")
        void givenNonBase64Key_whenGenerateToken_thenUseStringBytes() {
            // Arrange
            JwtTokenService plainTextKeyService = new JwtTokenService();
            ReflectionTestUtils.setField(plainTextKeyService, "jwtSecret", 
                "thisIsAPlainTextSecretKeyForTestingPurposes123456");
            ReflectionTestUtils.setField(plainTextKeyService, "accessTokenExpiration", 3600);

            // Act & Assert
            assertThatCode(() -> 
                plainTextKeyService.generateAccessToken(testUser, testTenant, testMembership))
                .doesNotThrowAnyException();
        }
    }
}