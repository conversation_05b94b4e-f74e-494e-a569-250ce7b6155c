package com.assessment.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import com.assessment.dto.CreateAssessmentRequest;
import com.assessment.entity.multitenant.AssessmentSubject;
import com.assessment.entity.multitenant.GlobalScaleRegistry;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.AssessmentSubjectRepository;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import com.assessment.service.scoring.ScoringStrategy;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * AssessmentService 全面测试类
 * 覆盖多租户评估核心业务逻辑
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("评估服务综合测试")
class AssessmentServiceExtensiveTest {

    @Mock
    private TenantAssessmentRecordRepository tenantAssessmentRecordRepository;

    @Mock
    private AssessmentSubjectRepository assessmentSubjectRepository;

    @Mock
    private GlobalScaleRegistryRepository globalScaleRegistryRepository;

    @Mock
    private PlatformUserRepository platformUserRepository;

    @Mock
    private TenantUserMembershipRepository tenantUserMembershipRepository;

    @Mock
    private ScoringStrategy scoringStrategy;

    @InjectMocks
    private AssessmentService assessmentService;

    @Captor
    private ArgumentCaptor<TenantAssessmentRecord> assessmentCaptor;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String tenantId = "tenant-123";
    private final String scaleId = "scale-123";
    private final String subjectId = "subject-123";
    private final String assessorId = "ba7c1c4e-8c5d-4f2b-9a3e-1b2c3d4e5f60";

    private CreateAssessmentRequest createMockRequest() {
        ObjectNode formData = objectMapper.createObjectNode();
        formData.put("q1", "独立");
        formData.put("q2", "需要帮助");

        CreateAssessmentRequest request = new CreateAssessmentRequest();
        request.setElderlyId(subjectId);
        request.setScaleId(scaleId);
        request.setAssessorId(assessorId);
        request.setFormData(formData);
        return request;
    }

    private AssessmentSubject createMockSubject() {
        return AssessmentSubject.builder()
                .id(subjectId)
                .tenantId(tenantId)
                .name("张三")
                .gender(AssessmentSubject.Gender.MALE)
                .isActive(true)
                .createdAt(LocalDateTime.now())
                .build();
    }

    private GlobalScaleRegistry createMockScale(GlobalScaleRegistry.ScaleStatus status) {
        ObjectNode formSchema = objectMapper.createObjectNode();
        formSchema.put("title", "老年人能力评估");
        ObjectNode scoringRules = objectMapper.createObjectNode();
        scoringRules.put("maxScore", 100);

        return GlobalScaleRegistry.builder()
                .id(scaleId)
                .code("ELDERLY_ASSESSMENT")
                .name("老年人能力评估量表")
                .version("1.0")
                .category("ability")
                .formSchema(formSchema)
                .scoringRules(scoringRules)
                .status(status)
                .isOfficial(true)
                .build();
    }

    private TenantAssessmentRecord createMockAssessment(TenantAssessmentRecord.RecordStatus status) {
        return TenantAssessmentRecord.builder()
                .id(UUID.randomUUID().toString())
                .tenantId(tenantId)
                .recordNumber("ASS202412200001")
                .subjectId(subjectId)
                .scaleId(scaleId)
                .assessorId(assessorId)
                .status(status)
                .scaleType(TenantAssessmentRecord.ScaleType.GLOBAL)
                .assessmentType(TenantAssessmentRecord.AssessmentType.REGULAR)
                .assessmentDate(LocalDateTime.now())
                .formData(objectMapper.createObjectNode())
                .totalScore(new BigDecimal("75.0"))
                .resultLevel("中等")
                .build();
    }

    @Test
    @DisplayName("创建租户评估记录 - 成功场景")
    void testCreateTenantAssessment_Success() {
        // Arrange
        CreateAssessmentRequest mockRequest = createMockRequest();
        AssessmentSubject mockSubject = createMockSubject();
        GlobalScaleRegistry mockScale = createMockScale(GlobalScaleRegistry.ScaleStatus.ACTIVE);
        
        when(assessmentSubjectRepository.findById(subjectId)).thenReturn(Optional.of(mockSubject));
        when(globalScaleRegistryRepository.findById(scaleId)).thenReturn(Optional.of(mockScale));
        when(platformUserRepository.existsById(any(UUID.class))).thenReturn(true);
        when(tenantUserMembershipRepository.existsByTenantIdAndUserId(tenantId, assessorId)).thenReturn(true);
        when(tenantAssessmentRecordRepository.save(any(TenantAssessmentRecord.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(scoringStrategy.calculateTotalScore(any(), any())).thenReturn(new BigDecimal("75.0"));
        when(scoringStrategy.determineResultLevel(any(), any())).thenReturn("中等");

        // Act
        assessmentService.createTenantAssessment(tenantId, mockRequest);

        // Assert
        verify(tenantAssessmentRecordRepository).save(assessmentCaptor.capture());
        TenantAssessmentRecord savedAssessment = assessmentCaptor.getValue();

        assertThat(savedAssessment).isNotNull();
        assertThat(savedAssessment.getTenantId()).isEqualTo(tenantId);
        assertThat(savedAssessment.getSubjectId()).isEqualTo(subjectId);
        assertThat(savedAssessment.getScaleId()).isEqualTo(scaleId);
        assertThat(savedAssessment.getStatus()).isEqualTo(TenantAssessmentRecord.RecordStatus.DRAFT);
        assertThat(savedAssessment.getTotalScore()).isEqualByComparingTo("75.0");
        assertThat(savedAssessment.getResultLevel()).isEqualTo("中等");

        verify(globalScaleRegistryRepository).incrementUsageCount(scaleId);
    }

    @Test
    @DisplayName("获取租户评估记录 - 无过滤")
    void testGetTenantAssessmentRecords_NoFilter() {
        // Arrange
        TenantAssessmentRecord mockAssessment = createMockAssessment(TenantAssessmentRecord.RecordStatus.DRAFT);
        Page<TenantAssessmentRecord> page = new PageImpl<>(List.of(mockAssessment));
        Pageable pageable = Pageable.unpaged();

        when(tenantAssessmentRecordRepository.findByTenantId(tenantId, pageable)).thenReturn(page);
        
        // Act
        Page<TenantAssessmentRecord> resultPage =
                assessmentService.getTenantAssessmentRecords(
                        tenantId, null, null, null, null, pageable);

        // Assert
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getContent()).hasSize(1);
        assertThat(resultPage.getContent().get(0)).isEqualTo(mockAssessment);
    }

    @Test
    @DisplayName("提交租户评估记录")
    void testSubmitTenantAssessment() {
        // Arrange
        String recordNumber = "ASS202412200001";
        TenantAssessmentRecord mockAssessment = createMockAssessment(TenantAssessmentRecord.RecordStatus.DRAFT);
        when(tenantAssessmentRecordRepository.findByTenantIdAndRecordNumber(tenantId, recordNumber))
                .thenReturn(Optional.of(mockAssessment));
        when(tenantAssessmentRecordRepository.save(any(TenantAssessmentRecord.class))).thenReturn(mockAssessment);

        // Act
        assessmentService.submitTenantAssessment(tenantId, recordNumber);

        // Assert
        verify(tenantAssessmentRecordRepository).findByTenantIdAndRecordNumber(tenantId, recordNumber);
        verify(tenantAssessmentRecordRepository).save(any(TenantAssessmentRecord.class));
    }

    @Test
    @DisplayName("审核租户评估记录 - 批准")
    void testReviewTenantAssessment_Approve() {
        // Arrange
        String recordNumber = "ASS202412200001";
        String reviewNotes = "评估质量良好";
        String reviewerId = "reviewer-123";
        TenantAssessmentRecord mockAssessment = createMockAssessment(TenantAssessmentRecord.RecordStatus.SUBMITTED);
        when(tenantAssessmentRecordRepository.findByTenantIdAndRecordNumber(tenantId, recordNumber))
                .thenReturn(Optional.of(mockAssessment));
        when(tenantAssessmentRecordRepository.save(any(TenantAssessmentRecord.class))).thenReturn(mockAssessment);

        // Act
        assessmentService.reviewTenantAssessment(tenantId, recordNumber, true, reviewNotes, reviewerId);

        // Assert
        verify(tenantAssessmentRecordRepository).findByTenantIdAndRecordNumber(tenantId, recordNumber);
        verify(tenantAssessmentRecordRepository).save(any(TenantAssessmentRecord.class));
    }

    @Test
    @DisplayName("获取租户评估记录详情")
    void testGetTenantAssessmentRecord() {
        // Arrange
        String recordNumber = "ASS202412200001";
        TenantAssessmentRecord mockAssessment = createMockAssessment(TenantAssessmentRecord.RecordStatus.DRAFT);
        when(tenantAssessmentRecordRepository.findByTenantIdAndRecordNumber(tenantId, recordNumber))
                .thenReturn(Optional.of(mockAssessment));

        // Act
        TenantAssessmentRecord result = assessmentService.getTenantAssessmentRecord(tenantId, recordNumber);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mockAssessment.getId());
        assertThat(result.getTenantId()).isEqualTo(tenantId);
        verify(tenantAssessmentRecordRepository).findByTenantIdAndRecordNumber(tenantId, recordNumber);
    }

    @Test
    @DisplayName("删除租户评估记录")
    void testDeleteTenantAssessment() {
        // Arrange
        String recordNumber = "ASS202412200001";
        TenantAssessmentRecord mockAssessment = createMockAssessment(TenantAssessmentRecord.RecordStatus.DRAFT);
        when(tenantAssessmentRecordRepository.findByTenantIdAndRecordNumber(tenantId, recordNumber))
                .thenReturn(Optional.of(mockAssessment));

        // Act
        assessmentService.deleteTenantAssessment(tenantId, recordNumber);

        // Assert
        verify(tenantAssessmentRecordRepository).findByTenantIdAndRecordNumber(tenantId, recordNumber);
        verify(tenantAssessmentRecordRepository).delete(mockAssessment);
    }

    @Test
    @DisplayName("获取评估对象的评估历史")
    void testGetSubjectAssessmentHistory() {
        // Arrange
        TenantAssessmentRecord mockAssessment = createMockAssessment(TenantAssessmentRecord.RecordStatus.DRAFT);
        List<TenantAssessmentRecord> assessments = Arrays.asList(mockAssessment);
        when(tenantAssessmentRecordRepository.findByTenantIdAndSubjectIdOrderByAssessmentDateDesc(tenantId, subjectId))
                .thenReturn(assessments);

        // Act
        List<TenantAssessmentRecord> result = assessmentService.getSubjectAssessmentHistory(tenantId, subjectId);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getSubjectId()).isEqualTo(subjectId);
        verify(tenantAssessmentRecordRepository).findByTenantIdAndSubjectIdOrderByAssessmentDateDesc(tenantId, subjectId);
    }

    @Test
    @DisplayName("创建评估记录 - 验证失败：评估对象不属于租户")
    void testCreateTenantAssessment_SubjectNotInTenant() {
        // Arrange
        AssessmentSubject wrongTenantSubject = AssessmentSubject.builder()
                .id(subjectId)
                .tenantId("other-tenant")
                .name("张三")
                .build();
        
        when(assessmentSubjectRepository.findById(subjectId)).thenReturn(Optional.of(wrongTenantSubject));

        // Act & Assert
        assertThatThrownBy(() -> assessmentService.createTenantAssessment(tenantId, createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("评估对象不属于当前租户");
    }

    @Test
    @DisplayName("创建评估记录 - 验证失败：量表不存在")
    void testCreateTenantAssessment_ScaleNotFound() {
        // Arrange
        when(assessmentSubjectRepository.findById(subjectId)).thenReturn(Optional.of(createMockSubject()));
        when(globalScaleRegistryRepository.findById(scaleId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> assessmentService.createTenantAssessment(tenantId, createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("评估量表不存在");
    }

    @Test
    @DisplayName("创建评估记录 - 验证失败：量表已停用")
    void testCreateTenantAssessment_ScaleInactive() {
        // Arrange
        // Arrange
        GlobalScaleRegistry inactiveScale = createMockScale(GlobalScaleRegistry.ScaleStatus.INACTIVE);
        
        when(assessmentSubjectRepository.findById(subjectId)).thenReturn(Optional.of(createMockSubject()));
        when(globalScaleRegistryRepository.findById(scaleId)).thenReturn(Optional.of(inactiveScale));

        // Act & Assert
        assertThatThrownBy(() -> assessmentService.createTenantAssessment(tenantId, createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("评估量表已停用");
    }

    @Test
    @DisplayName("创建评估记录 - 验证失败：评估员不存在")
    void testCreateTenantAssessment_AssessorNotFound() {
        // Arrange
        // Arrange
        when(assessmentSubjectRepository.findById(subjectId)).thenReturn(Optional.of(createMockSubject()));
        when(globalScaleRegistryRepository.findById(scaleId)).thenReturn(Optional.of(createMockScale(GlobalScaleRegistry.ScaleStatus.ACTIVE)));
        when(platformUserRepository.existsById(any(UUID.class))).thenReturn(false);

        // Act & Assert
        assertThatThrownBy(() -> assessmentService.createTenantAssessment(tenantId, createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("评估员不存在");
    }

    @Test
    @DisplayName("创建评估记录 - 验证失败：评估员不属于租户")
    void testCreateTenantAssessment_AssessorNotInTenant() {
        // Arrange
        // Arrange
        when(assessmentSubjectRepository.findById(subjectId)).thenReturn(Optional.of(createMockSubject()));
        when(globalScaleRegistryRepository.findById(scaleId)).thenReturn(Optional.of(createMockScale(GlobalScaleRegistry.ScaleStatus.ACTIVE)));
        when(platformUserRepository.existsById(any(UUID.class))).thenReturn(true);
        when(tenantUserMembershipRepository.existsByTenantIdAndUserId(tenantId, assessorId)).thenReturn(false);

        // Act & Assert
        assertThatThrownBy(() -> assessmentService.createTenantAssessment(tenantId, createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("评估员不属于当前租户");
    }

    @Test
    @DisplayName("获取不存在的评估记录")
    void testGetTenantAssessmentRecord_NotFound() {
        // Arrange
        String recordNumber = "ASS202412200001";
        when(tenantAssessmentRecordRepository.findByTenantIdAndRecordNumber(tenantId, recordNumber))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> assessmentService.getTenantAssessmentRecord(tenantId, recordNumber))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("评估记录不存在");
    }

    @Test
    @DisplayName("边界条件测试 - 空租户ID")
    void testCreateTenantAssessment_EmptyTenantId() {
        // Act & Assert
        assertThatThrownBy(() -> assessmentService.createTenantAssessment("", createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("租户ID不能为空");
        
        assertThatThrownBy(() -> assessmentService.createTenantAssessment(null, createMockRequest()))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("租户ID不能为空");
    }
}