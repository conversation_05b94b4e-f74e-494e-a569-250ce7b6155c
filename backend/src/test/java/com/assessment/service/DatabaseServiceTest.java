package com.assessment.service;

import com.assessment.dto.ExecuteDDLRequest;
import com.assessment.dto.ExecuteDDLResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * DatabaseService 单元测试
 * 测试数据库DDL操作和表管理功能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("数据库服务测试")
class DatabaseServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;
    
    @Mock
    private DataSource dataSource;

    private DatabaseService databaseService;

    private ExecuteDDLRequest basicRequest;
    private ExecuteDDLRequest.ExecutionOptions basicOptions;

    @BeforeEach
    void setUp() {
        // 设置DataSource
        when(jdbcTemplate.getDataSource()).thenReturn(dataSource);
        
        // 创建DatabaseService实例并使用反射注入mock的JdbcTemplate
        databaseService = new DatabaseService(jdbcTemplate);
        ReflectionTestUtils.setField(databaseService, "jdbcTemplate", jdbcTemplate);

        // 创建基本测试数据
        basicOptions = ExecuteDDLRequest.ExecutionOptions.builder()
            .checkTableExists(true)
            .backupExistingTable(false)
            .forceExecution(false)
            .build();

        basicRequest = ExecuteDDLRequest.builder()
            .sql("CREATE TABLE test_table (id SERIAL PRIMARY KEY, name VARCHAR(100))")
            .tableName("test_table")
            .tableComment("测试表")
            .overwriteExisting(false)
            .options(basicOptions)
            .build();
    }

    @Nested
    @DisplayName("构造函数和初始化测试")
    class ConstructorAndInitializationTests {

        @Test
        @DisplayName("给定有效的JdbcTemplate，当创建服务时，应该成功初始化")
        void givenValidJdbcTemplate_whenCreateService_thenInitializeSuccessfully() {
            // Arrange
            JdbcTemplate validJdbcTemplate = mock(JdbcTemplate.class);
            DataSource validDataSource = mock(DataSource.class);
            when(validJdbcTemplate.getDataSource()).thenReturn(validDataSource);

            // Act & Assert
            assertThatCode(() -> new DatabaseService(validJdbcTemplate))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定null JdbcTemplate，当创建服务时，应该抛出异常")
        void givenNullJdbcTemplate_whenCreateService_thenThrowException() {
            // Act & Assert
            assertThatThrownBy(() -> new DatabaseService(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("JdbcTemplate and its DataSource cannot be null");
        }

        @Test
        @DisplayName("给定JdbcTemplate但DataSource为null，当创建服务时，应该抛出异常")
        void givenJdbcTemplateWithNullDataSource_whenCreateService_thenThrowException() {
            // Arrange
            JdbcTemplate invalidJdbcTemplate = mock(JdbcTemplate.class);
            when(invalidJdbcTemplate.getDataSource()).thenReturn(null);

            // Act & Assert
            assertThatThrownBy(() -> new DatabaseService(invalidJdbcTemplate))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("JdbcTemplate and its DataSource cannot be null");
        }
    }

    @Nested
    @DisplayName("DDL执行成功场景测试")
    class DDLExecutionSuccessTests {

        @Test
        @DisplayName("给定新表的DDL请求，当执行时，应该成功创建表")
        void givenNewTableDDLRequest_whenExecute_thenCreateTableSuccessfully() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(0); // 表不存在
            doNothing().when(jdbcTemplate).execute(anyString());

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(basicRequest);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getMessage()).isEqualTo("表创建成功");
            assertThat(result.getTableName()).isEqualTo("test_table");
            assertThat(result.getExecutedSql()).isEqualTo(basicRequest.getSql());
            assertThat(result.getExecutionTimeMs()).isNotNull().isGreaterThanOrEqualTo(0L);
            assertThat(result.getExecutionTime()).isNotNull();
            assertThat(result.getAffectedRows()).isEqualTo(1);
            assertThat(result.getErrors()).isEmpty();
            assertThat(result.getOverwrittenExistingTable()).isFalse();
            assertThat(result.getBackupTableName()).isNull();

            // 验证方法调用
            verify(jdbcTemplate).queryForObject(anyString(), eq(Integer.class), eq("test_table"));
            verify(jdbcTemplate).execute(basicRequest.getSql());
        }

        @Test
        @DisplayName("给定覆盖现有表的DDL请求，当执行时，应该删除原表并创建新表")
        void givenOverwriteExistingTableDDLRequest_whenExecute_thenDropAndCreateTable() {
            // Arrange
            ExecuteDDLRequest overwriteRequest = ExecuteDDLRequest.builder()
                .sql("CREATE TABLE test_table (id SERIAL PRIMARY KEY, updated_name VARCHAR(200))")
                .tableName("test_table")
                .overwriteExisting(true)
                .options(basicOptions)
                .build();

            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(1); // 表存在
            doNothing().when(jdbcTemplate).execute(anyString());

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(overwriteRequest);

            // Assert
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getOverwrittenExistingTable()).isTrue();
            assertThat(result.getWarnings()).contains("已覆盖已存在的表: test_table");

            // 验证删除和创建操作
            verify(jdbcTemplate).execute("DROP TABLE IF EXISTS test_table CASCADE");
            verify(jdbcTemplate).execute(overwriteRequest.getSql());
        }

        @Test
        @DisplayName("给定带备份选项的DDL请求，当覆盖现有表时，应该先备份原表")
        void givenDDLRequestWithBackup_whenOverwriteExisting_thenBackupOriginalTable() {
            // Arrange
            ExecuteDDLRequest.ExecutionOptions backupOptions = ExecuteDDLRequest.ExecutionOptions.builder()
                .backupExistingTable(true)
                .build();

            ExecuteDDLRequest backupRequest = ExecuteDDLRequest.builder()
                .sql("CREATE TABLE test_table (id SERIAL PRIMARY KEY, new_column VARCHAR(50))")
                .tableName("test_table")
                .overwriteExisting(true)
                .options(backupOptions)
                .build();

            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(1); // 表存在
            doNothing().when(jdbcTemplate).execute(anyString());

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(backupRequest);

            // Assert
            assertThat(result.getSuccess()).isTrue();
            assertThat(result.getBackupTableName()).startsWith("test_table_backup_");
            assertThat(result.getWarnings()).anyMatch(w -> w.contains("已备份原表为"));

            // 验证备份、删除、创建操作
            verify(jdbcTemplate).execute(contains("CREATE TABLE test_table_backup_"));
            verify(jdbcTemplate).execute("DROP TABLE IF EXISTS test_table CASCADE");
            verify(jdbcTemplate).execute(backupRequest.getSql());
        }
    }

    @Nested
    @DisplayName("DDL执行失败场景测试")
    class DDLExecutionFailureTests {

        @Test
        @DisplayName("给定表已存在且不允许覆盖，当执行DDL时，应该返回失败结果")
        void givenTableExistsAndNoOverwrite_whenExecuteDDL_thenReturnFailure() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(1); // 表存在

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(basicRequest);

            // Assert
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getMessage()).contains("DDL执行失败");
            assertThat(result.getErrors()).hasSize(1);
            assertThat(result.getErrors().get(0)).contains("表 test_table 已存在");
            assertThat(result.getOverwrittenExistingTable()).isFalse();

            // 验证没有执行DDL语句
            verify(jdbcTemplate, never()).execute(basicRequest.getSql());
        }

        @Test
        @DisplayName("给定数据库异常，当执行DDL时，应该返回失败结果")
        void givenDatabaseException_whenExecuteDDL_thenReturnFailure() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(0); // 表不存在
            doThrow(new DataAccessException("SQL语法错误") {})
                .when(jdbcTemplate).execute(basicRequest.getSql());

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(basicRequest);

            // Assert
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getMessage()).contains("DDL执行失败");
            assertThat(result.getErrors()).hasSize(1);
            assertThat(result.getErrors().get(0)).contains("SQL语法错误");
            assertThat(result.getExecutionTimeMs()).isNotNull();
        }

        @Test
        @DisplayName("给定备份表创建失败，当执行DDL时，应该返回失败结果")
        void givenBackupTableCreationFails_whenExecuteDDL_thenReturnFailure() {
            // Arrange
            ExecuteDDLRequest.ExecutionOptions backupOptions = ExecuteDDLRequest.ExecutionOptions.builder()
                .backupExistingTable(true)
                .build();

            ExecuteDDLRequest backupRequest = ExecuteDDLRequest.builder()
                .sql("CREATE TABLE test_table (id SERIAL PRIMARY KEY)")
                .tableName("test_table")
                .overwriteExisting(true)
                .options(backupOptions)
                .build();

            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(1); // 表存在
            
            // 备份表创建失败
            doThrow(new DataAccessException("备份表创建失败") {})
                .when(jdbcTemplate).execute(contains("CREATE TABLE test_table_backup_"));

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(backupRequest);

            // Assert
            assertThat(result.getSuccess()).isFalse();
            assertThat(result.getErrors()).anyMatch(e -> e.contains("创建备份表失败"));
        }
    }

    @Nested
    @DisplayName("表存在性检查测试")
    class TableExistenceCheckTests {

        @Test
        @DisplayName("给定存在的表名，当检查表存在性时，应该返回true")
        void givenExistingTableName_whenCheckTableExists_thenReturnTrue() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("existing_table")))
                .thenReturn(1);

            // Act
            boolean exists = databaseService.tableExists("existing_table");

            // Assert
            assertThat(exists).isTrue();
            verify(jdbcTemplate).queryForObject(anyString(), eq(Integer.class), eq("existing_table"));
        }

        @Test
        @DisplayName("给定不存在的表名，当检查表存在性时，应该返回false")
        void givenNonExistentTableName_whenCheckTableExists_thenReturnFalse() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("non_existent_table")))
                .thenReturn(0);

            // Act
            boolean exists = databaseService.tableExists("non_existent_table");

            // Assert
            assertThat(exists).isFalse();
        }

        @Test
        @DisplayName("给定数据库查询异常，当检查表存在性时，应该返回false")
        void givenDatabaseQueryException_whenCheckTableExists_thenReturnFalse() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenThrow(new DataAccessException("查询异常") {});

            // Act
            boolean exists = databaseService.tableExists("some_table");

            // Assert
            assertThat(exists).isFalse();
        }

        @Test
        @DisplayName("给定查询结果为null，当检查表存在性时，应该返回false")
        void givenNullQueryResult_whenCheckTableExists_thenReturnFalse() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(null);

            // Act
            boolean exists = databaseService.tableExists("some_table");

            // Assert
            assertThat(exists).isFalse();
        }
    }

    @Nested
    @DisplayName("表结构信息获取测试")
    class TableStructureRetrievalTests {

        @Test
        @DisplayName("给定存在的表名，当获取表结构时，应该返回完整结构信息")
        void givenExistingTableName_whenGetTableStructure_thenReturnCompleteStructure() {
            // Arrange
            String tableName = "user_table";
            String tableComment = "用户信息表";
            
            when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq("user_table")))
                .thenReturn(tableComment);

            List<Map<String, Object>> columns = Arrays.asList(
                createColumnInfo("id", "integer", null, "NO", "nextval('user_table_id_seq'::regclass)", "主键ID"),
                createColumnInfo("username", "character varying", 50, "NO", null, "用户名"),
                createColumnInfo("email", "character varying", 100, "YES", null, "邮箱地址")
            );

            when(jdbcTemplate.queryForList(anyString(), eq("user_table")))
                .thenReturn(columns);

            // Act
            Map<String, Object> structure = databaseService.getTableStructure(tableName);

            // Assert
            assertThat(structure).isNotNull();
            assertThat(structure.get("tableName")).isEqualTo(tableName);
            assertThat(structure.get("tableComment")).isEqualTo(tableComment);
            assertThat(structure.get("columnCount")).isEqualTo(3);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> returnedColumns = (List<Map<String, Object>>) structure.get("columns");
            assertThat(returnedColumns).hasSize(3);
            assertThat(returnedColumns.get(0).get("column_name")).isEqualTo("id");
            assertThat(returnedColumns.get(1).get("column_name")).isEqualTo("username");
            assertThat(returnedColumns.get(2).get("column_name")).isEqualTo("email");
        }

        @Test
        @DisplayName("给定数据库查询异常，当获取表结构时，应该抛出运行时异常")
        void givenDatabaseQueryException_whenGetTableStructure_thenThrowRuntimeException() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(String.class), anyString()))
                .thenThrow(new DataAccessException("查询异常") {});

            // Act & Assert
            assertThatThrownBy(() -> databaseService.getTableStructure("some_table"))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("获取表结构失败");
        }

        private Map<String, Object> createColumnInfo(String columnName, String dataType, 
                Integer maxLength, String isNullable, String columnDefault, String comment) {
            Map<String, Object> column = new HashMap<>();
            column.put("column_name", columnName);
            column.put("data_type", dataType);
            column.put("character_maximum_length", maxLength);
            column.put("is_nullable", isNullable);
            column.put("column_default", columnDefault);
            column.put("column_comment", comment);
            return column;
        }
    }

    @Nested
    @DisplayName("边界值和异常测试")
    class BoundaryAndExceptionTests {

        @Test
        @DisplayName("给定空表名，当检查表存在性时，应该正常处理")
        void givenEmptyTableName_whenCheckTableExists_thenHandleGracefully() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("")))
                .thenReturn(0);

            // Act & Assert
            assertThatCode(() -> databaseService.tableExists(""))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定null表名，当检查表存在性时，应该返回false")
        void givenNullTableName_whenCheckTableExists_thenReturnFalse() {
            // Act
            boolean exists = databaseService.tableExists(null);

            // Assert
            assertThat(exists).isFalse();
        }

        @Test
        @DisplayName("给定包含特殊字符的表名，当执行DDL时，应该正确处理")
        void givenTableNameWithSpecialCharacters_whenExecuteDDL_thenHandleCorrectly() {
            // Arrange
            ExecuteDDLRequest specialRequest = ExecuteDDLRequest.builder()
                .sql("CREATE TABLE \"test-table_with.special@chars\" (id SERIAL)")
                .tableName("test-table_with.special@chars")
                .overwriteExisting(false)
                .build();

            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(0);
            doNothing().when(jdbcTemplate).execute(anyString());

            // Act & Assert
            assertThatCode(() -> databaseService.executeDDL(specialRequest))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定超长SQL语句，当执行DDL时，应该正确处理")
        void givenVeryLongSQL_whenExecuteDDL_thenHandleCorrectly() {
            // Arrange
            String longSql = "CREATE TABLE long_table (" +
                "id SERIAL PRIMARY KEY, " +
                "very_long_column_name_that_exceeds_normal_limits VARCHAR(1000), " +
                "another_column TEXT DEFAULT 'A very long default value that might cause issues'" +
                ")";

            ExecuteDDLRequest longSqlRequest = ExecuteDDLRequest.builder()
                .sql(longSql)
                .tableName("long_table")
                .overwriteExisting(false)
                .build();

            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(0);
            doNothing().when(jdbcTemplate).execute(anyString());

            // Act & Assert
            assertThatCode(() -> databaseService.executeDDL(longSqlRequest))
                .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("给定null DDL请求，当执行时，应该抛出异常")
        void givenNullDDLRequest_whenExecute_thenThrowException() {
            // Act & Assert
            assertThatThrownBy(() -> databaseService.executeDDL(null))
                .isInstanceOf(Exception.class);
        }
    }

    @Nested
    @DisplayName("性能和并发测试")
    class PerformanceAndConcurrencyTests {

        @Test
        @DisplayName("给定正常DDL操作，当测量执行时间时，应该记录合理的耗时")
        void givenNormalDDLOperation_whenMeasureExecutionTime_thenRecordReasonableTime() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), anyString()))
                .thenReturn(0);
            
            // 模拟一些执行延迟
            doAnswer(invocation -> {
                Thread.sleep(10); // 模拟10ms执行时间
                return null;
            }).when(jdbcTemplate).execute(anyString());

            // Act
            ExecuteDDLResult result = databaseService.executeDDL(basicRequest);

            // Assert
            assertThat(result.getExecutionTimeMs()).isGreaterThanOrEqualTo(10);
            assertThat(result.getExecutionTimeMs()).isLessThan(1000); // 应该不超过1秒
        }

        @Test
        @DisplayName("给定大量表结构查询，当并发执行时，应该正确处理")
        void givenMultipleTableStructureQueries_whenExecuteConcurrently_thenHandleCorrectly() {
            // Arrange
            when(jdbcTemplate.queryForObject(anyString(), eq(String.class), anyString()))
                .thenReturn("测试表注释");
            when(jdbcTemplate.queryForList(anyString(), anyString()))
                .thenReturn(Arrays.asList(createSimpleColumnInfo()));

            // Act & Assert - 多次调用应该都能成功
            for (int i = 0; i < 5; i++) {
                final int index = i; // 创建final变量供lambda使用
                assertThatCode(() -> databaseService.getTableStructure("test_table_" + index))
                    .doesNotThrowAnyException();
            }
        }

        private Map<String, Object> createSimpleColumnInfo() {
            Map<String, Object> column = new HashMap<>();
            column.put("column_name", "id");
            column.put("data_type", "integer");
            column.put("is_nullable", "NO");
            return column;
        }
    }
}