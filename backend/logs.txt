[INFO] Scanning for projects...
[INFO] 
[INFO] -------------< com.assessment:elderly-assessment-platform >-------------
[INFO] Building Elderly Assessment Platform 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] Parameter 'encoding' is unknown for plugin 'maven-checkstyle-plugin:3.3.1:check (validate)'
[INFO] 
[INFO] >>> spring-boot:3.5.0:run (default-cli) > test-compile @ elderly-assessment-platform >>>
[INFO] 
[INFO] --- checkstyle:3.3.1:check (validate) @ elderly-assessment-platform ---
[INFO] 开始检查……
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/FieldMappingDTO.java:243:19: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/FieldMappingDTO.java:263:28: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/ApiResponse.java:20:46: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/ApiResponse.java:29:46: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/ApiResponse.java:29:54: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/ApiResponse.java:38:44: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/ApiResponse.java:46:44: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/ApiResponse.java:46:60: 参数： errorCode 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:12:27: '{' 后应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:12:28: '}' 前应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:14:25: 参数： username 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:14:42: 参数： password 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:19:5: 既然类 'LoginRequest' 设计为可继承的，应为可重写的非空方法 'getUsername' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:23:5: 既然类 'LoginRequest' 设计为可继承的，应为可重写的非空方法 'setUsername' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:23:29: 参数： username 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:27:5: 既然类 'LoginRequest' 设计为可继承的，应为可重写的非空方法 'getPassword' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:31:5: 既然类 'LoginRequest' 设计为可继承的，应为可重写的非空方法 'setPassword' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginRequest.java:31:29: 参数： password 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:11:28: '{' 后应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:11:29: '}' 前应有空格。 [WhitespaceAround]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:13:26: 参数： token 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:13:40: 参数： refreshToken 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:13:61: 参数： user 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:13:72: 参数： expiresIn 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:20:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'getToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:24:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'setToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:24:26: 参数： token 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:28:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'getRefreshToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:32:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'setRefreshToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:32:33: 参数： refreshToken 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:36:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'getUser' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:40:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'setUser' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:40:25: 参数： user 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:44:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'getExpiresIn' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:48:5: 既然类 'LoginResponse' 设计为可继承的，应为可重写的非空方法 'setExpiresIn' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/dto/LoginResponse.java:48:30: 参数： expiresIn 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentTaskRepository.java:36: 本行字符数 123个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentTaskRepository.java:49: 本行字符数 125个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:48: 本行字符数 154个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:49: 本行字符数 127个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:60: 本行字符数 153个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:61: 本行字符数 129个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:66: 本行字符数 121个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:78: 本行字符数 165个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/repository/AssessmentRecordRepository.java:79: 本行字符数 132个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:19:24: 参数： success 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:19:41: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:19:57: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:26:24: 参数： success 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:26:41: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:26:57: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:26:65: 参数： errorCode 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:34:46: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:34:62: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:38:46: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:42:44: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:46:44: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:46:60: 参数： errorCode 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:51:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'isSuccess' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:55:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'setSuccess' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:55:28: 参数： success 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:59:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'getMessage' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:63:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'setMessage' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:63:28: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:67:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'getData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:71:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'setData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:71:25: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:75:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'getErrorCode' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:79:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'setErrorCode' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:79:30: 参数： errorCode 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:83:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'getTimestamp' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:87:5: 既然类 'ApiResponse' 设计为可继承的，应为可重写的非空方法 'setTimestamp' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/util/ApiResponse.java:87:30: 参数： timestamp 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/DataInitializer.java:12:28: 参数： userRepository 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/DataInitializer.java:17:21: 参数： args 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:21:9: 既然类 'Evaluation' 设计为可继承的，应为可重写的非空方法 'getMaxDuration' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:25:9: 既然类 'Evaluation' 设计为可继承的，应为可重写的非空方法 'setMaxDuration' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:29:9: 既然类 'Evaluation' 设计为可继承的，应为可重写的非空方法 'getAutoSaveInterval' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:33:9: 既然类 'Evaluation' 设计为可继承的，应为可重写的非空方法 'setAutoSaveInterval' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:42:9: 既然类 'Upload' 设计为可继承的，应为可重写的非空方法 'getAllowedTypes' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:46:9: 既然类 'Upload' 设计为可继承的，应为可重写的非空方法 'setAllowedTypes' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:50:9: 既然类 'Upload' 设计为可继承的，应为可重写的非空方法 'getMaxSize' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:54:9: 既然类 'Upload' 设计为可继承的，应为可重写的非空方法 'setMaxSize' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:65:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'getPasswordMinLength' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:69:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'setPasswordMinLength' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:73:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'isPasswordRequireSpecial' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:77:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'setPasswordRequireSpecial' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:81:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'getMaxLoginAttempts' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:85:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'setMaxLoginAttempts' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:89:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'getLockDuration' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:93:9: 既然类 'Security' 设计为可继承的，应为可重写的非空方法 'setLockDuration' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:98:5: 既然类 'ApplicationProperties' 设计为可继承的，应为可重写的非空方法 'getEvaluation' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:102:5: 既然类 'ApplicationProperties' 设计为可继承的，应为可重写的非空方法 'setEvaluation' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:106:5: 既然类 'ApplicationProperties' 设计为可继承的，应为可重写的非空方法 'getUpload' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:110:5: 既然类 'ApplicationProperties' 设计为可继承的，应为可重写的非空方法 'setUpload' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:114:5: 既然类 'ApplicationProperties' 设计为可继承的，应为可重写的非空方法 'getSecurity' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/config/ApplicationProperties.java:118:5: 既然类 'ApplicationProperties' 设计为可继承的，应为可重写的非空方法 'setSecurity' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:4:23: 不应使用 '.*' 形式的导入 - io.jsonwebtoken.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:25:5: 既然类 'JwtTokenProvider' 设计为可继承的，应为可重写的非空方法 'generateToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:25:33: 参数： authentication 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:37:5: 既然类 'JwtTokenProvider' 设计为可继承的，应为可重写的非空方法 'generateRefreshToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:37:40: 参数： username 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:48:5: 既然类 'JwtTokenProvider' 设计为可继承的，应为可重写的非空方法 'getUsernameFromToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:48:40: 参数： token 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:58:5: 既然类 'JwtTokenProvider' 设计为可继承的，应为可重写的非空方法 'validateToken' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:58:34: 参数： token 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtTokenProvider.java:79:5: 既然类 'JwtTokenProvider' 设计为可继承的，应为可重写的非空方法 'getJwtExpirationMs' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:31:5: 既然类 'SecurityConfig' 设计为可继承的，应为可重写的非空方法 'securityFilterChain' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:32:52: 参数： http 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:64:5: 既然类 'SecurityConfig' 设计为可继承的，应为可重写的非空方法 'passwordEncoder' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:69:5: 既然类 'SecurityConfig' 设计为可继承的，应为可重写的非空方法 'authenticationManager' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:70:56: 参数： config 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:74:5: 既然类 'SecurityConfig' 设计为可继承的，应为可重写的非空方法 'corsConfigurationSource' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/SecurityConfig.java:81:33: '3600L' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:29:37: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:29:65: 参数： response 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:30:35: 参数： filterChain 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:33:78: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:34:76: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:60:38: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationFilter.java:63:42: '7' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationEntryPoint.java:16:26: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationEntryPoint.java:16:54: 参数： response 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/security/JwtAuthenticationEntryPoint.java:17:26: 参数： authException 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentResult.java:3:27: 不应使用 '.*' 形式的导入 - jakarta.persistence.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentResult.java:57:5: 既然类 'AssessmentResult' 设计为可继承的，应为可重写的非空方法 'getRecord' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentResult.java:62:5: 既然类 'AssessmentResult' 设计为可继承的，应为可重写的非空方法 'setRecord' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentResult.java:63:27: 参数： record 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:3:27: 不应使用 '.*' 形式的导入 - jakarta.persistence.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:118:22: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:137:25: 参数： reviewNotes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:137:32: 'reviewNotes' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:137:45: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:137:52: 'reviewerId' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:147:24: 参数： reviewNotes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:147:31: 'reviewNotes' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:147:44: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:147:51: 'reviewerId' 隐藏属性。 [HiddenField]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:155:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:159:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:159:23: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:163:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getRecordNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:167:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setRecordNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:167:33: 参数： recordNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:171:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getTaskId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:175:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setTaskId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:175:27: 参数： taskId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:179:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getTask' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:184:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setTask' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:185:25: 参数： task 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:189:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getElderlyId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:193:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setElderlyId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:193:30: 参数： elderlyId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:197:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getElderly' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:202:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setElderly' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:203:28: 参数： elderly 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:207:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getScaleId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:211:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setScaleId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:211:28: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:215:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getScale' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:220:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setScale' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:221:26: 参数： scale 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:225:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getAssessorId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:229:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setAssessorId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:229:31: 参数： assessorId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:233:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getAssessor' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:238:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setAssessor' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:239:29: 参数： assessor 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:243:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getReviewerId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:247:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setReviewerId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:247:31: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:251:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getReviewer' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:256:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setReviewer' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:257:29: 参数： reviewer 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:261:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getAssessmentDate' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:265:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setAssessmentDate' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:265:35: 参数： assessmentDate 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:269:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getFormData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:273:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setFormData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:273:29: 参数： formData 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:277:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getScoreData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:281:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setScoreData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:281:30: 参数： scoreData 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:285:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getTotalScore' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:289:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setTotalScore' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:289:31: 参数： totalScore 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:293:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getResultLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:297:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setResultLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:297:32: 参数： resultLevel 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:301:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getStatus' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:305:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setStatus' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:305:27: 参数： status 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:309:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getReviewNotes' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:313:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setReviewNotes' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:313:32: 参数： reviewNotes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:317:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getReviewedAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:321:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setReviewedAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:321:31: 参数： reviewedAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:325:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'getIsValid' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:329:5: 既然类 'AssessmentRecord' 设计为可继承的，应为可重写的非空方法 'setIsValid' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentRecord.java:329:28: 参数： isValid 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/Institution.java:78:25: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/Institution.java:99:26: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:3:27: 不应使用 '.*' 形式的导入 - jakarta.persistence.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:78:16: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:100:30: 参数： birthDate 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:113:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:117:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:117:23: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:121:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:125:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:125:25: 参数： name 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:129:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getIdNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:133:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setIdNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:133:29: 参数： idNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:137:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getGender' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:141:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setGender' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:141:27: 参数： gender 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:145:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getAge' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:149:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setAge' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:149:24: 参数： age 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:153:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getPhone' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:157:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setPhone' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:157:26: 参数： phone 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:161:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getEmergencyContactName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:165:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setEmergencyContactName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:165:41: 参数： emergencyContactName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:169:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getEmergencyContactPhone' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:173:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setEmergencyContactPhone' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:173:42: 参数： emergencyContactPhone 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:177:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getAddress' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:181:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setAddress' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:181:28: 参数： address 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:185:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getMedicalInsuranceNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:189:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setMedicalInsuranceNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:189:43: 参数： medicalInsuranceNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:193:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getCareLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:197:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setCareLevel' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:197:30: 参数： careLevel 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:201:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getInstitutionId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:205:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setInstitutionId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:205:34: 参数： institutionId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:209:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getInstitution' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:214:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setInstitution' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:215:32: 参数： institution 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:219:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'getIsActive' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:223:5: 既然类 'ElderlyPerson' 设计为可继承的，应为可重写的非空方法 'setIsActive' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/ElderlyPerson.java:223:29: 参数： isActive 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:49:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'getCreatedAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:53:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'setCreatedAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:53:30: 参数： createdAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:57:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'getUpdatedAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:61:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'setUpdatedAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:61:30: 参数： updatedAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:65:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'getCreatedBy' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:69:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'setCreatedBy' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:69:30: 参数： createdBy 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:73:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'getUpdatedBy' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:77:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'setUpdatedBy' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:77:30: 参数： updatedBy 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:81:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'onCreate' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/BaseEntity.java:88:5: 既然类 'BaseEntity' 设计为可继承的，应为可重写的非空方法 'onUpdate' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentScale.java:3:27: 不应使用 '.*' 形式的导入 - jakarta.persistence.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentScale.java:161:19: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentScale.java:183:21: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:3:27: 不应使用 '.*' 形式的导入 - jakarta.persistence.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:73:18: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:90:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:94:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:94:23: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:98:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getUsername' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:102:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setUsername' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:102:29: 参数： username 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:106:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getPasswordHash' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:110:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setPasswordHash' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:110:33: 参数： passwordHash 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:114:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getRealName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:118:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setRealName' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:118:29: 参数： realName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:122:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getEmail' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:126:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setEmail' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:126:26: 参数： email 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:130:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getPhone' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:134:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setPhone' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:134:26: 参数： phone 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:138:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getRole' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:142:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setRole' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:142:25: 参数： role 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:146:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getInstitutionId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:150:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setInstitutionId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:150:34: 参数： institutionId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:154:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getInstitution' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:159:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setInstitution' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:160:32: 参数： institution 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:164:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getProfessionalTitle' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:168:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setProfessionalTitle' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:168:38: 参数： professionalTitle 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:172:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getLicenseNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:176:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setLicenseNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:176:34: 参数： licenseNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:180:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getIsActive' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:184:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setIsActive' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:184:29: 参数： isActive 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:188:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'getLastLoginAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:192:5: 既然类 'User' 设计为可继承的，应为可重写的非空方法 'setLastLoginAt' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:192:32: 参数： lastLoginAt 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:197:27: 参数： o 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:198:9: 'if' 结构必须使用大括号 '{}'。 [NeedBraces]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:199:9: 'if' 结构必须使用大括号 '{}'。 [NeedBraces]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:200:9: 'if' 结构必须使用大括号 '{}'。 [NeedBraces]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/User.java:202:44: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:3:27: 不应使用 '.*' 形式的导入 - jakarta.persistence.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:88:18: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:111:20: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:130:22: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:164:74: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:169:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:173:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:173:23: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:177:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getTaskNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:181:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setTaskNumber' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:181:31: 参数： taskNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:185:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getElderlyId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:189:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setElderlyId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:189:30: 参数： elderlyId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:193:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getElderly' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:198:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setElderly' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:199:28: 参数： elderly 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:203:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getScaleId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:207:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setScaleId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:207:28: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:211:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getScale' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:216:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setScale' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:217:26: 参数： scale 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:221:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getAssessorId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:225:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setAssessorId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:225:31: 参数： assessorId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:229:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getAssessor' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:234:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setAssessor' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:235:29: 参数： assessor 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:239:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getReviewerId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:243:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setReviewerId' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:243:31: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:247:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getReviewer' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:252:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setReviewer' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:253:29: 参数： reviewer 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:257:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getTaskType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:261:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setTaskType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:261:29: 参数： taskType 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:265:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getStatus' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:269:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setStatus' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:269:27: 参数： status 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:273:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getPriority' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:277:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setPriority' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:277:29: 参数： priority 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:281:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getScheduledDate' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:285:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setScheduledDate' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:285:34: 参数： scheduledDate 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:289:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getDeadline' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:293:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setDeadline' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:293:29: 参数： deadline 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:297:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'getNotes' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:301:5: 既然类 'AssessmentTask' 设计为可继承的，应为可重写的非空方法 'setNotes' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/entity/AssessmentTask.java:301:26: 参数： notes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:21:5: 既然类 'AssessmentSection' 设计为可继承的，应为可重写的非空方法 'getQuestions' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:25:5: 既然类 'AssessmentSection' 设计为可继承的，应为可重写的非空方法 'setQuestions' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:25:30: 参数： questions 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:29:5: 既然类 'AssessmentSection' 设计为可继承的，应为可重写的非空方法 'addQuestion' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentSection.java:29:29: 参数： question 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/QuestionType.java:15:18: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:39:5: 既然类 'AssessmentTable' 设计为可继承的，应为可重写的非空方法 'getHeaders' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:43:5: 既然类 'AssessmentTable' 设计为可继承的，应为可重写的非空方法 'setHeaders' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:43:28: 参数： headers 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:47:5: 既然类 'AssessmentTable' 设计为可继承的，应为可重写的非空方法 'getData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:51:5: 既然类 'AssessmentTable' 设计为可继承的，应为可重写的非空方法 'setData' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:51:25: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentTable.java:72:19: 参数： displayName 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:8:17: 不应使用 '.*' 形式的导入 - java.util.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:27:48: 参数： document 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:59:5: 方法 extractTablesFromText 52 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:59:57: 参数： pageText 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:59:74: 参数： pageNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:70:17: 'if' 结构必须使用大括号 '{}'。 [NeedBraces]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:115:49: 参数： rows 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:115:74: 参数： pageNumber 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:141:32: 参数： line 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:143:36: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:144:49: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:151:40: 参数： line 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:176:57: 参数： rows 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:184:57: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:185:68: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:191:58: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:192:68: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:193:61: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:199:60: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:200:68: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:206:68: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:217:38: 参数： rows 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:231:41: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/extractor/TableExtractionService.java:253:26: '0.5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:16:17: 不应使用 '.*' 形式的导入 - java.util.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:30: 本行字符数 122个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:31: 本行字符数 128个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:46:57: 参数： content 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:83:48: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:102:33: 参数： lines 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:106:33: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:106:58: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:107:61: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:116:58: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:127:35: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:146:39: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:153:66: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:161:17: 'if' 结构必须使用大括号 '{}'。 [NeedBraces]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:172:39: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:172:53: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:187:5: 方法 parseSections 60 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:187:51: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:187:64: 参数： tables 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:196:13: 'if' 结构必须使用大括号 '{}'。 [NeedBraces]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:196:51: '3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:251:45: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:262:47: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:276:43: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:289:48: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:293:82: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:310:42: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:325:40: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:335:35: 参数： sections 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:335:69: 参数： tables 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:354:46: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:359:44: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:364:46: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:372:44: 参数： tables 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:395:42: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/parser/AssessmentStructureParser.java:407:42: 参数： table 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:40:5: 既然类 'PDFContent' 设计为可继承的，应为可重写的非空方法 'getTables' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:44:5: 既然类 'PDFContent' 设计为可继承的，应为可重写的非空方法 'setTables' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:44:27: 参数： tables 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:48:5: 既然类 'PDFContent' 设计为可继承的，应为可重写的非空方法 'getImages' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:52:5: 既然类 'PDFContent' 设计为可继承的，应为可重写的非空方法 'setImages' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFContent.java:52:27: 参数： images 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:28:36: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:60:5: 方法 generateScoringRules 60 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:60:42: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:124:33: 参数： properties 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:124:56: 参数： required 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:154:37: 参数： properties 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:154:60: 参数： required 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:154:80: 参数： section 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:160: 本行字符数 133个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:188:38: 参数： properties 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:188:61: 参数： required 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:188:81: 参数： question 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:194: 本行字符数 131个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:239:42: 参数： questionSchema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:239:69: 参数： options 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:264:44: 参数： questionSchema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:264:71: 参数： options 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:294:39: 参数： questionSchema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:302:41: 参数： questionSchema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:310:39: 参数： questionSchema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:318:33: 参数： properties 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:318:56: 参数： key 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:318:68: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:318:82: 参数： required 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:331:35: 参数： scoringLevels 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:331:60: 参数： maxScore 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:334:17: 类型转换后应有空格。 [WhitespaceAfter]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:334:30: '0.9' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:335:17: 类型转换后应有空格。 [WhitespaceAfter]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:335:30: '0.8' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:336:17: 类型转换后应有空格。 [WhitespaceAfter]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:336:30: '0.6' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:337:17: 类型转换后应有空格。 [WhitespaceAfter]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:337:30: '0.4' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/generator/SchemaGeneratorService.java:357:32: 参数： input 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:19:5: 既然类 'AssessmentStructure' 设计为可继承的，应为可重写的非空方法 'getMetadata' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:26:5: 既然类 'AssessmentStructure' 设计为可继承的，应为可重写的非空方法 'getSections' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:30:5: 既然类 'AssessmentStructure' 设计为可继承的，应为可重写的非空方法 'getScoringRules' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:38:5: 既然类 'AssessmentStructure' 设计为可继承的，应为可重写的非空方法 'setMetadata' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:39:29: 参数： metadata 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:44:5: 既然类 'AssessmentStructure' 设计为可继承的，应为可重写的非空方法 'setSections' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:44:29: 参数： sections 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:48:5: 既然类 'AssessmentStructure' 设计为可继承的，应为可重写的非空方法 'setScoringRules' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentStructure.java:49:33: 参数： scoringRules 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:49:57: 参数： pdfFile 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:89:33: 参数： pdfFile 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:120:42: 参数： filePath 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:145:13: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:146:13: 参数： schema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:147:13: 参数： scoringRules 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:148:13: 参数： sourcePdfPath 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:170:38: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:180:48: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:186:25: '5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:186:44: '30' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:186:49: '60' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:192:39: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:199:39: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/PDFParserService.java:206:64: 参数： typeString 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:19:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'addOption' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:19:27: 参数： option 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:27:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'getTitle' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:31:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'getType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:35:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'isRequired' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:39:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'getOptions' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:44:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'setTitle' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:44:26: 参数： title 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:48:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'setType' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:48:25: 参数： type 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:52:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'setRequired' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:52:29: 参数： required 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:56:5: 既然类 'AssessmentQuestion' 设计为可继承的，应为可重写的非空方法 'setOptions' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/pdf/AssessmentQuestion.java:56:28: 参数： options 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/HealthController.java:15:5: 既然类 'HealthController' 设计为可继承的，应为可重写的非空方法 'health' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/HealthController.java:24:5: 既然类 'HealthController' 设计为可继承的，应为可重写的非空方法 'info' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/IndexController.java:13:5: 既然类 'IndexController' 设计为可继承的，应为可重写的非空方法 'index' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PasswordHashController.java:5:47: 不应使用 '.*' 形式的导入 - org.springframework.web.bind.annotation.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PasswordHashController.java:15:5: 既然类 'PasswordHashController' 设计为可继承的，应为可重写的非空方法 'hashPassword' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PasswordHashController.java:16:32: 参数： password 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:13:47: 不应使用 '.*' 形式的导入 - org.springframework.web.bind.annotation.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:37:13: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:61:13: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:63:13: 参数： fieldId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:65:13: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:91:13: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:93:13: 参数： fieldIds 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:123:13: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:125:13: 参数： fieldMapping 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:186:13: 参数： field 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:206:54: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:211:30: '0.85' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:227:29: '0.95' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:242:29: '0.90' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:246:31: '120.0' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:257:29: '0.88' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:259:34: '0.3' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:262:31: '5.0' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:271:58: 参数： fieldId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:271:74: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:287:49: 参数： field 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:304:75: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/FieldMappingController.java:305:82: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AuthController.java:46:5: 既然类 'AuthController' 设计为可继承的，应为可重写的非空方法 'login' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AuthController.java:48:61: 参数： loginRequest 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AuthController.java:91:5: 既然类 'AuthController' 设计为可继承的，应为可重写的非空方法 'logout' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AuthController.java:98:5: 既然类 'AuthController' 设计为可继承的，应为可重写的非空方法 'getCurrentUser' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AuthController.java:100:61: 参数： authentication 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:14:47: 不应使用 '.*' 形式的导入 - org.springframework.web.bind.annotation.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:39:13: 参数： status 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:73:13: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:100:13: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:102:13: 参数： scaleDTO 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:132:13: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:162:13: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:190:13: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/AssessmentScaleController.java:264:45: 参数： scale 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:15:47: 不应使用 '.*' 形式的导入 - org.springframework.web.bind.annotation.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:45:13: 参数： file 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:47:13: 参数： preview 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:90:13: 参数： files 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:196:13: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:225:34: 参数： file 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/PDFImportController.java:248:45: 参数： scale 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/TestController.java:4:47: 不应使用 '.*' 形式的导入 - org.springframework.web.bind.annotation.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/TestController.java:11:5: 既然类 'TestController' 设计为可继承的，应为可重写的非空方法 'publicEndpoint' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/TestController.java:16:5: 既然类 'TestController' 设计为可继承的，应为可重写的非空方法 'publicPostEndpoint' 添加 javadoc 文档说明。 [DesignForExtension]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/controller/TestController.java:17:54: 参数： data 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/ElderlyAssessmentApplication.java:6:1: 工具类不应有 public 或 default 构造器。 [HideUtilityClassConstructor]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/ElderlyAssessmentApplication.java:8:29: 参数： args 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:5:29: 不应使用 '.*' 形式的导入 - com.assessment.entity.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:6:33: 不应使用 '.*' 形式的导入 - com.assessment.repository.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:59:46: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:100:5: 方法 calculateScore 53 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:100:33: 参数： record 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:157:46: 参数： sectionScoring 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:157:71: 参数： sectionData 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:198:41: 参数： totalScore 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:198:64: 参数： scoringRules 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:225:34: 参数： recordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:238:34: 参数： recordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:238:51: 参数： approved 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:238:69: 参数： reviewNotes 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:238:89: 参数： reviewerId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:257:13: 参数： elderlyId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:257:31: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:257:47: 参数： assessorId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:258:13: 参数： status 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:258:51: 参数： pageable 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:267:49: 参数： recordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:275:34: 参数： recordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:288:63: 参数： elderlyId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:295:52: 参数： previousRecordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:295:77: 参数： currentRecordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:308:44: 参数： request 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:338:68: '10000' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:345:39: 参数： taskId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:345:54: 参数： recordId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:357:45: 参数： scoringRules 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentService.java:357:68: 参数： formData 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:30:42: 参数： dto 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:66:37: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:81:45: 参数： type 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:88:32: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:99:40: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:99:51: 参数： dto 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:106:52: '&&' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:134:41: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:153:46: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:178:52: 参数： status 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/AssessmentScaleService.java:185:29: 参数： id 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserDetailsServiceImpl.java:23:43: 参数： username 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/UserDetailsServiceImpl.java:38:67: 参数： user 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:17:17: 不应使用 '.*' 形式的导入 - java.util.* 。 [AvoidStarImport]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:37:62: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:37:78: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:69:74: 参数： structure 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:93:13: 参数： question 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:93:42: 参数： sectionIndex 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:93:60: 参数： questionIndex 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:93:79: 参数： fieldIndex 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:128:30: '0.1' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:139:66: 参数： questionType 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:153:74: 参数： option 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:166:13: 参数： question 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:166:42: 参数： fieldType 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:171:9: Switch 块未定义 default 。 [MissingSwitchDefault]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:179:48: '5.0' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:188: 本行字符数 146个，最多：120个。 [LineLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:210:45: 参数： question 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:211:29: '0.5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:215:27: '0.2' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:220:27: '0.2' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:231:38: 参数： text 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:245:5: 方法 generateSuggestedSchema 52 行（最多： 50 行）。 [MethodLength]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:245:46: 参数： fields 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:301:34: 参数： fieldType 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:315:40: 参数： fieldSchema 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:315:64: 参数： validation 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:336:5: 可执行语句 33 条 （最多： 30 条）。 [ExecutableStatementCount]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:336:52: 参数： fields 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:356:35: '80' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:363:30: '60' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:364:30: '79' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:370:30: '40' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:371:30: '59' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:378:30: '39' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:390:47: 参数： fields 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:396:91: '0.5' 是一个魔术数字（直接常数）。 [MagicNumber]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:405:47: 参数： scaleId 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:405:63: 参数： fieldMapping 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:436:42: 参数： fields 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:439:108: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:449:48: 参数： fields 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:452:108: '||' 应另起一行。 [OperatorWrap]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/service/FieldMappingService.java:462:64: 参数： scale 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/PDFParsingException.java:8:32: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/PDFParsingException.java:12:32: 参数： message 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/main/java/com/assessment/exception/PDFParsingException.java:12:48: 参数： cause 应定义为 final 的。 [FinalParameters]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/TestApplication.java:15:1: 工具类不应有 public 或 default 构造器。 [HideUtilityClassConstructor]
[WARN] /Volumes/acasis/Assessment/backend/src/test/java/com/assessment/TestApplication.java:20:29: 参数： args 应定义为 final 的。 [FinalParameters]
检查完成。
[INFO] You have 0 Checkstyle violations.
[INFO] 
[INFO] --- jacoco:0.8.11:prepare-agent (default) @ elderly-assessment-platform ---
[INFO] argLine set to -javaagent:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.11/org.jacoco.agent-0.8.11-runtime.jar=destfile=/Volumes/acasis/Assessment/backend/target/jacoco.exec
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ elderly-assessment-platform ---
[INFO] Copying 2 resources from src/main/resources to target/classes
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.14.0:compile (default-compile) @ elderly-assessment-platform ---
[INFO] Recompiling the module because of changed source code.
[INFO] Compiling 61 source files with javac [debug parameters release 21] to target/classes
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ elderly-assessment-platform ---
[INFO] Copying 1 resource from src/test/resources to target/test-classes
[INFO] 
[INFO] --- compiler:3.14.0:testCompile (default-testCompile) @ elderly-assessment-platform ---
[INFO] Recompiling the module because of changed dependency.
[INFO] Compiling 2 source files with javac [debug parameters release 21] to target/test-classes
[INFO] 
[INFO] <<< spring-boot:3.5.0:run (default-cli) < test-compile @ elderly-assessment-platform <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.5.0:run (default-cli) @ elderly-assessment-platform ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.0)

2025-06-14 18:55:54.842  INFO 74102 --- [           main] c.a.ElderlyAssessmentApplication         : Starting ElderlyAssessmentApplication using Java 21.0.6 with PID 74102 (/Volumes/acasis/Assessment/backend/target/classes started by dom in /Volumes/acasis/Assessment/backend)
2025-06-14 18:55:54.842 DEBUG 74102 --- [           main] c.a.ElderlyAssessmentApplication         : Running with Spring Boot v3.5.0, Spring v6.2.7
2025-06-14 18:55:54.842  INFO 74102 --- [           main] c.a.ElderlyAssessmentApplication         : The following 1 profile is active: "local"
2025-06-14 18:55:54.843 DEBUG 74102 --- [           main] o.s.boot.SpringApplication               : Loading source class com.assessment.ElderlyAssessmentApplication
2025-06-14 18:55:54.854 DEBUG 74102 --- [           main] ConfigServletWebServerApplicationContext : Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6821ea29
2025-06-14 18:55:55.253  INFO 74102 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-14 18:55:55.254  INFO 74102 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-14 18:55:55.261 DEBUG 74102 --- [           main] o.s.b.a.AutoConfigurationPackages        : @EnableAutoConfiguration was declared on a class in the package 'com.assessment'. Automatic @Repository and @Entity scanning is enabled.
2025-06-14 18:55:55.313  INFO 74102 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 55 ms. Found 6 JPA repository interfaces.
2025-06-14 18:55:55.318  INFO 74102 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-14 18:55:55.319  INFO 74102 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.assessment.repository.AssessmentRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.assessment.repository.AssessmentResultRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.assessment.repository.AssessmentScaleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.assessment.repository.AssessmentTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.assessment.repository.ElderlyPersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.assessment.repository.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-14 18:55:55.326  INFO 74102 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-06-14 18:55:55.518 DEBUG 74102 --- [           main] .s.b.w.e.t.TomcatServletWebServerFactory : Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.0/spring-boot-3.5.0.jar
2025-06-14 18:55:55.518 DEBUG 74102 --- [           main] .s.b.w.e.t.TomcatServletWebServerFactory : Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.0/spring-boot-3.5.0.jar
2025-06-14 18:55:55.518 DEBUG 74102 --- [           main] .s.b.w.e.t.TomcatServletWebServerFactory : None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-06-14 18:55:55.535  INFO 74102 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8181 (http)
2025-06-14 18:55:55.547  INFO 74102 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14 18:55:55.547  INFO 74102 --- [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14 18:55:55.576  INFO 74102 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-14 18:55:55.576 DEBUG 74102 --- [           main] w.s.c.ServletWebServerApplicationContext : Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-06-14 18:55:55.576  INFO 74102 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 722 ms
2025-06-14 18:55:55.661  INFO 74102 --- [           main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-14 18:55:55.677  INFO 74102 --- [           main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-06-14 18:55:55.688  INFO 74102 --- [           main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-14 18:55:55.772  INFO 74102 --- [           main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-14 18:55:55.782  INFO 74102 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-14 18:55:55.852  INFO 74102 --- [           main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2e40ea48
2025-06-14 18:55:55.852  INFO 74102 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-14 18:55:55.866  WARN 74102 --- [           main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-14 18:55:55.874  INFO 74102 --- [           main] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 15.13
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-14 18:55:56.253  INFO 74102 --- [           main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-14 18:55:56.377  INFO 74102 --- [           main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-14 18:55:56.483  INFO 74102 --- [           main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-14 18:55:56.695 DEBUG 74102 --- [           main] o.s.b.w.s.ServletContextInitializerBeans : Mapping filters: webMvcObservationFilter urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, jwtAuthenticationFilter urls=[/*] order=2147483647
2025-06-14 18:55:56.695 DEBUG 74102 --- [           main] o.s.b.w.s.ServletContextInitializerBeans : Mapping servlets: dispatcherServlet urls=[/]
2025-06-14 18:55:56.706 DEBUG 74102 --- [           main] o.s.b.w.s.f.OrderedRequestContextFilter  : Filter 'requestContextFilter' configured for use
2025-06-14 18:55:56.706 DEBUG 74102 --- [           main] c.a.security.JwtAuthenticationFilter     : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14 18:55:56.707 DEBUG 74102 --- [           main] o.s.w.f.ServerHttpObservationFilter      : Filter 'webMvcObservationFilter' configured for use
2025-06-14 18:55:56.707 DEBUG 74102 --- [           main] s.b.w.s.f.OrderedCharacterEncodingFilter : Filter 'characterEncodingFilter' configured for use
2025-06-14 18:55:56.707 DEBUG 74102 --- [           main] .DelegatingFilterProxyRegistrationBean$1 : Filter 'springSecurityFilterChain' configured for use
2025-06-14 18:55:56.707 DEBUG 74102 --- [           main] o.s.b.w.s.f.OrderedFormContentFilter     : Filter 'formContentFilter' configured for use
2025-06-14 18:55:56.831  INFO 74102 --- [           main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14 18:55:57.018  WARN 74102 --- [           main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-14 18:55:57.057 DEBUG 74102 --- [           main] s.w.s.m.m.a.RequestMappingHandlerMapping : 34 mappings in 'requestMappingHandlerMapping'
2025-06-14 18:55:57.072 DEBUG 74102 --- [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-06-14 18:55:57.199  INFO 74102 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 16 endpoints beneath base path '/actuator'
2025-06-14 18:55:57.252 DEBUG 74102 --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-14 18:55:57.281 DEBUG 74102 --- [           main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-14 18:55:57.302 DEBUG 74102 --- [           main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-14 18:55:57.440  WARN 74102 --- [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-14 18:55:57.445  INFO 74102 --- [           main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-14 18:55:57.446  INFO 74102 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-14 18:55:57.447  INFO 74102 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-14 18:55:57.457 DEBUG 74102 --- [           main] .s.b.a.l.ConditionEvaluationReportLogger : 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AopAutoConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   ApplicationAvailabilityAutoConfiguration#applicationAvailability matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.availability.ApplicationAvailability; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AuditEventsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration#beansEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.beans.BeansEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CacheMeterBinderProvidersConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.MeterBinder' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.RedisCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.cache.RedisCache' (OnClassCondition)

   CachesEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   CachesEndpointAutoConfiguration#cachesEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CachesEndpointAutoConfiguration#cachesEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) found bean 'cachesEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   org.springframework.boot.autoconfigure.http.client.reactive.ClientHttpConnectorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.http.client.reactive.ClientHttpConnector', 'reactor.core.publisher.Mono' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnector matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.client.reactive.ClientHttpConnector; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnectorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.reactive.ClientHttpConnectorBuilder<?>; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ClientHttpConnectorAutoConfiguration#clientHttpConnectorSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.reactive.ClientHttpConnectorSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CompositeMeterRegistryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.composite.CompositeMeterRegistry' (OnClassCondition)

   ConditionsReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ConditionsReportEndpointAutoConfiguration#conditionsReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.condition.ConditionsReportEndpoint; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) found bean 'configurationPropertiesReportEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)
      - @ConditionalOnMissingBean (types: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration matched:
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (DataSourceAutoConfiguration.PooledDataSourceCondition)
      - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration#jdbcConnectionDetails matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.jdbc.JdbcConnectionDetails; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found bean 'dataSource' (OnBeanCondition)

   DataSourceHealthContributorAutoConfiguration#dbHealthContributor matched:
      - @ConditionalOnMissingBean (names: dbHealthIndicator,dbHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceInitializationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer,org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'dataSource' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.DataSourcePoolMetadataMetricsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.jdbc.metadata.DataSourcePoolMetadataProvider; SearchStrategy: all) found bean 'hikariPoolDataSourceMetadataProvider' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.HikariDataSourceMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   DiskSpaceHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   DiskSpaceHealthContributorAutoConfiguration#diskSpaceHealthIndicator matched:
      - @ConditionalOnMissingBean (names: diskSpaceHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnWarDeployment the application is not deployed as a WAR file. (OnWarDeploymentCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   EndpointAutoConfiguration#endpointCachingOperationInvokerAdvisor matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoker.cache.CachingOperationInvokerAdvisor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#endpointOperationParameterMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoke.ParameterValueMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#propertiesEndpointAccessResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.EndpointAccessResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) found bean 'environmentEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnBooleanProperty (server.error.whitelabel.enabled=true) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthContributorAutoConfiguration#pingHealthContributor matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   HealthEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HealthEndpointConfiguration#healthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpointGroupMembershipValidator matched:
      - @ConditionalOnBooleanProperty (management.endpoint.health.validate-group-membership=true) matched (OnPropertyCondition)

   HealthEndpointConfiguration#healthEndpointGroups matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointGroups; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthHttpCodeStatusMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HttpCodeStatusMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthStatusAggregator matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.StatusAggregator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration#healthEndpointWebExtension matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration.MvcAdditionalHealthEndpointPathsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   HibernateJpaAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean', 'jakarta.persistence.EntityManager', 'org.hibernate.engine.spi.SessionImplementor' (OnClassCondition)

   HibernateJpaConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   HttpClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.client.ClientHttpRequestFactory' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder<?>; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientAutoConfiguration#clientHttpRequestFactorySettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.http.client.ClientHttpRequestFactorySettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientObservationsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.observation.Observation' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   HttpClientObservationsAutoConfiguration.MeterFilterConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (server.servlet.encoding.enabled=true) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpExchangesEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   InfoEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   InfoEndpointAutoConfiguration#infoEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.info.InfoEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonEndpointAutoConfiguration#endpointObjectMapper matched:
      - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.ObjectMapper', 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)
      - @ConditionalOnBooleanProperty (management.endpoints.jackson.isolated-object-mapper=true) matched (OnPropertyCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnPreferredJsonMapper JACKSON no property was configured and Jackson is the default (OnPreferredJsonMapperCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcClientAutoConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate; SearchStrategy: all) found a single bean 'namedParameterJdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.simple.JdbcClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#entityManagerFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean,jakarta.persistence.EntityManagerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#entityManagerFactoryBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#jpaVendorAdapter matched:
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.JpaVendorAdapter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration.JpaWebConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (spring.jpa.open-in-view=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor,org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration.PersistenceManagedTypesConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean,jakarta.persistence.EntityManagerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaBaseConfiguration.PersistenceManagedTypesConfiguration#persistenceManagedTypes matched:
      - @ConditionalOnMissingBean (types: org.springframework.orm.jpa.persistenceunit.PersistenceManagedTypes; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JpaRepositoriesAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)
      - @ConditionalOnBooleanProperty (spring.data.jpa.repositories.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found bean 'dataSource'; @ConditionalOnMissingBean (types: org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean,org.springframework.data.jpa.repository.config.JpaRepositoryConfigExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JtaAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.transaction.Transaction' (OnClassCondition)
      - @ConditionalOnBooleanProperty (spring.jta.enabled=true) matched (OnPropertyCondition)

   JvmMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   JvmMetricsAutoConfiguration#classLoaderMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmCompilationMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmCompilationMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmGcMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmGcMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmHeapPressureMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmHeapPressureMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmInfoMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmInfoMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmMemoryMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmThreadMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LettuceConnectionConfiguration matched:
      - @ConditionalOnClass found required class 'io.lettuce.core.RedisClient' (OnClassCondition)
      - @ConditionalOnProperty (spring.data.redis.client-type=lettuce) matched (OnPropertyCondition)

   LettuceConnectionConfiguration#lettuceClientResources matched:
      - @ConditionalOnMissingBean (types: io.lettuce.core.resource.ClientResources; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LettuceConnectionConfiguration#redisConnectionFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   LettuceMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.lettuce.core.RedisClient', 'io.lettuce.core.metrics.MicrometerCommandLatencyRecorder' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   LettuceMetricsAutoConfiguration#micrometerOptions matched:
      - @ConditionalOnMissingBean (types: io.lettuce.core.metrics.MicrometerOptions; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:
      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   LogFileWebEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   LogFileWebEndpointAutoConfiguration#logFileWebEndpoint matched:
      - Log File found logging.file.name logs/assessment-local.log (LogFileWebEndpointAutoConfiguration.LogFileCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LogFileWebEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LogbackMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'ch.qos.logback.classic.LoggerContext', 'org.slf4j.LoggerFactory' (OnClassCondition)
      - LogbackLoggingCondition ILoggerFactory is a Logback LoggerContext (LogbackMetricsAutoConfiguration.LogbackLoggingCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   LogbackMetricsAutoConfiguration#logbackMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.logging.LogbackMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LoggersEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   LoggersEndpointAutoConfiguration#loggersEndpoint matched:
      - Logging System enabled (LoggersEndpointAutoConfiguration.OnEnabledLoggingSystemCondition)
      - @ConditionalOnBean (types: org.springframework.boot.logging.LoggingSystem; SearchStrategy: all) found bean 'springBootLoggingSystem'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LoggersEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ManagementContextAutoConfiguration.SameManagementContextConfiguration matched:
      - Management Port actual port type (SAME) matched required type (OnManagementPortCondition)

   MappingsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration.SpringMvcConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   MetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)

   MetricsAutoConfiguration#micrometerClock matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MetricsEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   MetricsEndpointAutoConfiguration#metricsEndpoint matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.MetricsEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'jakarta.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBooleanProperty (spring.servlet.multipart.enabled=true) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: jakarta.servlet.MultipartConfigElement; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NamedParameterJdbcTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a single bean 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NettyAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.netty.util.NettyRuntime' (OnClassCondition)

   ObservationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.observation.ObservationRegistry' (OnClassCondition)

   ObservationAutoConfiguration#observationRegistry matched:
      - @ConditionalOnMissingBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.observation.MeterObservationHandler; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration.OnlyMetricsMeterObservationHandlerConfiguration matched:
      - @ConditionalOnMissingBean (types: ?; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ObservationAutoConfiguration.OnlyMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry'; @ConditionalOnMissingClass did not find unwanted class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnBooleanProperty (spring.dao.exceptiontranslation.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.prometheusmetrics.PrometheusMeterRegistry' (OnClassCondition)
      - @ConditionalOnEnabledMetricsExport management.prometheus.metrics.export.enabled is true (OnMetricsExportEnabledCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) found bean 'micrometerClock' (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusConfig matched:
      - @ConditionalOnMissingBean (types: io.micrometer.prometheusmetrics.PrometheusConfig; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusMeterRegistry matched:
      - @ConditionalOnMissingBean (types: io.micrometer.prometheusmetrics.PrometheusMeterRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration#prometheusRegistry matched:
      - @ConditionalOnMissingBean (types: io.prometheus.metrics.model.registry.PrometheusRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusScrapeEndpointConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusScrapeEndpointConfiguration#prometheusEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.export.prometheus.PrometheusScrapeEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ReactiveHealthEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   ReactiveHealthEndpointConfiguration#reactiveHealthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.ReactiveHealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ReactorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Hooks' (OnClassCondition)

   RedisAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisAutoConfiguration#redisConnectionDetails matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.data.redis.RedisConnectionDetails; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisAutoConfiguration#redisTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found a single bean 'redisConnectionFactory'; @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisAutoConfiguration#stringRedisTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found a single bean 'redisConnectionFactory'; @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisCacheConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - Cache org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration REDIS cache type (CacheCondition)

   RedisHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory' (OnBeanCondition)

   RedisReactiveAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'org.springframework.data.redis.core.ReactiveRedisTemplate', 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisReactiveAutoConfiguration#reactiveRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (names: reactiveRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveAutoConfiguration#reactiveStringRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (names: reactiveStringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory' (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration#redisHealthContributor matched:
      - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisRepositoriesAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)
      - @ConditionalOnBooleanProperty (spring.data.redis.repositories.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (types: org.springframework.data.redis.repository.support.RedisRepositoryFactoryBean; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RepositoryMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.repository.Repository' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#metricsRepositoryMethodInvocationListener matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.MetricsRepositoryMethodInvocationListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#repositoryTagsProvider matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.RepositoryTagsProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.VirtualThreadsExecutorEnabled found non-matching nested conditions @ConditionalOnThreading did not find VIRTUAL; NestedCondition on NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.NotReactiveWebApplication NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition)

   RestClientAutoConfiguration#httpMessageConvertersRestClientCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientBuilderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientAutoConfiguration#restClientSsl matched:
      - @ConditionalOnBean (types: org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found bean 'sslBundleRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestClientSsl; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestClientObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestClient' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.client.RestClient$Builder; SearchStrategy: all) found bean 'restClientBuilder' (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateObservationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) found bean 'restTemplateBuilder' (OnBeanCondition)

   SbomEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   SbomEndpointAutoConfiguration#sbomEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.sbom.SbomEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SbomEndpointAutoConfiguration#sbomEndpointWebExtension matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.sbom.SbomEndpoint; SearchStrategy: all) found bean 'sbomEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.sbom.SbomEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration#scheduledTasksEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.scheduling.ScheduledTasksEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ScheduledTasksObservabilityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   SecurityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityAutoConfiguration#authenticationEventPublisher matched:
      - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationEventPublisher; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SecurityFilterAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer', 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityFilterAutoConfiguration#securityFilterChainRegistration matched:
      - @ConditionalOnBean (names: springSecurityFilterChain; SearchStrategy: all) found bean 'springSecurityFilterChain' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath; SearchStrategy: all) found bean 'dispatcherServletRegistration' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration#requestMatcherProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.security.servlet.RequestMatcherProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletEndpointManagementContextConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   ServletEndpointManagementContextConfiguration.WebMvcServletEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)

   ServletManagementContextAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.Servlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ShutdownEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ShutdownEndpointAutoConfiguration#shutdownEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.ShutdownEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootWebSecurityConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   SpringDataWebAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#pageableCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#sortCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#springDataWebSettings matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SpringDataWebSettings; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfigProperties matched:
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   SpringDocConfiguration#fileSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.FileSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#openAPIBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.OpenAPIService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#operationBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.OperationService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#parameterBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.GenericParameterService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#polymorphicModelConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.polymorphic-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PolymorphicModelConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#requestBodyBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.RequestBodyService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#responseSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.ResponseSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#schemaPropertyDeprecatingConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.deprecating-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SchemaPropertyDeprecatingConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#securityParser matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.SecurityService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.SpringDocCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocProviders matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDocProviders; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springdocObjectMapperProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.ObjectMapperProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.QuerydslProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.data.querydsl.binding.QuerydslBindingsFactory' (OnClassCondition)

   SpringDocConfiguration.QuerydslProvider#queryDslQuerydslPredicateOperationCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.QuerydslPredicateOperationCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider#springDataWebPropertiesProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDataWebPropertiesProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration#webFluxSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.WebFluxSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.WebConversionServiceConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.web.format.WebConversionService' (OnClassCondition)

   SpringDocKotlinConfiguration matched:
      - @ConditionalOnClass found required class 'kotlin.coroutines.Continuation' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true} and ${springdoc.enable-kotlin:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocKotlinConfiguration#kotlinCoroutinesReturnTypeParser matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.parsers.KotlinCoroutinesReturnTypeParser; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocKotlinConfiguration#nullableKotlinRequestParameterCustomizer matched:
      - @ConditionalOnProperty (springdoc.nullable-request-parameter-enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.ParameterCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Pageable' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocPageableConfiguration#delegatingMethodParameterCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DelegatingMethodParameterCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration#pageOpenAPIConverter matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PagedModel', 'org.springframework.data.web.config.SpringDataWebSettings' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocPageableConfiguration#pageableOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.pageable-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageableOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocSecurityConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.SecurityFilterChain' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true} and ${springdoc.enable-spring-security:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocSecurityConfiguration.SpringSecurityLoginEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.servlet.Filter' (OnClassCondition)

   SpringDocSortConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Sort' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocSortConfiguration#sortOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.sort-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SortOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocUIConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SpringDocWebMvcConfiguration#openApiResource matched:
      - @ConditionalOnExpression (#{(${springdoc.use-management-port:false} == false ) and ${springdoc.enable-default-api-docs:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.api.OpenApiWebMvcResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#requestBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.service.RequestService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#responseBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.service.GenericResponseService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#springWebProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringWebProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.function.RouterFunction' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration#routerFunctionProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.providers.RouterFunctionWebMvcProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SqlInitializationAutoConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.sql.init.enabled=true) matched (OnPropertyCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'spring.sql.init.mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)

   SslAutoConfiguration#sslBundleRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.ssl.SslBundleRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   SslHealthContributorAutoConfiguration#sslHealthIndicator matched:
      - @ConditionalOnMissingBean (names: sslHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslHealthContributorAutoConfiguration#sslInfo matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SslObservabilityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry,org.springframework.boot.ssl.SslBundles; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'sslBundleRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration#startupTimeMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.startup.StartupTimeMetricsListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SwaggerConfig#indexPageTransformer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerIndexTransformer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerConfigResource matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerConfigResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerResourceResolver matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerResourceResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerWebMvcConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerWebMvcConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerWelcome matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerWelcomeWebMvc; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerUiConfigParameters matched:
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SwaggerUiConfigProperties matched:
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SwaggerUiOAuthProperties matched:
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.configuration.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.configuration.SpringDocConfiguration' (OnBeanCondition)

   SystemMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   SystemMetricsAutoConfiguration#diskSpaceMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.system.DiskSpaceMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#fileDescriptorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.FileDescriptorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#processorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.ProcessorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#uptimeMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.UptimeMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutorConfigurations.AsyncConfigurerConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.scheduling.annotation.AsyncConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.AsyncConfigurerConfiguration#applicationTaskExecutorAsyncConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.scheduling.annotation.AsyncConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration matched:
      - AnyNestedCondition 1 matched 1 did not; NestedCondition on TaskExecutorConfigurations.OnExecutorCondition.ModelCondition @ConditionalOnProperty (spring.task.execution.mode=force) did not find property 'spring.task.execution.mode'; NestedCondition on TaskExecutorConfigurations.OnExecutorCondition.ExecutorBeanCondition @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (TaskExecutorConfigurations.OnExecutorCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskExecutorConfigurations.ThreadPoolTaskExecutorBuilderConfiguration#threadPoolTaskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics' (OnClassCondition)
      - @ConditionalOnBean (types: java.util.concurrent.Executor,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'prometheusMeterRegistry', 'applicationTaskExecutor' (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)

   TaskSchedulingConfigurations.ThreadPoolTaskSchedulerBuilderConfiguration#threadPoolTaskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThreadDumpEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   ThreadDumpEndpointAutoConfiguration#dumpEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.management.ThreadDumpEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TomcatMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.binder.tomcat.TomcatMetrics', 'org.apache.catalina.Manager' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   TomcatMetricsAutoConfiguration#tomcatMetricsBinder matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.tomcat.TomcatMetrics,org.springframework.boot.actuate.metrics.web.tomcat.TomcatMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a single bean 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionManagerCustomizationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionManagerCustomizationAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'jakarta.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/jakarta.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: jakarta.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration#controllerEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#endpointMediaTypes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.EndpointMediaTypes; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#pathMappedEndpoints matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.PathMappedEndpoints; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#webEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration#servletEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnBooleanProperty (spring.mvc.formcontent.filter.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:
      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:
      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:
      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#viewNameTranslator matched:
      - @ConditionalOnMissingBean (names: viewNameTranslator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet,org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) found beans 'webEndpointDiscoverer', 'dispatcherServlet' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#controllerEndpointHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.ControllerEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#endpointObjectMapperWebMvcConfigurer matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.endpoint.jackson.EndpointObjectMapper; SearchStrategy: all) found bean 'endpointObjectMapper' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#webEndpointServletHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcObservationAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.web.servlet.DispatcherServlet', 'io.micrometer.observation.Observation' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: io.micrometer.observation.ObservationRegistry; SearchStrategy: all) found bean 'observationRegistry' (OnBeanCondition)

   WebMvcObservationAutoConfiguration#webMvcObservationFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.ServerHttpObservationFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcObservationAutoConfiguration.MeterFilterConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'prometheusMeterRegistry' (OnBeanCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'jakarta.servlet.Servlet', 'jakarta.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=false) did not find property 'spring.aop.proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AppOpticsMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.appoptics.AppOpticsMeterRegistry' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   AtlasMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.atlas.AtlasMeterRegistry' (OnClassCondition)

   AuditAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnBooleanProperty (management.auditevents.enabled=true) matched (OnPropertyCondition)

   AuditEventsEndpointAutoConfiguration#auditEventsEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)

   AvailabilityHealthContributorAutoConfiguration#livenessStateHealthIndicator:
      Did not match:
         - @ConditionalOnBooleanProperty (management.health.livenessstate.enabled=true) did not find property 'management.health.livenessstate.enabled' (OnPropertyCondition)

   AvailabilityHealthContributorAutoConfiguration#readinessStateHealthIndicator:
      Did not match:
         - @ConditionalOnBooleanProperty (management.health.readinessstate.enabled=true) did not find property 'management.health.readinessstate.enabled' (OnPropertyCondition)

   AvailabilityProbesAutoConfiguration:
      Did not match:
         - Probes availability not running on a supported cloud platform (AvailabilityProbesAutoConfiguration.ProbesCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   BatchObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.configuration.annotation.BatchObservabilityBeanPostProcessor' (OnClassCondition)

   BraveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'brave.Tracer' (OnClassCondition)

   Cache2kCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.cache2k.Cache2kBuilder' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.Cache2kCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.cache2k.Cache2kBuilder', 'org.cache2k.extra.spring.SpringCache2kCache', 'org.cache2k.extra.micrometer.Cache2kCacheMetrics' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.CaffeineCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Cache' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.HazelcastCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.spring.cache.HazelcastCache', 'com.hazelcast.core.Hazelcast' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.JCacheCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.cache.CacheManager' (OnClassCondition)

   CacheMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) did not find any beans of type org.springframework.cache.CacheManager (OnBeanCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Caffeine' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration.ReactorNetty:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   CloudFoundryActuatorAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnBooleanProperty (management.cloudfoundry.enabled=true) matched (OnPropertyCondition)

   CodecsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CompositeMeterRegistryConfiguration:
      Did not match:
         - NoneNestedConditions 1 matched 1 did not; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.SingleInjectableMeterRegistry @ConditionalOnSingleCandidate (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found a single bean 'prometheusMeterRegistry'; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.NoMeterRegistryCondition @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition)

   ConnectionFactoryHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   ConnectionPoolMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.pool.ConnectionPool' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource spring.datasource.url is set (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceCheckpointRestoreConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.crac.Resource' (OnClassCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceConfiguration.OracleUcp:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found beans of type 'org.springframework.transaction.TransactionManager' transactionManager (OnBeanCondition)

   DatadogMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.datadog.DatadogMeterRegistry' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DynatraceMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.dynatrace.DynatraceMeterRegistry' (OnClassCondition)

   ElasticMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.elastic.ElasticMeterRegistry' (OnClassCondition)

   ElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.elasticsearch.ElasticsearchClient' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate' (OnClassCondition)

   ElasticsearchReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.repository.ElasticsearchRepository' (OnClassCondition)

   ElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClientBuilder' (OnClassCondition)

   ElasticsearchRestHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration#tomcatVirtualThreadsProtocolHandlerCustomizer:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FlywayEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GangliaMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.ganglia.GangliaMeterRegistry' (OnClassCondition)

   GenericCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration unknown cache type (CacheCondition)

   GraphQlAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlRSocketAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.querydsl.core.Query' (OnClassCondition)

   GraphQlWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphiteMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.graphite.GraphiteMeterRegistry' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.JakartaWebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HealthEndpointReactiveWebExtensionConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   HealthEndpointWebExtensionConfiguration.JerseyAdditionalHealthEndpointPathsConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   HeapDumpWebEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnAvailableEndpoint the configured access for endpoint 'heapdump' is NONE (OnAvailableEndpointCondition)

   HibernateMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.hibernate.stat.HibernateMetrics' (OnClassCondition)

   HttpExchangesAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnBooleanProperty (management.httpexchanges.recording.enabled=true) matched (OnPropertyCondition)

   HttpExchangesAutoConfiguration.ReactiveHttpExchangesConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)
         - Ancestor org.springframework.boot.actuate.autoconfigure.web.exchanges.HttpExchangesAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   HttpExchangesAutoConfiguration.ServletHttpExchangesConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.actuate.autoconfigure.web.exchanges.HttpExchangesAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   HttpExchangesEndpointAutoConfiguration#httpExchangesEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.web.exchanges.HttpExchangeRepository (OnBeanCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HumioMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.humio.HumioMeterRegistry' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.influx.InfluxMeterRegistry' (OnClassCondition)

   InfoContributorAutoConfiguration#buildInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.BuildProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#envInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.env.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#gitInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.GitProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#javaInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.java.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#osInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.os.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#processInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.process.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#sslInfo:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.ssl.enabled is not true (OnEnabledInfoContributorCondition)
      Matched:
         - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) did not find any beans (OnBeanCondition)

   InfoContributorAutoConfiguration#sslInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.ssl.enabled is not true (OnEnabledInfoContributorCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   IntegrationGraphEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.graph.IntegrationGraphServer' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.cache.Caching' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JedisConnectionConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.apache.commons.pool2.impl.GenericObjectPool', 'redis.clients.jedis.Jedis' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JerseySameManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JerseyServerMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.micrometer.server.ObservationApplicationEventListener' (OnClassCondition)

   JerseyWebEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JettyMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.server.Server' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.Message' (OnClassCondition)

   JmsHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.jms.ConnectionFactory' (OnClassCondition)

   JmxAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)

   JmxEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.jmx.enabled=true) did not find property 'spring.jmx.enabled' (OnPropertyCondition)

   JmxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.jmx.JmxMeterRegistry' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'spring.datasource.jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JndiJtaConfiguration:
      Did not match:
         - @ConditionalOnJndi JNDI environment is not available (OnJndiCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.transaction.jta.JtaTransactionManager' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration#entityManagerFactoryBootstrapExecutorCustomizer:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on JpaRepositoriesAutoConfiguration.BootstrapExecutorCondition.LazyBootstrapMode @ConditionalOnProperty (spring.data.jpa.repositories.bootstrap-mode=lazy) did not find property 'spring.data.jpa.repositories.bootstrap-mode'; NestedCondition on JpaRepositoriesAutoConfiguration.BootstrapExecutorCondition.DeferredBootstrapMode @ConditionalOnProperty (spring.data.jpa.repositories.bootstrap-mode=deferred) did not find property 'spring.data.jpa.repositories.bootstrap-mode' (JpaRepositoriesAutoConfiguration.BootstrapExecutorCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.json.bind.Jsonb' (OnClassCondition)

   JvmMetricsAutoConfiguration#virtualThreadMetrics:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.java21.instrument.binder.jdk.VirtualThreadMetrics' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   KafkaMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.ProducerFactory' (OnClassCondition)

   KairosMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.kairos.KairosMeterRegistry' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.LdapOperations' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LettuceConnectionConfiguration#redisConnectionFactoryVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found beans of type 'org.springframework.data.redis.connection.RedisConnectionFactory' redisConnectionFactory (OnBeanCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   LiquibaseEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.integration.spring.SpringLiquibase' (OnClassCondition)

   Log4J2MetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.logging.log4j.core.LoggerContext' (OnClassCondition)

   MailHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.mail.javamail.JavaMailSenderImpl; SearchStrategy: all) did not find any beans of type org.springframework.mail.javamail.JavaMailSenderImpl (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnClassCondition)
         - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'jakarta.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mail.test-connection=true) did not find property 'spring.mail.test-connection' (OnPropertyCondition)

   ManagementContextAutoConfiguration.DifferentManagementContextConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   ManagementWebSecurityAutoConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' securityFilterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ReactiveWebConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MetricsAspectsAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.observations.annotations.enabled=true) did not find property 'management.observations.annotations.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'org.aspectj.weaver.Advice' (OnClassCondition)

   MicrometerTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.MongoTemplate' (OnClassCondition)

   MongoMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClientSettings' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.ReactiveMongoTemplate' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MultipleOpenApiSupportConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 0 matched 3 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnListGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) did not find any beans of type org.springdoc.core.models.GroupedOpenApi; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) did not find any beans of type org.springdoc.core.models.GroupedOpenApi (MultipleOpenApiSupportCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   MultipleOpenApiSupportConfiguration.SpringDocWebMvcActuatorDifferentConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)
         - Ancestor org.springdoc.webmvc.core.configuration.MultipleOpenApiSupportConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   Neo4jAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   NewRelicMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.newrelic.NewRelicMeterRegistry' (OnClassCondition)

   NoOpCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration unknown cache type (CacheCondition)

   NoOpMeterRegistryConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (OnBeanCondition)

   NoopTracerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   OAuth2AuthorizationServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2Authorization' (OnClassCondition)

   OAuth2AuthorizationServerJwtAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.nimbusds.jose.jwk.source.JWKSource' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   OAuth2ClientWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.web.OAuth2AuthorizedClientRepository' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken' (OnClassCondition)

   ObservationAutoConfiguration.MeterObservationHandlerConfiguration.TracingAndMetricsObservationHandlerConfiguration:
      Did not match:
         - @ConditionalOnBean did not find required type 'io.micrometer.tracing.Tracer' (OnBeanCondition)
         - @ConditionalOnBean (types: ?; SearchStrategy: all) did not find any beans of type ? (OnBeanCondition)

   ObservationAutoConfiguration.MetricsWithTracingConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   ObservationAutoConfiguration.ObservedAspectConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.observations.annotations.enabled=true) did not find property 'management.observations.annotations.enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   ObservationAutoConfiguration.OnlyTracingConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   OpenTelemetryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.sdk.OpenTelemetrySdk' (OnClassCondition)

   OpenTelemetryLoggingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.api.OpenTelemetry' (OnClassCondition)

   OpenTelemetryTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.otel.bridge.OtelTracer' (OnClassCondition)

   OtlpLoggingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.opentelemetry.api.OpenTelemetry' (OnClassCondition)

   OtlpMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.registry.otlp.OtlpMeterRegistry' (OnClassCondition)

   OtlpTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.otel.bridge.OtelTracer' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   PrometheusExemplarsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.tracing.Tracer' (OnClassCondition)

   PrometheusMetricsExportAutoConfiguration.PrometheusPushGatewayConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.prometheus.metrics.exporter.pushgateway.PushGateway' (OnClassCondition)

   PulsarAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   PulsarReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.pulsar.client.api.PulsarClient' (OnClassCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   QuartzEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   R2dbcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)

   R2dbcInitializationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)

   R2dbcObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcProxyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.proxy.ProxyConnectionFactory' (OnClassCondition)

   R2dbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcTransactionManagerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)

   RSocketGraphQlClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   RabbitHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   RabbitMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.ConnectionFactory' (OnClassCondition)

   ReactiveCloudFoundryActuatorAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveElasticsearchClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'co.elastic.clients.transport.ElasticsearchTransport' (OnClassCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient' (OnClassCondition)

   ReactiveManagementContextAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveManagementWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveMultipartAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   ReactiveOAuth2ClientWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication.ReactiveWebApplicationCondition did not find reactive web application classes; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication.RSocketSecurityEnabledCondition @ConditionalOnBean (types: ?; SearchStrategy: all) did not find any beans of type ? (ReactiveUserDetailsServiceAutoConfiguration.RSocketEnabledOrReactiveWebApplication)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)
         - AnyNestedCondition 1 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.PasswordConfigured @ConditionalOnProperty (spring.security.user.password) did not find property 'spring.security.user.password'; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.NameConfigured @ConditionalOnProperty (spring.security.user.name) did not find property 'spring.security.user.name'; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.MissingAlternative @ConditionalOnMissingClass did not find unwanted classes 'org.springframework.security.oauth2.client.registration.ClientRegistrationRepository', 'org.springframework.security.oauth2.server.resource.introspection.ReactiveOpaqueTokenIntrospector' (ReactiveUserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   RedisHealthContributorAutoConfiguration#redisHealthContributor:
      Did not match:
         - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) found beans named redisHealthContributor (OnBeanCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityDataConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.data.repository.query.SecurityEvaluationContextExtension' (OnClassCondition)

   SecurityRequestMatchersManagementContextConfiguration.JerseyRequestMatcherConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletEndpointManagementContextConfiguration.JerseyServletEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   ServletManagementContextAutoConfiguration.ApplicationContextFilterConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (management.server.add-application-context-header=true) did not find property 'management.server.add-application-context-header' (OnPropertyCondition)

   ServletWebServerFactoryAutoConfiguration.ForwardedHeaderFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.ee10.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SessionsEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SignalFxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.signalfx.SignalFxMeterRegistry' (OnClassCondition)

   SimpleCacheConfiguration:
      Did not match:
         - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration unknown cache type (CacheCondition)

   SimpleMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' prometheusMeterRegistry (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledMetricsExport management.defaults.metrics.export.enabled is considered true (OnMetricsExportEnabledCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.application.admin.enabled=true) did not find property 'spring.application.admin.enabled' (OnPropertyCondition)

   SpringBootWebSecurityConfiguration.SecurityFilterChainConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.web.SecurityFilterChain' securityFilterChain; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)

   SpringBootWebSecurityConfiguration.WebSecurityEnablerConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (names: springSecurityFilterChain; SearchStrategy: all) found beans named springSecurityFilterChain (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   SpringDocConfiguration#propertiesResolverForSchema:
      Did not match:
         - @ConditionalOnProperty (springdoc.api-docs.resolve-schema-properties) did not find property 'springdoc.api-docs.resolve-schema-properties' (OnPropertyCondition)

   SpringDocConfiguration#propertyCustomizingConverter:
      Did not match:
         - @ConditionalOnBean (types: org.springdoc.core.customizers.PropertyCustomizer; SearchStrategy: all) did not find any beans of type org.springdoc.core.customizers.PropertyCustomizer (OnBeanCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on CacheOrGroupedOpenApiCondition.OnCacheDisabled found non-matching nested conditions @ConditionalOnProperty (springdoc.cache.disabled) did not find property 'springdoc.cache.disabled'; NestedCondition on CacheOrGroupedOpenApiCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 0 matched 2 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 0 matched 3 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnListGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) did not find any beans of type org.springdoc.core.models.GroupedOpenApi; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.models.GroupedOpenApi; SearchStrategy: all) did not find any beans of type org.springdoc.core.models.GroupedOpenApi (CacheOrGroupedOpenApiCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor2:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)

   SpringDocConfiguration.SpringDocActuatorConfiguration:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocRepositoryRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocDataRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocFunctionCatalogConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.function.web.function.FunctionEndpointInitializer' (OnClassCondition)

   SpringDocGroovyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.lang.MetaClass' (OnClassCondition)

   SpringDocHateoasConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.server.LinkRelationProvider' (OnClassCondition)

   SpringDocJacksonKotlinModuleConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.module.kotlin.KotlinModule' (OnClassCondition)

   SpringDocJavadocConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.therapi.runtimejavadoc.CommentFormatter' (OnClassCondition)

   SpringDocKotlinxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'kotlinx.coroutines.flow.Flow' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringDocSecurityOAuth2ClientConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.annotation.RegisteredOAuth2AuthorizedClient' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringDocSecurityOAuth2Configuration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService' (OnClassCondition)

   SpringDocSecurityConfiguration.SpringSecurityLoginEndpointConfiguration#springSecurityLoginEndpointCustomiser:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-login-endpoint) did not find property 'springdoc.show-login-endpoint' (OnPropertyCondition)

   SpringDocSortConfiguration#delegatingMethodParameterCustomizer:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DelegatingMethodParameterCustomizer; SearchStrategy: all) found beans of type 'org.springdoc.core.customizers.DelegatingMethodParameterCustomizer' delegatingMethodParameterCustomizer (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#actuatorProvider:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.show-actuator:false} or ${springdoc.use-management-port:false}}) resulted in false (OnExpressionCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#openApiActuatorResource:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.use-management-port:false} and ${springdoc.enable-default-api-docs:true}}) resulted in false (OnExpressionCondition)

   SslObservabilityAutoConfiguration#sslInfoProvider:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.info.SslInfo; SearchStrategy: all) found beans of type 'org.springframework.boot.info.SslInfo' sslInfo (OnBeanCondition)

   StackdriverMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.stackdriver.StackdriverMeterRegistry' (OnClassCondition)

   StartupEndpointAutoConfiguration:
      Did not match:
         - ApplicationStartup configured applicationStartup is of type class org.springframework.core.metrics.DefaultApplicationStartup, expected BufferingApplicationStartup. (StartupEndpointAutoConfiguration.ApplicationStartupCondition)

   StatsdMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.statsd.StatsdMeterRegistry' (OnClassCondition)

   SwaggerConfig#springWebProvider:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringWebProvider; SearchStrategy: all) found beans of type 'org.springdoc.core.providers.SpringWebProvider' springWebProvider (OnBeanCondition)

   SwaggerConfig#swaggerUiConfigParameters:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.properties.SwaggerUiConfigParameters; SearchStrategy: all) found beans of type 'org.springdoc.core.properties.SwaggerUiConfigParameters' org.springdoc.core.properties.SwaggerUiConfigParameters (OnBeanCondition)

   SwaggerConfig#swaggerUiHome:
      Did not match:
         - @ConditionalOnProperty (springdoc.swagger-ui.use-root-path=true) did not find property 'springdoc.swagger-ui.use-root-path' (OnPropertyCondition)

   SwaggerConfig.SwaggerActuatorWelcomeConfiguration:
      Did not match:
         - @ConditionalOnProperty (springdoc.use-management-port) did not find property 'springdoc.use-management-port' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   TaskExecutorConfigurations.SimpleAsyncTaskExecutorBuilderConfiguration#simpleAsyncTaskExecutorBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder' simpleAsyncTaskExecutorBuilder (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutorVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   TaskSchedulingConfigurations.SimpleAsyncTaskSchedulerBuilderConfiguration#simpleAsyncTaskSchedulerBuilderVirtualThreads:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder; SearchStrategy: all) found beans of type 'org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder' simpleAsyncTaskSchedulerBuilder (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.spring6.SpringTemplateEngine' (OnClassCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.AspectJTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.transaction.aspectj.AbstractTransactionAspect; SearchStrategy: all) did not find any beans of type org.springframework.transaction.aspectj.AbstractTransactionAspect (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.aop.proxy-target-class=false) did not find property 'spring.aop.proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationManager,org.springframework.security.authentication.AuthenticationProvider,org.springframework.security.core.userdetails.UserDetailsService,org.springframework.security.authentication.AuthenticationManagerResolver,?; SearchStrategy: all) found beans of type 'org.springframework.security.authentication.AuthenticationManager' authenticationManager and found beans of type 'org.springframework.security.core.userdetails.UserDetailsService' userDetailsServiceImpl (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - AnyNestedCondition 1 matched 2 did not; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.PasswordConfigured @ConditionalOnProperty (spring.security.user.password) did not find property 'spring.security.user.password'; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.NameConfigured @ConditionalOnProperty (spring.security.user.name) did not find property 'spring.security.user.name'; NestedCondition on UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured.MissingAlternative @ConditionalOnMissingClass did not find unwanted classes 'org.springframework.security.oauth2.client.registration.ClientRegistrationRepository', 'org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector', 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (UserDetailsServiceAutoConfiguration.MissingAlternativeOrUserPropertiesConfigured)

   WavefrontAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.application.ApplicationTags' (OnClassCondition)

   WavefrontMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WavefrontTracingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebClientObservationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebFluxEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   WebFluxObservationAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mvc.hiddenmethod.filter.enabled=true) did not find property 'spring.mvc.hiddenmethod.filter.enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ProblemDetailsErrorHandlingConfiguration:
      Did not match:
         - @ConditionalOnBooleanProperty (spring.mvc.problemdetails.enabled=true) did not find property 'spring.mvc.problemdetails.enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarVersionLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#managementHealthEndpointWebMvcHandlerMapping:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.client.core.WebServiceTemplate' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSessionIdResolverAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.ee10.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.jdbc.XADataSourceWrapper; SearchStrategy: all) did not find any beans of type org.springframework.boot.jdbc.XADataSourceWrapper (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'jakarta.transaction.TransactionManager', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   ZipkinAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'zipkin2.reporter.Encoding' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.availability.AvailabilityHealthContributorAutoConfiguration

    org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.info.InfoContributorAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.metrics.integration.IntegrationMetricsAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.jackson.JacksonEndpointAutoConfiguration

    org.springdoc.core.configuration.SpringDocSpecPropertiesConfiguration

    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



2025-06-14 18:55:57.462 DEBUG 74102 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : Application failed to start due to an exception

org.springframework.boot.web.server.PortInUseException: Port 8181 is already in use
	at org.springframework.boot.web.server.PortInUseException.lambda$throwIfPortBindingException$0(PortInUseException.java:71) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.server.PortInUseException.lambda$ifPortBindingException$1(PortInUseException.java:86) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.server.PortInUseException.ifCausedBy(PortInUseException.java:104) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.server.PortInUseException.ifPortBindingException(PortInUseException.java:83) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.server.PortInUseException.throwIfPortBindingException(PortInUseException.java:70) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:250) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.servlet.context.WebServerStartStopLifecycle.start(WebServerStartStopLifecycle.java:44) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:405) ~[spring-context-6.2.7.jar:6.2.7]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:394) ~[spring-context-6.2.7.jar:6.2.7]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:586) ~[spring-context-6.2.7.jar:6.2.7]
	at java.base/java.lang.Iterable.forEach(Iterable.java:75) ~[na:na]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:364) ~[spring-context-6.2.7.jar:6.2.7]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:310) ~[spring-context-6.2.7.jar:6.2.7]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:1006) ~[spring-context-6.2.7.jar:6.2.7]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:630) ~[spring-context-6.2.7.jar:6.2.7]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351) ~[spring-boot-3.5.0.jar:3.5.0]
	at com.assessment.ElderlyAssessmentApplication.main(ElderlyAssessmentApplication.java:9) ~[classes/:na]
Caused by: java.lang.IllegalArgumentException: standardService.connector.startFailed
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:222) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.addPreviouslyRemovedConnectors(TomcatWebServer.java:310) ~[spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:236) ~[spring-boot-3.5.0.jar:3.5.0]
	... 16 common frames omitted
Caused by: org.apache.catalina.LifecycleException: Protocol handler start failed
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1082) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.core.StandardService.addConnector(StandardService.java:219) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	... 18 common frames omitted
Caused by: java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method) ~[na:na]
	at java.base/sun.nio.ch.Net.bind(Net.java:565) ~[na:na]
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344) ~[na:na]
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301) ~[na:na]
	at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:240) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:195) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1304) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.tomcat.util.net.AbstractEndpoint.start(AbstractEndpoint.java:1390) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.coyote.AbstractProtocol.start(AbstractProtocol.java:644) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	at org.apache.catalina.connector.Connector.startInternal(Connector.java:1079) ~[tomcat-embed-core-10.1.41.jar:10.1.41]
	... 20 common frames omitted

2025-06-14 18:55:57.464 ERROR 74102 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8181 was already in use.

Action:

Identify and stop the process that's listening on port 8181 or configure this application to listen on another port.

[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  7.148 s
[INFO] Finished at: 2025-06-14T18:55:58+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.5.0:run (default-cli) on project elderly-assessment-platform: Process terminated with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
