<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
  
  <!-- 
    OWASP Dependency Check Suppressions
    
    This file contains rules to suppress false positives in dependency vulnerability scans.
    Each suppression should include:
    1. A detailed justification for why the vulnerability is not applicable
    2. The specific CVE or vulnerability being suppressed
    3. An expiration date for review
    
    Guidelines:
    - Only suppress vulnerabilities that are confirmed false positives
    - Always include justification comments
    - Set reasonable expiration dates for regular review
    - Consider upgrading dependencies instead of suppressing when possible
  -->
  
  <!-- Example suppression (remove if not needed)
  <suppress>
    <notes>
      <![CDATA[
      Suppress specific CVE for spring-boot-starter-web as it's not applicable to our usage.
      The vulnerability affects a component we don't use directly.
      Review date: 2025-07-01
      ]]>
    </notes>
    <cve>CVE-2023-XXXXX</cve>
    <until>2025-07-01</until>
  </suppress>
  -->
  
</suppressions>