<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>
    <!-- JPA Entity exposure patterns are acceptable -->
    <Match>
        <Class name="~com\.assessment\.entity\..*" />
        <Bug pattern="EI_EXPOSE_REP" />
    </Match>
    
    <Match>
        <Class name="~com\.assessment\.entity\..*" />
        <Bug pattern="EI_EXPOSE_REP2" />
    </Match>
    
    <!-- PDF Builder patterns from Lombok -->
    <Match>
        <Class name="~.*\$.*Builder" />
        <Bug pattern="EI_EXPOSE_REP2" />
    </Match>
    
    <!-- PDF data classes with defensive copying -->
    <Match>
        <Class name="~com\.assessment\.pdf\..*" />
        <Bug pattern="EI_EXPOSE_REP" />
    </Match>
    
    <Match>
        <Class name="~com\.assessment\.pdf\..*" />
        <Bug pattern="EI_EXPOSE_REP2" />
    </Match>
    
    <!-- Custom RuntimeException subclasses are acceptable -->
    <Match>
        <Class name="com.assessment.pdf.generator.SchemaGeneratorService" />
        <Bug pattern="THROWS_METHOD_THROWS_RUNTIMEEXCEPTION" />
    </Match>
    
    <!-- Business validation exceptions in AssessmentService -->
    <Match>
        <Class name="com.assessment.service.AssessmentService" />
        <Method name="validateAssessmentRequest" />
        <Bug pattern="THROWS_METHOD_THROWS_RUNTIMEEXCEPTION" />
    </Match>
    
    <!-- REC_CATCH_EXCEPTION for reflection operations -->
    <Match>
        <Class name="com.assessment.pdf.generator.SchemaGeneratorService" />
        <Bug pattern="REC_CATCH_EXCEPTION" />
    </Match>
</FindBugsFilter>