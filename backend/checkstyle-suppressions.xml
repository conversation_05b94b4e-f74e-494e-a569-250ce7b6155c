<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suppressions PUBLIC
    "-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
    "https://checkstyle.org/dtds/suppressions_1_2.dtd">

<suppressions>
    <!-- 实体类相关抑制 -->
    <!-- JPA实体类通常需要可继承，但不需要为每个getter/setter添加javadoc -->
    <suppress checks="DesignForExtension" files=".*[/\\]entity[/\\].*\.java"/>
    
    <!-- PDF相关类抑制 -->
    <!-- PDF解析类的方法名可能较长，参数较多 -->
    <suppress checks="DesignForExtension" files=".*[/\\]pdf[/\\].*\.java"/>
    <suppress checks="MethodLength" files=".*[/\\]pdf[/\\].*\.java"/>
    <suppress checks="ParameterNumber" files=".*[/\\]pdf[/\\].*\.java"/>
    
    <!-- 测试类相关抑制 -->
    <!-- 测试方法名允许使用下划线和更长的描述性名称 -->
    <suppress checks="MethodName" files=".*[/\\]test[/\\].*\.java"/>
    <suppress checks="MagicNumber" files=".*[/\\]test[/\\].*\.java"/>
    
    <!-- 配置类抑制 -->
    <!-- 配置类的方法通常需要可重写 -->
    <suppress checks="DesignForExtension" files=".*[/\\]config[/\\].*\.java"/>
    
    <!-- 控制器类抑制 -->
    <!-- 控制器方法通常需要可重写，参数为final可能影响框架使用 -->
    <suppress checks="DesignForExtension" files=".*[/\\]controller[/\\].*\.java"/>
    <suppress checks="FinalParameters" files=".*[/\\]controller[/\\].*\.java"/>
    
    <!-- 服务类部分抑制 -->
    <!-- 服务类的参数final检查可以放宽 -->
    <suppress checks="FinalParameters" files=".*[/\\]service[/\\].*\.java"/>
    
    <!-- DTO类抑制 -->
    <!-- DTO类通常是简单的数据传输对象，不需要强制final参数和每个方法的javadoc -->
    <suppress checks="FinalParameters" files=".*[/\\]dto[/\\].*\.java"/>
    <suppress checks="DesignForExtension" files=".*[/\\]dto[/\\].*\.java"/>
    
    <!-- 工具类抑制 -->
    <!-- 应用启动类和测试启动类允许没有私有构造器 -->
    <suppress checks="HideUtilityClassConstructor" files=".*Application\.java"/>
    <!-- 工具类的参数final检查可以放宽 -->
    <suppress checks="FinalParameters" files=".*[/\\]util[/\\].*\.java"/>
    <suppress checks="DesignForExtension" files=".*[/\\]util[/\\].*\.java"/>
    
    <!-- 安全类抑制 -->
    <!-- Spring Security相关类需要灵活配置 -->
    <suppress checks="FinalParameters" files=".*[/\\]security[/\\].*\.java"/>
    <suppress checks="DesignForExtension" files=".*[/\\]security[/\\].*\.java"/>
    
    <!-- Repository接口抑制 -->
    <!-- Repository接口的参数不需要final -->
    <suppress checks="FinalParameters" files=".*[/\\]repository[/\\].*\.java"/>
    
    <!-- 通用抑制 -->
    <!-- 避免星号导入检查，在某些情况下星号导入是合理的 -->
    <suppress checks="AvoidStarImport" files=".*\.java"/>
    
    <!-- 魔术数字在某些上下文中是可接受的 -->
    <suppress checks="MagicNumber" files=".*[/\\]pdf[/\\].*\.java"/>
    <suppress checks="MagicNumber" files=".*[/\\]config[/\\].*\.java"/>
    
    <!-- 方法长度检查放宽 -->
    <suppress checks="MethodLength" files=".*[/\\]service[/\\].*\.java"/>
    
    <!-- 可执行语句数量检查放宽 -->
    <suppress checks="ExecutableStatementCount" files=".*[/\\]service[/\\].*\.java"/>
    
    <!-- Switch语句的default检查放宽 -->
    <suppress checks="MissingSwitchDefault" files=".*\.java"/>
    
    <!-- 大括号检查在某些简单情况下可以放宽 -->
    <suppress checks="NeedBraces" files=".*\.java"/>
</suppressions>