name: Assessment
channels:
  - conda-forge
  - defaults
dependencies:
  # 核心Python环境
  - python=3.11
  - pip
  
  # 基础开发工具
  - git
  - curl
  - wget
  - tree
  
  # Node.js（让conda自动选择兼容版本）
  - nodejs>=18
  - npm
  
  # Java环境（使用Java 17 LTS）
  - openjdk=17
  - maven
  
  # Python包（通过pip安装最新稳定版）
  - pip:
    # 数据库驱动
    - psycopg2-binary==2.9.9
    - redis==5.0.3
    
    # 项目管理工具
    - poetry==1.8.2
    - pre-commit==3.6.2
    
    # 测试工具
    - pytest==8.1.1
    - pytest-cov==4.0.0
    
    # 代码质量工具
    - black==24.3.0
    - ruff==0.3.2
    - isort==5.13.2
    
    # 文档工具
    - mkdocs==1.5.3
    - mkdocs-material==9.5.13
    
    # 部署工具
    - docker-compose==2.24.6
    
    # 监控工具
    - prometheus-client==0.20.0
    
    # 实用工具
    - requests==2.31.0
    - pyyaml==6.0.1
    - python-dotenv==1.0.1
    - typer==0.9.0
    - rich==13.7.1