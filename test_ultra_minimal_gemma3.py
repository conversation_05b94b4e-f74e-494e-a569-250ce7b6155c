#!/usr/bin/env python3
"""
极简版提示词测试gemma-3模型
专门突破token限制问题
"""

import requests
import json
import time
from datetime import datetime

def test_ultra_minimal_gemma3():
    """使用极简版提示词测试gemma-3模型"""
    print("🚀 极简版提示词gemma-3模型测试")
    print("=" * 60)
    
    # 配置
    lm_studio_url = "http://192.168.1.225:1234"
    
    # 极简提示词（仅100字符左右）
    ultra_minimal_prompt = """请分析文档并设计PostgreSQL数据库。

输出要求：
1. 表名和用途
2. 完整建表SQL
3. 字段说明JSON

必须包含：主键、created_at、updated_at字段。
SQL必须可执行。"""
    
    # 读取文档（仅前2000字符）
    print("📄 读取文档前2000字符...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        document = f.read()[:2000]  # 只取前2000字符
    
    # 组合提示词
    full_prompt = ultra_minimal_prompt + "\n\n文档内容：\n" + document
    
    try:
        print(f"🔍 连接到LM Studio: {lm_studio_url}")
        
        # 获取模型列表
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法连接到LM Studio")
            return False
        
        models_data = models_response.json()
        selected_model = None
        
        # 找到gemma模型
        for model in models_data['data']:
            if 'gemma' in model['id'].lower():
                selected_model = model['id']
                break
        
        if not selected_model:
            print("❌ 未找到gemma模型")
            return False
        
        print(f"✅ 选择模型: {selected_model}")
        print(f"📊 提示词总长度: {len(full_prompt)} 字符")
        print(f"📄 文档长度: {len(document)} 字符")
        print(f"🎯 策略: 极简提示词 + 截断文档")
        
        # 发送请求
        request_body = {
            "model": selected_model,
            "messages": [{"role": "user", "content": full_prompt}],
            "stream": False
        }
        
        print("⏳ 发送请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=300  # 5分钟超时
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ gemma-3极简版测试成功！")
                print(f"⏱️ 处理时间: {processing_time:.1f}秒")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 基础质量检查
                has_sql = 'CREATE TABLE' in ai_response.upper()
                has_json = '{' in ai_response and '}' in ai_response
                has_created_at = 'created_at' in ai_response.lower()
                has_updated_at = 'updated_at' in ai_response.lower()
                has_primary_key = 'PRIMARY KEY' in ai_response.upper()
                
                print(f"\n📊 极简版质量检查:")
                print(f"   ✅ SQL建表: {'通过' if has_sql else '❌未通过'}")
                print(f"   ✅ JSON格式: {'通过' if has_json else '❌未通过'}")
                print(f"   ✅ created_at: {'通过' if has_created_at else '❌未通过'}")
                print(f"   ✅ updated_at: {'通过' if has_updated_at else '❌未通过'}")
                print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌未通过'}")
                
                # 计算基础分数
                basic_score = 0
                if has_sql: basic_score += 30
                if has_json: basic_score += 20
                if has_created_at: basic_score += 15
                if has_updated_at: basic_score += 15
                if has_primary_key: basic_score += 20
                
                print(f"\n🏆 极简版评分: {basic_score}/100分")
                
                # 保存结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/ultra_minimal_gemma3_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 极简版提示词gemma-3测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**模型**: {selected_model}\n")
                    f.write(f"**提示词版本**: 极简版（突破token限制）\n")
                    f.write(f"**处理时间**: {processing_time:.1f}秒\n")
                    f.write(f"**文档长度**: {len(document)} 字符（截断）\n")
                    f.write(f"**基础评分**: {basic_score}/100分\n\n")
                    f.write("---\n\n")
                    f.write("## gemma-3模型生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 结果已保存: {result_file}")
                
                # 显示响应预览
                print(f"\n📄 响应预览:")
                print("=" * 60)
                print(ai_response[:500] + "..." if len(ai_response) > 500 else ai_response)
                print("=" * 60)
                
                return True
            else:
                print("❌ 响应格式异常")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            if response.status_code == 400:
                print("🔍 可能仍然存在token限制问题")
                error_text = response.text[:500]
                print(f"错误详情: {error_text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🎯 极简版提示词突破token限制测试")
    print("=" * 60)
    print("💡 策略: 最简提示词 + 文档截断")
    print("🎯 目标: 突破gemma-3的8192 token限制")
    print("=" * 60)
    
    if test_ultra_minimal_gemma3():
        print("\n✅ 极简版测试成功!")
        print("💡 证明了即使在严格token限制下也能生成基础数据库设计")
    else:
        print("\n❌ 极简版测试失败")