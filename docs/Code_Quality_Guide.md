# Assessment项目代码质量指南

## 概述

本文档描述了Assessment项目的代码质量标准、工具配置和检查流程。

## 代码质量检查脚本

### 使用方法

```bash
# 完整检查（推荐）
./scripts/code-quality-check.sh --report

# 仅检查后端
./scripts/code-quality-check.sh --backend-only

# 仅检查前端
./scripts/code-quality-check.sh --frontend-only

# 自动修复可修复的问题
./scripts/code-quality-check.sh --fix

# 查看帮助
./scripts/code-quality-check.sh --help
```

### 检查内容

#### 后端Java代码
- ✅ **编译检查**: Maven编译是否通过
- ✅ **单元测试**: 测试是否通过
- ✅ **代码覆盖率**: JaCoCo覆盖率报告
- ✅ **静态分析**: SpotBugs问题检测
- ✅ **代码风格**: Checkstyle规范检查
- ✅ **常见问题**: TODO/FIXME、System.out.println、长方法检测

#### 前端代码
- ✅ **ESLint检查**: JavaScript/TypeScript代码规范
- ✅ **TypeScript检查**: 类型检查
- ✅ **常见问题**: console.log、TODO/FIXME检测
- ✅ **依赖检查**: package.json和node_modules状态

#### 通用检查
- ✅ **Git状态**: 未提交更改、大文件检测
- ✅ **文件编码**: UTF-8编码检查
- ✅ **行尾符**: CRLF/LF统一性检查
- ✅ **空白字符**: 行尾空白字符检测

## 代码规范

### Java代码规范

#### 命名规范
- **类名**: PascalCase (如: `AssessmentService`)
- **方法名**: camelCase (如: `createAssessment`)
- **变量名**: camelCase (如: `assessmentId`)
- **常量名**: UPPER_SNAKE_CASE (如: `MAX_RETRY_COUNT`)
- **包名**: 小写，用点分隔 (如: `com.assessment.service`)

#### 代码结构
```java
// 好的示例
@Service
public class AssessmentService {
    
    private static final int MAX_RETRY_COUNT = 3;
    
    private final AssessmentRepository repository;
    
    public AssessmentService(AssessmentRepository repository) {
        this.repository = repository;
    }
    
    public Assessment createAssessment(CreateAssessmentRequest request) {
        // 方法体不超过50行
        validateRequest(request);
        return repository.save(buildAssessment(request));
    }
    
    private void validateRequest(CreateAssessmentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
    }
}
```

#### 避免的模式
```java
// 避免使用System.out.println
System.out.println("Debug info"); // ❌
log.info("Debug info"); // ✅

// 避免魔法数字
if (age > 65) { // ❌
if (age > RETIREMENT_AGE) { // ✅

// 避免过长的方法
public void longMethod() {
    // 超过50行的方法应该拆分
}
```

### 前端代码规范

#### Vue组件规范
```vue
<template>
  <div class="assessment-form">
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

interface AssessmentForm {
  name: string;
  age: number;
}

const formRef = ref<FormInstance>();
const form = reactive<AssessmentForm>({
  name: '',
  age: 0,
});

const rules: FormRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
};
</script>

<style scoped>
.assessment-form {
  padding: 20px;
}
</style>
```

#### TypeScript规范
```typescript
// 使用明确的类型定义
interface User {
  id: number;
  name: string;
  email?: string; // 可选属性
}

// 避免使用any
const data: any = {}; // ❌
const data: User = {}; // ✅

// 使用类型断言时要谨慎
const user = data as User; // 确保data确实是User类型
```

## 工具配置

### ESLint配置 (.eslintrc.js)
项目根目录的`.eslintrc.js`包含了完整的ESLint规则配置，包括：
- Vue 3 + TypeScript支持
- 代码质量规则
- 安全规则
- 代码风格规则

### Prettier配置 (.prettierrc)
统一的代码格式化配置：
- 使用单引号
- 行宽80字符
- 使用分号
- LF行尾符

### Checkstyle配置 (backend/checkstyle.xml)
Java代码风格检查配置：
- 命名约定检查
- 代码结构检查
- 常见问题检测

## IDE集成

### IntelliJ IDEA (Java开发)
1. 安装插件：
   - Checkstyle-IDEA
   - SonarLint
   - Save Actions

2. 配置Checkstyle：
   - File → Settings → Tools → Checkstyle
   - 添加配置文件：`backend/checkstyle.xml`

3. 配置代码格式化：
   - File → Settings → Editor → Code Style → Java
   - 导入Google Java Style或配置自定义风格

### VS Code (前端开发)
1. 安装扩展：
   - ESLint
   - Prettier
   - Vetur (Vue 2) 或 Volar (Vue 3)
   - SonarLint

2. 配置settings.json：
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "eslint.validate": [
    "javascript",
    "typescript",
    "vue"
  ],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 持续集成

### GitHub Actions配置示例
```yaml
name: Code Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Run quality check
      run: ./scripts/code-quality-check.sh --report
    
    - name: Upload quality reports
      uses: actions/upload-artifact@v3
      with:
        name: quality-reports
        path: quality-reports/
```

## 质量指标

### 目标指标
- **测试覆盖率**: ≥ 80%
- **代码重复率**: ≤ 3%
- **圈复杂度**: ≤ 10
- **方法长度**: ≤ 50行
- **类长度**: ≤ 500行
- **ESLint错误**: 0个
- **TypeScript错误**: 0个

### 质量门禁
在合并代码前，必须满足：
1. 所有单元测试通过
2. 代码覆盖率不降低
3. 无ESLint错误
4. 无TypeScript类型错误
5. 通过代码审查

## 最佳实践

### 开发流程
1. **开发前**: 运行`./scripts/code-quality-check.sh`确保环境正常
2. **开发中**: 使用IDE的实时检查功能
3. **提交前**: 运行`./scripts/code-quality-check.sh --fix --report`
4. **提交时**: 使用规范的提交信息格式
5. **合并前**: 确保所有质量检查通过

### 代码审查要点
- 逻辑正确性
- 性能考虑
- 安全性检查
- 可读性和可维护性
- 测试覆盖
- 文档完整性

### 技术债务管理
- 定期运行质量检查
- 及时修复TODO/FIXME
- 重构复杂代码
- 更新过时依赖
- 改进测试覆盖率

## 常见问题

### Q: 如何处理遗留代码的质量问题？
A: 采用渐进式改进策略：
1. 新代码严格遵循规范
2. 修改旧代码时同步改进
3. 定期重构高风险模块

### Q: 质量检查太严格，影响开发效率怎么办？
A: 
1. 可以临时禁用特定规则（添加注释）
2. 在团队中讨论调整规则
3. 使用`--fix`选项自动修复

### Q: 如何在团队中推广代码质量实践？
A:
1. 提供培训和文档
2. 在代码审查中强调质量
3. 设置质量门禁
4. 定期分享质量报告

## 相关资源

- [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)
- [Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)
- [Vue.js风格指南](https://vuejs.org/style-guide/)
- [TypeScript编码规范](https://typescript-eslint.io/rules/)
- [ESLint规则文档](https://eslint.org/docs/rules/)
- [Prettier配置选项](https://prettier.io/docs/en/options.html)