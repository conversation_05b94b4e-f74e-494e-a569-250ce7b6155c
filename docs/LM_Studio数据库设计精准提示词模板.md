# LM Studio数据库设计精准提示词模板

## 概述

基于LLM分析质量对比报告的发现，本文档提供了一套经过优化的提示词模板，旨在提升LM Studio中本地模型在PostgreSQL数据库设计方面的输出质量。

---

## 🎯 核心优化策略

### 1. 结构化提示词框架
- **角色定义** → **业务背景** → **技术要求** → **输出格式** → **质量标准**

### 2. 弥补LM Studio相对不足
- 增强业务逻辑理解
- 强化架构设计思维
- 提升扩展性考虑
- 优化性能设计意识

---

## 📋 精准提示词模板

### 模板1: 完整数据库设计（推荐）

```markdown
你是一个拥有15年经验的高级数据库架构师，专门负责医疗健康领域的PostgreSQL数据库设计。你对养老评估、医疗记录管理和数据安全合规有深入理解。

## 业务背景
我正在设计一个智慧养老评估平台的数据库，该系统将服务于：
- 养老机构（预计500+机构，10万+老年人）
- 评估师和护理人员（日均1000+评估）
- 管理人员和家属（查询和报告需求）
- 政府监管部门（数据统计和质量监控）

## 技术环境
- 数据库：PostgreSQL 15+
- 并发要求：100+同时在线用户
- 数据量：预计年增长100万+评估记录
- 性能要求：查询响应时间<500ms
- 合规要求：医疗数据保护，审计追踪

## 请分析以下评估量表文档，设计完整的数据库架构：

{评估量表内容}

## 设计要求

### 1. 架构设计原则
- 采用模块化设计，支持多种评估量表类型
- 设计用户权限和多租户架构
- 考虑数据完整性和一致性约束
- 规划审计日志和版本控制
- 支持数据备份和恢复机制

### 2. 表结构设计
- 主表：核心业务实体（老年人、机构、用户、评估记录）
- 明细表：评估项目和分数详情
- 配置表：量表定义、评分规则、权限配置
- 日志表：操作审计、数据变更历史

### 3. 性能优化
- 为频繁查询字段创建合适索引
- 设计分区策略（按时间、机构等）
- 考虑读写分离和缓存策略
- 优化大数据量统计查询

### 4. 数据安全
- 敏感数据加密存储设计
- 行级安全策略（RLS）
- 数据脱敏和匿名化支持
- 符合医疗数据保护法规

## 输出格式要求

请按以下结构提供完整的数据库设计：

### 第一部分：架构概述
```markdown
## 1. 数据库架构总览
- 核心表数量：X个
- 主要业务模块：列出5-8个核心模块
- 数据流向：简述数据的创建、流转、归档流程
- 扩展策略：说明如何支持新的评估量表类型
```

### 第二部分：完整SQL语句
```sql
-- ==============================================
-- 智慧养老评估平台数据库设计
-- 版本：v1.0
-- 创建日期：{当前日期}
-- ==============================================

-- 1. 创建数据库和扩展
CREATE DATABASE elderly_assessment_platform;
\c elderly_assessment_platform;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 2. 核心业务表
-- 2.1 机构管理
CREATE TABLE institutions (
    id SERIAL PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'nursing_home', 'community_center', 'hospital'
    level VARCHAR(20), -- '一级', '二级', '三级'
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    license_number VARCHAR(100),
    established_date DATE,
    capacity INTEGER DEFAULT 0,
    current_residents INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'suspended', 'closed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

-- 添加索引和约束
CREATE INDEX idx_institutions_code ON institutions(code);
CREATE INDEX idx_institutions_type ON institutions(type);
CREATE INDEX idx_institutions_status ON institutions(status);

-- 添加注释
COMMENT ON TABLE institutions IS '机构信息表';
COMMENT ON COLUMN institutions.code IS '机构编码';
COMMENT ON COLUMN institutions.type IS '机构类型';

-- 继续其他表的完整定义...
```

### 第三部分：JSON配置数据
```json
{
  "database_design": {
    "name": "elderly_assessment_platform",
    "version": "1.0",
    "tables": [
      {
        "name": "institutions",
        "purpose": "机构信息管理",
        "fields": [
          {
            "name": "id",
            "type": "SERIAL",
            "nullable": false,
            "isPrimaryKey": true,
            "comment": "机构主键ID",
            "importance": 100
          },
          {
            "name": "code",
            "type": "VARCHAR",
            "length": "20",
            "nullable": false,
            "isUnique": true,
            "comment": "机构唯一编码",
            "importance": 95
          }
        ],
        "indexes": [
          {
            "name": "idx_institutions_code",
            "columns": ["code"],
            "type": "btree",
            "unique": true
          }
        ],
        "relationships": [
          {
            "type": "one_to_many",
            "target_table": "users",
            "foreign_key": "institution_id"
          }
        ]
      }
    ],
    "performance_considerations": {
      "partitioning": "按年份分区assessment_records表",
      "indexing": "为查询频繁的外键和日期字段创建索引",
      "caching": "评估量表配置数据适合缓存"
    },
    "security_features": {
      "encryption": "敏感字段使用pgcrypto加密",
      "row_level_security": "基于机构ID的行级安全",
      "audit_trail": "所有关键操作记录审计日志"
    }
  }
}
```

## 质量检查清单
生成的设计必须包含：
✅ 至少8个核心业务表
✅ 完整的主键、外键约束
✅ 合适的索引策略（至少15个索引）
✅ 数据类型选择合理
✅ 包含审计字段（created_at, updated_at, created_by, updated_by）
✅ 考虑软删除机制
✅ 支持多租户架构
✅ 性能优化建议
✅ 安全性考虑
✅ 扩展性设计

请确保：
1. 所有SQL语句语法正确，可直接执行
2. 字段命名遵循snake_case规范
3. 表名使用复数形式
4. 包含完整的注释和文档
5. 考虑实际业务场景的复杂性
```

---

### 模板2: 单表优化设计（轻量级）

```markdown
你是一个PostgreSQL性能优化专家，专门负责单表结构的深度优化设计。

## 任务目标
基于以下评估量表，设计一个高性能的PostgreSQL表结构，重点关注：
- 字段类型的精确选择
- 约束条件的完整性
- 索引策略的优化
- 查询性能的提升

{评估量表内容}

## 设计要求

### 1. 字段设计精准度
- 数值字段：选择最合适的数据类型（SMALLINT vs INTEGER vs BIGINT）
- 文本字段：精确估算长度，避免浪费存储空间
- 枚举字段：使用CHECK约束或ENUM类型
- 时间字段：区分DATE、TIMESTAMP、TIMESTAMPTZ

### 2. 约束设计完整性
- 主键约束：选择合适的主键策略
- 外键约束：明确引用关系
- 检查约束：业务规则的数据库层面验证
- 唯一约束：防止重复数据

### 3. 索引策略优化
- 单列索引：为高频查询字段
- 复合索引：为多条件查询
- 部分索引：为条件查询优化
- 函数索引：为计算字段查询

### 4. 性能考虑
- 表分区策略建议
- 统计信息收集
- 查询计划优化提示

## 输出格式

```sql
-- ============================================
-- 表名：{建议的表名}
-- 目的：{表的业务用途}
-- 预估数据量：{预估记录数}
-- 主要查询模式：{列出3-5种主要查询}
-- ============================================

CREATE TABLE {表名} (
    -- 主键设计
    id BIGSERIAL PRIMARY KEY,
    
    -- 业务标识
    assessment_id VARCHAR(50) NOT NULL UNIQUE,
    
    -- 评估项目字段（精确类型选择）
    {字段名} SMALLINT NOT NULL CHECK ({字段名} BETWEEN {最小值} AND {最大值}),
    
    -- 汇总字段
    total_score NUMERIC(5,2) NOT NULL CHECK (total_score >= 0),
    result_level VARCHAR(20) NOT NULL CHECK (result_level IN ('完全依赖', '重度依赖', '中度依赖', '轻度依赖', '完全自理')),
    
    -- 关联字段
    elderly_id INTEGER NOT NULL,
    assessor_id INTEGER NOT NULL,
    institution_id INTEGER NOT NULL,
    
    -- 业务字段
    assessment_date DATE NOT NULL,
    notes TEXT,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'completed', 'reviewed', 'archived')),
    is_valid BOOLEAN DEFAULT true,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    
    -- 版本控制
    version INTEGER DEFAULT 1,
    
    -- 软删除
    deleted_at TIMESTAMP,
    deleted_by INTEGER
);

-- 索引策略
-- 主要查询索引
CREATE INDEX idx_{表名}_elderly_date ON {表名}(elderly_id, assessment_date DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_{表名}_institution_date ON {表名}(institution_id, assessment_date DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_{表名}_assessor_date ON {表名}(assessor_id, assessment_date DESC) WHERE deleted_at IS NULL;

-- 状态查询索引
CREATE INDEX idx_{表名}_status ON {表名}(status) WHERE deleted_at IS NULL;

-- 分数范围查询索引
CREATE INDEX idx_{表名}_total_score ON {表名}(total_score) WHERE deleted_at IS NULL AND status = 'completed';

-- 复合查询索引
CREATE INDEX idx_{表名}_institution_status_date ON {表名}(institution_id, status, assessment_date DESC) WHERE deleted_at IS NULL;

-- 外键约束
ALTER TABLE {表名} ADD CONSTRAINT fk_{表名}_elderly 
    FOREIGN KEY (elderly_id) REFERENCES elderly_persons(id);
ALTER TABLE {表名} ADD CONSTRAINT fk_{表名}_assessor 
    FOREIGN KEY (assessor_id) REFERENCES users(id);
ALTER TABLE {表名} ADD CONSTRAINT fk_{表名}_institution 
    FOREIGN KEY (institution_id) REFERENCES institutions(id);

-- 触发器（自动更新updated_at）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表注释
COMMENT ON TABLE {表名} IS '{详细的表用途说明}';

-- 字段注释（为每个字段添加详细注释）
COMMENT ON COLUMN {表名}.{字段名} IS '{字段的详细说明，包括取值范围和业务含义}';

-- 性能优化建议
-- 1. 分区策略：建议按assessment_date年份分区
-- 2. 统计信息：定期ANALYZE表以保持查询计划最优
-- 3. 维护任务：定期清理deleted_at不为空的记录
```

请确保生成的设计：
✅ 字段类型选择最优（考虑存储空间和性能）
✅ 包含完整的业务约束
✅ 索引策略覆盖主要查询场景
✅ 考虑了并发访问和锁争用
✅ 包含性能监控和优化建议
```

---

### 模板3: 评估量表专用模板

```markdown
你是医疗信息系统的数据库设计专家，拥有丰富的评估量表数字化经验。你深度理解各种医疗评估工具（ADL、IADL、MMSE、GDS等）的数据特征和使用模式。

## 专业背景
- 熟悉国际标准评估量表的结构特点
- 了解评估数据的统计分析需求
- 掌握医疗数据的合规性要求
- 具备大规模评估数据的性能优化经验

## 评估量表分析任务

请分析以下评估量表，设计一个既支持当前量表又具备扩展性的数据库结构：

{评估量表内容}

## 设计策略

### 1. 量表结构解析
- 识别评估维度和子项目
- 分析评分方式和计算逻辑
- 理解结果分级和临床意义
- 考虑量表的标准化程度

### 2. 数据模型选择
优先考虑以下模式：
- **单表模式**：适用于固定结构的标准量表
- **EAV模式**：适用于多变的自定义量表
- **混合模式**：核心字段单表 + 扩展字段EAV

### 3. 评估数据特征
- 评估项目的数据类型（数值、选择、文本）
- 分数计算的复杂度（简单求和 vs 权重计算）
- 结果解释的层次（项目级 + 总体级）
- 时间序列分析需求（趋势、对比）

## 输出要求

### 第一部分：量表分析报告
```markdown
## 量表特征分析
- **量表类型**: {如：ADL量表、认知评估、情绪量表}
- **评估维度**: {列出主要评估维度}
- **项目数量**: {总项目数}
- **评分方式**: {如：Likert量表、二分类、多选}
- **分数范围**: {最低分-最高分}
- **结果分级**: {列出所有等级及分数范围}
- **临床意义**: {简述评估结果的临床应用价值}

## 数据特征
- **数据类型分布**: 数值型{X}项, 分类型{Y}项, 文本型{Z}项
- **必填字段**: {列出核心必填项目}
- **计算字段**: {需要通过计算得出的字段}
- **关联数据**: {需要关联的其他信息}
```

### 第二部分：数据库设计
```sql
-- ==============================================
-- {量表名称}数字化数据库设计
-- 设计模式：{选择的数据模型}
-- 适用场景：{描述适用的业务场景}
-- ==============================================

-- 主评估记录表
CREATE TABLE {量表标识}_assessments (
    id BIGSERIAL PRIMARY KEY,
    
    -- 评估基本信息
    assessment_id VARCHAR(50) UNIQUE NOT NULL,
    elderly_id INTEGER NOT NULL,
    assessor_id INTEGER NOT NULL,
    institution_id INTEGER NOT NULL,
    
    -- 评估执行信息
    assessment_date DATE NOT NULL,
    assessment_duration INTEGER, -- 评估耗时（分钟）
    assessment_location VARCHAR(50), -- 'home', 'institution', 'hospital'
    
    -- 具体评估项目（根据量表结构生成）
    {为每个评估项目生成对应字段},
    
    -- 计算结果
    total_score DECIMAL(8,2),
    max_possible_score DECIMAL(8,2),
    score_percentage DECIMAL(5,2),
    result_level VARCHAR(50),
    risk_level VARCHAR(20), -- 'low', 'medium', 'high'
    
    -- 临床信息
    clinical_notes TEXT,
    recommendations TEXT,
    follow_up_date DATE,
    
    -- 质量控制
    is_complete BOOLEAN DEFAULT false,
    completion_rate DECIMAL(5,2), -- 完成度百分比
    data_quality_score INTEGER, -- 数据质量评分
    
    -- 审核流程
    status VARCHAR(20) DEFAULT 'draft',
    reviewed_by INTEGER,
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    
    -- 标准审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    version INTEGER DEFAULT 1,
    
    -- 软删除
    deleted_at TIMESTAMP,
    deleted_by INTEGER,
    
    -- 约束定义
    CONSTRAINT chk_{量表标识}_total_score 
        CHECK (total_score >= 0 AND total_score <= max_possible_score),
    CONSTRAINT chk_{量表标识}_percentage 
        CHECK (score_percentage >= 0 AND score_percentage <= 100),
    CONSTRAINT chk_{量表标识}_status 
        CHECK (status IN ('draft', 'completed', 'reviewed', 'approved', 'archived'))
);

-- 评估项目明细表（支持扩展和审计）
CREATE TABLE {量表标识}_assessment_items (
    id BIGSERIAL PRIMARY KEY,
    assessment_id BIGINT REFERENCES {量表标识}_assessments(id),
    item_code VARCHAR(20) NOT NULL, -- 项目编码
    item_name VARCHAR(200) NOT NULL, -- 项目名称
    item_category VARCHAR(50), -- 项目分类
    response_value TEXT, -- 原始回答
    score_value DECIMAL(8,2), -- 得分
    max_score DECIMAL(8,2), -- 满分
    is_scored BOOLEAN DEFAULT true, -- 是否计入总分
    notes TEXT, -- 项目备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 量表配置表（支持多版本和自定义）
CREATE TABLE {量表标识}_scale_config (
    id SERIAL PRIMARY KEY,
    scale_name VARCHAR(100) NOT NULL,
    scale_version VARCHAR(20) NOT NULL,
    item_code VARCHAR(20) NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_order INTEGER NOT NULL,
    item_type VARCHAR(20) NOT NULL, -- 'numeric', 'choice', 'text', 'boolean'
    options_json JSONB, -- 选项配置
    scoring_rules_json JSONB, -- 评分规则
    is_required BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(scale_version, item_code)
);

-- 性能优化索引
-- 主要查询模式索引
CREATE INDEX idx_{量表标识}_elderly_date 
    ON {量表标识}_assessments(elderly_id, assessment_date DESC) 
    WHERE deleted_at IS NULL;

CREATE INDEX idx_{量表标识}_institution_date 
    ON {量表标识}_assessments(institution_id, assessment_date DESC) 
    WHERE deleted_at IS NULL;

-- 分析查询索引
CREATE INDEX idx_{量表标识}_score_level 
    ON {量表标识}_assessments(total_score, result_level) 
    WHERE deleted_at IS NULL AND status = 'approved';

-- 状态和质量索引
CREATE INDEX idx_{量表标识}_status_quality 
    ON {量表标识}_assessments(status, data_quality_score) 
    WHERE deleted_at IS NULL;

-- 时间序列分析索引
CREATE INDEX idx_{量表标识}_time_series 
    ON {量表标识}_assessments(elderly_id, assessment_date, total_score) 
    WHERE deleted_at IS NULL AND status IN ('reviewed', 'approved');

-- 明细表索引
CREATE INDEX idx_{量表标识}_items_assessment 
    ON {量表标识}_assessment_items(assessment_id, item_code);

CREATE INDEX idx_{量表标识}_items_category 
    ON {量表标识}_assessment_items(item_category, score_value);
```

### 第三部分：扩展功能设计
```sql
-- 统计分析视图
CREATE VIEW {量表标识}_assessment_summary AS
SELECT 
    institution_id,
    DATE_TRUNC('month', assessment_date) as month,
    COUNT(*) as total_assessments,
    AVG(total_score) as avg_score,
    STDDEV(total_score) as score_stddev,
    COUNT(*) FILTER (WHERE result_level = '完全自理') as self_care_count,
    COUNT(*) FILTER (WHERE result_level = '完全依赖') as dependent_count
FROM {量表标识}_assessments 
WHERE deleted_at IS NULL AND status = 'approved'
GROUP BY institution_id, DATE_TRUNC('month', assessment_date);

-- 趋势分析函数
CREATE OR REPLACE FUNCTION get_{量表标识}_trend(
    p_elderly_id INTEGER,
    p_months INTEGER DEFAULT 12
)
RETURNS TABLE (
    assessment_date DATE,
    total_score DECIMAL,
    result_level VARCHAR,
    score_change DECIMAL,
    trend_direction VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    WITH score_data AS (
        SELECT 
            a.assessment_date,
            a.total_score,
            a.result_level,
            LAG(a.total_score) OVER (ORDER BY a.assessment_date) as prev_score
        FROM {量表标识}_assessments a
        WHERE a.elderly_id = p_elderly_id 
            AND a.assessment_date >= CURRENT_DATE - INTERVAL '{p_months} months'
            AND a.deleted_at IS NULL 
            AND a.status = 'approved'
        ORDER BY a.assessment_date
    )
    SELECT 
        sd.assessment_date,
        sd.total_score,
        sd.result_level,
        COALESCE(sd.total_score - sd.prev_score, 0) as score_change,
        CASE 
            WHEN sd.total_score > sd.prev_score THEN 'improving'
            WHEN sd.total_score < sd.prev_score THEN 'declining'
            ELSE 'stable'
        END as trend_direction
    FROM score_data sd;
END;
$$ LANGUAGE plpgsql;
```

请确保设计包含：
✅ 量表特征的准确识别和建模
✅ 支持量表版本管理和配置化
✅ 完整的评估流程状态管理
✅ 丰富的统计分析支持
✅ 时间序列和趋势分析能力
✅ 数据质量控制机制
✅ 性能优化的索引策略
✅ 临床应用的实用功能
```

---

## 🚀 使用指南

### 1. 模板选择建议

| 应用场景 | 推荐模板 | 适用条件 |
|----------|----------|----------|
| **完整系统设计** | 模板1 | 需要完整的多表架构设计 |
| **单表性能优化** | 模板2 | 专注于单个表的深度优化 |
| **标准评估量表** | 模板3 | 医疗评估量表专门设计 |

### 2. 提示词优化技巧

#### 🎯 **关键优化要素**
- **具体化角色**：资深数据库架构师 vs 普通开发者
- **明确业务背景**：医疗、养老、评估等领域特定知识
- **技术约束**：PostgreSQL版本、性能要求、并发量
- **输出格式**：结构化的SQL + JSON + 文档

#### 📋 **质量提升检查点**
- 是否包含完整的约束定义
- 是否考虑了索引优化策略  
- 是否包含审计和版本控制
- 是否考虑了扩展性和维护性

### 3. 实际应用建议

#### 🔧 **项目集成流程**
1. **选择合适模板** → 根据具体需求选择模板
2. **定制化调整** → 根据实际评估量表内容调整
3. **LM Studio生成** → 使用优化后的提示词
4. **人工review** → 检查和优化生成结果
5. **测试验证** → 在开发环境测试SQL执行

#### 📊 **效果评估标准**
- SQL语法正确性：100%
- 业务逻辑完整性：90%+
- 性能优化合理性：85%+
- 扩展性考虑：80%+

---

## 📈 预期效果

基于这套精准提示词模板，LM Studio在数据库设计方面的质量预期提升：

- **代码质量**：从91分提升至95分+
- **业务理解**：从75分提升至88分+
- **架构思维**：从70分提升至85分+
- **扩展性设计**：从65分提升至82分+

通过结构化的提示词工程，弥补LM Studio相对于Claude在架构设计方面的不足，使其在数据库设计领域达到接近专家级的输出质量。