# 智慧养老评估平台数据库架构设计方案

**文档版本**: v1.0  
**创建日期**: 2025-06-21  
**最后更新**: 2025-06-21  
**负责人**: 开发团队  

## 📋 目录
- [1. 项目背景](#1-项目背景)
- [2. 当前架构分析](#2-当前架构分析)
- [3. 问题分析](#3-问题分析)
- [4. 方案评估](#4-方案评估)
- [5. 推荐架构方案](#5-推荐架构方案)
- [6. 实施计划](#6-实施计划)
- [7. 性能优化策略](#7-性能优化策略)
- [8. 运维监控](#8-运维监控)

---

## 1. 项目背景

智慧养老评估平台是一个**多租户SaaS平台**，需要支持：
- 多个机构同时使用（养老院、社区中心、医院等）
- 多种评估量表（老年人能力评估、情绪快评、interRAI等）
- 大规模并发评估操作
- 严格的数据安全和隔离要求

### 核心挑战
1. **多租户数据隔离** - 机构间数据完全隔离
2. **量表多样性** - 不同行业的评估量表结构差异巨大
3. **扩展性要求** - 支持数百个机构，数万用户同时使用
4. **数据安全合规** - 医疗数据保护法律法规要求

---

## 2. 当前架构分析

### 2.1 现有数据库结构

```mermaid
erDiagram
    USERS ||--o{ ASSESSMENT_TASKS : creates
    INSTITUTIONS ||--o{ USERS : contains
    ASSESSMENT_SCALES ||--o{ ASSESSMENT_TASKS : uses
    ASSESSMENT_TASKS ||--o{ ASSESSMENT_RECORDS : generates
    ASSESSMENT_RECORDS ||--o{ ASSESSMENT_RESULTS : contains
    ELDERLY_PERSONS ||--o{ ASSESSMENT_RECORDS : assessed

    USERS {
        string id PK
        string username
        string institutionId FK
        enum role
        boolean isActive
    }
    
    INSTITUTIONS {
        string id PK
        string name
        string code
        enum type
        boolean isActive
    }
    
    ASSESSMENT_SCALES {
        string id PK
        string name
        string code
        jsonb formSchema
        jsonb scoringRules
        boolean isActive
    }
    
    ASSESSMENT_RECORDS {
        string id PK
        string elderlyId FK
        string scaleId FK
        string assessorId FK
        jsonb formData
        jsonb scoreData
        enum status
    }
```

### 2.2 当前架构优点
- ✅ **基础多租户支持** - 通过institutionId实现租户隔离
- ✅ **柔性量表设计** - JSON Schema支持动态表单结构
- ✅ **完整业务流程** - 覆盖评估全生命周期
- ✅ **审计追踪** - BaseEntity提供创建/更新时间戳

### 2.3 当前架构问题
- ❌ **隔离不彻底** - 所有租户数据在同一表中
- ❌ **查询性能** - 随着数据量增长，查询性能下降
- ❌ **安全风险** - 单一数据库，安全边界不清晰
- ❌ **扩展瓶颈** - 无法为不同租户独立扩展

---

## 3. 问题分析

### 3.1 多租户隔离问题

**问题描述**: 当前通过应用层过滤(WHERE institutionId = ?)实现租户隔离，存在安全风险

**风险评估**:
- 🔴 **数据泄露风险** - 应用程序bug可能导致跨租户数据访问
- 🔴 **性能问题** - 大量数据时，过滤查询性能差
- 🔴 **运维复杂** - 无法为单个租户进行数据维护操作

### 3.2 量表动态性挑战

**问题描述**: 不同行业的评估量表结构差异巨大，单一schema难以满足

**具体挑战**:
- 医疗行业量表 vs 养老行业量表结构完全不同
- 评分算法差异（线性评分 vs 权重评分 vs 复合评分）
- 数据验证规则不同
- 报告格式要求不同

### 3.3 扩展性瓶颈

**性能指标预测**:
```
预期规模:
- 机构数量: 500+
- 每机构用户: 50-200人
- 每日评估量: 10,000+次
- 数据存储: 100GB+/年
```

---

## 4. 方案评估

### 4.1 方案A: 每个量表一个数据库（用户提出）

#### 架构设计
```
Database Per Scale Architecture:
assessment_scale_elderly_ability/     # 老年人能力评估专用DB
assessment_scale_emotional_quick/     # 情绪快评专用DB  
assessment_scale_interrai/            # interRAI评估专用DB
assessment_scale_custom_hospital_a/   # 医院A自定义量表DB
...
```

#### 详细评估

**✅ 优点**:
- **完全隔离**: 量表间数据完全分离，互不影响
- **独立扩展**: 可为热门量表独立扩展资源
- **故障隔离**: 单个量表问题不影响其他量表
- **定制优化**: 可为特定量表优化数据库配置

**❌ 缺点**:
- **管理复杂度爆炸**: 500机构×10量表 = 5000个数据库
- **资源浪费严重**: 每个DB独立连接池，小量表资源利用率极低
- **数据孤岛严重**: 
  - 用户跨量表评估历史无法统一查询
  - 机构整体数据分析困难
  - 报告汇总需要跨多个数据库
- **事务一致性问题**: 跨量表操作无法保证ACID
- **运维成本高昂**: 
  - 备份恢复需协调数千个数据库
  - 版本升级复杂度指数级增长
  - 监控告警配置工作量巨大

**💰 成本分析**:
```
运维成本评估:
- 数据库实例成本: 5000个DB × $50/月 = $250,000/月
- 运维人力成本: 需要专门的DBA团队 (3-5人)
- 监控告警成本: 监控系统复杂度×10
- 备份存储成本: 存储成本×数据库数量
```

**🚫 强烈不推荐**: 成本收益比极差，技术债务巨大

### 4.2 方案B: 单数据库应用层隔离（当前方案）

#### 当前实现
```sql
-- 当前的租户隔离方式
SELECT * FROM assessment_records 
WHERE institution_id = ? AND elderly_id = ?;
```

**✅ 优点**:
- **实现简单**: 开发复杂度低
- **资源利用高**: 单一数据库资源集中利用
- **运维简单**: 只需维护一个数据库

**❌ 缺点**:
- **安全风险**: 应用层bug可能导致数据泄露
- **性能瓶颈**: 随着租户数量增长，查询性能下降
- **无法独立扩展**: 无法为大租户独立优化

### 4.3 方案C: 分层多租户架构（推荐方案）

#### 核心设计理念
```
Tenant-Aware Multi-Layer Architecture:
1. 租户维度隔离 (Institution Level)
2. 数据分区策略 (Partitioning)
3. 行级安全控制 (Row Level Security)
4. 柔性Schema设计 (JSON Schema + Strong Typing)
```

---

## 5. 推荐架构方案

**⚡ 重新设计结论**: 基于深度分析，推荐采用**"租户数据库 + 动态表结构"**的全新架构

### 5.1 全新架构总览

```mermaid
graph TB
    subgraph "元数据层 (Meta Database)"
        A[量表Schema注册中心] --> B[租户管理]
        A --> C[用户权限管理]  
        A --> D[数据字典]
    end
    
    subgraph "租户数据层 (Tenant Databases)"
        E[医院A数据库] --> F[老年评估表_hospital_a]
        E --> G[护理评估表_hospital_a]
        
        H[养老院B数据库] --> I[能力评估表_nursing_b]  
        H --> J[情绪评估表_nursing_b]
        
        K[社区C数据库] --> L[社区评估表_community_c]
    end
    
    subgraph "应用服务层"
        M[Schema管理服务] --> A
        N[租户路由服务] --> E
        N --> H  
        N --> K
        O[动态表生成服务] --> E
        O --> H
        O --> K
    end
```

### 5.2 核心设计理念

#### 5.2.1 架构核心原则
```
1. 每个租户(机构) = 一个独立数据库
2. 每个量表 = 该租户数据库中的动态生成表
3. 全局Schema注册中心 = 统一管理量表定义  
4. 自动化表结构生成 = 根据量表Schema自动创建表
```

#### 5.2.2 数据库命名规范
```
元数据库: system_meta
租户数据库: tenant_{tenant_code}
评估表: assessment_{scale_code}
```

### 5.3 元数据管理数据库设计

#### 5.3.1 核心元数据表结构
```sql
-- 元数据库: system_meta

-- 1. 租户管理表  
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    industry VARCHAR(50) NOT NULL, -- 行业类型
    database_name VARCHAR(100) NOT NULL, -- 对应的数据库名
    status VARCHAR(20) DEFAULT 'active',
    config JSONB, -- 租户特定配置
    created_at TIMESTAMP DEFAULT NOW()
);

-- 2. 量表Schema注册表
CREATE TABLE scale_schemas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    version VARCHAR(20) NOT NULL,
    industry VARCHAR(50), -- 适用行业
    schema_definition JSONB NOT NULL, -- 表结构定义
    form_config JSONB NOT NULL, -- 表单配置
    scoring_rules JSONB, -- 评分规则
    report_template JSONB, -- 报告模板
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. 租户量表关联表
CREATE TABLE tenant_scales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    schema_id UUID REFERENCES scale_schemas(id),
    table_name VARCHAR(100) NOT NULL, -- 在租户DB中的表名
    customizations JSONB, -- 租户自定义配置
    is_active BOOLEAN DEFAULT true,
    activated_at TIMESTAMP DEFAULT NOW()
);
```

#### 5.3.2 动态表结构Schema设计
```json
{
  "scale_schema_example": {
    "code": "elderly_ability_v2",
    "name": "老年人能力评估量表",
    "version": "2.0",
    "table_definition": {
      "base_fields": [
        {"name": "id", "type": "UUID", "constraint": "PRIMARY KEY"},
        {"name": "subject_id", "type": "UUID", "constraint": "NOT NULL"},
        {"name": "assessor_id", "type": "UUID", "constraint": "NOT NULL"},
        {"name": "assessment_date", "type": "TIMESTAMP", "constraint": "NOT NULL"}
      ],
      "custom_fields": [
        {"name": "daily_living_score", "type": "INTEGER"},
        {"name": "cognitive_score", "type": "INTEGER"},
        {"name": "total_score", "type": "INTEGER"},
        {"name": "form_data", "type": "JSONB"}
      ],
      "indexes": [
        {"columns": ["subject_id", "assessment_date"]},
        {"columns": ["assessor_id", "status"]}
      ]
    },
    "form_config": {
      "sections": [
        {
          "id": "daily_living",
          "title": "日常生活能力",
          "fields": [
            {
              "id": "eating",
              "type": "radio",
              "title": "进食能力",
              "options": [
                {"value": 1, "label": "完全依赖", "score": 1},
                {"value": 2, "label": "需要帮助", "score": 2},
                {"value": 3, "label": "基本独立", "score": 3}
              ]
            }
          ]
        }
      ]
    }
  }
}
```

### 5.4 租户数据库设计

#### 5.4.1 租户数据库模板
```sql
-- 租户数据库命名: tenant_{tenant_code}
-- 例如: tenant_hospital_a, tenant_nursing_home_b

-- 1. 基础审计表
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,  
    action VARCHAR(20) NOT NULL, -- INSERT/UPDATE/DELETE
    old_data JSONB,
    new_data JSONB,
    user_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 2. 评估对象表 (通用)
CREATE TABLE assessment_subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject_type VARCHAR(50) NOT NULL, -- elderly/patient/resident
    name VARCHAR(100) NOT NULL,
    id_number VARCHAR(50),
    basic_info JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 3. 动态生成的评估表 (根据Schema自动创建)
-- 表名格式: assessment_{scale_code}
-- 例如: assessment_elderly_ability, assessment_emotional_quick
-- 具体字段根据Schema定义动态生成
```

#### 5.4.2 动态表生成示例
```sql
-- 示例：根据老年人能力评估Schema生成的表
CREATE TABLE assessment_elderly_ability (
    -- 基础字段 (所有评估表共有)
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject_id UUID NOT NULL REFERENCES assessment_subjects(id),
    assessor_id UUID NOT NULL,
    assessment_date TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    
    -- 自定义字段 (根据Schema定义生成)
    daily_living_score INTEGER,
    cognitive_score INTEGER,
    mobility_score INTEGER,
    total_score INTEGER,
    result_level VARCHAR(20),
    form_data JSONB, -- 原始表单数据
    recommendations TEXT,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 自动创建索引
CREATE INDEX idx_elderly_ability_subject_date 
ON assessment_elderly_ability (subject_id, assessment_date DESC);

CREATE INDEX idx_elderly_ability_assessor_status 
ON assessment_elderly_ability (assessor_id, status);
```

### 5.5 核心服务架构

#### 5.5.1 Schema管理服务
```java
@Service
public class SchemaManagementService {
    
    /**
     * 注册新的量表Schema
     */
    public void registerSchema(ScaleSchemaDefinition definition) {
        // 1. 验证Schema合法性
        validateSchema(definition);
        
        // 2. 保存到Schema注册表
        scaleSchemaRepository.save(definition);
        
        // 3. 为已激活的租户自动创建表结构
        List<Tenant> activeTenants = getEligibleTenants(definition.getIndustry());
        for (Tenant tenant : activeTenants) {
            createTableForTenant(tenant, definition);
        }
    }
    
    /**
     * 为租户激活量表
     */
    public void activateScaleForTenant(UUID tenantId, UUID schemaId) {
        Tenant tenant = tenantRepository.findById(tenantId);
        ScaleSchema schema = schemaRepository.findById(schemaId);
        
        // 1. 在租户数据库中创建对应表
        String tableName = "assessment_" + schema.getCode();
        createTableInTenantDB(tenant.getDatabaseName(), tableName, schema.getTableDefinition());
        
        // 2. 记录租户量表关联
        TenantScale tenantScale = TenantScale.builder()
            .tenantId(tenantId)
            .schemaId(schemaId)
            .tableName(tableName)
            .build();
        tenantScaleRepository.save(tenantScale);
    }
}
```

#### 5.5.2 动态表生成服务
```java
@Service
public class DynamicTableService {
    
    /**
     * 根据Schema定义生成SQL建表语句
     */
    public String generateCreateTableSQL(String tableName, TableDefinition definition) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        // 基础字段
        for (FieldDefinition field : definition.getBaseFields()) {
            sql.append("  ").append(field.getName())
               .append(" ").append(field.getType());
            if (field.getConstraint() != null) {
                sql.append(" ").append(field.getConstraint());
            }
            sql.append(",\n");
        }
        
        // 自定义字段
        for (FieldDefinition field : definition.getCustomFields()) {
            sql.append("  ").append(field.getName())
               .append(" ").append(field.getType());
            if (field.getDefaultValue() != null) {
                sql.append(" DEFAULT ").append(field.getDefaultValue());
            }
            sql.append(",\n");
        }
        
        // 审计字段
        sql.append("  created_at TIMESTAMP DEFAULT NOW(),\n");
        sql.append("  updated_at TIMESTAMP DEFAULT NOW()\n");
        sql.append(");");
        
        return sql.toString();
    }
    
    /**
     * 在指定租户数据库中执行建表
     */
    public void createTableInTenantDB(String databaseName, String tableName, TableDefinition definition) {
        DataSource tenantDS = getTenantDataSource(databaseName);
        String createSQL = generateCreateTableSQL(tableName, definition);
        
        try (Connection conn = tenantDS.getConnection();
             Statement stmt = conn.createStatement()) {
            
            stmt.execute(createSQL);
            
            // 创建索引
            for (IndexDefinition index : definition.getIndexes()) {
                String indexSQL = generateCreateIndexSQL(tableName, index);
                stmt.execute(indexSQL);
            }
            
        } catch (SQLException e) {
            throw new RuntimeException("创建表失败: " + tableName, e);
        }
    }
}
```

#### 5.5.3 租户路由服务
```java
@Service
public class TenantRoutingService {
    
    private final Map<String, DataSource> tenantDataSources = new ConcurrentHashMap<>();
    
    /**
     * 获取租户对应的数据源
     */
    public DataSource getTenantDataSource(String tenantCode) {
        return tenantDataSources.computeIfAbsent(tenantCode, code -> {
            Tenant tenant = tenantRepository.findByCode(code);
            return createDataSource(tenant.getDatabaseName());
        });
    }
    
    /**
     * 动态创建租户数据库
     */
    public void createTenantDatabase(Tenant tenant) {
        String dbName = "tenant_" + tenant.getCode().toLowerCase();
        
        // 1. 创建数据库
        try (Connection conn = masterDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            stmt.execute(String.format("CREATE DATABASE %s", dbName));
            
        } catch (SQLException e) {
            throw new RuntimeException("创建租户数据库失败: " + dbName, e);
        }
        
        // 2. 初始化基础表结构
        initializeTenantDatabase(dbName);
        
        // 3. 更新租户信息
        tenant.setDatabaseName(dbName);
        tenantRepository.save(tenant);
    }
}
```

### 5.4 架构优势分析

#### 5.4.1 性能优势
```
性能提升评估:
- 查询性能: 分区pruning减少90%数据扫描
- 并发能力: 分区锁降低锁竞争，并发能力提升5x
- 维护效率: 分区维护可并行进行，维护窗口缩短80%
```

#### 5.4.2 安全优势
```
安全控制层级:
1. 网络层: VPC隔离 + 白名单
2. 应用层: 租户身份验证 + 权限控制
3. 数据库层: 行级安全策略 + 分区隔离
4. 审计层: 完整操作日志 + 实时监控
```

#### 5.4.3 扩展优势
```
扩展能力:
- 水平扩展: 支持读写分离 + 分片
- 垂直扩展: 大租户可独立分区优化
- 弹性扩展: 支持动态添加分区
- 跨区域: 支持多数据中心部署
```

---

## 6. 实施计划

### 6.1 Phase 1: 基础架构改造 (2周)

#### 6.1.1 数据库Schema升级
```sql
-- 1. 添加租户标识字段
ALTER TABLE users ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE assessment_records ADD COLUMN tenant_id UUID;

-- 2. 数据迁移脚本
UPDATE users SET tenant_id = (
    SELECT id FROM tenants WHERE code = 
    (SELECT code FROM institutions WHERE id = users.institution_id)
);

-- 3. 创建分区表结构
-- (使用上述SQL脚本)
```

#### 6.1.2 应用层改造
```java
// 1. 租户上下文管理
@Component
public class TenantContext {
    private static final ThreadLocal<UUID> CURRENT_TENANT = new ThreadLocal<>();
    
    public static void setTenantId(UUID tenantId) {
        CURRENT_TENANT.set(tenantId);
    }
    
    public static UUID getCurrentTenantId() {
        return CURRENT_TENANT.get();
    }
}

// 2. 数据访问拦截器
@Aspect
@Component
public class TenantDataInterceptor {
    
    @Before("execution(* com.assessment.repository.*.*(..))")
    public void setTenantContext(JoinPoint joinPoint) {
        UUID tenantId = TenantContext.getCurrentTenantId();
        if (tenantId != null) {
            // 设置数据库会话变量
            jdbcTemplate.execute(
                "SET app.current_tenant_id = '" + tenantId + "'");
        }
    }
}
```

### 6.2 Phase 2: 动态量表支持 (3周)

#### 6.2.1 Schema管理系统
```java
// 量表定义DSL
@Data
public class ScaleDefinition {
    private String code;
    private String name;
    private String version;
    private List<SectionDefinition> sections;
    private ScoringRules scoringRules;
    private ValidationRules validationRules;
}

// 动态验证器
public interface ScaleValidator {
    ValidationResult validate(JsonNode formData);
    ScoreResult calculateScore(JsonNode formData);
}
```

#### 6.2.2 报告生成引擎
```java
@Service
public class ReportGenerationService {
    
    public ReportData generateReport(AssessmentRecord record) {
        AssessmentScale scale = getScale(record.getScaleId());
        
        // 使用模板引擎渲染报告
        Template template = getReportTemplate(scale.getCode());
        
        return ReportData.builder()
            .scaleInfo(scale)
            .assessmentData(record.getFormData())
            .scoreAnalysis(calculateScoreAnalysis(record))
            .recommendations(generateRecommendations(record))
            .build();
    }
}
```

### 6.3 Phase 3: 性能优化 (2周)

#### 6.3.1 索引优化
```sql
-- 1. 复合索引优化
CREATE INDEX CONCURRENTLY idx_records_tenant_elderly_date 
ON tenant_assessment_records (tenant_id, elderly_id, created_at DESC);

-- 2. 部分索引优化
CREATE INDEX CONCURRENTLY idx_records_active 
ON tenant_assessment_records (tenant_id, status, created_at) 
WHERE status IN ('draft', 'submitted');

-- 3. JSONB索引优化
CREATE INDEX CONCURRENTLY idx_form_data_gin 
ON tenant_assessment_records USING GIN (form_data);
```

#### 6.3.2 查询优化
```java
// 1. 批量查询优化
@Repository
public class OptimizedAssessmentRepository {
    
    @Query("""
        SELECT r FROM AssessmentRecord r
        WHERE r.tenantId = :tenantId
        AND r.elderlyId IN :elderlyIds
        AND r.createdAt >= :startDate
        ORDER BY r.createdAt DESC
        """)
    List<AssessmentRecord> findByTenantAndElderlyIds(
        @Param("tenantId") UUID tenantId,
        @Param("elderlyIds") List<UUID> elderlyIds,
        @Param("startDate") LocalDateTime startDate,
        Pageable pageable);
}

// 2. 缓存策略
@Cacheable(value = "scaleSchemas", key = "#scaleCode")
public AssessmentScale getScaleSchema(String scaleCode) {
    return scaleRepository.findByCode(scaleCode);
}
```

### 6.4 Phase 4: 监控和运维 (1周)

#### 6.4.1 监控指标
```yaml
# Prometheus监控配置
groups:
  - name: assessment_platform
    rules:
      - alert: HighTenantQueryLatency
        expr: avg(rate(postgresql_query_duration_seconds_sum[5m])) > 0.5
        labels:
          severity: warning
        annotations:
          summary: "租户查询延迟过高"
          
      - alert: TenantDataIsolationViolation
        expr: increase(tenant_isolation_violations_total[1h]) > 0
        labels:
          severity: critical
        annotations:
          summary: "检测到租户数据隔离违规"
```

---

## 7. 性能优化策略

### 7.1 查询性能优化

#### 7.1.1 分区Pruning优化
```sql
-- 1. 确保查询包含分区键
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM tenant_assessment_records 
WHERE tenant_id = 'specific-tenant-uuid'  -- 分区裁剪生效
AND elderly_id = 'elderly-uuid'
AND created_at >= '2025-01-01';

-- 查询计划应显示:
-- Seq Scan on tenant_records_001 (actual time=0.123..1.456 rows=100 loops=1)
-- Buffers: shared hit=50  -- 只扫描特定分区
```

#### 7.1.2 索引优化策略
```sql
-- 1. 分区特定索引
CREATE INDEX CONCURRENTLY ON tenant_records_001 
(elderly_id, created_at DESC) 
WHERE status = 'completed';

-- 2. 表达式索引
CREATE INDEX CONCURRENTLY ON tenant_assessment_records 
((form_data->>'total_score')::numeric) 
WHERE form_data->>'total_score' IS NOT NULL;

-- 3. 多列索引优化
CREATE INDEX CONCURRENTLY ON tenant_assessment_records 
(tenant_id, scale_id, status, created_at DESC);
```

### 7.2 缓存策略

#### 7.2.1 多级缓存架构
```java
@Configuration
public class CacheConfig {
    
    // 1. 本地缓存 (热点数据)
    @Bean
    public CacheManager localCacheManager() {
        return Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();
    }
    
    // 2. 分布式缓存 (跨节点共享)
    @Bean 
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(jedisConnectionFactory());
        return template;
    }
}

// 3. 智能缓存策略
@Service
public class CacheOptimizedService {
    
    @Cacheable(value = "scaleSchemas", key = "#scaleCode", unless = "#result == null")
    public AssessmentScale getScale(String scaleCode) {
        return scaleRepository.findByCode(scaleCode);
    }
    
    @CacheEvict(value = "tenantStats", key = "#tenantId")
    public void invalidateTenantCache(UUID tenantId) {
        // 租户数据更新时，清除相关缓存
    }
}
```

### 7.3 连接池优化

```yaml
# HikariCP优化配置
spring:
  datasource:
    hikari:
      # 连接池大小 = CPU核心数 * 2
      maximum-pool-size: 20
      minimum-idle: 5
      # 连接超时
      connection-timeout: 30000
      # 空闲连接超时
      idle-timeout: 600000
      # 连接最大存活时间
      max-lifetime: 1800000
      # 验证查询
      connection-test-query: SELECT 1
      # 连接泄漏检测
      leak-detection-threshold: 60000
```

---

## 8. 运维监控

### 8.1 监控指标体系

#### 8.1.1 核心业务指标
```sql
-- 1. 租户活跃度监控
CREATE VIEW tenant_activity_stats AS
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    COUNT(DISTINCT u.id) as active_users,
    COUNT(r.id) as daily_assessments,
    AVG(EXTRACT(EPOCH FROM (r.updated_at - r.created_at))) as avg_completion_time
FROM tenants t
LEFT JOIN users u ON u.tenant_id = t.id AND u.last_login_at >= CURRENT_DATE
LEFT JOIN tenant_assessment_records r ON r.tenant_id = t.id AND r.created_at >= CURRENT_DATE
GROUP BY t.id, t.name;

-- 2. 系统性能指标
CREATE VIEW system_performance_stats AS
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables 
WHERE tablename LIKE 'tenant_%';
```

#### 8.1.2 告警规则配置
```yaml
# Grafana告警规则
alerts:
  - name: "租户查询延迟告警"
    condition: "avg(postgresql_query_duration_seconds_sum) > 0.5"
    frequency: "10s"
    executionErrorState: "alerting"
    noDataState: "no_data"
    for: "1m"
    
  - name: "分区不均衡告警"  
    condition: "max(tenant_partition_size) / min(tenant_partition_size) > 5"
    frequency: "1h"
    
  - name: "缓存命中率告警"
    condition: "redis_cache_hit_rate < 0.8"
    frequency: "5m"
```

### 8.2 自动化运维

#### 8.2.1 分区自动管理
```sql
-- 1. 自动分区创建函数
CREATE OR REPLACE FUNCTION auto_create_monthly_partitions()
RETURNS VOID AS $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name VARCHAR;
BEGIN
    -- 为未来3个月创建分区
    FOR i IN 0..2 LOOP
        start_date := date_trunc('month', CURRENT_DATE + (i || ' months')::INTERVAL);
        end_date := start_date + INTERVAL '1 month';
        partition_name := 'tenant_records_' || to_char(start_date, 'YYYY_MM');
        
        -- 检查分区是否已存在
        IF NOT EXISTS (
            SELECT 1 FROM pg_class WHERE relname = partition_name
        ) THEN
            EXECUTE format('
                CREATE TABLE %I PARTITION OF tenant_assessment_records
                FOR VALUES FROM (%L) TO (%L)',
                partition_name, start_date, end_date
            );
            
            RAISE NOTICE '创建分区: %', partition_name;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 2. 定时任务调度
SELECT cron.schedule('auto-partition', '0 2 1 * *', 'SELECT auto_create_monthly_partitions();');
```

#### 8.2.2 性能自动调优
```python
# 性能监控和自动调优脚本
import psycopg2
import json
from datetime import datetime

class DatabaseTuner:
    def __init__(self, connection_string):
        self.conn = psycopg2.connect(connection_string)
    
    def analyze_slow_queries(self):
        """分析慢查询并自动优化"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT query, mean_time, calls, rows, 100.0 * shared_blks_hit /
                       nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
                FROM pg_stat_statements 
                WHERE query LIKE '%tenant_assessment_records%'
                AND mean_time > 100
                ORDER BY mean_time DESC
                LIMIT 10;
            """)
            
            slow_queries = cur.fetchall()
            return self.suggest_optimizations(slow_queries)
    
    def suggest_optimizations(self, slow_queries):
        """根据慢查询建议优化方案"""
        suggestions = []
        for query_info in slow_queries:
            query, mean_time, calls, rows, hit_percent = query_info
            
            if hit_percent < 90:
                suggestions.append(f"增加索引以提高缓存命中率: {query[:100]}...")
            
            if rows / calls > 1000:
                suggestions.append(f"查询返回行数过多，建议添加LIMIT: {query[:100]}...")
        
        return suggestions
```

---

## 9. 总结和建议

### 9.1 方案对比总结

| 维度 | 每量表独立DB | 单DB应用隔离 | 分层多租户架构 |
|------|-------------|-------------|---------------|
| **隔离安全性** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **扩展性** | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **运维复杂度** | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **资源利用率** | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **查询性能** | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **开发效率** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **成本控制** | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

### 9.2 最终建议

**🎯 强烈推荐采用分层多租户架构**

**核心理由**:
1. **平衡安全与效率** - 既保证数据隔离，又避免管理复杂度爆炸
2. **技术栈契合** - 充分利用PostgreSQL分区特性和JSON支持
3. **扩展性优秀** - 支持从小规模到大规模的平滑扩展
4. **运维可控** - 复杂度适中，工具链成熟

### 9.3 实施建议

#### 9.3.1 短期实施 (1个月)
- ✅ 完成Phase 1基础架构改造
- ✅ 建立租户上下文管理机制
- ✅ 实施行级安全策略

#### 9.3.2 中期完善 (3个月)
- ✅ 完成动态量表支持
- ✅ 优化查询性能和索引策略
- ✅ 建立完整监控体系

#### 9.3.3 长期优化 (6个月)
- ✅ 支持读写分离和分片
- ✅ 实现智能化运维
- ✅ 建立多数据中心容灾

### 9.4 风险控制

#### 9.4.1 技术风险
- **迁移风险**: 制定详细的数据迁移方案和回滚计划
- **性能风险**: 在测试环境充分验证性能表现
- **兼容风险**: 确保现有API接口兼容性

#### 9.4.2 运维风险
- **监控盲区**: 建立全面的监控告警体系
- **技能要求**: 团队需要学习PostgreSQL分区特性
- **运维复杂度**: 逐步提升运维自动化水平

---

## 10. 附录

### 10.1 相关技术资料
- [PostgreSQL分区表文档](https://www.postgresql.org/docs/current/ddl-partitioning.html)
- [行级安全策略最佳实践](https://www.postgresql.org/docs/current/ddl-rowsecurity.html) 
- [多租户架构设计模式](https://docs.microsoft.com/en-us/azure/architecture/guide/multitenant/overview)

### 10.2 实施检查清单
- [ ] 数据库架构升级
- [ ] 应用层租户上下文改造
- [ ] 安全策略实施
- [ ] 性能测试验证
- [ ] 监控告警配置
- [ ] 运维文档完善

---

**文档维护**: 请定期更新此文档以反映架构演进和实施进展
**联系方式**: 如有疑问，请联系架构团队进行讨论