# 智慧养老评估平台本地部署实施指南

## 1. 文档信息

- **版本**: v1.0
- **创建日期**: 2024年
- **适用场景**: 本地服务器 + 专线网络环境
- **目标用户**: 系统管理员、运维工程师

## 2. 部署概述

### 2.1 部署架构

本地私有化部署采用容器化架构，支持高可用和横向扩展：

```
┌─────────────────────────────────────────────────────────────┐
│                        外网接入                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    防火墙 + VPN                             │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                 负载均衡器 (Nginx)                          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                应用服务器集群                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ App Server 1│  │ App Server 2│  │ API Gateway │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   数据层                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │PostgreSQL主 │  │PostgreSQL从 │  │ Redis集群   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                   存储层                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  MinIO-1    │  │  MinIO-2    │  │  MinIO-3    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心特性

- **高可用**: 应用服务器双机热备，数据库主从复制
- **可扩展**: 容器化部署，支持水平扩展
- **安全性**: 多层网络隔离，数据加密存储
- **监控**: 全方位监控告警体系
- **备份**: 自动化备份和灾难恢复

## 3. 硬件环境准备

### 3.1 服务器配置清单

#### 生产环境配置 (推荐)

| 服务器类型 | 数量 | CPU | 内存 | 存储 | 网络 | 用途 |
|-----------|------|-----|------|------|------|------|
| 应用服务器 | 2台 | 16核 | 64GB | 1TB SSD + 4TB HDD | 双千兆 | 运行应用服务 |
| 数据库服务器 | 2台 | 32核 | 128GB | 2TB SSD + 8TB HDD | 双万兆 | PostgreSQL主从 |
| 存储服务器 | 3台 | 8核 | 32GB | 500GB SSD + 20TB HDD | 双千兆 | MinIO对象存储 |
| 监控服务器 | 1台 | 8核 | 32GB | 1TB SSD | 千兆 | 监控告警 |
| 跳板机 | 1台 | 4核 | 16GB | 500GB SSD | 千兆 | 运维管理 |

#### 测试环境配置 (最低)

| 服务器类型 | 数量 | CPU | 内存 | 存储 | 网络 | 用途 |
|-----------|------|-----|------|------|------|------|
| 一体化服务器 | 1台 | 16核 | 64GB | 2TB SSD | 千兆 | 所有服务 |

### 3.2 网络设备要求

- **核心交换机**: 48口千兆交换机 (生产环境建议万兆)
- **防火墙**: 企业级防火墙，支持VPN和入侵检测
- **负载均衡器**: 可使用软件负载均衡 (Nginx) 或硬件设备
- **专线带宽**: 100Mbps以上 (根据并发用户数调整)

### 3.3 网络规划

```
网络段规划:
- 管理网络: 192.168.1.0/24
- 业务网络: 192.168.10.0/24
- 存储网络: 192.168.20.0/24
- 监控网络: ************/24
```

## 4. 软件环境准备

### 4.1 操作系统要求

**推荐操作系统**:
- CentOS 8 / RHEL 8
- Ubuntu 20.04 LTS
- Rocky Linux 8

**系统配置**:
```bash
# 关闭SELinux
sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config

# 关闭防火墙 (使用硬件防火墙)
systemctl stop firewalld
systemctl disable firewalld

# 配置时间同步
yum install -y chrony
systemctl enable chronyd
systemctl start chronyd

# 优化内核参数
cat >> /etc/sysctl.conf << EOF
net.ipv4.ip_forward = 1
net.bridge.bridge-nf-call-iptables = 1
net.bridge.bridge-nf-call-ip6tables = 1
vm.swappiness = 10
vm.max_map_count = 262144
EOF
sysctl -p
```

### 4.2 Docker环境安装

```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash
systemctl enable docker
systemctl start docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 配置Docker镜像加速
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << EOF
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF
systemctl restart docker
```

## 5. 部署实施步骤

### 5.1 创建部署目录结构

```bash
mkdir -p /opt/elderly-assessment/{config,data,logs,backup,scripts}
cd /opt/elderly-assessment

# 目录结构
/opt/elderly-assessment/
├── config/          # 配置文件
├── data/           # 数据目录
│   ├── postgres/   # 数据库数据
│   ├── redis/      # Redis数据
│   └── minio/      # 文件存储
├── logs/           # 日志目录
├── backup/         # 备份目录
└── scripts/        # 脚本目录
```

### 5.2 配置文件准备

#### 5.2.1 环境变量配置

```bash
# config/.env
cat > config/.env << 'EOF'
# 数据库配置
DB_HOST=postgresql
DB_PORT=5432
DB_NAME=elderly_assessment
DB_USER=elderly_user
DB_PASSWORD=your_secure_password_here

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# MinIO配置
MINIO_HOST=minio
MINIO_PORT=9000
MINIO_USER=admin
MINIO_PASSWORD=your_minio_password_here

# 应用配置
APP_ENV=production
APP_SECRET=your_app_secret_key_here
JWT_SECRET=your_jwt_secret_here

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_ADMIN_PASSWORD=your_grafana_password_here
EOF
```

#### 5.2.2 Docker Compose配置

```yaml
# docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    container_name: elderly-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api-gateway
    restart: unless-stopped
    networks:
      - elderly-network

  # API网关
  api-gateway:
    image: elderly-assessment/gateway:latest
    container_name: elderly-gateway
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - NACOS_SERVER_ADDR=nacos:8848
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - nacos
      - redis
    restart: unless-stopped
    networks:
      - elderly-network

  # 服务注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: elderly-nacos
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos
      - MYSQL_SERVICE_USER=nacos
      - MYSQL_SERVICE_PASSWORD=${DB_PASSWORD}
    volumes:
      - ./data/nacos:/home/<USER>/data
      - ./logs/nacos:/home/<USER>/logs
    ports:
      - "8848:8848"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - elderly-network

  # 评估服务
  assessment-service:
    image: elderly-assessment/assessment:latest
    container_name: elderly-assessment
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DB_HOST=${DB_HOST}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - NACOS_SERVER_ADDR=nacos:8848
    depends_on:
      - postgresql
      - redis
      - nacos
    restart: unless-stopped
    networks:
      - elderly-network

  # 用户服务
  user-service:
    image: elderly-assessment/user:latest
    container_name: elderly-user
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DB_HOST=${DB_HOST}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - NACOS_SERVER_ADDR=nacos:8848
    depends_on:
      - postgresql
      - redis
      - nacos
    restart: unless-stopped
    networks:
      - elderly-network

  # PostgreSQL主数据库
  postgresql:
    image: postgres:15
    container_name: elderly-postgres
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./config/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backup/postgres:/backup
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - elderly-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: elderly-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - ./data/redis:/data
      - ./config/redis.conf:/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - elderly-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: elderly-minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}
    volumes:
      - ./data/minio:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    restart: unless-stopped
    networks:
      - elderly-network

  # MySQL (Nacos配置中心)
  mysql:
    image: mysql:8.0
    container_name: elderly-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=nacos
      - MYSQL_USER=nacos
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - ./data/mysql:/var/lib/mysql
      - ./config/mysql/nacos.sql:/docker-entrypoint-initdb.d/nacos.sql:ro
    restart: unless-stopped
    networks:
      - elderly-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: elderly-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./data/prometheus:/prometheus
    ports:
      - "${PROMETHEUS_PORT}:9090"
    restart: unless-stopped
    networks:
      - elderly-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: elderly-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - ./data/grafana:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "${GRAFANA_PORT}:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - elderly-network

networks:
  elderly-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
  minio_data:
  mysql_data:
  prometheus_data:
  grafana_data:
EOF
```

### 5.3 配置文件详细设置

#### 5.3.1 Nginx配置

```nginx
# config/nginx.conf
cat > config/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 上游服务器
    upstream api_backend {
        server api-gateway:8080 weight=1 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS主站
    server {
        listen 443 ssl http2;
        server_name _;
        
        # SSL配置
        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # 静态文件
        location /static/ {
            alias /usr/share/nginx/html/static/;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }
        
        # API代理
        location /api/ {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
        
        # WebSocket代理
        location /ws/ {
            proxy_pass http://api_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
        }
        
        # 默认页面
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }
}
EOF
```

#### 5.3.2 PostgreSQL初始化脚本

```sql
-- config/postgres/init.sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 创建数据库用户
CREATE USER elderly_user WITH PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE elderly_assessment TO elderly_user;

-- 创建基础表结构
\c elderly_assessment;

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    real_name VARCHAR(50),
    role VARCHAR(20) DEFAULT 'USER',
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 老年人信息表
CREATE TABLE elderly_info (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    id_card VARCHAR(18),
    gender VARCHAR(10),
    birth_date DATE,
    phone VARCHAR(20),
    address TEXT,
    emergency_contact VARCHAR(50),
    emergency_phone VARCHAR(20),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估量表表
CREATE TABLE assessment_scales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    config JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评估记录表
CREATE TABLE assessment_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    elderly_id UUID REFERENCES elderly_info(id),
    scale_id UUID REFERENCES assessment_scales(id),
    assessor_id UUID REFERENCES users(id),
    answers JSONB NOT NULL,
    scores JSONB,
    result JSONB,
    status VARCHAR(20) DEFAULT 'COMPLETED',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_elderly_info_id_card ON elderly_info(id_card);
CREATE INDEX idx_assessment_records_elderly_id ON assessment_records(elderly_id);
CREATE INDEX idx_assessment_records_created_at ON assessment_records(created_at);

-- 插入默认管理员用户
INSERT INTO users (username, password_hash, email, real_name, role) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLkxNqyNuVdxJOWHNvKi', '<EMAIL>', '系统管理员', 'ADMIN');

-- 插入默认评估量表
INSERT INTO assessment_scales (name, type, config) VALUES 
('老年人能力评估量表', 'elderlyCapacity', '{
  "sections": [
    {
      "title": "日常生活能力",
      "questions": [
        {
          "id": "q1",
          "text": "进食能力",
          "type": "radio",
          "options": [
            {"value": 0, "text": "完全依赖他人"},
            {"value": 5, "text": "需要部分帮助"},
            {"value": 10, "text": "完全独立"}
          ]
        }
      ]
    }
  ]
}');
```

### 5.4 启动部署

```bash
# 1. 生成SSL证书 (自签名)
mkdir -p config/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout config/ssl/server.key \
  -out config/ssl/server.crt \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=Organization/CN=localhost"

# 2. 设置权限
chmod 600 config/ssl/server.key
chown -R 1000:1000 data/

# 3. 启动服务
docker-compose up -d

# 4. 检查服务状态
docker-compose ps
docker-compose logs -f
```

## 6. 部署验证

### 6.1 服务健康检查

```bash
#!/bin/bash
# scripts/health_check.sh

echo "=== 智慧养老评估平台健康检查 ==="

# 检查容器状态
echo "1. 检查容器状态:"
docker-compose ps

# 检查端口监听
echo "\n2. 检查端口监听:"
netstat -tlnp | grep -E ':(80|443|5432|6379|9000|8848)'

# 检查API健康状态
echo "\n3. 检查API健康状态:"
curl -k -s https://localhost/api/health | jq .

# 检查数据库连接
echo "\n4. 检查数据库连接:"
docker exec elderly-postgres pg_isready -U elderly_user -d elderly_assessment

# 检查Redis连接
echo "\n5. 检查Redis连接:"
docker exec elderly-redis redis-cli -a your_redis_password_here ping

# 检查MinIO状态
echo "\n6. 检查MinIO状态:"
curl -s http://localhost:9000/minio/health/live

echo "\n=== 健康检查完成 ==="
```

### 6.2 功能测试

```bash
# 1. 测试用户登录
curl -k -X POST https://localhost/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 2. 测试评估量表获取
curl -k -H "Authorization: Bearer <token>" \
  https://localhost/api/scales

# 3. 测试文件上传
curl -k -X POST https://localhost/api/upload \
  -H "Authorization: Bearer <token>" \
  -F "file=@test.jpg"
```

## 7. 监控配置

### 7.1 Prometheus配置

```yaml
# config/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'elderly-assessment'
    static_configs:
      - targets: ['api-gateway:8080', 'assessment-service:8080']
    metrics_path: '/actuator/prometheus'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
```

### 7.2 告警规则

```yaml
# config/rules/alerts.yml
groups:
  - name: elderly-assessment-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 已停止"
          description: "服务 {{ $labels.instance }} 已停止超过1分钟"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 磁盘使用率超过90%"
```

## 8. 备份恢复

### 8.1 自动备份脚本

```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/opt/elderly-assessment/backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

echo "开始备份 - $DATE"

# 1. 备份PostgreSQL数据库
echo "备份PostgreSQL数据库..."
docker exec elderly-postgres pg_dump -U elderly_user elderly_assessment | gzip > $BACKUP_DIR/$DATE/postgres_$DATE.sql.gz

# 2. 备份Redis数据
echo "备份Redis数据..."
docker exec elderly-redis redis-cli -a your_redis_password_here --rdb /data/dump_$DATE.rdb
cp data/redis/dump_$DATE.rdb $BACKUP_DIR/$DATE/

# 3. 备份MinIO数据
echo "备份MinIO数据..."
tar -czf $BACKUP_DIR/$DATE/minio_$DATE.tar.gz -C data/minio .

# 4. 备份配置文件
echo "备份配置文件..."
tar -czf $BACKUP_DIR/$DATE/config_$DATE.tar.gz config/

# 5. 清理旧备份 (保留30天)
find $BACKUP_DIR -type d -mtime +30 -exec rm -rf {} \;

echo "备份完成 - $DATE"

# 6. 上传到远程存储 (可选)
# rsync -av $BACKUP_DIR/$DATE/ user@backup-server:/backup/elderly-assessment/
```

### 8.2 恢复脚本

```bash
#!/bin/bash
# scripts/restore.sh

if [ $# -ne 1 ]; then
    echo "使用方法: $0 <备份日期>"
    echo "示例: $0 20240101_120000"
    exit 1
fi

BACKUP_DATE=$1
BACKUP_DIR="/opt/elderly-assessment/backup/$BACKUP_DATE"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "备份目录不存在: $BACKUP_DIR"
    exit 1
fi

echo "开始恢复 - $BACKUP_DATE"

# 1. 停止服务
echo "停止服务..."
docker-compose down

# 2. 恢复PostgreSQL数据库
echo "恢复PostgreSQL数据库..."
docker-compose up -d postgresql
sleep 30
zcat $BACKUP_DIR/postgres_$BACKUP_DATE.sql.gz | docker exec -i elderly-postgres psql -U elderly_user elderly_assessment

# 3. 恢复Redis数据
echo "恢复Redis数据..."
cp $BACKUP_DIR/dump_$BACKUP_DATE.rdb data/redis/dump.rdb

# 4. 恢复MinIO数据
echo "恢复MinIO数据..."
rm -rf data/minio/*
tar -xzf $BACKUP_DIR/minio_$BACKUP_DATE.tar.gz -C data/minio/

# 5. 恢复配置文件 (可选)
# tar -xzf $BACKUP_DIR/config_$BACKUP_DATE.tar.gz

# 6. 启动所有服务
echo "启动所有服务..."
docker-compose up -d

echo "恢复完成 - $BACKUP_DATE"
```

## 9. 运维管理

### 9.1 日常维护脚本

```bash
#!/bin/bash
# scripts/maintenance.sh

echo "=== 智慧养老评估平台日常维护 ==="

# 1. 清理Docker镜像
echo "清理无用的Docker镜像..."
docker image prune -f

# 2. 清理日志文件
echo "清理旧日志文件..."
find logs/ -name "*.log" -mtime +7 -delete
find data/*/logs/ -name "*.log" -mtime +7 -delete

# 3. 检查磁盘空间
echo "检查磁盘空间:"
df -h

# 4. 检查内存使用
echo "检查内存使用:"
free -h

# 5. 检查服务状态
echo "检查服务状态:"
docker-compose ps

# 6. 数据库维护
echo "执行数据库维护..."
docker exec elderly-postgres psql -U elderly_user elderly_assessment -c "VACUUM ANALYZE;"

echo "=== 日常维护完成 ==="
```

### 9.2 性能优化

```bash
#!/bin/bash
# scripts/optimize.sh

echo "=== 性能优化 ==="

# 1. 优化PostgreSQL配置
echo "优化PostgreSQL配置..."
docker exec elderly-postgres psql -U elderly_user elderly_assessment -c "
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
"

# 2. 优化Redis配置
echo "优化Redis配置..."
docker exec elderly-redis redis-cli -a your_redis_password_here CONFIG SET maxmemory-policy allkeys-lru

# 3. 重建索引
echo "重建数据库索引..."
docker exec elderly-postgres psql -U elderly_user elderly_assessment -c "REINDEX DATABASE elderly_assessment;"

echo "=== 性能优化完成 ==="
```

## 10. 故障排除

### 10.1 常见问题

**问题1: 服务启动失败**
```bash
# 检查日志
docker-compose logs <service_name>

# 检查端口占用
netstat -tlnp | grep <port>

# 检查磁盘空间
df -h
```

**问题2: 数据库连接失败**
```bash
# 检查数据库状态
docker exec elderly-postgres pg_isready

# 检查连接配置
docker exec elderly-postgres psql -U elderly_user elderly_assessment -c "SELECT version();"
```

**问题3: 性能问题**
```bash
# 检查系统资源
top
iotop

# 检查数据库性能
docker exec elderly-postgres psql -U elderly_user elderly_assessment -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC LIMIT 10;
"
```

### 10.2 应急处理

```bash
#!/bin/bash
# scripts/emergency.sh

case $1 in
  "restart")
    echo "紧急重启所有服务..."
    docker-compose restart
    ;;
  "stop")
    echo "紧急停止所有服务..."
    docker-compose down
    ;;
  "backup")
    echo "紧急备份..."
    ./scripts/backup.sh
    ;;
  "logs")
    echo "查看错误日志..."
    docker-compose logs --tail=100
    ;;
  *)
    echo "使用方法: $0 {restart|stop|backup|logs}"
    ;;
esac
```

## 11. 安全加固

### 11.1 系统安全

```bash
#!/bin/bash
# scripts/security_hardening.sh

echo "=== 系统安全加固 ==="

# 1. 更新系统
yum update -y

# 2. 配置防火墙
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=22/tcp
firewall-cmd --reload

# 3. 配置SSH安全
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
systemctl restart sshd

# 4. 安装fail2ban
yum install -y epel-release
yum install -y fail2ban
systemctl enable fail2ban
systemctl start fail2ban

echo "=== 安全加固完成 ==="
```

### 11.2 应用安全

- **定期更新**: 定期更新Docker镜像和依赖包
- **密码策略**: 使用强密码，定期更换
- **访问控制**: 最小权限原则，定期审查权限
- **审计日志**: 启用详细的审计日志
- **漏洞扫描**: 定期进行安全漏洞扫描

## 12. 联系支持

如果在部署过程中遇到问题，请联系技术支持：

- **技术支持邮箱**: <EMAIL>
- **技术支持电话**: 400-xxx-xxxx
- **在线文档**: https://docs.elderly-assessment.com
- **GitHub仓库**: https://github.com/elderly-assessment/platform

---

**文档版本**: v1.0  
**最后更新**: 2024年  
**维护团队**: 智慧养老评估平台技术团队