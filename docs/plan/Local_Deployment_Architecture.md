# 智慧养老评估平台本地化部署技术架构方案

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-06-12
- **文档目的**: 为智慧养老评估平台提供完整的本地化部署技术架构方案
- **适用场景**: 拥有本地服务器和专线的私有化部署环境

## 一、项目背景

本方案针对拥有本地服务器资源和专线网络的部署场景，提供一套完整的私有化部署解决方案。相比云服务部署，本地化部署具有以下优势：

1. **数据安全可控**：所有数据存储在本地，完全自主可控
2. **成本优势明显**：一次性投入，长期使用成本低
3. **网络性能优秀**：内网访问，响应速度快
4. **合规要求满足**：满足特定行业的数据本地化要求

## 二、总体架构设计

### 2.1 架构概览

```mermaid
graph TB
    subgraph 本地网络环境
        subgraph DMZ区
            LB[Nginx负载均衡<br/>反向代理]
        end
        
        subgraph 应用服务区
            A1[应用服务器1<br/>Docker]
            A2[应用服务器2<br/>Docker]
        end
        
        subgraph 数据存储区
            D1[(PostgreSQL主库)]
            D2[(PostgreSQL从库)]
            D3[(Redis集群)]
            D4[MinIO<br/>文件存储]
        end
        
        subgraph 基础服务区
            B1[GitLab<br/>代码仓库]
            B2[Harbor<br/>镜像仓库]
            B3[Jenkins<br/>CI/CD]
            B4[Prometheus<br/>监控]
        end
    end
    
    subgraph 外部访问
        U1[移动端用户]
        U2[PC端用户]  
        U3[小程序用户]
    end
    
    U1 -->|专线/VPN| LB
    U2 -->|内网| LB
    U3 -->|专线| LB
    LB --> A1
    LB --> A2
    A1 --> D1
    A2 --> D1
    D1 -.同步.-> D2
```

### 2.2 网络架构设计

```yaml
网络划分:
  DMZ区:
    - 网段: 192.168.1.0/24
    - 部署: Nginx负载均衡
    - 功能: 外部访问入口
    
  应用服务区:
    - 网段: 192.168.2.0/24
    - 部署: Docker应用容器
    - 功能: 业务逻辑处理
    
  数据存储区:
    - 网段: 192.168.3.0/24
    - 部署: 数据库、缓存、文件存储
    - 功能: 数据持久化
    
  管理区:
    - 网段: 192.168.4.0/24
    - 部署: 运维管理工具
    - 功能: 系统运维管理
    
访问控制:
  - 防火墙规则严格控制
  - 仅开放必要端口
  - VPN访问认证
```

## 三、技术栈选型

### 3.1 后端技术栈

```yaml
核心技术选型:
  编程语言: Java 17 LTS
  应用框架: Spring Boot 3.x
  架构模式: 单体应用（模块化设计）
  
  理由:
    - Java在医疗行业认可度高
    - Spring Boot生态成熟完善
    - 单体架构简化运维复杂度
    - 模块化便于未来拆分
    
数据存储:
  主数据库: PostgreSQL 15
    - 支持JSON字段，适合多样化量表
    - ACID事务保证数据一致性
    - 主从复制实现高可用
    
  缓存数据库: Redis 7.x
    - 哨兵模式保证高可用
    - 用于会话管理、热点数据缓存
    
  文件存储: MinIO
    - 本地化的对象存储方案
    - 兼容S3协议
    - 支持分布式部署
    
  搜索引擎: Elasticsearch 8.x（可选）
    - 用于评估记录全文搜索
    - 数据分析和统计
    
中间件精简:
  - 取消云原生组件（Nacos、RocketMQ）
  - 使用本地配置文件管理
  - Redis实现轻量级消息队列
  - 简化架构，降低运维成本
```

### 3.2 前端技术栈

```yaml
多端统一方案: uni-app
  - 一套代码支持多端
  - Vue 3 + TypeScript
  - 支持离线功能
  
管理后台: Vue 3 + Element Plus
  - 纯Web应用
  - 响应式设计
  - 丰富的组件库
```

### 3.3 容器化部署

```yaml
容器技术:
  容器引擎: Docker CE 24.x
  编排工具: Docker Compose
  
  选择理由:
    - 轻量级容器化方案
    - 运维简单，学习成本低
    - 满足中小规模部署需求
    - 不需要K8s的复杂性
    
镜像管理:
  镜像仓库: Harbor
  基础镜像: Alpine Linux
  镜像优化: 多阶段构建
```

## 四、详细部署方案

### 4.1 项目结构

```bash
assessment-platform/
├── src/                      # 源代码目录
│   ├── main/
│   │   ├── java/            # Java源代码
│   │   │   └── com/assessment/
│   │   │       ├── config/          # 配置类
│   │   │       ├── controller/      # API控制器
│   │   │       ├── service/         # 业务逻辑
│   │   │       ├── repository/      # 数据访问
│   │   │       ├── entity/          # 实体类
│   │   │       ├── dto/            # 数据传输对象
│   │   │       ├── utils/          # 工具类
│   │   │       └── security/       # 安全配置
│   │   └── resources/
│   │       ├── application.yml      # 主配置
│   │       ├── application-local.yml # 本地配置
│   │       └── static/             # 静态资源
│   └── test/                # 测试代码
├── docker/                  # Docker相关文件
│   ├── Dockerfile          # 应用镜像定义
│   ├── docker-compose.yml  # 编排文件
│   └── nginx.conf         # Nginx配置
├── scripts/                # 运维脚本
│   ├── backup.sh          # 备份脚本
│   ├── deploy.sh          # 部署脚本
│   └── monitor.sh         # 监控脚本
├── config/                 # 配置文件
│   ├── nginx/             # Nginx配置
│   ├── redis/             # Redis配置
│   └── postgres/          # PostgreSQL配置
├── data/                  # 数据目录
│   ├── postgres/          # 数据库数据
│   ├── redis/             # Redis数据
│   └── minio/             # 文件存储数据
├── logs/                  # 日志目录
└── backup/                # 备份目录
```

### 4.2 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # PostgreSQL主库
  postgres-master:
    image: postgres:15-alpine
    container_name: assessment-db-master
    environment:
      POSTGRES_DB: assessment
      POSTGRES_USER: assessment_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "-E UTF8 --locale=zh_CN.UTF-8"
    volumes:
      - ./data/postgres-master:/var/lib/postgresql/data
      - ./config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U assessment_user"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - assessment-net

  # PostgreSQL从库
  postgres-slave:
    image: postgres:15-alpine
    container_name: assessment-db-slave
    environment:
      POSTGRES_DB: assessment
      POSTGRES_USER: assessment_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_MASTER_SERVICE: postgres-master
    volumes:
      - ./data/postgres-slave:/var/lib/postgresql/data
      - ./config/postgres/postgresql-slave.conf:/etc/postgresql/postgresql.conf
    depends_on:
      postgres-master:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - assessment-net

  # Redis缓存（哨兵模式）
  redis:
    image: redis:7-alpine
    container_name: assessment-redis
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./data/redis:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - assessment-net

  # MinIO文件存储
  minio:
    image: minio/minio:latest
    container_name: assessment-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD}
      MINIO_VOLUMES: "/data"
    volumes:
      - ./data/minio:/data
    ports:
      - "9000:9000"     # API端口
      - "9001:9001"     # 控制台端口
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - assessment-net

  # Spring Boot应用（实例1）
  app1:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: assessment-app1
    environment:
      SPRING_PROFILES_ACTIVE: local
      SERVER_PORT: 8080
      DB_HOST: postgres-master
      DB_PORT: 5432
      DB_NAME: assessment
      DB_USER: assessment_user
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      MINIO_ENDPOINT: http://minio:9000
      MINIO_ACCESS_KEY: ${MINIO_USER}
      MINIO_SECRET_KEY: ${MINIO_PASSWORD}
    volumes:
      - ./logs/app1:/app/logs
      - ./config/app:/app/config
    depends_on:
      postgres-master:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - assessment-net

  # Spring Boot应用（实例2）
  app2:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: assessment-app2
    environment:
      SPRING_PROFILES_ACTIVE: local
      SERVER_PORT: 8080
      DB_HOST: postgres-master
      DB_PORT: 5432
      DB_NAME: assessment
      DB_USER: assessment_user
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      MINIO_ENDPOINT: http://minio:9000
      MINIO_ACCESS_KEY: ${MINIO_USER}
      MINIO_SECRET_KEY: ${MINIO_PASSWORD}
    volumes:
      - ./logs/app2:/app/logs
      - ./config/app:/app/config
    depends_on:
      postgres-master:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - assessment-net

  # Nginx反向代理和负载均衡
  nginx:
    image: nginx:alpine
    container_name: assessment-nginx
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./www:/usr/share/nginx/html
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - app1
      - app2
    restart: unless-stopped
    networks:
      - assessment-net

  # Prometheus监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: assessment-prometheus
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./data/prometheus:/prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    restart: unless-stopped
    networks:
      - assessment-net

  # Grafana可视化（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: assessment-grafana
    volumes:
      - ./data/grafana:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - assessment-net

networks:
  assessment-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 4.3 应用配置示例

```yaml
# application-local.yml
spring:
  application:
    name: assessment-platform
    
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        
minio:
  endpoint: ${MINIO_ENDPOINT}
  access-key: ${MINIO_ACCESS_KEY}
  secret-key: ${MINIO_SECRET_KEY}
  bucket-name: assessment-files
  
security:
  jwt:
    secret: ${JWT_SECRET}
    expiration: 86400000 # 24小时
    
logging:
  level:
    root: INFO
    com.assessment: DEBUG
  file:
    name: /app/logs/assessment.log
    max-size: 100MB
    max-history: 30
```

### 4.4 Nginx配置示例

```nginx
# nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    sendfile on;
    tcp_nopush on;
    keepalive_timeout 65;
    gzip on;
    
    # 负载均衡配置
    upstream assessment-backend {
        least_conn;
        server app1:8080 weight=1 max_fails=3 fail_timeout=30s;
        server app2:8080 weight=1 max_fails=3 fail_timeout=30s;
    }
    
    # HTTP服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 强制跳转HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS服务器配置
    server {
        listen 443 ssl http2;
        server_name localhost;
        
        # SSL证书配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;
        
        # 安全头部
        add_header Strict-Transport-Security "max-age=31536000" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # API路由
        location /api {
            proxy_pass http://assessment-backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # 静态资源
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
        }
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
        }
    }
}
```

## 五、数据安全方案

### 5.1 网络安全

```yaml
安全措施:
  网络隔离:
    - VLAN划分，不同区域隔离
    - 防火墙规则严格控制
    - 仅开放必要端口
    
  访问控制:
    - VPN接入（移动办公）
    - IP白名单限制
    - 多因素认证
    
  传输加密:
    - HTTPS/TLS 1.3
    - 内部通信也采用加密
    - 证书定期更新
```

### 5.2 应用安全

```java
// Spring Security配置示例
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .cors().and()
            .csrf().disable()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/health").permitAll()
                .anyRequest().authenticated()
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
            
        return http.build();
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
```

### 5.3 数据安全

```yaml
数据保护:
  存储安全:
    - 数据库透明加密
    - 敏感字段单独加密
    - 文件存储加密
    
  备份策略:
    - 每日全量备份
    - 每小时增量备份
    - 异地备份（可选）
    
  访问审计:
    - 完整操作日志
    - 敏感数据访问记录
    - 异常行为监控
```

## 六、备份与恢复

### 6.1 自动备份脚本

```bash
#!/bin/bash
# backup.sh - 自动备份脚本

# 配置
BACKUP_DIR="/backup/assessment"
RETENTION_DAYS=7
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p ${BACKUP_DIR}/{db,files,config}

# 1. 备份PostgreSQL数据库
echo "开始备份数据库..."
docker exec assessment-db-master pg_dump -U assessment_user -d assessment \
    --format=custom --compress=9 \
    > ${BACKUP_DIR}/db/assessment_${DATE}.dump

# 2. 备份MinIO文件
echo "开始备份文件存储..."
docker run --rm \
    -v assessment_minio_data:/data \
    -v ${BACKUP_DIR}/files:/backup \
    alpine tar czf /backup/minio_${DATE}.tar.gz /data

# 3. 备份配置文件
echo "开始备份配置文件..."
tar czf ${BACKUP_DIR}/config/config_${DATE}.tar.gz \
    ./config ./docker-compose.yml .env

# 4. 清理旧备份
echo "清理超过${RETENTION_DAYS}天的备份..."
find ${BACKUP_DIR} -type f -mtime +${RETENTION_DAYS} -delete

# 5. 生成备份报告
echo "生成备份报告..."
cat > ${BACKUP_DIR}/backup_${DATE}.log <<EOF
备份时间: $(date)
数据库备份: $(ls -lh ${BACKUP_DIR}/db/assessment_${DATE}.dump | awk '{print $5}')
文件备份: $(ls -lh ${BACKUP_DIR}/files/minio_${DATE}.tar.gz | awk '{print $5}')
配置备份: $(ls -lh ${BACKUP_DIR}/config/config_${DATE}.tar.gz | awk '{print $5}')
EOF

echo "备份完成！"

# 6. 可选：同步到远程备份服务器
# rsync -avz ${BACKUP_DIR}/ backup@remote-server:/backup/assessment/
```

### 6.2 恢复脚本

```bash
#!/bin/bash
# restore.sh - 数据恢复脚本

# 使用方法: ./restore.sh 20240612_120000

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <备份时间戳>"
    echo "例如: $0 20240612_120000"
    exit 1
fi

TIMESTAMP=$1
BACKUP_DIR="/backup/assessment"

# 1. 确认备份文件存在
if [ ! -f "${BACKUP_DIR}/db/assessment_${TIMESTAMP}.dump" ]; then
    echo "错误: 备份文件不存在！"
    exit 1
fi

# 2. 停止应用服务
echo "停止应用服务..."
docker-compose stop app1 app2

# 3. 恢复数据库
echo "恢复数据库..."
docker exec -i assessment-db-master pg_restore \
    -U assessment_user -d assessment --clean \
    < ${BACKUP_DIR}/db/assessment_${TIMESTAMP}.dump

# 4. 恢复文件存储
echo "恢复文件存储..."
docker-compose stop minio
docker run --rm \
    -v assessment_minio_data:/data \
    -v ${BACKUP_DIR}/files:/backup \
    alpine sh -c "rm -rf /data/* && tar xzf /backup/minio_${TIMESTAMP}.tar.gz -C /"
docker-compose start minio

# 5. 恢复配置（可选）
read -p "是否恢复配置文件？(y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    tar xzf ${BACKUP_DIR}/config/config_${TIMESTAMP}.tar.gz -C ./
fi

# 6. 重启所有服务
echo "重启所有服务..."
docker-compose restart

echo "恢复完成！"
```

## 七、监控运维

### 7.1 监控指标

```yaml
系统监控:
  服务器指标:
    - CPU使用率 < 70%
    - 内存使用率 < 80%
    - 磁盘使用率 < 85%
    - 网络流量监控
    
  应用指标:
    - JVM内存使用
    - 线程池状态
    - 垃圾回收频率
    - 响应时间分布
    
  数据库指标:
    - 连接数
    - 查询性能
    - 锁等待
    - 复制延迟
    
  业务指标:
    - 在线用户数
    - 评估完成率
    - API调用量
    - 错误率
```

### 7.2 告警配置

```yaml
# prometheus告警规则示例
groups:
  - name: assessment_alerts
    rules:
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "服务器 {{ $labels.instance }} CPU使用率超过80%"
          
      - alert: ServiceDown
        expr: up{job="assessment-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务宕机"
          description: "评估服务 {{ $labels.instance }} 已停止响应"
```

### 7.3 日志管理

```yaml
日志策略:
  应用日志:
    - 位置: /logs/app/
    - 轮转: 每日轮转，保留30天
    - 级别: INFO（生产），DEBUG（开发）
    
  访问日志:
    - 位置: /logs/nginx/
    - 格式: Combined Log Format
    - 分析: 定期分析访问模式
    
  错误日志:
    - 实时监控
    - 告警通知
    - 问题追踪
```

## 八、性能优化

### 8.1 数据库优化

```sql
-- 创建必要的索引
CREATE INDEX idx_assessment_user_status ON assessments(user_id, status);
CREATE INDEX idx_assessment_created ON assessments(created_at);
CREATE INDEX idx_assessment_scale_type ON assessments(scale_type);

-- 分区表（数据量大时使用）
CREATE TABLE assessments_2024 PARTITION OF assessments
    FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 定期维护
VACUUM ANALYZE assessments;
REINDEX INDEX idx_assessment_user_status;
```

### 8.2 缓存策略

```java
@Service
public class AssessmentService {
    
    // 缓存量表模板
    @Cacheable(value = "scaleTemplates", key = "#scaleType")
    public ScaleTemplate getTemplate(String scaleType) {
        return scaleRepository.findByType(scaleType);
    }
    
    // 缓存用户权限
    @Cacheable(value = "userPermissions", key = "#userId")
    public Set<String> getUserPermissions(Long userId) {
        return permissionRepository.findByUserId(userId);
    }
    
    // 缓存失效
    @CacheEvict(value = "assessmentReports", key = "#assessmentId")
    public void updateAssessment(Long assessmentId, AssessmentDTO dto) {
        // 更新逻辑
    }
}
```

### 8.3 应用优化

```yaml
性能优化措施:
  连接池优化:
    - 数据库连接池: 20-50
    - Redis连接池: 10-20
    - HTTP连接池: 按需配置
    
  JVM调优:
    - 堆内存: 4-8GB
    - 新生代比例: 1/3
    - GC策略: G1GC
    
  异步处理:
    - 报告生成异步化
    - 消息通知异步化
    - 数据统计异步化
```

## 九、部署实施

### 9.1 部署前准备

```bash
# 1. 系统要求检查
#!/bin/bash
echo "=== 系统环境检查 ==="

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装"
    exit 1
else
    echo "✅ Docker版本: $(docker --version)"
fi

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装"
    exit 1
else
    echo "✅ Docker Compose版本: $(docker-compose --version)"
fi

# 检查端口占用
for port in 80 443 5432 6379 8080 9000; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ 端口 $port 已被占用"
    else
        echo "✅ 端口 $port 可用"
    fi
done

# 检查磁盘空间
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "⚠️  磁盘使用率过高: ${DISK_USAGE}%"
else
    echo "✅ 磁盘使用率: ${DISK_USAGE}%"
fi
```

### 9.2 一键部署脚本

```bash
#!/bin/bash
# deploy.sh - 一键部署脚本

set -e

echo "=== 智慧养老评估平台部署脚本 ==="

# 1. 加载环境变量
if [ -f .env ]; then
    source .env
else
    echo "创建环境变量文件..."
    cat > .env <<EOF
DB_PASSWORD=$(openssl rand -base64 32)
REDIS_PASSWORD=$(openssl rand -base64 32)
MINIO_USER=minioadmin
MINIO_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)
GRAFANA_PASSWORD=admin
EOF
    echo "✅ 环境变量文件已创建"
fi

# 2. 创建必要的目录
echo "创建目录结构..."
mkdir -p data/{postgres-master,postgres-slave,redis,minio,prometheus,grafana}
mkdir -p logs/{app1,app2,nginx}
mkdir -p backup/{db,files,config}
mkdir -p config/{nginx/conf.d,redis,postgres,prometheus,grafana/provisioning}
mkdir -p ssl www

# 3. 生成自签名SSL证书（开发环境）
if [ ! -f ssl/cert.pem ]; then
    echo "生成SSL证书..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout ssl/key.pem -out ssl/cert.pem \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=Assessment/CN=localhost"
fi

# 4. 初始化配置文件
echo "初始化配置文件..."

# Redis配置
cat > config/redis/redis.conf <<EOF
bind 0.0.0.0
protected-mode yes
port 6379
requirepass ${REDIS_PASSWORD}
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
EOF

# PostgreSQL配置
cat > config/postgres/postgresql.conf <<EOF
listen_addresses = '*'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
EOF

# 5. 构建应用镜像
echo "构建应用镜像..."
docker build -t assessment-app:latest -f docker/Dockerfile .

# 6. 启动服务
echo "启动服务..."
docker-compose up -d

# 7. 等待服务就绪
echo "等待服务就绪..."
sleep 30

# 8. 健康检查
echo "执行健康检查..."
curl -f http://localhost/health || echo "⚠️  健康检查失败"

# 9. 初始化数据库
echo "初始化数据库..."
docker exec assessment-db-master psql -U assessment_user -d assessment -c "
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);"

# 10. 创建定时任务
echo "设置定时任务..."
(crontab -l 2>/dev/null; echo "0 2 * * * /path/to/backup.sh") | crontab -

echo "=== 部署完成 ==="
echo "访问地址: https://localhost"
echo "请查看 .env 文件获取密码信息"
```

### 9.3 部署后验证

```bash
#!/bin/bash
# verify.sh - 部署验证脚本

echo "=== 部署验证 ==="

# 1. 检查容器状态
echo "检查容器状态..."
docker-compose ps

# 2. 检查服务健康状态
echo -e "\n检查服务健康状态..."
services=("postgres-master" "redis" "minio" "app1" "app2" "nginx")
for service in "${services[@]}"; do
    if docker-compose ps | grep $service | grep -q "Up"; then
        echo "✅ $service 运行正常"
    else
        echo "❌ $service 运行异常"
    fi
done

# 3. 测试API访问
echo -e "\n测试API访问..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/health)
if [ $response -eq 200 ]; then
    echo "✅ API访问正常"
else
    echo "❌ API访问异常 (HTTP $response)"
fi

# 4. 测试数据库连接
echo -e "\n测试数据库连接..."
docker exec assessment-db-master pg_isready -U assessment_user && echo "✅ 数据库连接正常" || echo "❌ 数据库连接异常"

# 5. 检查日志
echo -e "\n检查错误日志..."
for log in logs/app1/assessment.log logs/app2/assessment.log logs/nginx/error.log; do
    if [ -f $log ]; then
        errors=$(grep -i error $log | tail -5)
        if [ -n "$errors" ]; then
            echo "⚠️  发现错误日志 ($log):"
            echo "$errors"
        fi
    fi
done
```

## 十、故障处理

### 10.1 常见问题处理

```yaml
问题处理手册:
  服务无法启动:
    - 检查端口占用: lsof -i :端口号
    - 检查日志: docker-compose logs 服务名
    - 检查配置: 环境变量是否正确
    
  数据库连接失败:
    - 检查网络: ping数据库容器
    - 检查认证: 用户名密码是否正确
    - 检查防火墙: 端口是否开放
    
  性能问题:
    - 查看资源使用: docker stats
    - 分析慢查询: PostgreSQL日志
    - 检查缓存: Redis命中率
    
  数据丢失:
    - 立即停止写入
    - 从备份恢复
    - 分析原因
```

### 10.2 应急响应流程

```mermaid
graph TD
    A[发现故障] --> B{故障级别判断}
    B -->|严重| C[立即响应]
    B -->|一般| D[计划处理]
    C --> E[停止服务]
    C --> F[保护现场]
    C --> G[启动备用]
    E --> H[故障诊断]
    F --> H
    G --> H
    H --> I[修复问题]
    I --> J[验证修复]
    J --> K[恢复服务]
    K --> L[总结复盘]
```

## 十一、维护计划

### 11.1 日常维护

```yaml
日常维护项:
  每日:
    - 检查服务状态
    - 查看错误日志
    - 监控告警处理
    
  每周:
    - 数据库维护(VACUUM)
    - 日志清理
    - 性能分析
    
  每月:
    - 安全补丁更新
    - 证书有效期检查
    - 容量规划评估
    
  每季度:
    - 全面健康检查
    - 灾难恢复演练
    - 架构优化评估
```

### 11.2 版本升级

```bash
#!/bin/bash
# upgrade.sh - 版本升级脚本

# 1. 备份当前版本
./backup.sh

# 2. 拉取新版本代码
git pull origin main

# 3. 构建新镜像
docker build -t assessment-app:new -f docker/Dockerfile .

# 4. 滚动升级
docker-compose stop app1
docker-compose up -d app1
sleep 30
docker-compose stop app2
docker-compose up -d app2

# 5. 验证升级
./verify.sh
```

## 十二、成本分析

### 12.1 硬件成本

```yaml
硬件配置方案:
  最小配置(支持500用户):
    服务器: 2台 x 5万 = 10万
    存储: NAS 2万
    网络设备: 1万
    UPS: 1万
    总计: 14万
    
  推荐配置(支持2000用户):
    服务器: 3台 x 8万 = 24万
    存储: 企业级NAS 5万
    网络设备: 2万
    UPS: 2万
    总计: 33万
    
  高级配置(支持5000+用户):
    服务器: 4台 x 12万 = 48万
    存储: SAN存储 15万
    网络设备: 5万
    UPS: 5万
    总计: 73万
```

### 12.2 运维成本

```yaml
运维成本评估:
  人力成本:
    - 兼职运维: 0.5人 x 1.5万/月 = 0.75万/月
    - 全职运维: 1人 x 2万/月 = 2万/月(大规模部署)
    
  其他成本:
    - 电费: 约1000元/月
    - 网络: 专线费用
    - 维保: 硬件维保费用
    
  年度总成本:
    - 最小配置: 约10万/年
    - 推荐配置: 约15万/年
    - 高级配置: 约30万/年
```

### 12.3 TCO对比

```yaml
三年总拥有成本(TCO)对比:
  云服务:
    - 月费用: 1.5万
    - 三年: 54万
    
  本地部署(推荐配置):
    - 硬件: 33万(一次性)
    - 运维: 15万 x 3 = 45万
    - 总计: 78万
    
  节省分析:
    - 第二年开始盈亏平衡
    - 三年节省: 约24万(考虑硬件折旧)
    - 数据完全自主可控
```

## 十三、总结

### 13.1 方案优势

1. **成本优势**：长期使用成本大幅降低，一般两年即可回本
2. **性能优秀**：内网访问，延迟低，用户体验好
3. **数据安全**：数据完全掌控，符合数据本地化要求
4. **灵活扩展**：可根据业务增长灵活扩容
5. **运维简单**：Docker容器化部署，运维自动化程度高

### 13.2 实施建议

1. **分阶段实施**
   - 第一阶段：搭建基础环境，部署核心服务
   - 第二阶段：完善监控告警，优化性能
   - 第三阶段：建立完整的运维体系

2. **团队建设**
   - 至少配备1名运维人员（可兼职）
   - 建立应急响应机制
   - 定期培训和演练

3. **持续优化**
   - 根据实际使用情况调整配置
   - 定期评估和优化架构
   - 关注新技术发展

### 13.3 风险提示

1. **硬件故障风险**：需要做好备份和冗余
2. **人员依赖风险**：需要完善的文档和知识传承
3. **扩展性限制**：超大规模时可能需要重新架构

---

**文档维护**
- 文档版本：v1.0
- 创建日期：2025-06-12
- 最后更新：2025-06-12
- 下次评审：2025-06-19
- 负责人：技术架构组
- 联系方式：<EMAIL>