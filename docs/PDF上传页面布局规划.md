# PDF上传页面布局规划

基于《PDF上传关键逻辑文档》的五个阶段业务流程，重新设计页面布局结构。

## 整体布局架构

```
┌─────────────────────────────────────────────────────────────┐
│                    页面标题区域                               │
│           智慧养老评估平台 - 量表数字化转换                     │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  流程进度指示器                              │
│ ① 文档上传 → ② 解析编辑 → ②.5 量表属性 → ③ AI分析 → ④ 结构设计 → ⑤ SQL生成 │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    主要内容区域                              │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                     ① 文档上传                          │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌────────────────────────────┐ ┌────────────────────────────┐  │
│  │                            │ │                            │  │
│  │         ② 解析编辑          │ │       ②.5 量表属性         │  │
│  │                            │ │                            │  │
│  │                            │ │                            │  │
│  └────────────────────────────┘ └────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                     ③ AI分析                            │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                 ④ 数据库结构设计                         │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                   ⑤ SQL生成执行                         │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     底部操作区域                            │
│    历史记录管理  |  快速模板  |  帮助文档  |  系统状态       │
└─────────────────────────────────────────────────────────────┘
```

## 六阶段布局详细设计

基于量表属性功能的重要性分析，将原有的5阶段调整为6阶段，在解析编辑和AI分析之间增加量表属性配置阶段。

### 阶段一：文档上传区域 (Stage1UploadSection)

**位置**: 顶部独占一栏  
**状态**: 始终可见，完成后显示文件信息  
**现有组件**: `TopControlSection.vue` (需要重构)

```vue
<template>
  <el-card class="stage-card stage1-upload">
    <template #header>
      <div class="stage-header">
        <div class="stage-number completed">1</div>
        <h3>文档上传</h3>
        <el-tag :type="stageStatus">{{ statusText }}</el-tag>
      </div>
    </template>
    
    <!-- 拖拽上传区 -->
    <el-upload drag multiple :on-change="handleUpload">
      <el-icon class="upload-icon"><upload-filled /></el-icon>
      <div class="upload-text">
        <p>拖拽文件到此处，或<em>点击上传</em></p>
        <p class="upload-hint">支持 PDF, DOCX, XLSX, HTML, 图片</p>
      </div>
    </el-upload>
    
    <!-- 文件列表 -->
    <div v-if="uploadedFiles.length" class="file-list">
      <div v-for="file in uploadedFiles" :key="file.id" class="file-item">
        <span>{{ file.name }}</span>
        <el-tag type="success">已上传</el-tag>
      </div>
    </div>
    
    <!-- 上传进度 -->
    <el-progress v-if="uploading" :percentage="uploadProgress" />
  </el-card>
</template>
```

### 阶段二：解析编辑区域 (Stage2ParseEditSection)

**位置**: 第二行左侧（50%宽度）  
**状态**: 上传完成后激活  
**现有组件**: `MarkdownEditor.vue` (可复用)

```vue
<template>
  <el-card class="stage-card stage2-parse-edit" :class="{ disabled: !stage1Complete }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ active: stage1Complete }">2</div>
        <h3>解析编辑</h3>
        <div class="stage-actions">
          <el-button @click="reparse" size="small">重新解析</el-button>
        </div>
      </div>
    </template>
    
    <!-- Docling解析状态 -->
    <div class="parse-status">
      <el-steps :active="parseStep" finish-status="success">
        <el-step title="文档上传" />
        <el-step title="结构分析" />
        <el-step title="内容提取" />
        <el-step title="格式转换" />
      </el-steps>
    </div>
    
    <!-- Markdown编辑器 -->
    <div class="markdown-editor-wrapper">
      <el-tabs v-model="editMode">
        <el-tab-pane label="预览" name="preview">
          <div class="markdown-preview" v-html="markdownHtml"></div>
        </el-tab-pane>
        <el-tab-pane label="编辑" name="edit">
          <el-input 
            type="textarea" 
            v-model="markdownContent"
            :rows="15"
            placeholder="Markdown内容将在此显示..."
          />
        </el-tab-pane>
        <el-tab-pane label="分屏" name="split">
          <div class="split-view">
            <div class="editor-pane">
              <el-input type="textarea" v-model="markdownContent" :rows="15" />
            </div>
            <div class="preview-pane">
              <div class="markdown-preview" v-html="markdownHtml"></div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-card>
</template>
```

### 阶段二点五：量表属性配置区域 (Stage2_5ScalePropertiesSection)

**位置**: 第二行右侧（50%宽度）  
**状态**: 解析完成后激活，作为连接解析和AI分析的关键环节  
**新建组件**: 需要从备份文件中提取功能

```vue
<template>
  <el-card class="stage-card stage2-5-scale-properties" :class="{ disabled: !stage2Complete }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ active: stage2Complete }">2.5</div>
        <h3>量表属性</h3>
        <div class="stage-actions">
          <el-button @click="generateCode" size="small">生成代码</el-button>
        </div>
      </div>
    </template>
    
    <!-- 量表基本信息 -->
    <el-form :model="scaleProperties" label-width="80px" size="small">
      <el-form-item label="量表名称" required>
        <el-input 
          v-model="scaleProperties.name" 
          placeholder="请输入量表名称" 
          clearable 
        />
      </el-form-item>
      
      <el-form-item label="量表代码">
        <el-input v-model="scaleProperties.code" placeholder="AUTO_GENERATE">
          <template #append>
            <el-button @click="generateCode">生成</el-button>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="量表类型" required>
        <el-select v-model="scaleProperties.type" placeholder="选择类型" @change="onTypeChange">
          <el-option label="老年人能力评估" value="ELDERLY_ABILITY" />
          <el-option label="情绪快评" value="EMOTIONAL_QUICK" />
          <el-option label="interRAI评估" value="INTER_RAI" />
          <el-option label="长护险评估" value="LONG_CARE_INSURANCE" />
          <el-option label="自定义量表" value="CUSTOM" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="版本号">
        <el-input v-model="scaleProperties.version" placeholder="1.0.0">
          <template #prepend>v</template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="预估时长">
        <el-input-number 
          v-model="scaleProperties.estimatedDuration" 
          :min="1" :max="180" :step="5"
          controls-position="right" 
          style="width: 100%;"
        />
        <div class="help-text">分钟</div>
      </el-form-item>
    </el-form>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-button @click="saveProperties" type="primary" size="small" :loading="saving">
        保存属性
      </el-button>
      <el-button @click="loadTemplate" size="small">
        加载模板
      </el-button>
    </div>
  </el-card>
</template>
```

### 阶段三：AI分析区域 (Stage3AIAnalysisSection)

**位置**: 第三行独占一栏（100%宽度）  
**状态**: 量表属性配置完成后激活  
**现有组件**: `AIAnalysisSection.vue` (存在，需要适配)

```vue
<template>
  <el-card class="stage-card stage3-ai-analysis" :class="{ disabled: !stage2Complete }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ active: stage2_5Complete }">3</div>
        <h3>AI结构分析</h3>
        <div class="model-info">
          <el-tag size="small">{{ currentModel }}</el-tag>
        </div>
      </div>
    </template>
    
    <!-- 提示词配置 -->
    <div class="prompt-config">
      <el-collapse>
        <el-collapse-item title="自定义分析提示词" name="prompt">
          <el-input 
            type="textarea"
            v-model="customPrompt"
            :rows="4"
            placeholder="输入自定义提示词，用于指导AI分析..."
          />
          <div class="prompt-actions">
            <el-button @click="resetPrompt" size="small">重置</el-button>
            <el-button @click="savePrompt" size="small" type="primary">保存</el-button>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    
    <!-- 分析控制 -->
    <div class="analysis-control">
      <el-button 
        @click="startAnalysis" 
        type="primary" 
        :loading="analyzing"
        :disabled="!stage2_5Complete"
      >
        开始AI分析
      </el-button>
    </div>
    
    <!-- 分析结果流式显示 -->
    <div v-if="analyzing || analysisResult" class="analysis-output">
      <el-card class="output-card">
        <div class="stream-output" ref="streamContainer">
          <div v-if="analyzing" class="analyzing-indicator">
            <el-icon class="rotating"><loading /></el-icon>
            AI正在分析中...
          </div>
          <div class="analysis-text">{{ analysisStreamText }}</div>
        </div>
      </el-card>
    </div>
  </el-card>
</template>
```

### 阶段四：数据库结构设计区域 (Stage4DatabaseDesignSection)

**位置**: 第四行独占一栏（100%宽度）  
**状态**: AI分析完成后激活  
**现有组件**: `DatabaseStructureEditor.vue` (存在，需要适配)

```vue
<template>
  <el-card class="stage-card stage4-database-design" :class="{ disabled: !stage3Complete }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ active: stage3Complete }">4</div>
        <h3>数据库结构设计</h3>
        <div class="stage-actions">
          <el-button @click="autoDetectFields" size="small">自动检测字段</el-button>
          <el-button @click="importTemplate" size="small">导入模板</el-button>
        </div>
      </div>
    </template>
    
    <div class="database-design-content">
      <el-row :gutter="16">
        <!-- 表基本信息 -->
        <el-col :span="8">
          <div class="table-basic-info">
            <h4>表基本信息</h4>
            <el-form :model="tableInfo" label-width="80px" size="small">
              <el-form-item label="表名">
                <el-input v-model="tableInfo.tableName" placeholder="assessment_scale" />
              </el-form-item>
              <el-form-item label="中文名">
                <el-input v-model="tableInfo.tableComment" placeholder="评估量表" />
              </el-form-item>
              <el-form-item label="描述">
                <el-input type="textarea" v-model="tableInfo.description" :rows="3" />
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        
        <!-- 字段设计 -->
        <el-col :span="16">
          <div class="fields-design">
            <div class="fields-header">
              <h4>字段设计</h4>
              <el-button @click="addField" size="small" type="primary">添加字段</el-button>
            </div>
            
            <el-table :data="tableFields" border size="small">
              <el-table-column prop="fieldName" label="字段名" width="120">
                <template #default="{ row }">
                  <el-input v-model="row.fieldName" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="fieldComment" label="中文名" width="100">
                <template #default="{ row }">
                  <el-input v-model="row.fieldComment" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="fieldType" label="数据类型" width="100">
                <template #default="{ row }">
                  <el-select v-model="row.fieldType" size="small">
                    <el-option label="VARCHAR" value="VARCHAR" />
                    <el-option label="INT" value="INT" />
                    <el-option label="DECIMAL" value="DECIMAL" />
                    <el-option label="DATETIME" value="DATETIME" />
                    <el-option label="TEXT" value="TEXT" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="fieldLength" label="长度" width="80">
                <template #default="{ row }">
                  <el-input v-model="row.fieldLength" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="nullable" label="可空" width="60">
                <template #default="{ row }">
                  <el-checkbox v-model="row.nullable" />
                </template>
              </el-table-column>
              <el-table-column prop="defaultValue" label="默认值" width="100">
                <template #default="{ row }">
                  <el-input v-model="row.defaultValue" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button @click="removeField($index)" size="small" type="danger">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
```

### 阶段五：SQL生成执行区域 (Stage5SQLGenerationSection)

**位置**: 第五行独占一栏（100%宽度）  
**状态**: 结构设计完成后激活  
**新建组件**: 需要新建，整合SQL生成逻辑

```vue
<template>
  <el-card class="stage-card stage5-sql-generation" :class="{ disabled: !stage4Complete }">
    <template #header>
      <div class="stage-header">
        <div class="stage-number" :class="{ active: stage4Complete }">5</div>
        <h3>SQL生成与执行</h3>
        <div class="stage-actions">
          <el-button @click="generateSQL" size="small" type="primary" :loading="generatingSQL">
            生成SQL
          </el-button>
          <el-button @click="validateSQL" size="small">验证SQL</el-button>
          <el-button @click="executeSQL" size="small" type="success" :disabled="!sqlGenerated">
            执行建表
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="sql-generation-content">
      <el-row :gutter="16">
        <!-- SQL预览 -->
        <el-col :span="16">
          <div class="sql-preview">
            <div class="sql-header">
              <h4>生成的SQL语句</h4>
              <div class="sql-actions">
                <el-button @click="copySQL" size="small">复制</el-button>
                <el-button @click="downloadSQL" size="small">下载</el-button>
              </div>
            </div>
            <div class="sql-editor">
              <pre class="sql-code"><code>{{ generatedSQL }}</code></pre>
            </div>
          </div>
        </el-col>
        
        <!-- 执行状态 -->
        <el-col :span="8">
          <div class="execution-status">
            <h4>执行状态</h4>
            <div class="status-info">
              <el-steps direction="vertical" :active="executionStep">
                <el-step title="SQL生成" :status="sqlGenerated ? 'success' : 'wait'" />
                <el-step title="语法验证" :status="sqlValidated ? 'success' : 'wait'" />
                <el-step title="安全检查" :status="sqlSecurityChecked ? 'success' : 'wait'" />
                <el-step title="执行建表" :status="tableCreated ? 'success' : 'wait'" />
              </el-steps>
            </div>
            
            <!-- 执行结果 -->
            <div v-if="executionResult" class="execution-result">
              <el-alert 
                :type="executionResult.success ? 'success' : 'error'"
                :title="executionResult.message"
                show-icon
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
```

## 整体页面组件结构

```vue
<!-- PdfUpload.vue 主页面 -->
<template>
  <div class="pdf-upload-page">
    <!-- 页面标题 -->
    <PageHeader />
    
    <!-- 流程进度指示器 -->
    <ProcessIndicator :current-stage="currentStage" />
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 第一行：文档上传（独占一栏） -->
      <div class="stage-row-1">
        <Stage1UploadSection @stage-complete="onStage1Complete" />
      </div>
      
      <!-- 第二行：解析编辑 + 量表属性（并排） -->
      <el-row :gutter="16" class="stage-row-2">
        <el-col :span="12">
          <Stage2ParseEditSection 
            :disabled="!stage1Complete" 
            @stage-complete="onStage2Complete" 
          />
        </el-col>
        <el-col :span="12">
          <Stage2_5ScalePropertiesSection 
            :disabled="!stage2Complete" 
            @stage-complete="onStage2_5Complete" 
          />
        </el-col>
      </el-row>
      
      <!-- 第三行：AI分析（独占一栏） -->
      <div class="stage-row-3">
        <Stage3AIAnalysisSection 
          :disabled="!stage2_5Complete" 
          @stage-complete="onStage3Complete"
        />
      </div>
      
      <!-- 第四行：数据库结构设计（独占一栏） -->
      <div class="stage-row-4">
        <Stage4DatabaseDesignSection 
          :disabled="!stage3Complete" 
          @stage-complete="onStage4Complete" 
        />
      </div>
      
      <!-- 第五行：SQL生成执行（独占一栏） -->
      <div class="stage-row-5">
        <Stage5SQLGenerationSection 
          :disabled="!stage4Complete" 
          @stage-complete="onStage5Complete" 
        />
      </div>
    </div>
    
    <!-- 底部操作区域 -->
    <BottomActionArea />
  </div>
</template>
```

## 关键设计原则

### 1. 流程导向
- 每个阶段都有明确的完成状态
- 后续阶段依赖前一阶段的完成
- 进度指示器清晰显示当前位置

### 2. 状态管理
```typescript
interface PageState {
  currentStage: 1 | 2 | 2.5 | 3 | 4 | 5;
  stage1Complete: boolean;
  stage2Complete: boolean;
  stage2_5Complete: boolean;  // 量表属性配置完成
  stage3Complete: boolean;
  stage4Complete: boolean;
  stage5Complete: boolean;
  
  // 各阶段数据
  uploadedFiles: File[];
  markdownContent: string;
  scaleProperties: {
    name: string;
    code: string;
    type: string;
    version: string;
    estimatedDuration: number;
    description: string;
    assessmentModes: string[];
    complianceStandard: string;
    isActive: boolean;
  };
  analysisResult: any;
  databaseStructure: any;
  generatedSQL: string;
}
```

### 3. 响应式设计
- 大屏显示：6阶段布局（①②②.5③并行 + ④⑤垂直）
- 中屏显示：3+2+1布局（①②③并行 + ②.5④垂直 + ⑤单独）
- 小屏显示：垂直堆叠（①→②→②.5→③→④→⑤）

### 4. 交互体验
- 实时状态反馈
- 流式数据显示
- 错误恢复机制
- 操作撤销支持

## 实施优先级

### 第一期（基础布局）
1. 创建6个阶段组件的基本结构
2. 实现流程进度指示器（①→②→②.5→③→④→⑤）
3. 建立状态管理机制
4. 完成基本的UI布局

### 第二期（功能集成）
1. 集成现有的上传和解析功能
2. **重点实现量表属性配置功能**
3. 实现AI分析流式显示（基于量表类型优化提示词）
4. 完成数据库结构设计器
5. 实现SQL生成功能

### 第三期（优化完善）
1. 添加量表模板库管理
2. 完善历史记录和快速加载
3. 优化用户体验和错误处理
4. 添加高级配置功能
5. 性能调优

## 量表属性功能详细分析

### 功能重要性评估

#### ✅ 极高必要性功能：
1. **量表基本标识** - 业务核心必需
   - 量表名称：用户识别标识
   - 量表代码：系统唯一标识
   - 量表类型：决定评估流程和AI分析策略
   - 版本号：支持量表迭代和版本控制

2. **业务流程支持** - 评估执行必需
   - 预估时长：帮助评估师合理安排时间
   - 量表描述：说明适用场景和注意事项

3. **历史记录管理** - 用户体验必需
   - 最近使用量表：提升工作效率
   - 快速加载功能：避免重复配置

#### ✅ 中等必要性功能：
4. **评估模式配置** - 部署灵活性需要
   - 移动端/网页端/离线评估：支持不同使用场景

5. **合规标准配置** - 专业性要求
   - 国家标准/地方标准：满足合规性要求

### 在业务流程中的作用

```mermaid
graph TD
    A[②解析编辑完成] --> B[②.5量表属性配置]
    B --> C{量表类型确定}
    C --> D[选择AI分析策略]
    C --> E[设置数据库前缀]
    C --> F[配置评估规则]
    D --> G[③AI智能分析]
    E --> H[④数据库结构设计]
    F --> I[评估流程优化]
```

### 与AI分析的关联

**量表类型驱动的AI分析策略：**

```typescript
// AI分析提示词根据量表类型动态调整
const getPromptByScaleType = (scaleType: string) => {
  const prompts = {
    'ELDERLY_ABILITY': `
      你是专业的老年人能力评估专家。请分析以下量表内容，重点关注：
      1. 日常生活能力评估维度
      2. 认知功能评估项目
      3. 生理功能评估指标
      4. 社会参与能力评估
      请生成符合国家标准的数据库结构...
    `,
    'EMOTIONAL_QUICK': `
      你是心理健康评估专家。请分析以下情绪快评量表，重点关注：
      1. 情绪状态评估维度
      2. 心理健康风险指标
      3. 快速筛查关键项目
      请生成心理评估数据库结构...
    `,
    'INTER_RAI': `
      你是interRAI国际评估标准专家。请分析以下量表，重点关注：
      1. 国际标准化评估项目
      2. 跨文化适应性评估
      3. 综合功能评估维度
      请生成符合interRAI标准的数据库结构...
    `
  };
  return prompts[scaleType] || prompts['CUSTOM'];
};
```

### 数据库设计影响

**量表类型影响表命名和结构：**

```sql
-- 根据量表类型生成不同的表名前缀和结构
-- 老年人能力评估
CREATE TABLE elderly_ability_assessment_v1 (
    id BIGINT PRIMARY KEY,
    basic_info_score INT,
    cognitive_score INT,
    physical_score INT,
    social_score INT
);

-- 情绪快评
CREATE TABLE emotional_quick_assessment_v1 (
    id BIGINT PRIMARY KEY,
    mood_score INT,
    anxiety_level INT,
    depression_risk INT
);
```

### 用户体验优化

**快速配置流程：**

1. **智能默认值** - 根据解析内容自动推断量表类型
2. **代码自动生成** - 基于类型和时间戳生成唯一标识
3. **模板快速加载** - 常用量表配置一键应用
4. **历史记录管理** - 最近使用的配置快速重用

### 实现优先级

#### 第一优先级（MVP必需）：
- [x] 量表名称、类型、版本配置
- [x] 代码自动生成
- [x] 基本保存加载功能

#### 第二优先级（体验提升）：
- [ ] 模板库管理
- [ ] 历史记录快速加载
- [ ] 智能默认值推断

#### 第三优先级（高级功能）：
- [ ] 评估模式配置
- [ ] 合规标准管理
- [ ] 批量配置功能

### 技术实现要点

```vue
<!-- 量表属性组件核心结构 -->
<template>
  <el-card class="scale-properties-card">
    <!-- 基本信息表单 -->
    <el-form :model="scaleForm" :rules="validationRules">
      <!-- 必填字段 -->
      <el-form-item label="量表名称" prop="name" required>
        <el-input v-model="scaleForm.name" />
      </el-form-item>
      
      <!-- 自动生成字段 -->
      <el-form-item label="量表代码" prop="code">
        <el-input v-model="scaleForm.code">
          <template #append>
            <el-button @click="generateCode">自动生成</el-button>
          </template>
        </el-input>
      </el-form-item>
      
      <!-- 类型选择驱动后续流程 -->
      <el-form-item label="量表类型" prop="type" required>
        <el-select v-model="scaleForm.type" @change="onTypeChange">
          <el-option 
            v-for="type in scaleTypes" 
            :key="type.value"
            :label="type.label" 
            :value="type.value" 
          />
        </el-select>
      </el-form-item>
    </el-form>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-button @click="saveProperties" type="primary">保存配置</el-button>
      <el-button @click="loadTemplate">加载模板</el-button>
    </div>
  </el-card>
</template>
```

这个功能是连接文档解析和AI分析的关键桥梁，直接影响后续所有阶段的数据质量和用户体验。

## 组件拆分规划

### 现有组件利用

```
frontend/admin/src/views/assessment/components/
├── TopControlSection.vue          ✅ 可复用 → Stage1UploadSection
├── MainContentArea.vue           🔄 需重构 → Stage2ParseEditSection  
├── MarkdownEditor.vue            ✅ 可复用 → 内嵌到Stage2中
├── PromptManagement.vue          🔄 需整合 → 内嵌到Stage3中
└── BottomActionArea.vue          ✅ 可复用 → 保持不变
```

### 新建组件规划

```
frontend/admin/src/views/assessment/components/stages/
├── Stage1UploadSection.vue       📝 新建 - 基于TopControlSection
├── Stage2ParseEditSection.vue    📝 新建 - 基于MainContentArea
├── Stage2_5ScalePropertiesSection.vue  📝 新建 - 从备份提取
├── Stage3AIAnalysisSection.vue   📝 新建 - 整合现有AI功能
├── Stage4DatabaseDesignSection.vue     📝 新建 - 基于现有组件
├── Stage5SQLGenerationSection.vue      📝 新建 - 全新开发
└── ProcessIndicator.vue          📝 新建 - 流程进度指示器
```

### 组件职责划分

#### Stage1UploadSection.vue
**职责**: 文档上传、格式选择、服务状态检查
**输入**: 无
**输出**: `@stage-complete(uploadResult)`
**复用**: `TopControlSection.vue` 90%功能
```typescript
interface UploadResult {
  fileName: string;
  fileSize: number;
  uploadSuccess: boolean;
  doclingResult?: any;
}
```

#### Stage2ParseEditSection.vue  
**职责**: Markdown编辑、预览、内容校对
**输入**: `uploadResult`
**输出**: `@stage-complete(editResult)`
**复用**: `MarkdownEditor.vue` + 部分`MainContentArea.vue`
```typescript
interface EditResult {
  markdownContent: string;
  contentValidated: boolean;
  editMode: 'preview' | 'edit' | 'split';
}
```

#### Stage2_5ScalePropertiesSection.vue
**职责**: 量表属性配置、模板管理、历史记录
**输入**: `editResult`
**输出**: `@stage-complete(scaleProperties)`
**复用**: 从备份文件提取功能
```typescript
interface ScaleProperties {
  name: string;
  code: string;
  type: 'ELDERLY_ABILITY' | 'EMOTIONAL_QUICK' | 'INTER_RAI' | 'LONG_CARE_INSURANCE' | 'CUSTOM';
  version: string;
  estimatedDuration: number;
  description: string;
  // 高级配置
  assessmentModes: string[];
  complianceStandard: string;
  isActive: boolean;
}
```

#### Stage3AIAnalysisSection.vue
**职责**: AI分析、提示词管理、流式输出
**输入**: `scaleProperties` + `markdownContent`  
**输出**: `@stage-complete(analysisResult)`
**复用**: 整合`AIAnalysisSection.vue` + `PromptManagement.vue`
```typescript
interface AnalysisResult {
  databaseSuggestion: any;
  analysisContent: string;
  confidence: number;
  processingTime: number;
}
```

#### Stage4DatabaseDesignSection.vue
**职责**: 数据库结构设计、字段编辑、关系定义
**输入**: `analysisResult`
**输出**: `@stage-complete(databaseStructure)`
**复用**: 基于`DatabaseStructureEditor.vue`
```typescript
interface DatabaseStructure {
  tableName: string;
  tableComment: string;
  fields: FieldDefinition[];
  indexes: IndexDefinition[];
  relationships: RelationshipDefinition[];
}
```

#### Stage5SQLGenerationSection.vue
**职责**: SQL生成、验证、执行
**输入**: `databaseStructure`
**输出**: `@stage-complete(sqlResult)`
**复用**: 全新开发
```typescript
interface SQLResult {
  generatedSQL: string;
  sqlValidated: boolean;
  executionResult?: {
    success: boolean;
    message: string;
    tableCreated: boolean;
  };
}
```

### 组件间通信

```typescript
// 主页面状态管理
interface PdfUploadPageState {
  currentStage: 1 | 2 | 2.5 | 3 | 4 | 5;
  
  // 各阶段完成状态
  stageCompletions: {
    stage1: boolean;
    stage2: boolean;
    stage2_5: boolean;
    stage3: boolean;
    stage4: boolean;
    stage5: boolean;
  };
  
  // 各阶段数据流
  uploadResult?: UploadResult;
  editResult?: EditResult;
  scaleProperties?: ScaleProperties;
  analysisResult?: AnalysisResult;
  databaseStructure?: DatabaseStructure;
  sqlResult?: SQLResult;
}
```

### 组件开发优先级

#### 第一期（MVP核心功能）
1. `Stage1UploadSection.vue` - 重构现有上传功能
2. `Stage2ParseEditSection.vue` - 重构现有编辑功能  
3. `Stage2_5ScalePropertiesSection.vue` - 新建量表属性功能
4. `ProcessIndicator.vue` - 新建进度指示器

#### 第二期（AI分析和数据库设计）
5. `Stage3AIAnalysisSection.vue` - 整合AI分析功能
6. `Stage4DatabaseDesignSection.vue` - 重构数据库设计功能

#### 第三期（SQL生成和完善）
7. `Stage5SQLGenerationSection.vue` - 新建SQL生成功能
8. 组件间联调和优化
9. 错误处理和用户体验完善

---

**文档版本**: v1.0  
**最后更新**: 2025-06-19  
**状态**: 🎯 待实施