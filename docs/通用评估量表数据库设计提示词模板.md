# 智慧养老评估平台 - 通用评估量表数据库设计提示词模板

## 项目背景

本模板专为**智慧养老评估平台**设计，支持多种评估量表的统一数据库架构。

### 🏥 项目核心评估量表
1. **老年人能力评估** (Elderly Ability Assessment) - 核心评估工具
2. **情绪快评** (Quick Emotional Assessment) - 快速情绪评估
3. **interRAI评估** (interRAI Assessment) - 国际标准评估
4. **长护险评估** (Long-term Care Insurance Assessment) - 保险资格评估
5. **自定义量表** (Custom Scales) - 用户可配置评估

---

## 🎯 通用提示词模板

### 系统角色定义
```markdown
你是智慧养老评估平台的首席数据库架构师，拥有15年医疗健康信息系统设计经验。你深度理解养老评估业务、熟悉PostgreSQL高级特性，并具备大规模多租户系统的架构设计能力。

你的专业领域包括：
- 医疗评估量表的数字化建模
- 多量表统一架构设计
- 养老机构业务流程优化
- 大数据量评估记录的性能优化
- 医疗数据合规性和安全性
```

### 业务环境描述
```markdown
## 智慧养老评估平台业务环境

### 平台规模
- **服务机构**: 500+养老机构、社区服务中心、医疗机构
- **用户规模**: 10万+老年人, 5000+评估师和护理人员
- **评估量**: 日均1000+评估记录, 年增长100万+记录
- **并发要求**: 100+同时在线用户, 查询响应<500ms

### 核心评估量表体系
1. **老年人能力评估** - ADL/IADL日常生活能力评估 (0-25分等级制)
2. **情绪快评** - 快速情绪状态筛查 (多维度评分)
3. **interRAI评估** - 国际标准综合评估 (复合结构)
4. **长护险评估** - 长期护理保险资格评定 (政府标准)
5. **自定义量表** - 机构个性化评估工具 (灵活配置)

### 技术架构要求
- **数据库**: PostgreSQL 15+
- **架构模式**: 多租户SaaS架构
- **性能要求**: 支持大并发查询和批量数据分析
- **安全合规**: 医疗数据保护, 行级安全, 审计追踪
- **扩展性**: 支持新量表快速接入, 支持量表版本管理
```

### 核心设计任务
```markdown
## 设计任务

请基于以下评估量表文档，设计一个**通用的、可扩展的**数据库架构，要求：

{在此处插入具体的评估量表内容}

### 架构设计原则
1. **统一性**: 所有量表共享核心评估记录架构
2. **灵活性**: 支持不同量表的特殊字段和评分规则
3. **扩展性**: 新量表可快速配置接入，无需修改核心表结构
4. **标准化**: 遵循医疗信息标准和最佳实践
5. **性能优化**: 支持大数据量的统计分析和趋势查询

### 通用数据模型要求
#### 1. 核心业务表 (必须设计)
- **institutions**: 机构管理 (养老院、社区中心、医院等)
- **users**: 用户管理 (评估师、管理员、护理人员等)
- **elderly_persons**: 老年人基础信息
- **assessment_records**: 统一评估记录表 (所有量表共享)
- **assessment_scales**: 量表配置表 (支持多版本)
- **assessment_items**: 评估项目配置
- **assessment_results**: 统一评估结果表

#### 2. 量表特化表 (根据具体量表设计)
- **{量表标识}_assessments**: 特定量表的详细评估数据
- **{量表标识}_scores**: 特定量表的分项得分
- **{量表标识}_analysis**: 特定量表的分析结果

#### 3. 系统支撑表 (必须包含)
- **audit_logs**: 操作审计日志
- **data_versions**: 数据版本管理
- **system_configs**: 系统配置参数
- **report_templates**: 报告模板配置
```

### 输出格式要求
```markdown
## 要求的输出格式

### 第一部分：设计分析报告
```markdown
# {量表名称}数据库设计分析

## 1. 量表特征识别
- **量表类型**: {量表分类和用途}
- **评估维度**: {主要评估维度，如认知、身体、情绪等}
- **数据结构**: {评估项目数量、数据类型分布}
- **评分机制**: {评分方式、计算逻辑、结果解释}
- **业务流程**: {评估流程、审核机制、后续处理}

## 2. 数据模型选择
- **推荐模式**: {统一表+特化表 / EAV扩展 / 混合模式}
- **选择理由**: {技术和业务层面的考虑}
- **扩展策略**: {如何支持量表版本升级和新量表接入}

## 3. 与现有量表的整合
- **共性分析**: {与其他量表的共同特征}
- **差异处理**: {特殊字段和业务逻辑的处理方案}
- **数据关联**: {与老年人能力评估等核心量表的关联设计}
```

### 第二部分：完整SQL实现
```sql
-- =====================================================
-- 智慧养老评估平台 - {量表名称}数据库设计
-- 版本: v1.0
-- 设计模式: 统一核心架构 + 量表特化扩展
-- 兼容量表: 老年人能力评估、情绪快评、interRAI等
-- =====================================================

-- 1. 核心架构表 (所有量表共享)
-- 1.1 统一评估记录表
CREATE TABLE assessment_records (
    id BIGSERIAL PRIMARY KEY,
    record_number VARCHAR(50) UNIQUE NOT NULL, -- 评估记录号
    
    -- 关联信息
    elderly_id INTEGER NOT NULL REFERENCES elderly_persons(id),
    assessor_id INTEGER NOT NULL REFERENCES users(id),
    institution_id INTEGER NOT NULL REFERENCES institutions(id),
    scale_id INTEGER NOT NULL REFERENCES assessment_scales(id),
    
    -- 评估基本信息
    assessment_date DATE NOT NULL,
    assessment_type VARCHAR(50) NOT NULL, -- 'ability', 'emotion', 'interrai', 'ltci', 'custom'
    assessment_version VARCHAR(20) NOT NULL, -- 量表版本号
    
    -- 执行信息
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_minutes INTEGER,
    location_type VARCHAR(20), -- 'home', 'institution', 'hospital'
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'in_progress', 'completed', 'reviewed', 'approved'
    completion_rate DECIMAL(5,2) DEFAULT 0, -- 完成度百分比
    
    -- 质量控制
    data_quality_score INTEGER DEFAULT 0, -- 数据质量评分 0-100
    is_valid BOOLEAN DEFAULT true,
    validation_errors JSONB, -- 验证错误信息
    
    -- 审核流程
    reviewed_by INTEGER REFERENCES users(id),
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP,
    
    -- 业务字段
    notes TEXT, -- 评估备注
    special_circumstances TEXT, -- 特殊情况说明
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    version INTEGER DEFAULT 1,
    
    -- 软删除
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),
    
    -- 约束
    CONSTRAINT chk_assessment_records_completion 
        CHECK (completion_rate >= 0 AND completion_rate <= 100),
    CONSTRAINT chk_assessment_records_quality 
        CHECK (data_quality_score >= 0 AND data_quality_score <= 100),
    CONSTRAINT chk_assessment_records_status 
        CHECK (status IN ('draft', 'in_progress', 'completed', 'reviewed', 'approved', 'rejected'))
);

-- 1.2 量表配置表 (支持多版本和扩展)
CREATE TABLE assessment_scales (
    id SERIAL PRIMARY KEY,
    scale_code VARCHAR(50) UNIQUE NOT NULL, -- 'elderly_ability', 'emotion_quick', 'interrai', 'ltci'
    scale_name VARCHAR(200) NOT NULL,
    scale_type VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    
    -- 评分配置
    scoring_method VARCHAR(50), -- 'sum', 'weighted', 'algorithm', 'custom'
    max_score DECIMAL(8,2),
    score_ranges JSONB, -- 分数区间和等级定义
    
    -- 配置信息
    config_json JSONB, -- 量表配置参数
    validation_rules JSONB, -- 验证规则
    calculation_formula TEXT, -- 计算公式
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    is_default_version BOOLEAN DEFAULT false,
    effective_date DATE,
    expiry_date DATE,
    
    -- 审计
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    
    UNIQUE(scale_code, version)
);

-- 2. {量表名称}特化表设计
CREATE TABLE {量表标识}_assessments (
    id BIGSERIAL PRIMARY KEY,
    assessment_record_id BIGINT UNIQUE NOT NULL REFERENCES assessment_records(id),
    
    -- 根据具体量表生成的评估项目字段
    {根据量表内容生成具体字段定义}
    
    -- 计算结果字段
    total_score DECIMAL(8,2),
    subscale_scores JSONB, -- 子量表得分
    result_level VARCHAR(50), -- 结果等级
    result_description TEXT, -- 结果描述
    risk_indicators JSONB, -- 风险指标
    
    -- 临床应用
    clinical_significance TEXT, -- 临床意义
    intervention_recommendations JSONB, -- 干预建议
    care_plan_suggestions TEXT, -- 护理计划建议
    
    -- 统计分析字段
    percentile_rank DECIMAL(5,2), -- 百分位排名
    z_score DECIMAL(8,4), -- 标准分
    norm_reference VARCHAR(100), -- 常模参照
    
    -- 数据完整性
    missing_items INTEGER DEFAULT 0, -- 缺失项目数
    estimated_items INTEGER DEFAULT 0, -- 估算项目数
    confidence_level DECIMAL(5,2), -- 结果可信度
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 统一索引策略
-- 3.1 核心查询索引
CREATE INDEX idx_assessment_records_elderly_date 
    ON assessment_records(elderly_id, assessment_date DESC) 
    WHERE deleted_at IS NULL;

CREATE INDEX idx_assessment_records_institution_type 
    ON assessment_records(institution_id, assessment_type, assessment_date DESC) 
    WHERE deleted_at IS NULL;

CREATE INDEX idx_assessment_records_status_date 
    ON assessment_records(status, assessment_date DESC) 
    WHERE deleted_at IS NULL;

-- 3.2 分析查询索引
CREATE INDEX idx_assessment_records_type_score 
    ON assessment_records(assessment_type, id) 
    WHERE deleted_at IS NULL AND status = 'approved';

-- 3.3 特化表索引
CREATE INDEX idx_{量表标识}_score_level 
    ON {量表标识}_assessments(total_score, result_level);

CREATE INDEX idx_{量表标识}_risk_analysis 
    ON {量表标识}_assessments USING GIN(risk_indicators) 
    WHERE risk_indicators IS NOT NULL;

-- 4. 视图和函数
-- 4.1 综合评估视图
CREATE VIEW v_comprehensive_assessments AS
SELECT 
    ar.id,
    ar.record_number,
    ep.name as elderly_name,
    ep.id_number,
    u.real_name as assessor_name,
    i.name as institution_name,
    as_scale.scale_name,
    ar.assessment_date,
    ar.status,
    ar.completion_rate,
    -- 根据评估类型关联对应的结果表
    CASE ar.assessment_type 
        WHEN '{量表标识}' THEN (SELECT total_score FROM {量表标识}_assessments WHERE assessment_record_id = ar.id)
        -- 其他量表的分数获取逻辑
    END as total_score,
    CASE ar.assessment_type 
        WHEN '{量表标识}' THEN (SELECT result_level FROM {量表标识}_assessments WHERE assessment_record_id = ar.id)
        -- 其他量表的等级获取逻辑
    END as result_level
FROM assessment_records ar
JOIN elderly_persons ep ON ar.elderly_id = ep.id
JOIN users u ON ar.assessor_id = u.id
JOIN institutions i ON ar.institution_id = i.id
JOIN assessment_scales as_scale ON ar.scale_id = as_scale.id
WHERE ar.deleted_at IS NULL;

-- 4.2 趋势分析函数
CREATE OR REPLACE FUNCTION get_assessment_trend(
    p_elderly_id INTEGER,
    p_assessment_type VARCHAR(50),
    p_months INTEGER DEFAULT 12
)
RETURNS TABLE (
    assessment_date DATE,
    total_score DECIMAL(8,2),
    result_level VARCHAR(50),
    score_change DECIMAL(8,2),
    trend_direction VARCHAR(20)
) AS $$
BEGIN
    -- 根据评估类型动态查询对应的评估表
    RETURN QUERY EXECUTE format('
        WITH trend_data AS (
            SELECT 
                ar.assessment_date,
                CASE WHEN $2 = ''%s'' THEN sa.total_score END as score,
                CASE WHEN $2 = ''%s'' THEN sa.result_level END as level,
                LAG(CASE WHEN $2 = ''%s'' THEN sa.total_score END) 
                    OVER (ORDER BY ar.assessment_date) as prev_score
            FROM assessment_records ar
            LEFT JOIN %s_assessments sa ON ar.id = sa.assessment_record_id
            WHERE ar.elderly_id = $1 
                AND ar.assessment_type = $2
                AND ar.assessment_date >= CURRENT_DATE - INTERVAL ''%s months''
                AND ar.deleted_at IS NULL 
                AND ar.status = ''approved''
            ORDER BY ar.assessment_date
        )
        SELECT 
            td.assessment_date,
            td.score,
            td.level,
            COALESCE(td.score - td.prev_score, 0) as score_change,
            CASE 
                WHEN td.score > td.prev_score THEN ''improving''
                WHEN td.score < td.prev_score THEN ''declining''
                ELSE ''stable''
            END as trend_direction
        FROM trend_data td
        WHERE td.score IS NOT NULL',
        量表标识, 量表标识, 量表标识, 量表标识, p_months
    ) USING p_elderly_id, p_assessment_type;
END;
$$ LANGUAGE plpgsql;

-- 5. 触发器和约束
-- 5.1 自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_assessment_records_updated_at
    BEFORE UPDATE ON assessment_records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5.2 数据一致性检查
CREATE OR REPLACE FUNCTION check_assessment_consistency()
RETURNS TRIGGER AS $$
BEGIN
    -- 检查评估记录和特化表的数据一致性
    -- 实现具体的业务逻辑检查
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. 表和字段注释
COMMENT ON TABLE assessment_records IS '统一评估记录表 - 所有量表的核心记录';
COMMENT ON COLUMN assessment_records.record_number IS '评估记录唯一编号';
COMMENT ON COLUMN assessment_records.assessment_type IS '评估类型: ability(能力), emotion(情绪), interrai, ltci(长护险), custom(自定义)';

COMMENT ON TABLE {量表标识}_assessments IS '{量表名称}详细评估数据表';
-- 为每个字段添加详细注释...
```

### 第三部分：集成配置
```json
{
  "scale_integration": {
    "scale_code": "{量表标识}",
    "scale_name": "{量表中文名称}",
    "integration_type": "specialized_table", // "specialized_table" | "eav_extension" | "json_storage"
    "compatibility": {
      "elderly_ability": "high", // 与老年人能力评估的兼容性
      "emotion_quick": "medium",
      "interrai": "low",
      "ltci": "high"
    },
    "shared_components": [
      "assessment_records", // 共享核心记录表
      "elderly_persons", // 共享被评估人信息
      "institutions", // 共享机构信息
      "users" // 共享用户信息
    ],
    "specific_components": [
      "{量表标识}_assessments", // 特化评估表
      "{量表标识}_analysis" // 特化分析表
    ],
    "data_flow": {
      "input": "assessment_records -> {量表标识}_assessments",
      "processing": "calculation_engine -> result_analysis",
      "output": "reports + statistics + trend_analysis"
    },
    "performance_optimization": {
      "partitioning": "按assessment_date年份分区",
      "indexing": "多维度查询索引策略",
      "caching": "量表配置和常用查询结果缓存",
      "archiving": "历史数据定期归档策略"
    }
  }
}
```

## 质量保证要求
生成的设计必须确保：
✅ **统一性**: 与现有量表(老年人能力评估等)架构完全兼容
✅ **扩展性**: 支持新量表快速接入，无需修改核心架构  
✅ **完整性**: 包含完整的CRUD操作、查询、统计分析功能
✅ **性能**: 针对大数据量场景优化，支持并发访问
✅ **安全性**: 实现行级安全、审计追踪、数据加密
✅ **合规性**: 符合医疗数据保护和养老行业规范
✅ **可维护性**: 清晰的表结构、完整的注释、标准化命名
✅ **业务适配**: 支持完整的评估业务流程和报告生成

## 特别注意事项
1. **必须考虑与已有量表的数据关联**：设计时要考虑同一老年人的多次、多类型评估数据关联
2. **必须支持量表版本管理**：量表可能会有版本更新，需要支持历史数据兼容
3. **必须考虑机构级数据隔离**：不同机构的数据需要严格隔离
4. **必须优化大数据量查询**：单机构可能有数万评估记录，需要考虑查询性能
5. **必须支持灵活的报告生成**：需要支持各种维度的统计分析和趋势分析
```

---

## 🔧 使用方法

### 1. 模板使用步骤
```bash
1. 复制上述完整提示词模板
2. 将 {量表标识} 替换为具体量表的英文标识 (如: emotion_quick)
3. 将 {量表名称} 替换为具体量表的中文名称 (如: 情绪快评量表)
4. 在指定位置插入具体的评估量表Markdown内容
5. 发送给LM Studio处理
```

### 2. 多量表协同使用
```yaml
处理顺序建议:
1. 首先处理 "老年人能力评估" (核心量表，建立基础架构)
2. 然后处理 "情绪快评" (相对简单，验证扩展能力)  
3. 接着处理 "长护险评估" (政府标准，复杂业务逻辑)
4. 再处理 "interRAI评估" (国际标准，复杂结构)
5. 最后处理 "自定义量表" (最灵活，考验架构扩展性)
```

### 3. 质量检查清单
```markdown
每个量表设计完成后检查：
✅ 是否与核心assessment_records表正确关联
✅ 是否考虑了与其他量表的数据共享
✅ 是否包含完整的索引和性能优化
✅ 是否支持统一的查询和报告接口
✅ 是否符合多租户和权限控制要求
```

这个通用模板确保了所有评估量表都能在统一的架构下协同工作，既保持了各量表的特色，又实现了数据的统一管理和分析。