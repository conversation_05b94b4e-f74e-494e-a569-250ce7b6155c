# VS Code 中编译 uni-app 到各种平台的详细指南

## 目录
- [环境准备](#环境准备)
- [VS Code 配置](#vs-code-配置)
- [开发模式编译](#开发模式编译)
- [生产构建](#生产构建)
- [平台特定配置](#平台特定配置)
- [调试和预览](#调试和预览)
- [常见问题解决](#常见问题解决)
- [性能优化](#性能优化)

## 环境准备

### 1. 基础环境
```bash
# 确保已安装 Node.js 20+
node --version

# 确保已安装 npm
npm --version

# 激活项目环境
conda activate Assessment
```

### 2. 项目依赖安装
```bash
# 进入 uni-app 目录
cd /Volumes/acasis/Assessment/frontend/uni-app

# 安装依赖
npm install

# 验证依赖安装
npm list @dcloudio/vite-plugin-uni
```

## VS Code 配置

### 1. 必需插件安装
```json
// 推荐的 VS Code 插件
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "uni-helper.uni-app-schemas",
    "uni-helper.uni-app-snippets",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ]
}
```

### 2. VS Code 工作区配置
创建 `.vscode/settings.json`：
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "vue.codeActions.enabled": false,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "files.associations": {
    "*.vue": "vue",
    "*.nvue": "vue",
    "*.ux": "vue",
    "*.wxml": "html",
    "*.wxs": "javascript",
    "*.wxss": "css"
  }
}
```

### 3. 任务配置
创建 `.vscode/tasks.json`：
```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "uni-app: 开发H5",
      "type": "shell",
      "command": "npm",
      "args": ["run", "dev:h5"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend/uni-app"
      }
    },
    {
      "label": "uni-app: 开发微信小程序",
      "type": "shell",
      "command": "npm",
      "args": ["run", "dev:mp-weixin"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend/uni-app"
      }
    },
    {
      "label": "uni-app: 构建H5",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build:h5"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "options": {
        "cwd": "${workspaceFolder}/frontend/uni-app"
      }
    }
  ]
}
```

## 开发模式编译

### 1. H5 平台开发
```bash
# 方法一：使用 npm 脚本
cd frontend/uni-app
npm run dev:h5

# 方法二：使用 VS Code 任务
# Ctrl+Shift+P -> Tasks: Run Task -> uni-app: 开发H5

# 方法三：使用集成终端
# 在 VS Code 中打开终端，运行上述命令
```

**访问地址：** http://localhost:5273

**特点：**
- 热重载支持
- 浏览器调试工具可用
- 支持 Vue DevTools
- 代理配置自动生效

### 2. 微信小程序开发
```bash
# 启动微信小程序开发模式
npm run dev:mp-weixin

# 编译完成后，使用微信开发者工具打开
# 项目路径：frontend/uni-app/dist/dev/mp-weixin
```

**配置步骤：**
1. 打开微信开发者工具
2. 选择「导入项目」
3. 项目目录选择：`frontend/uni-app/dist/dev/mp-weixin`
4. AppID 填写测试号或正式 AppID

### 3. App 平台开发
```bash
# 启动 App 开发模式
npm run dev:app

# 需要配合 HBuilderX 或云端打包
```

**注意事项：**
- App 平台需要原生环境支持
- 建议使用 HBuilderX 进行真机调试
- 或使用 DCloud 云端打包服务

## 生产构建

### 1. H5 生产构建
```bash
# 构建 H5 生产版本
npm run build:h5

# 构建完成后，文件位于：
# frontend/uni-app/dist/build/h5/
```

**部署准备：**
```bash
# 检查构建结果
ls -la dist/build/h5/

# 可以直接部署到 Web 服务器
# 或集成到项目的 Nginx 配置中
```

### 2. 微信小程序生产构建
```bash
# 构建微信小程序生产版本
npm run build:mp-weixin

# 构建完成后，文件位于：
# frontend/uni-app/dist/build/mp-weixin/
```

**发布步骤：**
1. 使用微信开发者工具打开构建目录
2. 点击「上传」按钮
3. 填写版本号和项目备注
4. 在微信公众平台提交审核

### 3. App 生产构建
```bash
# 构建 App 生产版本
npm run build:app

# 需要进一步打包成原生应用
```

## 平台特定配置

### 1. 支持的平台列表
根据项目 `manifest.json` 配置，支持以下平台：

- ✅ **H5** - Web 浏览器
- ✅ **微信小程序** - mp-weixin
- ✅ **支付宝小程序** - mp-alipay
- ✅ **百度小程序** - mp-baidu
- ✅ **头条小程序** - mp-toutiao
- ✅ **App** - Android/iOS 原生应用
- ✅ **快应用** - quickapp

### 2. 平台编译命令对照表

| 平台 | 开发命令 | 构建命令 | 输出目录 |
|------|----------|----------|----------|
| H5 | `npm run dev:h5` | `npm run build:h5` | `dist/build/h5` |
| 微信小程序 | `npm run dev:mp-weixin` | `npm run build:mp-weixin` | `dist/build/mp-weixin` |
| 支付宝小程序 | `uni -p mp-alipay` | `uni build -p mp-alipay` | `dist/build/mp-alipay` |
| 百度小程序 | `uni -p mp-baidu` | `uni build -p mp-baidu` | `dist/build/mp-baidu` |
| 头条小程序 | `uni -p mp-toutiao` | `uni build -p mp-toutiao` | `dist/build/mp-toutiao` |
| App | `npm run dev:app` | `npm run build:app` | `dist/build/app` |

### 3. 自定义平台编译
```bash
# 编译到支付宝小程序
cd frontend/uni-app
npx uni -p mp-alipay

# 编译到百度小程序
npx uni -p mp-baidu

# 编译到头条小程序
npx uni -p mp-toutiao

# 编译到快应用
npx uni -p quickapp
```

## 调试和预览

### 1. H5 平台调试
```bash
# 启动开发服务器
npm run dev:h5

# 浏览器访问：http://localhost:5273
# 使用浏览器开发者工具进行调试
```

**调试技巧：**
- 使用 Chrome DevTools
- 安装 Vue DevTools 扩展
- 利用 Network 面板检查 API 请求
- 使用 Console 面板查看日志

### 2. 小程序调试
```bash
# 启动小程序开发模式
npm run dev:mp-weixin

# 使用微信开发者工具调试
```

**调试步骤：**
1. 在微信开发者工具中打开项目
2. 使用调试器面板
3. 查看 Console 输出
4. 使用网络面板检查请求
5. 利用 Storage 面板查看本地存储

### 3. 真机预览
```bash
# 生成预览二维码（需要微信开发者工具）
# 在微信开发者工具中点击「预览」按钮
# 使用微信扫码在真机上预览
```

## 常见问题解决

### 1. 编译错误

**问题：** `Module not found: Error: Can't resolve '@dcloudio/uni-app'`
```bash
# 解决方案：重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

**问题：** TypeScript 类型错误
```bash
# 解决方案：检查类型定义
npm run type-check

# 修复 ESLint 错误
npm run lint:fix
```

### 2. 端口冲突
```bash
# 检查端口占用
lsof -i :5273

# 修改端口配置
# 编辑 vite.config.js 中的 server.port
```

### 3. 代理配置问题
```javascript
// vite.config.js 中的代理配置
proxy: {
  '/api': {
    target: 'http://localhost:8181',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^/api/, '/api')
  }
}
```

### 4. 小程序权限问题
```json
// manifest.json 中配置小程序权限
"mp-weixin": {
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于小程序位置接口的效果展示"
    }
  }
}
```

## 性能优化

### 1. 构建优化
```javascript
// vite.config.js 优化配置
export default defineConfig({
  build: {
    // 启用代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'pinia'],
          utils: ['lodash-es', 'dayjs']
        }
      }
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

### 2. 开发优化
```bash
# 使用并行编译
npm run dev:h5 & npm run dev:mp-weixin

# 启用缓存
export VITE_CACHE=true
npm run dev:h5
```

### 3. 代码质量检查
```bash
# 运行完整的代码检查
npm run lint
npm run type-check
npm run format:check

# 自动修复代码格式
npm run lint:fix
npm run format
```

## 快速命令参考

```bash
# 开发模式
npm run dev:h5              # H5 开发
npm run dev:mp-weixin        # 微信小程序开发
npm run dev:app              # App 开发

# 生产构建
npm run build:h5             # H5 构建
npm run build:mp-weixin      # 微信小程序构建
npm run build:app            # App 构建

# 代码质量
npm run lint                 # ESLint 检查
npm run lint:fix             # 自动修复 ESLint 错误
npm run type-check           # TypeScript 类型检查
npm run format               # 代码格式化
npm run format:check         # 检查代码格式

# 其他平台
npx uni -p mp-alipay         # 支付宝小程序
npx uni -p mp-baidu          # 百度小程序
npx uni -p mp-toutiao        # 头条小程序
npx uni build -p quickapp    # 快应用构建
```

## 总结

本指南涵盖了在 VS Code 中开发和编译 uni-app 项目的完整流程。通过合理配置开发环境和使用适当的工具，可以高效地进行多平台开发。

**关键要点：**
1. 确保环境配置正确
2. 安装必要的 VS Code 插件
3. 熟悉各平台的编译命令
4. 掌握调试和预览技巧
5. 注意平台特定的配置要求

如有问题，请参考项目文档或联系开发团队。