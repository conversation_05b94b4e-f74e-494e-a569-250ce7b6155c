# PDF上传页面功能说明

## 页面概览
`http://localhost:5274/assessment/pdf-upload` - 智能文档解析与数据库结构分析平台

## 🎯 核心功能

### 1. 多格式文档上传
- **支持格式**: PDF, DOCX, XLSX, HTML, 图片文件 (JPG, PNG, GIF)
- **最大文件**: 50MB
- **输出格式**: Markdown (推荐), HTML, JSON
- **拖放上传**: 支持文件拖拽上传

### 2. AI智能解析引擎
- **Docling AI**: 后端文档解析服务 (localhost:8088)
- **实时进度**: 6阶段解析进度显示
- **详细时间线**: 处理步骤详情展示
- **质量评分**: 解析质量自动评估

### 3. 文档编辑与预览
#### 三种编辑模式：
- **👁️ 预览模式**: Markdown渲染预览
- **✏️ 编辑模式**: 纯文本编辑器
- **🔀 分屏模式**: 编辑/预览同时显示

#### 功能特性：
- **实时预览**: Markdown实时渲染
- **内容编辑**: 支持手动修改解析结果
- **多格式下载**: MD/HTML/JSON格式下载

### 4. 🗄️ 数据库结构智能分析
#### AI驱动的数据库设计：
- **LM Studio集成**: 使用gemma-3-27b模型 (*************:1234)
- **智能字段识别**: 自动检测量表字段和类型
- **表结构建议**: 自动生成表名、字段名、数据类型
- **置信度评估**: AI分析结果置信度评分

#### 字段配置管理：
- **可视化编辑**: 表格形式编辑字段属性
- **数据类型选择**: VARCHAR, TEXT, INT, DECIMAL, DATE, DATETIME, BOOLEAN
- **字段属性**: 字段名、类型、长度、是否必填、注释
- **手动调整**: 支持添加、删除、修改字段

#### SQL生成与执行：
- **自动SQL生成**: 根据字段配置生成CREATE TABLE语句
- **SQL预览**: 实时显示生成的SQL语句
- **一键执行**: 直接在数据库中创建表结构
- **表存在性检查**: 自动检查表是否已存在

### 5. 量表配置管理
#### 量表属性编辑：
- **基本信息**: 量表名称、代码、类型、版本
- **时长估算**: 预估评估完成时间
- **详细描述**: 量表用途和说明
- **分类标签**: 支持五种标准量表类型

#### 统计信息显示：
- **文件统计**: 文件大小、内容长度
- **解析性能**: 处理时间、质量评分
- **内容分析**: 表格数量、图片数量

### 6. 配置持久化
- **数据库保存**: 将量表配置保存到数据库
- **版本控制**: 支持配置版本管理
- **配置查询**: 获取已保存的配置列表

## 🔧 技术架构

### 前端技术栈
- **Vue 3**: 响应式前端框架
- **Element Plus**: UI组件库
- **Markdown渲染**: 支持富文本预览
- **文件上传**: 支持拖拽和进度显示

### 后端服务集成
- **Docling Service**: 文档解析服务 (localhost:8088)
- **Spring Boot API**: 主后端服务 (localhost:8181)
- **LM Studio AI**: 本地AI模型服务 (*************:1234)
- **PostgreSQL**: 数据持久化存储

### AI能力
- **文档理解**: 深度解析评估量表结构
- **字段映射**: 智能识别表单字段和选项
- **类型推断**: 自动推断最适合的数据类型
- **结构优化**: 建议最佳的数据库表设计

## 📋 使用流程

### 标准工作流程
1. **📁 选择文件**: 上传PDF/DOCX等评估量表文档
2. **🤖 AI解析**: Docling引擎自动解析文档内容
3. **✏️ 内容编辑**: 手动校对和优化解析结果
4. **🧠 结构分析**: AI分析文档并建议数据库结构
5. **⚙️ 字段配置**: 调整字段名称、类型和属性
6. **💻 SQL生成**: 自动生成CREATE TABLE语句
7. **🗄️ 执行建表**: 在数据库中创建表结构
8. **💾 保存配置**: 持久化量表配置信息

### 高级功能
- **批量处理**: 支持多个文档的批量解析
- **模板管理**: 保存常用的字段配置模板
- **验证规则**: 自动检查字段命名规范
- **备份恢复**: 表结构变更时自动备份

## 🎨 界面布局

### 三栏式设计
- **顶部控制区**: 文件上传、格式选择、服务状态
- **左侧主区域 (75%)**: 文档编辑和数据库结构配置
- **右侧面板 (25%)**: 量表属性和下载选项

### 响应式设计
- **进度显示**: 实时上传和解析进度
- **错误处理**: 友好的错误提示和恢复建议
- **状态指示**: 服务可用性和连接状态显示

## 🚀 性能特性

### 优化策略
- **增量渲染**: 大文档分块渲染防止卡顿
- **缓存机制**: 解析结果本地缓存
- **异步处理**: 文件上传和AI分析异步执行
- **内存管理**: 大文件智能分片处理

### 容错能力
- **服务降级**: AI服务不可用时提供基础解析
- **重试机制**: 网络异常自动重试
- **数据恢复**: 意外中断后的状态恢复

## 📊 监控与分析

### 实时监控
- **解析性能**: 处理时间和成功率统计
- **AI效果**: 分析置信度和准确性跟踪
- **用户行为**: 操作路径和使用模式分析

### 质量保证
- **准确性验证**: AI解析结果的人工校验机制
- **一致性检查**: 确保数据库结构的一致性
- **完整性保障**: 防止数据丢失和不完整解析

---

## 💡 使用建议

1. **最佳实践**: 上传清晰、结构化的PDF文档获得最佳解析效果
2. **AI建议**: 充分利用AI分析建议，但保持人工校验
3. **版本管理**: 为重要量表配置做好版本标记
4. **测试验证**: 建表后进行数据插入测试验证结构正确性

这个页面集成了最先进的AI文档解析技术，为养老评估量表的数字化提供了完整的解决方案。