# LM Studio 配置管理指南

## 🔴 重要更新（v2.0）

**2025-06-17**: 所有模型参数（temperature、max_tokens、top_p等）现在由LM Studio管理，本项目不再设置这些参数。

🚀 **新的工作流程**：
1. 本项目只发送提示词和MD内容给LM Studio
2. 所有模型参数在LM Studio中设置
3. LM Studio返回分析结果供手动确认

## 📖 概述

为了方便管理和升级 LM Studio 的地址和模型配置，我们创建了一个集中的配置管理系统。这个系统支持：

- 🔄 动态切换服务器地址
- 🤖 自动选择最佳模型
- 🚀 简化API调用（只发送提示词和MD内容）
- 🔍 健康检查和自动故障转移
- 📊 实时状态监控

## 🗂️ 配置文件结构

### 1. 主配置文件：`lm-studio-config.yml`

```yaml
lm-studio:
  server:
    primary-url: "http://*************:1234"
    backup-urls:
      - "http://*************:1234"
      - "http://localhost:1234"
    connection-timeout: 10000
    read-timeout: 60000
    max-retries: 3
    health-check-interval: 30

  models:
    preferred:
      chat-models:
        - id: "deepseek/deepseek-r1-0528-qwen3-8b"
          display-name: "DeepSeek R1 (Qwen3 8B)"
          priority: 1
          capabilities: ["chat", "code", "analysis"]
        - id: "qwen3-8b-mlx"
          display-name: "Qwen3 8B MLX"
          priority: 2
          capabilities: ["chat", "chinese"]

  api:
    analysis:
      temperature: 0.1
      max-tokens: 4000
      top-p: 0.9
    chat:
      temperature: 0.7
      max-tokens: 2000
      top-p: 0.9
```

### 2. 配置类：`LMStudioConfig.java`

提供强类型的配置管理，包括：
- 服务器配置
- 模型偏好设置
- API参数配置
- 自动切换规则

### 3. 服务管理：`LMStudioService.java`

负责运行时的服务管理：
- 服务健康检查
- 模型自动选择
- 故障自动切换
- 模型列表缓存

## 🚀 使用方法

### 1. 快速配置更换

如需更换LM Studio服务器：

```yaml
# 编辑 lm-studio-config.yml
lm-studio:
  server:
    primary-url: "http://NEW_IP:1234"  # 修改为新地址
```

### 2. 调整模型选择策略

系统现在**自动从LM Studio获取模型列表**，无需手动配置模型。只需调整选择策略：

```yaml
lm-studio:
  models:
    selection:
      # 优先选择模式（按优先级排序）
      preferred-patterns:
        - "your-new-model.*"      # 添加新的优先模式
        - "deepseek.*r1.*qwen"    # 保持现有优先级
        - ".*chat.*"              # 通用chat模型
```

### 3. 调整API参数

```yaml
lm-studio:
  api:
    analysis:
      temperature: 0.05  # 更保守的温度
      max-tokens: 6000   # 更多token
```

## 🔧 API接口

### 1. 服务状态查询

```bash
# 获取服务状态
GET /api/lm-studio/status

# 获取当前模型信息
GET /api/lm-studio/model/current

# 获取可用模型列表
GET /api/lm-studio/models
```

### 2. 动态切换

```bash
# 切换模型
POST /api/lm-studio/model/switch/{modelId}

# 切换服务器
POST /api/lm-studio/server/switch
Content-Type: application/json
{
  "serverUrl": "http://*************:1234"
}

# 自动切换到最佳服务器
POST /api/lm-studio/server/auto-switch
```

### 3. 配置管理

```bash
# 获取完整配置
GET /api/lm-studio/config

# 获取首选模型配置
GET /api/lm-studio/models/preferred

# 获取自动切换配置
GET /api/lm-studio/config/auto-switch

# 注意：API参数配置已移除，所有模型参数由LM Studio管理
```

## 📊 监控和故障转移

### 自动健康检查

系统每30秒自动检查：
- ✅ 当前服务器健康状态
- 🔄 可用模型列表更新
- 🚨 故障自动切换

### 故障转移策略

1. **服务器故障**：自动切换到备用服务器
2. **模型不可用**：自动选择备用模型
3. **响应超时**：重试或切换服务器

## 🛠️ 高级配置

### 环境变量覆盖

```bash
# 开发环境
export LM_STUDIO_DEV_URL="http://localhost:1234"
export LM_STUDIO_DEV_MODEL="local-model"

# 生产环境
export LM_STUDIO_PROD_URL="http://*************:1234"
export LM_STUDIO_PROD_MODEL="deepseek/deepseek-r1-0528-qwen3-8b"
```

### 自定义配置

```yaml
lm-studio:
  auto-switch:
    enabled: true
    fallback-enabled: true
    server-fallback-enabled: true
    response-time-threshold: 30000  # 30秒超时切换
```

## 🎯 最佳实践

### 1. 模型配置建议

- **分析任务**：使用低温度(0.1)，高精度模型
- **对话任务**：使用中等温度(0.7)，平衡创造性
- **代码生成**：使用专门的代码模型

### 2. 服务器配置建议

- **主服务器**：性能最强的机器
- **备用服务器**：确保至少一个备用
- **本地服务器**：开发和测试环境

### 3. 监控建议

- 定期检查服务状态 API
- 监控模型响应时间
- 设置服务器健康告警

## 🔍 故障排查

### 常见问题

1. **服务连接失败**
   ```bash
   # 检查服务状态
   curl http://*************:1234/v1/models
   ```

2. **模型切换失败**
   ```bash
   # 查看可用模型
   GET /api/lm-studio/models
   ```

3. **配置不生效**
   - 检查 YAML 语法
   - 重启应用加载新配置
   - 查看应用日志

### 日志检查

```bash
# 查看 LM Studio 相关日志
grep "LMStudio\|lm-studio" application.log
```

## 📝 配置变更记录

建议在每次配置变更时记录：

```yaml
# 配置变更日志示例
# 2025-06-17: 添加新的 DeepSeek R1 模型配置
# 2025-06-17: 更新服务器地址为 *************
# 2025-06-17: 优化分析任务温度参数
```

## 🔮 未来扩展

配置系统支持未来扩展：

- 🌐 多区域服务器支持
- 🤖 智能模型选择算法
- 📈 性能指标收集
- 🔐 认证和授权机制
- 🎛️ Web界面配置管理

---

**配置文件位置**：
- 主配置：`/backend/src/main/resources/lm-studio-config.yml`
- Java配置：`/backend/src/main/java/com/assessment/config/LMStudioConfig.java`
- 服务管理：`/backend/src/main/java/com/assessment/service/LMStudioService.java`
- API接口：`/backend/src/main/java/com/assessment/controller/LMStudioController.java`