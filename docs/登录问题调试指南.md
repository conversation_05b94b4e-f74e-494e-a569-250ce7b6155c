# 登录问题调试指南

## 🔍 问题诊断

当前症状：可以直接访问管理后台而不需要登录

## 📋 调试步骤

### 1. 检查浏览器存储状态

在浏览器开发者工具的 **Console** 中执行：

```javascript
// 检查当前认证状态
console.log('=== 认证状态检查 ===');
console.log('Token:', localStorage.getItem('token'));
console.log('User:', localStorage.getItem('user'));
console.log('RefreshToken:', localStorage.getItem('refreshToken'));
console.log('Session Token:', sessionStorage.getItem('token'));
console.log('Dev Startup Clear:', sessionStorage.getItem('dev_startup_clear'));
console.log('Clear Executed:', sessionStorage.getItem('clear_executed'));
```

### 2. 强制清理认证状态

如果发现有token存在，执行以下命令清理：

```javascript
// 强制清理所有认证数据
localStorage.clear();
sessionStorage.clear();
console.log('✅ 所有数据已清理');

// 重新加载页面
window.location.href = '/login';
```

### 3. 检查路由守卫日志

刷新页面后，在Console中查看路由守卫的详细日志：
- 🔐 路由守卫检查
- 🎫 Token状态
- 🔒 页面是否需要认证
- ✅ 路由守卫通过

### 4. 测试正常登录流程

1. 确保页面跳转到 `/login`
2. 使用默认账户登录：
   - 用户名: `admin`
   - 密码: `admin123`
3. 检查是否正确跳转到首页

## ⚠️ 常见问题

### 问题1：开发环境自动清理未生效
**原因**: auth-clear.ts 中的清理机制可能没有正确触发
**解决**: 手动清理localStorage和sessionStorage

### 问题2：Token格式问题
**原因**: 可能存在格式错误的token
**解决**: 清理所有认证数据并重新登录

### 问题3：路由守卫未执行
**原因**: 路由配置问题或Vue Router版本兼容性
**解决**: 检查路由守卫日志输出

## 🔧 快速修复脚本

复制以下代码到浏览器Console执行：

```javascript
// === 快速修复登录问题 ===
console.log('🔧 开始修复登录问题...');

// 1. 清理所有认证数据
localStorage.clear();
sessionStorage.clear();

// 2. 清理可能的认证cookies
document.cookie.split(";").forEach(function(c) { 
  document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
});

// 3. 设置强制登录标记
sessionStorage.setItem('force_login', 'true');

console.log('✅ 修复完成，即将跳转到登录页...');

// 4. 跳转到登录页
setTimeout(() => {
  window.location.href = '/login';
}, 500);
```

## 🔒 安全检查

### 验证登录保护是否正常工作：

1. **清理状态后直接访问**: `http://localhost:5274/assessment/pdf-upload`
   - 预期：自动跳转到登录页
   
2. **登录后访问**: 先登录，再访问管理页面
   - 预期：正常显示管理后台
   
3. **Token过期测试**: 
   ```javascript
   // 设置一个过期的token
   localStorage.setItem('token', 'expired.token.here');
   // 然后访问需要认证的页面，应该跳转到登录页
   ```

## 📊 预期行为

### ✅ 正常情况
- 未登录访问管理页面 → 自动跳转到登录页
- 输入正确用户名密码 → 登录成功并跳转到首页
- 已登录状态访问登录页 → 自动跳转到首页
- Token过期 → 自动清理并跳转到登录页

### ❌ 异常情况（需要修复）
- 未登录直接进入管理后台
- 登录页面无法正常登录
- Token验证失败但仍能访问页面

## 🛠️ 技术细节

### 路由守卫配置
- 所有 `/assessment/*` 路径都需要认证
- Token验证包括格式检查和过期时间检查
- 开发环境会自动清理认证状态

### 安全配置
- 后端API除了公开接口外都需要JWT认证
- Token有效期24小时
- RefreshToken有效期7天

如果以上步骤都无法解决问题，请提供Console中的详细日志信息。