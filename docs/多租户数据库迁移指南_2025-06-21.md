# 智慧养老评估平台 - 多租户数据库迁移指南

**文档版本**: v1.0  
**创建日期**: 2025-06-21  
**最后更新**: 2025-06-21  
**负责人**: 开发团队  

## 📋 迁移概述

本指南描述如何将现有的单租户数据库架构迁移到全新的分层多租户SaaS架构。

### 🎯 迁移目标

- ✅ 从单租户架构升级到多租户SaaS架构
- ✅ 实现完全的数据隔离（物理分区+行级安全）
- ✅ 支持租户自定义量表功能
- ✅ 优化查询性能（分区裁剪）
- ✅ 增强数据安全（敏感数据加密）

### 🏗️ 新架构特点

```
三层架构设计:
├── SaaS平台管理层（全局共享）
│   ├── 租户管理
│   ├── 全局量表注册中心
│   └── 用户身份管理
├── 租户数据隔离层（分区表）
│   ├── 评估对象按租户分区
│   ├── 评估记录按租户分区
│   └── 审计日志按租户分区
└── 业务扩展层
    ├── 任务调度
    ├── 报告模板
    └── 系统配置
```

---

## 🚀 快速开始

### 1. 环境准备

确保你的环境满足以下要求：

```bash
# 检查PostgreSQL版本（需要13+）
psql --version

# 检查必要的扩展
psql -d assessment -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";"
psql -d assessment -c "CREATE EXTENSION IF NOT EXISTS \"pgcrypto\";"
psql -d assessment -c "CREATE EXTENSION IF NOT EXISTS \"pg_stat_statements\";"

# 确保有足够的磁盘空间（建议至少5GB空闲空间）
df -h
```

### 2. 备份现有数据

**⚠️ 重要：迁移前必须备份数据！**

```bash
# 完整数据库备份
pg_dump -h localhost -p 5432 -d assessment -U postgres \
  --clean --create --verbose \
  --file="assessment_backup_$(date +%Y%m%d_%H%M%S).sql"

# 验证备份文件
ls -lh assessment_backup_*.sql
```

### 3. 执行迁移

```bash
# 进入项目目录
cd /Volumes/acasis/Assessment

# 设置数据库连接参数（可选）
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=assessment
export DB_USER=postgres

# 执行迁移脚本
./scripts/migrate-to-multi-tenant.sh
```

### 4. 验证迁移结果

```bash
# 运行测试脚本
./scripts/test-multi-tenant-db.sh

# 检查关键表
psql -d assessment -c "
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('tenants', 'global_scale_registry', 'platform_users')
ORDER BY table_name;
"

# 检查分区表
psql -d assessment -c "
SELECT schemaname, tablename, partitionname 
FROM pg_partitions 
WHERE tablename LIKE 'tenant_%'
ORDER BY tablename, partitionname;
"
```

---

## 📊 详细迁移步骤

### Step 1: 数据库结构迁移

迁移脚本会自动执行以下操作：

1. **备份现有表** - 重命名为 `*_backup_20250621`
2. **创建新表结构** - 基于V2迁移脚本
3. **设置分区** - 创建8个分区表
4. **配置索引** - 优化查询性能
5. **启用RLS** - 行级安全策略
6. **创建触发器** - 自动审计和时间戳

### Step 2: 数据迁移

数据迁移包括以下映射关系：

```sql
-- 机构 → 租户
institutions → tenants
  type → industry (映射规则)
  is_active → status

-- 用户 → 平台用户 + 租户成员
users → platform_users + tenant_user_memberships
  username → username (保持)
  institution_id → tenant_id (通过code映射)

-- 被评估人 → 评估对象
elderly_persons → assessment_subjects
  institution_id → tenant_id (通过code映射)
  所有字段基本保持不变

-- 量表 → 全局注册中心 + 自定义量表
assessment_scales → global_scale_registry (官方量表)
assessment_scales → tenant_custom_scales (自定义量表)
```

### Step 3: 应用层更新

更新后的实体类位于：
- `com.assessment.entity.multitenant.Tenant`
- `com.assessment.entity.multitenant.GlobalScaleRegistry`
- `com.assessment.entity.multitenant.PlatformUser`
- `com.assessment.entity.multitenant.TenantUserMembership`
- `com.assessment.entity.multitenant.AssessmentSubject`
- `com.assessment.entity.multitenant.TenantAssessmentRecord`

---

## 🔧 新功能使用指南

### 1. 租户管理

```java
// 创建新租户
Tenant tenant = Tenant.builder()
    .code("hospital_001")
    .name("第一人民医院")
    .industry("healthcare")
    .subscriptionPlan(SubscriptionPlan.PREMIUM)
    .maxUsers(200)
    .maxMonthlyAssessments(5000)
    .build();
tenantRepository.save(tenant);

// 检查配额
if (tenant.isQuotaExceeded(QuotaType.MONTHLY_ASSESSMENTS)) {
    throw new QuotaExceededException("月度评估次数已达上限");
}
```

### 2. 全局量表注册

```java
// 发布量表到全局注册中心
GlobalScaleRegistry scale = GlobalScaleRegistry.builder()
    .code("elderly_comprehensive_v3")
    .name("老年人综合评估量表v3.0")
    .category("elderly_ability")
    .formSchema(schemaJson)
    .scoringRules(rulesJson)
    .publisherType(PublisherType.PLATFORM)
    .visibility(Visibility.PUBLIC)
    .build();
scaleRegistryRepository.save(scale);
```

### 3. 租户自定义量表

```java
// 租户创建自定义量表
TenantCustomScale customScale = TenantCustomScale.builder()
    .tenantId(currentTenantId)
    .code("cardiac_assessment")
    .name("心脏病专科评估")
    .formSchema(customSchemaJson)
    .scoringRules(customRulesJson)
    .build();
customScaleRepository.save(customScale);
```

### 4. 多租户数据查询

```java
// 设置租户上下文
TenantContext.setTenantId(tenantId);

// 查询会自动应用租户过滤
List<AssessmentSubject> subjects = assessmentSubjectRepository.findAll();
// 只返回当前租户的数据

// 跨租户统计（需要特殊权限）
List<TenantActivityStats> stats = tenantActivityStatsRepository.findAll();
```

---

## 🎯 配置优化建议

### 1. 数据库配置优化

```postgresql
-- postgresql.conf 推荐配置
shared_preload_libraries = 'pg_stat_statements'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

-- 分区相关配置
constraint_exclusion = partition
enable_partition_pruning = on
enable_partitionwise_join = on
enable_partitionwise_aggregate = on
```

### 2. 应用配置

```yaml
# application-multitenant.yml
multitenant:
  identification:
    strategy: header # header, subdomain, jwt
    header-name: X-Tenant-Code
  
  cache:
    tenant-config-ttl: 300s
    scale-schema-ttl: 600s
    
  security:
    encryption:
      enabled: true
      algorithm: AES-256
    audit:
      enabled: true
      retention-days: 365
```

---

## 📈 性能监控

### 1. 关键指标

```sql
-- 租户活跃度统计
SELECT * FROM tenant_activity_stats 
WHERE tenant_code = 'your_tenant_code';

-- 量表使用统计
SELECT * FROM scale_usage_stats 
ORDER BY monthly_usage DESC;

-- 分区大小监控
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    n_tup_ins + n_tup_upd - n_tup_del as row_count
FROM pg_stat_user_tables 
WHERE tablename LIKE 'tenant_%_p%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 2. 性能告警

设置以下监控告警：
- 分区数据不均衡（比例>5:1）
- 查询延迟过高（>500ms）
- 缓存命中率过低（<80%）
- 租户配额接近上限（>90%）

---

## 🚨 故障排除

### 1. 常见问题

**Q: 迁移失败，提示权限不足**
```bash
# 确保数据库用户有足够权限
GRANT ALL PRIVILEGES ON DATABASE assessment TO postgres;
GRANT ALL ON SCHEMA public TO postgres;
```

**Q: 分区查询性能不佳**
```sql
-- 检查分区裁剪是否生效
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM tenant_assessment_records 
WHERE tenant_id = 'specific-tenant-uuid';

-- 应该看到 "Partition Pruning" 信息
```

**Q: 行级安全策略阻止查询**
```sql
-- 检查当前会话设置
SELECT current_setting('app.current_tenant_id', true);

-- 手动设置租户上下文
SET app.current_tenant_id = 'your-tenant-uuid';
```

### 2. 回滚方案

如果迁移失败，可以使用备份恢复：

```bash
# 删除当前数据库
dropdb assessment

# 从备份恢复
psql -f assessment_backup_YYYYMMDD_HHMMSS.sql
```

---

## 📚 参考资料

### 1. 相关文档
- [分层多租户架构设计方案](./分层多租户架构设计方案_2025-06-21.md)
- [PostgreSQL分区表文档](https://www.postgresql.org/docs/current/ddl-partitioning.html)
- [行级安全策略文档](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)

### 2. 脚本文件
- `V2__Create_multi_tenant_architecture.sql` - 数据库结构迁移
- `migrate-to-multi-tenant.sh` - 自动化迁移脚本
- `test-multi-tenant-db.sh` - 测试验证脚本

### 3. 实体类
- `backend/src/main/java/com/assessment/entity/multitenant/` - 新的实体类

---

## ✅ 迁移检查清单

### 迁移前检查
- [ ] 确认PostgreSQL版本（13+）
- [ ] 安装必要的扩展
- [ ] 备份现有数据库
- [ ] 检查磁盘空间充足
- [ ] 停止应用服务

### 迁移执行
- [ ] 运行迁移脚本
- [ ] 检查迁移日志
- [ ] 验证表结构
- [ ] 确认数据迁移
- [ ] 测试基本功能

### 迁移后配置
- [ ] 更新应用配置
- [ ] 部署新实体类
- [ ] 配置监控告警
- [ ] 测试多租户功能
- [ ] 性能基准测试

### 上线准备
- [ ] 更新部署脚本
- [ ] 培训运维团队
- [ ] 准备应急预案
- [ ] 制定回滚计划
- [ ] 用户沟通通知

---

**迁移完成后，你将拥有一个现代化的SaaS多租户评估平台！** 🎉

如有问题，请参考详细日志文件或联系开发团队。