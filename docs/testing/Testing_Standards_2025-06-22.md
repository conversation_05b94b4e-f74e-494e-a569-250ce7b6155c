# 智能评估平台 - 测试规范文档

**文档版本**: v1.0  
**创建日期**: 2025-06-22  
**适用范围**: 后端服务、前端应用  
**目标覆盖率**: 50%（第一阶段）

## 一、测试策略

### 1.1 测试金字塔

```
    E2E Tests (5%)
    ↗ 用户核心流程验证
    
  Integration Tests (30%)
  ↗ API、数据库、外部服务
  
    Unit Tests (65%)
    ↗ 业务逻辑、算法、工具类
```

### 1.2 覆盖率目标

| 类型 | 当前 | 第一阶段目标 | 长期目标 |
|------|------|-------------|----------|
| **整体覆盖率** | 15% | 50% | 75% |
| **Service层** | 10% | 85%+ | 90%+ |
| **Controller层** | 0% | 60% | 80% |
| **Repository层** | 0% | 40% | 70% |

## 二、测试规范

### 2.1 命名规范

#### 测试类命名
```java
// Service测试
public class MultiTenantAuthServiceTest {
    // 单元测试方法
}

// Integration测试  
public class MultiTenantAuthServiceIT {
    // 集成测试方法
}

// Controller测试
public class MultiTenantAuthControllerTest {
    // API测试方法
}
```

#### 测试方法命名
使用 `given_when_then` 模式：
```java
@Test
public void givenValidCredentials_whenAuthenticateUser_thenReturnToken() {
    // 测试逻辑
}

@Test  
public void givenInvalidPassword_whenAuthenticateUser_thenThrowException() {
    // 测试逻辑
}
```

### 2.2 测试结构

#### AAA模式 (Arrange-Act-Assert)
```java
@Test
public void givenValidTenantAndUser_whenAuthenticate_thenReturnTokens() {
    // Arrange - 准备测试数据
    Tenant tenant = TestDataFactory.createDefaultTestTenant();
    PlatformUser user = TestDataFactory.createDefaultTestUser();
    MultiTenantLoginRequest request = new MultiTenantLoginRequest();
    request.setTenantCode(tenant.getCode());
    request.setUsername(user.getUsername());
    request.setPassword("test123");
    
    when(tenantRepository.findByCode(tenant.getCode()))
        .thenReturn(Optional.of(tenant));
    when(userRepository.findByUsername(user.getUsername()))
        .thenReturn(Optional.of(user));
    
    // Act - 执行被测试的方法
    MultiTenantLoginResponse response = authService.authenticateUser(request);
    
    // Assert - 验证结果
    assertThat(response).isNotNull();
    assertThat(response.getAccessToken()).isNotBlank();
    assertThat(response.getRefreshToken()).isNotBlank();
    assertThat(response.getTenantId()).isEqualTo(tenant.getId().toString());
}
```

### 2.3 Mock使用规范

#### 优先级规则
1. **真实对象** > **测试专用对象** > **Mock对象**
2. 只Mock外部依赖和复杂对象
3. 尽量使用`@MockBean`而不是`@Mock`（Spring环境）

#### Mock示例
```java
@ExtendWith(MockitoExtension.class)
class MultiTenantAuthServiceTest {

    @Mock
    private TenantRepository tenantRepository;
    
    @Mock  
    private PlatformUserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @InjectMocks
    private MultiTenantAuthService authService;
    
    // 测试方法...
}
```

## 三、测试分类和要求

### 3.1 单元测试 (Unit Tests)

**覆盖范围**：
- Service层业务逻辑
- 工具类和算法
- 验证逻辑
- 数据转换

**要求**：
- 快速执行（<100ms/test）
- 无外部依赖
- 覆盖率>85%
- 包含边界值测试

**示例**：
```java
@ExtendWith(MockitoExtension.class)
class DefaultScoringStrategyTest {

    @InjectMocks
    private DefaultScoringStrategy scoringStrategy;

    @Test
    void givenAllCorrectAnswers_whenCalculateScore_thenReturnMaxScore() {
        // 测试最高分场景
    }
    
    @Test
    void givenAllIncorrectAnswers_whenCalculateScore_thenReturnZeroScore() {
        // 测试最低分场景
    }
    
    @Test
    void givenNullAnswers_whenCalculateScore_thenThrowException() {
        // 测试异常场景
    }
}
```

### 3.2 集成测试 (Integration Tests)

**覆盖范围**：
- Controller层API测试
- 数据库操作验证
- 多租户隔离验证
- 安全认证流程

**要求**：
- 使用TestContainers或H2数据库
- 真实的Spring上下文
- 覆盖率>60%
- 包含错误场景

**示例**：
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = Replace.NONE)
@Testcontainers
class MultiTenantAuthServiceIT {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("assessment_test")
            .withUsername("test")
            .withPassword("test");

    @Autowired
    private MultiTenantAuthService authService;
    
    @Autowired
    private TestEntityManager entityManager;

    @Test
    @Transactional
    void givenTwoTenants_whenAuthenticateUsers_thenDataIsIsolated() {
        // 测试租户隔离
    }
}
```

### 3.3 API测试 (Controller Tests)

**覆盖范围**：
- REST API端点
- 请求/响应格式
- HTTP状态码
- 权限验证

**要求**：
- 使用MockMvc
- 测试所有HTTP方法
- 验证JSON格式
- 测试权限边界

**示例**：
```java
@WebMvcTest(MultiTenantAuthController.class)
@Import({SecurityConfig.class, TestConfig.class})
class MultiTenantAuthControllerTest {

    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private MultiTenantAuthService authService;

    @Test
    void givenValidLoginRequest_whenPostLogin_thenReturnTokens() throws Exception {
        // Arrange
        MultiTenantLoginRequest request = new MultiTenantLoginRequest();
        // ... 设置请求数据
        
        MultiTenantLoginResponse response = new MultiTenantLoginResponse();
        // ... 设置响应数据
        
        when(authService.authenticateUser(any())).thenReturn(response);
        
        // Act & Assert
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpected(jsonPath("$.accessToken").isNotEmpty())
                .andExpected(jsonPath("$.tenantId").value(response.getTenantId()));
    }
}
```

### 3.4 安全测试 (Security Tests)

**重点场景**：
- 多租户数据隔离
- 权限边界验证
- JWT Token验证
- SQL注入防护

**示例**：
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = Replace.ANY)
class TenantDataIsolationTest {

    @Test
    void givenUserFromTenantA_whenAccessTenantBData_thenAccessDenied() {
        // 创建两个租户的数据
        // 使用租户A的用户尝试访问租户B的数据
        // 验证访问被拒绝
    }
    
    @Test
    void givenMaliciousSQL_whenQueryData_thenNoDataLeakage() {
        // 测试SQL注入防护
    }
}
```

## 四、测试工具和框架

### 4.1 测试依赖

```xml
<!-- 已配置的测试依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>

<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>postgresql</artifactId>
    <scope>test</scope>
</dependency>

<dependency>
    <groupId>com.github.tomakehurst</groupId>
    <artifactId>wiremock-jre8</artifactId>
    <scope>test</scope>
</dependency>
```

### 4.2 测试配置

#### application-test.yml
```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  security:
    password-encoder:
      strength: 4  # 降低密码编码强度，提升测试速度

logging:
  level:
    com.assessment: DEBUG
    org.springframework.security: DEBUG
```

### 4.3 测试数据管理

#### 使用TestDataFactory
```java
// 在测试中使用
@Test
void testUserAuthentication() {
    Tenant tenant = TestDataFactory.createDefaultTestTenant();
    PlatformUser user = TestDataFactory.createDefaultTestUser();
    
    // 保存到数据库
    tenantRepository.save(tenant);
    userRepository.save(user);
    
    // 执行测试...
}
```

## 五、覆盖率监控

### 5.1 JaCoCo配置

已配置目标：
- 指令覆盖率: ≥50%
- 分支覆盖率: ≥40%

### 5.2 排除规则

```xml
<!-- 排除不需要测试的类 -->
<excludes>
    <exclude>**/AssessmentApplication.class</exclude>
    <exclude>**/config/**</exclude>
    <exclude>**/dto/**</exclude>
    <exclude>**/entity/**</exclude>
</excludes>
```

### 5.3 报告查看

```bash
# 运行测试并生成覆盖率报告
mvn clean test

# 查看报告
open target/site/jacoco/index.html
```

## 六、CI/CD集成

### 6.1 构建命令

```bash
# 运行所有测试
mvn clean test verify

# 仅运行单元测试
mvn clean test

# 仅运行集成测试  
mvn clean verify -DskipUTs

# 生成覆盖率报告
mvn clean test jacoco:report
```

### 6.2 质量门禁

构建失败条件：
- 测试覆盖率<50%
- 存在P1级别的测试失败
- OWASP扫描发现高危漏洞

## 七、最佳实践

### 7.1 测试编写顺序

1. **先写失败的测试用例**（TDD红阶段）
2. **编写最少代码使测试通过**（TDD绿阶段）
3. **重构代码保持测试通过**（TDD重构阶段）

### 7.2 测试维护

- 定期清理无用的测试
- 保持测试用例的简洁性
- 及时更新测试数据
- 确保测试之间的独立性

### 7.3 常见陷阱

❌ **避免**：
- 测试依赖外部网络
- 测试之间有依赖关系
- 过度使用Mock
- 忽略边界值测试

✅ **推荐**：
- 使用内存数据库进行测试
- 每个测试独立运行
- 优先使用真实对象
- 全面覆盖边界场景

---

**备注**：
- 本规范将随着项目发展持续更新
- 所有开发者必须遵循此规范
- 每周团队会review测试覆盖率报告