# 多租户架构迁移指南

## 概述

本指南描述了如何将智慧养老评估平台从单租户架构迁移到多租户SaaS架构。

## 架构对比

### 旧架构（单租户）
- `users` - 用户表
- `institutions` - 机构表  
- `elderly_persons` - 老人表
- `assessment_scales` - 评估量表表
- `assessment_records` - 评估记录表

### 新架构（多租户）
- **平台层**
  - `platform_users` - 平台用户（跨租户）
  - `tenants` - 租户管理
  - `tenant_user_memberships` - 租户用户关联
  - `global_scale_registry` - 全局量表注册中心

- **租户数据层（分区表）**
  - `assessment_subjects` - 评估对象（替代elderly_persons）
  - `tenant_assessment_records` - 租户评估记录
  - `tenant_custom_scales` - 租户自定义量表

## 实体映射关系

| 旧实体 | 新实体 | 主要变化 |
|--------|--------|----------|
| User | PlatformUser + TenantUserMembership | 用户信息分为平台级和租户级 |
| Institution | Tenant | 机构升级为租户概念 |
| ElderlyPerson | AssessmentSubject | 支持更通用的评估对象 |
| AssessmentScale | GlobalScaleRegistry + TenantCustomScale | 量表分为全局和租户自定义 |
| AssessmentRecord | TenantAssessmentRecord | 按租户分区存储 |

## 迁移完成状态

### ✅ 完全迁移完成

项目已经完全从单租户架构迁移到多租户架构：

1. **数据库层**：完整的多租户数据库架构
2. **实体层**：全部使用多租户实体
3. **Repository层**：完整的多租户数据访问层
4. **Service层**：完整的多租户业务逻辑
5. **Controller层**：多租户认证控制器
6. **代码清理**：移除所有旧的单租户代码

### 🎯 系统状态

- **编译状态**：✅ 通过
- **代码质量**：✅ 通过 Checkstyle 和 Spotless 检查
- **架构一致性**：✅ 完全多租户架构
- **向后兼容性**：❌ 不再支持旧API（已完全迁移）

## 剩余工作

### 需要完成的任务

1. **前端适配**
   - [ ] 更新登录页面支持租户代码输入
   - [ ] 更新 API 调用以包含租户上下文
   - [ ] 更新用户界面显示租户信息

2. **API 更新**
   - [ ] 创建多租户版本的评估控制器
   - [ ] 创建多租户版本的量表管理控制器
   - [ ] 创建多租户版本的评估对象管理控制器

3. **安全增强**
   - [ ] 实现租户数据隔离过滤器
   - [ ] 添加租户级别的权限检查
   - [ ] 实现跨租户访问控制

4. **数据迁移**
   - [ ] 创建旧数据到新架构的迁移脚本（如果需要）
   - [ ] 迁移现有评估记录（如果需要）
   - [ ] 迁移现有量表数据（如果需要）

## 测试账号

### 租户账号
| 租户代码 | 租户名称 | 管理员账号 | 密码 |
|----------|----------|------------|------|
| demo_hospital | 演示医院 | demo_hospital_admin | password123 |
| demo_nursing | 演示养老院 | demo_nursing_admin | password123 |
| demo_community | 演示社区 | demo_community_admin | password123 |

### 超级管理员
- 用户名：superadmin
- 密码：password123

## 运行检查

使用以下命令检查多租户架构的完整性：

```bash
./scripts/check-multitenant-setup.sh
```

## 启动应用

1. 确保 Docker 服务正在运行
2. 启动应用：
   ```bash
   ./scripts/dev-start-m4.sh
   ```

## 注意事项

1. **完全迁移**：已完全迁移到多租户架构，不再支持旧的单租户代码
2. **数据隔离**：所有租户数据通过分区表物理隔离
3. **性能优化**：使用 8 个分区提高查询性能
4. **安全性**：JWT Token 包含租户信息，确保 API 调用的租户隔离
5. **API变更**：所有新的API都需要租户上下文，旧的API已被移除
6. **编译要求**：项目使用 Java 21 + Spring Boot 3.5.3，需要相应的开发环境
7. **代码质量**：项目强制执行 Checkstyle 和 Spotless 代码规范检查

## 下一步计划

1. 完成前端多租户适配
2. 实现完整的多租户 API
3. 添加租户管理功能
4. 实现量表市场功能
5. 添加租户使用统计和计费功能
