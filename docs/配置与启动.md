激活Assessment环境并启动开发服务：
  # 1. 激活conda环境
  conda activate Assessment

  # 2. 启动数据库服务
  docker-compose -f docker-compose.dev.yml up -d

  # 3. 启动后端服务
  cd backend && ./mvnw spring-boot:run

  # 4. 启动前端服务
  cd frontend/uni-app && npm run dev:h5
  cd frontend/admin && npm run dev

  Apple M4优化版本：
  # 启动优化环境
  ./scripts/dev-start-m4.sh

  # 性能监控
  ./scripts/monitor-m4.sh

 # 测试后端启动
  ./mvnw spring-boot:run

  # 访问健康检查
  curl http://localhost:8080/api/health

  # 启动前端开发
  cd ../frontend/uni-app && npm run dev:h5
  cd ../admin && npm run dev