# LM Studio 通用数据库设计提示词 - 优化版

## 🎯 设计目标

这是一个经过优化的完全通用提示词，能够自动适配任何类型的MD文档，生成高质量的PostgreSQL数据库设计。

**优化重点**:
1. ✅ 强化命名规范一致性
2. ✅ 改进JSON格式标准化  
3. ✅ 增强索引策略设计

---

## 📖 优化版完整提示词（可直接复制使用）

```text
你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为高质量的数据库设计。

## 分析任务
请分析以下文档内容，为其设计一个完整的PostgreSQL数据库结构：

## 设计要求

### 1. 智能识别文档类型
- 自动识别文档是评估量表、调查问卷、数据记录表还是其他类型
- 根据文档结构和内容特征选择合适的数据建模方式
- 提取关键的数据实体和字段信息

### 2. 表结构设计原则
- 根据文档内容创建合适的主表，表名要清晰反映文档用途
- 为文档中的每个数据项目创建对应字段
- 智能选择最合适的PostgreSQL数据类型
- 添加必要的约束条件保证数据完整性
- **命名规范**: 严格使用snake_case命名法，所有表名、字段名、约束名、索引名都必须使用下划线分隔

### 3. 通用必需字段（根据文档类型自动调整）
- id (主键)
- record_id (记录唯一标识)
- 根据文档内容确定的核心业务字段
- 文档中明确的数据项目字段
- created_at, updated_at (时间戳)
- 其他根据文档特征识别的重要字段

### 4. 数据完整性和性能优化
- 添加主键约束和外键约束
- 根据字段特征添加检查约束
- 为经常单独查询的字段创建单列索引
- 为常见的多字段组合查询创建复合索引（如：user_id + created_date、institution_id + status等）
- 考虑数据的实际使用场景和查询性能需求

## 输出格式

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {自动识别：评估量表/调查问卷/数据记录表/其他}
- **主要内容**: {文档核心内容概述}
- **数据项目**: {识别出的数据项目数量和类型}
- **结构特征**: {评分方式/记录格式/数据特征等}
```

### 第二部分：完整SQL设计
```sql
-- ==========================================
-- {文档标题} PostgreSQL数据库设计
-- ==========================================

-- 主数据表
CREATE TABLE {根据文档内容自动确定表名，使用snake_case} (
    -- 主键
    id BIGSERIAL PRIMARY KEY,
    
    -- 记录标识
    record_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- 根据文档内容自动生成的核心字段（严格使用snake_case命名）
    {根据文档具体内容生成所有必要字段},
    
    -- 如果是评估类文档，包含汇总字段
    {如果适用：total_score, result_level等},
    
    -- 业务字段
    notes TEXT,
    status VARCHAR(20) DEFAULT 'active',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 根据内容特征添加的约束条件（约束名使用snake_case）
    {根据文档内容生成合适的CHECK约束}
);

-- 单列索引（为经常单独查询的字段）
{为核心查询字段生成单列索引，索引名使用idx_表名_字段名格式};

-- 复合索引（为常见的多字段组合查询）
{为多字段查询场景生成复合索引，如：
CREATE INDEX idx_表名_用户_日期 ON 表名(user_id, created_at DESC);
CREATE INDEX idx_表名_机构_状态 ON 表名(institution_id, status);
等};

-- 触发器（自动更新时间戳）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

CREATE TRIGGER update_{表名}_updated_at
    BEFORE UPDATE ON {表名}
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 表和字段注释
COMMENT ON TABLE {表名} IS '{根据文档内容生成的表用途说明}';
{为每个字段生成详细注释};
```

### 第三部分：JSON字段定义
```json
{
  "database_design": {
    "document_type": "{识别的文档类型}",
    "table_name": "{生成的表名}",
    "description": "{表的用途说明}",
    "total_fields": {字段总数},
    "fields": [
      {
        "name": "{字段名}",
        "type": "{PostgreSQL数据类型}",
        "length": "{长度(如适用)}",
        "nullable": true/false,
        "default_value": "{默认值}",
        "comment": "{字段说明}",
        "constraints": ["{约束说明}"],
        "source": "{来源于文档的哪个部分}"
      }
    ],
    "indexes": [
      {
        "name": "{索引名}",
        "columns": ["{字段列表}"],
        "type": "btree/gin/gist",
        "index_type": "single/composite",
        "purpose": "{索引用途说明}"
      }
    ],
    "usage_recommendations": [
      "{使用建议1}",
      "{使用建议2}"
    ]
  }
}
```

## 质量要求
✅ 智能识别文档类型，自动适配设计策略
✅ SQL语法完全正确，可直接执行
✅ 字段类型选择合理，充分利用PostgreSQL特性
✅ 包含完整的约束条件和数据验证
✅ 为预期的查询模式创建合适索引（单列+复合）
✅ 包含详细的注释和使用说明
✅ 考虑数据完整性、一致性和实际使用场景
✅ 严格遵循snake_case命名规范

## 重要提醒
- 请根据文档的实际内容和结构进行分析，不要预设文档类型
- 生成的数据库设计应该实用、高效、符合PostgreSQL最佳实践
- 如果文档内容不清晰，请基于常见的数据模式进行合理推断
- 确保生成的SQL可以直接在PostgreSQL中执行
- 所有命名必须使用snake_case格式，包括表名、字段名、约束名、索引名
- JSON输出必须使用标准英文字段名，如"type"而非"类型"
- 索引设计要考虑实际查询场景，既要有单列索引也要有复合索引
```

---

## 🚀 优化改进点

### 1. **命名规范强化** ✅
- 明确要求所有命名使用snake_case
- 约束名和索引名规范化
- 表名和字段名一致性要求

### 2. **JSON格式标准化** ✅
- 强调使用标准英文字段名
- 添加index_type字段区分单列/复合索引
- 改进字段结构的完整性

### 3. **索引策略增强** ✅
- 明确区分单列索引和复合索引
- 提供具体的复合索引使用场景
- 强调查询性能优化考虑

### 4. **质量控制提升** ✅
- 增加了更严格的命名规范检查
- 强化了索引设计要求
- 明确了JSON格式标准

---

## 🎯 关键优化特点

### **更严格的规范要求**
```sql
-- 优化前可能的命名
CREATE TABLE elderlyAssessment (
    recordId VARCHAR(50),
    totalScore INT
);

-- 优化后的标准命名
CREATE TABLE elderly_assessment (
    record_id VARCHAR(50),
    total_score INT
);
```

### **增强的索引策略**
```sql
-- 单列索引
CREATE INDEX idx_elderly_assessment_record_id ON elderly_assessment(record_id);
CREATE INDEX idx_elderly_assessment_created_at ON elderly_assessment(created_at);

-- 复合索引（新增重点）
CREATE INDEX idx_elderly_assessment_user_date ON elderly_assessment(user_id, created_at DESC);
CREATE INDEX idx_elderly_assessment_institution_status ON elderly_assessment(institution_id, status);
```

### **标准化JSON输出**
```json
{
  "fields": [
    {
      "name": "user_id",
      "type": "INTEGER",           // 而非 "类型": "INTEGER"
      "index_type": "composite",   // 新增字段
      "purpose": "User identification and filtering"
    }
  ]
}
```

这个优化版本将进一步提升生成结果的质量和一致性！