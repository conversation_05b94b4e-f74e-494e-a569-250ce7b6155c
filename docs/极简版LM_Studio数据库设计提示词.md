# 极简版LM Studio数据库设计提示词

**设计目标**: 突破token限制，保持核心功能
**适用场景**: Context length < 10000 tokens的模型
**预期压缩率**: 98%+ (相比完整版)

---

## 🎯 极简提示词模板

```
请分析文档并设计PostgreSQL数据库。

输出要求：
1. 表名和用途
2. 完整建表SQL
3. 字段说明JSON

必须包含：主键、created_at、updated_at字段。
SQL必须可执行。
```

---

## 📊 提示词对比

| 版本 | 字符数 | Token估算 | 适用模型 | 质量预期 |
|------|--------|-----------|----------|----------|
| 完整版 | ~3000 | ~1500 | 高性能模型 | 100% |
| 简洁版 | ~1500 | ~750 | 中等模型 | 85% |
| **极简版** | **~100** | **~50** | **所有模型** | **60-70%** |

---

## ⚡ 使用策略

### 自动降级策略
```python
def get_optimal_prompt(model_context_length):
    if model_context_length > 20000:
        return "完整版提示词"
    elif model_context_length > 10000:
        return "简洁版提示词"  
    else:
        return "极简版提示词"  # ← gemma-3使用此版本
```

### 质量保证措施
1. **保留核心要求**: 建表SQL必须可执行
2. **强制字段**: 主键、时间戳字段
3. **输出结构**: 保持三部分格式
4. **后处理验证**: 检查SQL语法正确性

---

## 🔍 预期效果

### 优势
- ✅ 突破任何token限制
- ✅ 处理速度最快
- ✅ 兼容所有模型
- ✅ 减少超时风险

### 劣势  
- ⚠️ 字段设计可能不够详细
- ⚠️ 业务理解可能简化
- ⚠️ 约束条件可能减少
- ⚠️ 注释可能不完整

### 适用场景
- Token限制严格的模型
- 快速原型验证
- 简单文档处理
- 性能测试基准

---

## 🎯 测试目标

使用此极简版提示词测试gemma-3-27b-it模型：
- **主要目标**: 成功生成可执行SQL
- **次要目标**: 保持基本的字段设计合理性
- **性能目标**: 处理时间 < 3分钟
- **质量底线**: >= 50分 (满分145分)

---

## 📝 版本记录

- **v1.0** (2025-06-17): 初版极简提示词
- **测试模型**: gemma-3-27b-it  
- **Context Limit**: 8192 tokens
- **预期结果**: 突破token限制，生成基础数据库设计