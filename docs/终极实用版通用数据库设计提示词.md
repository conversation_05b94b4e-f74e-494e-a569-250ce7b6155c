#!/usr/bin/env python3
"""
使用终极实用版提示词测试qwq-32b模型
专注于SQL质量和可执行性
"""

import requests
import json
import time
from datetime import datetime

def test_ultimate_version():
    """测试终极实用版提示词"""
    print("🚀 测试终极实用版通用数据库设计提示词")
    print("=" * 80)
    
    # LM Studio配置
    lm_studio_url = "http://192.168.1.223:1234"
    target_model = "qwq-32b"
    
    # 终极实用版提示词
    ultimate_prompt = """你是一个经验丰富的PostgreSQL数据库设计师，专门负责将文档内容转换为可直接执行的数据库SQL脚本。

## 核心任务
分析以下文档内容，生成完整的、可直接在PostgreSQL中执行的数据库创建脚本。

## 设计要求

### 1. 文档理解
- 准确识别文档类型和业务用途
- 提取所有数据项并映射为数据库字段
- 理解数据之间的关系和业务规则

### 2. SQL设计规范
- 所有标识符使用snake_case命名（表名、字段名、约束名、索引名）
- 选择最合适的PostgreSQL数据类型
- 添加完整的约束条件保证数据完整性
- 设计高效的索引策略

### 3. 必需的设计元素
- 主键：id BIGSERIAL PRIMARY KEY
- 业务标识：合适的业务唯一标识字段
- 审计字段：created_at, updated_at
- 数据完整性：NOT NULL, CHECK, UNIQUE等约束
- 性能优化：单列索引和复合索引
- 自动化：触发器和函数

## 输出要求

### 第一部分：简要分析（必需）
用2-3句话说明：
- 识别的文档类型
- 主要数据结构
- 设计思路

### 第二部分：完整可执行的SQL脚本（核心输出）
生成结构清晰的SQL脚本，包含：
1. 主表创建
2. 索引设计
3. 约束添加
4. 触发器创建
5. 注释说明
6. 相关表（如需要）

### 第三部分：使用说明（简洁）
简要说明：
- 表的主要用途
- 重要的业务规则
- 查询优化建议

## 质量标准
✅ SQL语法100%正确，可直接复制执行
✅ 完整覆盖文档中的所有数据项
✅ 数据类型选择合理且高效
✅ 包含必要的数据完整性约束
✅ 索引设计平衡了查询性能和写入性能
✅ 代码格式清晰，注释完整

## 注意事项
- 重点是生成可执行的SQL，而不是理论分析
- 优先考虑实用性和可维护性
- 根据文档内容灵活调整表结构，不要过度设计
- 如果文档适合单表设计就用单表，不要强行拆分
- 企业级特性：包含软删除、版本控制、审计字段"""
    
    # 读取国标评估文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        national_standard_doc = f.read()
    
    # 完整提示词
    full_prompt = ultimate_prompt + "\n\n" + national_standard_doc

    try:
        print(f"🔍 连接到LM Studio: {lm_studio_url}")
        print(f"🎯 查找{target_model}模型...")
        
        # 获取可用模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print(f"❌ 无法获取模型列表")
            return False
            
        models_data = models_response.json()
        selected_model = None
        
        for model in models_data['data']:
            if target_model.lower() in model['id'].lower():
                selected_model = model['id']
                break
        
        if not selected_model:
            print(f"❌ 未找到{target_model}模型")
            return False
            
        print(f"✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送终极实用版提示词...")
        print(f"🤖 模型: {selected_model}")
        print(f"📊 提示词长度: {len(full_prompt)} 字符")
        print(f"🎯 专注目标: 可执行的SQL脚本")
        print("⏳ 等待AI生成可执行SQL...")
        
        start_time = time.time()
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=1800
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 终极实用版测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 专注于SQL质量检查
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_primary_key = 'BIGSERIAL PRIMARY KEY' in ai_response.upper()
                has_snake_case = '_' in ai_response and 'camelCase' not in ai_response
                has_constraints = 'CHECK' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_triggers = 'CREATE TRIGGER' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                has_audit_fields = all(field in ai_response for field in ['created_at', 'updated_at'])
                has_enterprise_features = any(feature in ai_response for feature in ['version', 'is_deleted', 'deleted_at'])
                
                # 检查SQL语法合理性
                sql_blocks = ai_response.count('```sql')
                has_proper_sql_structure = sql_blocks >= 1
                
                print(f"\n📊 SQL质量检查（专注实用性）:")
                print(f"   ✅ CREATE TABLE语句: {'通过' if has_create_table else '❌未通过'}")
                print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌未通过'}")
                print(f"   ✅ snake_case命名: {'通过' if has_snake_case else '❌未通过'}")
                print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
                print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
                print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
                print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
                print(f"   ✅ 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
                print(f"   ✅ 企业特性: {'通过' if has_enterprise_features else '❌未通过'}")
                print(f"   ✅ SQL结构: {'通过' if has_proper_sql_structure else '❌未通过'}")
                
                # 保存结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/ultimate_version_test_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 终极实用版提示词测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**LM Studio地址**: {lm_studio_url}\n")
                    f.write(f"**模型名称**: {selected_model}\n")
                    f.write(f"**提示词版本**: 终极实用版（无JSON要求）\n")
                    f.write(f"**处理时间**: {analysis_time:.1f}秒\n")
                    f.write(f"**测试重点**: SQL可执行性和实用性\n\n")
                    f.write("---\n\n")
                    f.write("## AI生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: {result_file}")
                
                # 计算实用性评分
                sql_quality_score = 0
                if has_create_table: sql_quality_score += 15
                if has_primary_key: sql_quality_score += 10
                if has_snake_case: sql_quality_score += 10
                if has_constraints: sql_quality_score += 10
                if has_indexes: sql_quality_score += 10
                if has_triggers: sql_quality_score += 10
                if has_comments: sql_quality_score += 10
                if has_audit_fields: sql_quality_score += 15
                if has_enterprise_features: sql_quality_score += 10
                
                print(f"\n🏆 SQL实用性评分: {sql_quality_score}/100分")
                
                if sql_quality_score >= 90:
                    grade = "A级 - 生产就绪"
                elif sql_quality_score >= 80:
                    grade = "B级 - 优秀"
                elif sql_quality_score >= 70:
                    grade = "C级 - 良好"
                else:
                    grade = "D级 - 需改进"
                
                print(f"🎯 评级: {grade}")
                print(f"🔄 对比: 移除JSON要求后的效果")
                
                # 检查是否有JSON输出（不应该有）
                has_json_output = '"database_design"' in ai_response
                print(f"✅ 确认无冗余JSON: {'成功' if not has_json_output else '仍有JSON输出'}")
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🎯 终极实用版提示词测试")
    print("=" * 80)
    print("💡 核心改进: 移除JSON要求，专注SQL质量")
    print("🎯 测试目标: 验证SQL的可执行性和实用性")
    print("=" * 80)
    
    if test_ultimate_version():
        print(f"\n✅ 终极实用版测试完成!")
        print(f"🎯 专注于生成可执行的SQL脚本")
    else:
        print(f"\n❌ 测试失败")