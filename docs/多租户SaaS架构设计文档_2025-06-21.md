# 智能评估平台 - 多租户SaaS架构设计文档

**文档版本**: v1.0  
**创建日期**: 2025-06-21  
**维护负责人**: 开发团队  
**项目名称**: 智能评估平台 (Assessment Platform)  
**架构类型**: 多租户SaaS (Multi-Tenant Software as a Service)  

---

## 1. 架构概览

### 1.1 设计理念
本项目采用完整的多租户SaaS架构，支持多个机构（租户）共享同一套应用实例，同时确保数据完全隔离和业务逻辑独立。每个租户可以是医疗机构、养老院、评估中心等不同类型的组织。

### 1.2 架构优势
- **成本效益**: 共享基础设施，降低运营成本
- **数据隔离**: 租户间数据完全隔离，确保安全性
- **弹性扩展**: 支持租户数量无限扩展
- **统一管理**: 集中式运维和升级
- **业务独立**: 每个租户可独立配置业务规则

---

## 2. 数据库架构设计

### 2.1 多租户数据模型

#### 2.1.1 核心实体关系图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PlatformUser  │    │     Tenant      │    │AssessmentSubject│
│                 │    │                 │    │                 │
│ - id (UUID)     │    │ - id (UUID)     │    │ - id (UUID)     │
│ - username      │    │ - code (唯一)   │    │ - tenant_id     │
│ - email         │    │ - name          │    │ - name          │
│ - password_hash │    │ - status        │    │ - personal_info │
│ - is_active     │    │ - created_at    │    │ - created_at    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         └───────┐         ┌─────┴─────┐         ┌───────┘
                 │         │           │         │
                 ▼         ▼           ▼         ▼
       ┌─────────────────────┐    ┌─────────────────────┐
       │TenantUserMembership │    │TenantAssessmentRecord│
       │                     │    │                     │
       │ - tenant_id (FK)    │    │ - tenant_id (FK)    │
       │ - user_id (FK)      │    │ - subject_id (FK)   │  
       │ - role              │    │ - scale_id (FK)     │
       │ - permissions       │    │ - assessor_id (FK)  │
       │ - joined_at         │    │ - form_data (JSON)  │
       └─────────────────────┘    │ - total_score       │
                                  │ - result_level      │
                                  │ - status           │
                                  └─────────────────────┘
                                           │
                                           │
                                           ▼
                                  ┌─────────────────────┐
                                  │GlobalScaleRegistry  │
                                  │                     │
                                  │ - id (UUID)         │
                                  │ - name              │
                                  │ - category          │
                                  │ - structure (JSON)  │
                                  │ - scoring_rules     │
                                  │ - visibility        │
                                  │ - usage_count       │
                                  └─────────────────────┘
```

#### 2.1.2 数据隔离策略
本项目采用**基于租户ID的行级安全(Row-Level Security)**策略：

1. **租户标识**: 每个业务数据表都包含 `tenant_id` 字段
2. **查询过滤**: 所有业务查询都必须包含租户过滤条件
3. **索引优化**: 所有业务表的主要索引都以 `tenant_id` 为前缀
4. **数据完整性**: 通过外键约束确保跨表数据一致性

### 2.2 核心数据表设计

#### 2.2.1 平台用户表 (platform_users)
```sql
CREATE TABLE platform_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url TEXT,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.2 租户表 (tenants)
```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url TEXT,
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    address TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    subscription_plan VARCHAR(50) DEFAULT 'BASIC',
    subscription_expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.3 租户用户关系表 (tenant_user_memberships)
```sql
CREATE TABLE tenant_user_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES platform_users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL DEFAULT 'ASSESSOR',
    permissions TEXT[],
    is_active BOOLEAN DEFAULT true,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMP,
    UNIQUE(tenant_id, user_id)
);
```

#### 2.2.4 全局量表注册中心 (global_scale_registry)
```sql
CREATE TABLE global_scale_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    structure JSONB NOT NULL,
    scoring_rules JSONB,
    visibility VARCHAR(20) DEFAULT 'PUBLIC',
    status VARCHAR(20) DEFAULT 'ACTIVE',
    usage_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.2.5 租户评估记录表 (tenant_assessment_records)
```sql
CREATE TABLE tenant_assessment_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    record_number VARCHAR(50) NOT NULL,
    subject_id UUID REFERENCES assessment_subjects(id),
    scale_id UUID REFERENCES global_scale_registry(id),
    scale_type VARCHAR(20) DEFAULT 'GLOBAL',
    assessor_id UUID REFERENCES platform_users(id),
    assessment_date TIMESTAMP NOT NULL,
    assessment_type VARCHAR(20) DEFAULT 'REGULAR',
    form_data JSONB NOT NULL,
    total_score DECIMAL(10,2),
    result_level VARCHAR(50),
    status VARCHAR(20) DEFAULT 'DRAFT',
    reviewer_id UUID REFERENCES platform_users(id),
    review_notes TEXT,
    reviewed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, record_number)
);
```

#### 2.2.6 评估对象表 (assessment_subjects)
```sql
CREATE TABLE assessment_subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10),
    birth_date DATE,
    id_number VARCHAR(50),
    contact_phone VARCHAR(20),
    contact_address TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    medical_history TEXT,
    personal_info JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 3. 应用架构设计

### 3.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   Mobile App    │  │   Admin Web     │  │   REST APIs     ││
│  │   (uni-app)     │  │  (Vue 3 + EP)   │  │  (OpenAPI 3)    ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │ Auth Service    │  │Assessment Service│  │ Scale Service   ││
│  │ JWT + MultiTenant│  │ Multi-Tenant    │  │ Global Registry ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │ User Service    │  │ Report Service  │  │ File Service    ││
│  │ RBAC + Tenant   │  │ PDF/Excel Gen   │  │ MinIO Storage   ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Persistence Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  PostgreSQL 15  │  │    Redis 7      │  │   MinIO Store   ││
│  │  Multi-Tenant   │  │    Cache        │  │  File Storage   ││
│  │  Row Security   │  │    Session      │  │  PDF/Images     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心服务组件

#### 3.2.1 多租户认证服务 (MultiTenantAuthService)
**职责**: 处理租户级别的用户认证和授权
```java
@Service
public class MultiTenantAuthService {
    // 租户登录: tenantCode + username + password
    public MultiTenantLoginResponse login(MultiTenantLoginRequest request)
    
    // JWT令牌生成和验证
    public String generateToken(PlatformUser user, TenantUserMembership membership)
    
    // 权限验证
    public boolean hasPermission(String userId, String tenantId, String permission)
}
```

#### 3.2.2 全局量表服务 (MultiTenantScaleController)
**职责**: 管理全局量表注册中心
```java
@RestController
@RequestMapping("/api/multi-tenant/scales")
public class MultiTenantScaleController {
    // 获取公开量表
    GET /public - 分页查询公开可用量表
    
    // 量表详情
    GET /{scaleId} - 获取量表详细信息
    
    // 分类管理
    GET /categories - 获取所有量表分类
    
    // 使用统计
    POST /{scaleId}/usage - 记录量表使用次数
}
```

#### 3.2.3 多租户评估服务 (AssessmentService)
**职责**: 处理租户范围内的评估业务
```java
@Service
public class AssessmentService {
    // 创建评估记录
    public TenantAssessmentRecord createTenantAssessment(String tenantId, CreateAssessmentRequest request)
    
    // 评估审核流程
    public void reviewTenantAssessment(String tenantId, String recordId, boolean approved, String reviewNotes, String reviewerId)
    
    // 评估历史查询
    public List<TenantAssessmentRecord> getSubjectAssessmentHistory(String tenantId, String subjectId)
}
```

---

## 4. 多租户安全策略

### 4.1 认证机制
1. **租户识别**: 登录时需要提供租户代码(tenantCode)
2. **用户验证**: 验证用户在指定租户中的成员资格
3. **JWT令牌**: 令牌中包含用户ID、租户ID和角色信息
4. **会话管理**: Redis存储会话状态，支持单点登录

### 4.2 授权模型
采用**基于角色的访问控制(RBAC)**：

#### 4.2.1 预定义角色
```yaml
ADMIN:      # 租户管理员
  - 用户管理
  - 系统配置
  - 数据导出
  - 审核管理

ASSESSOR:   # 评估员
  - 创建评估
  - 编辑评估
  - 查看报告

REVIEWER:   # 审核员
  - 审核评估
  - 生成报告
  - 数据统计

VIEWER:     # 查看者
  - 查看评估
  - 查看报告
```

#### 4.2.2 权限粒度控制
```java
@PreAuthorize("@multiTenantSecurityService.hasPermission(authentication.name, #tenantId, 'ASSESSMENT_CREATE')")
public TenantAssessmentRecord createAssessment(@PathVariable String tenantId, @RequestBody CreateAssessmentRequest request)
```

### 4.3 数据隔离保障
1. **查询级别**: 所有数据查询都强制包含租户过滤
2. **ORM级别**: JPA Repository方法都包含租户参数
3. **索引优化**: 复合索引以tenant_id为前缀
4. **审计日志**: 记录所有跨租户操作尝试

---

## 5. API设计规范

### 5.1 RESTful API设计

#### 5.1.1 多租户认证接口
```
POST /api/multi-tenant/auth/login
POST /api/multi-tenant/auth/logout
POST /api/multi-tenant/auth/refresh
GET  /api/multi-tenant/auth/profile
```

#### 5.1.2 租户资源接口
```
# 量表管理
GET    /api/multi-tenant/scales/public
GET    /api/multi-tenant/scales/{scaleId}
GET    /api/multi-tenant/scales/categories
POST   /api/multi-tenant/scales/{scaleId}/usage

# 评估管理 (需要租户上下文)
GET    /api/tenants/{tenantId}/assessments
POST   /api/tenants/{tenantId}/assessments
GET    /api/tenants/{tenantId}/assessments/{recordId}
PUT    /api/tenants/{tenantId}/assessments/{recordId}
DELETE /api/tenants/{tenantId}/assessments/{recordId}

# 评估对象管理
GET    /api/tenants/{tenantId}/subjects
POST   /api/tenants/{tenantId}/subjects
GET    /api/tenants/{tenantId}/subjects/{subjectId}
PUT    /api/tenants/{tenantId}/subjects/{subjectId}
```

### 5.2 统一响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... },
  "errorCode": null,
  "timestamp": 1703156789000
}
```

### 5.3 错误处理
```json
{
  "success": false,
  "message": "租户不存在或已停用",
  "data": null,
  "errorCode": "TENANT_NOT_FOUND",
  "timestamp": 1703156789000
}
```

---

## 6. 部署架构

### 6.1 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  assessment-backend:
    image: assessment-platform:latest
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DATABASE_URL=******************************************************
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=http://minio:9000
    depends_on:
      - postgres
      - redis
      - minio

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=assessment_multitenant
      - POSTGRES_USER=assessment
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
```

### 6.2 扩展策略
1. **水平扩展**: 应用服务无状态，支持多实例部署
2. **数据库分片**: 按租户ID进行数据库分片
3. **缓存分层**: Redis集群 + 应用层缓存
4. **文件存储**: MinIO集群存储

---

## 7. 监控与运维

### 7.1 关键指标监控
```yaml
业务指标:
  - 活跃租户数量
  - 每日评估完成量
  - 用户登录成功率
  - API响应时间

技术指标:
  - 数据库连接池使用率
  - Redis缓存命中率
  - JVM内存使用率
  - 磁盘IO性能
```

### 7.2 日志管理
```yaml
应用日志:
  - 业务操作日志 (INFO)
  - 错误异常日志 (ERROR)
  - 安全审计日志 (AUDIT)

系统日志:
  - 数据库慢查询日志
  - HTTP访问日志
  - 系统资源使用日志
```

---

## 8. 技术栈总结

### 8.1 后端技术栈
```yaml
核心框架: Spring Boot 3.5.3
编程语言: Java 21 LTS
数据库: PostgreSQL 15 + Redis 7
对象存储: MinIO
安全框架: Spring Security + JWT
API文档: SpringDoc OpenAPI 3
构建工具: Maven 3.9+
容器化: Docker + Docker Compose
```

### 8.2 前端技术栈
```yaml
移动端: Vue 3 + uni-app + TypeScript
管理后台: Vue 3 + Element Plus + Vite
状态管理: Pinia
HTTP客户端: Axios
构建工具: Vite 5.x
```

### 8.3 开发工具
```yaml
IDE: IntelliJ IDEA / VS Code
版本控制: Git + GitHub
API测试: Postman / Swagger UI
数据库工具: DBeaver / pgAdmin
性能监控: Spring Boot Actuator
```

---

## 9. 项目状态与路线图

### 9.1 当前完成度
```
✅ 多租户数据模型设计     - 100%
✅ 认证授权系统          - 100%
✅ 核心API接口          - 100%
✅ 数据库迁移脚本        - 100%
✅ 容器化部署配置        - 100%
🚧 前端界面开发          - 60%
🚧 报告生成功能          - 40%
⏳ 性能优化            - 待开始
⏳ 监控告警系统          - 待开始
```

### 9.2 下阶段开发计划
1. **Phase 1 (1个月)**: 完善移动端评估界面
2. **Phase 2 (2个月)**: 实现报告生成和数据导出
3. **Phase 3 (3个月)**: 性能优化和监控系统
4. **Phase 4 (6个月)**: 高级功能和AI集成

---

## 10. 结论

本项目成功实现了完整的多租户SaaS架构，具备了企业级应用的所有核心特性：

- **架构完整性**: 从数据层到应用层全面支持多租户
- **安全可靠性**: 完善的认证授权和数据隔离机制
- **扩展性**: 支持租户数量和用户数量的无限扩展
- **可维护性**: 清晰的分层架构和标准化的开发规范
- **部署便利性**: 完整的容器化部署方案

该架构为智能评估平台提供了坚实的技术基础，能够满足各类机构的评估需求，并支持平台的长期发展和扩展。

---

**文档状态**: ✅ 已完成  
**最后更新**: 2025-06-21 16:30:00  
**下次审查**: 2025-07-21  