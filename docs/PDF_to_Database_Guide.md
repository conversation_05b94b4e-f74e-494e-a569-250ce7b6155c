# PDF评估标准文件转数据库指南

## 概述

本指南详细说明如何将PDF格式的评估标准文件转换为智慧养老评估平台的数据库结构和数据。该过程包括PDF解析、数据提取、结构化处理和数据库生成等步骤。

## 技术架构

### 1. PDF处理技术栈

- **Apache PDFBox**: PDF文档解析和文本提取
- **Apache Tika**: 多格式文档内容提取
- **OpenCV**: 图像处理和表格识别
- **Tesseract OCR**: 光学字符识别
- **Jackson**: JSON数据处理
- **Spring Boot**: 后端服务框架

### 2. 数据处理流程

```mermaid
flowchart TD
    A[PDF文件上传] --> B[PDF解析]
    B --> C[文本提取]
    C --> D[结构化分析]
    D --> E[表格识别]
    E --> F[数据验证]
    F --> G[JSON Schema生成]
    G --> H[数据库结构生成]
    H --> I[评估量表配置]
    I --> J[评分规则配置]
```

## 实施步骤

### 第一步：环境准备

#### 1.1 添加Maven依赖

在 `backend/pom.xml` 中添加以下依赖：

```xml
<!-- PDF处理 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>3.0.1</version>
</dependency>

<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox-tools</artifactId>
    <version>3.0.1</version>
</dependency>

<!-- 文档内容提取 -->
<dependency>
    <groupId>org.apache.tika</groupId>
    <artifactId>tika-core</artifactId>
    <version>2.9.1</version>
</dependency>

<dependency>
    <groupId>org.apache.tika</groupId>
    <artifactId>tika-parsers-standard-package</artifactId>
    <version>2.9.1</version>
</dependency>

<!-- OCR支持 -->
<dependency>
    <groupId>net.sourceforge.tess4j</groupId>
    <artifactId>tess4j</artifactId>
    <version>5.9.0</version>
</dependency>

<!-- 表格处理 -->
<dependency>
    <groupId>technology.tabula</groupId>
    <artifactId>tabula-java</artifactId>
    <version>1.0.5</version>
</dependency>

<!-- JSON Schema生成 -->
<dependency>
    <groupId>com.github.victools</groupId>
    <artifactId>jsonschema-generator</artifactId>
    <version>4.32.0</version>
</dependency>
```

#### 1.2 创建目录结构

```bash
mkdir -p backend/src/main/java/com/assessment/pdf
mkdir -p backend/src/main/java/com/assessment/pdf/parser
mkdir -p backend/src/main/java/com/assessment/pdf/extractor
mkdir -p backend/src/main/java/com/assessment/pdf/generator
mkdir -p backend/src/main/resources/pdf-templates
mkdir -p data/pdf-uploads
mkdir -p data/extracted-schemas
```

### 第二步：PDF解析服务开发

#### 2.1 PDF解析核心服务

创建 `PDFParserService.java`：

```java
@Service
@Slf4j
public class PDFParserService {
    
    @Autowired
    private FileStorageService fileStorageService;
    
    @Autowired
    private SchemaGeneratorService schemaGeneratorService;
    
    /**
     * 解析PDF文件并提取评估标准
     */
    public AssessmentScaleDTO parsePDFToAssessmentScale(MultipartFile pdfFile) {
        try {
            // 1. 保存PDF文件
            String filePath = fileStorageService.storePDFFile(pdfFile);
            
            // 2. 提取PDF内容
            PDFContent content = extractPDFContent(filePath);
            
            // 3. 解析评估标准结构
            AssessmentStructure structure = parseAssessmentStructure(content);
            
            // 4. 生成JSON Schema
            JsonNode schema = schemaGeneratorService.generateSchema(structure);
            
            // 5. 生成评分规则
            JsonNode scoringRules = generateScoringRules(structure);
            
            // 6. 创建评估量表DTO
            return createAssessmentScaleDTO(structure, schema, scoringRules);
            
        } catch (Exception e) {
            log.error("PDF解析失败: {}", e.getMessage(), e);
            throw new PDFParsingException("PDF解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 提取PDF内容
     */
    private PDFContent extractPDFContent(String filePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            String text = stripper.getText(document);
            
            // 提取表格
            List<Table> tables = extractTables(document);
            
            // 提取图像（如果有）
            List<BufferedImage> images = extractImages(document);
            
            return PDFContent.builder()
                .text(text)
                .tables(tables)
                .images(images)
                .pageCount(document.getNumberOfPages())
                .build();
        }
    }
    
    /**
     * 解析评估标准结构
     */
    private AssessmentStructure parseAssessmentStructure(PDFContent content) {
        // 使用正则表达式和自然语言处理技术解析结构
        AssessmentStructureParser parser = new AssessmentStructureParser();
        return parser.parse(content);
    }
}
```

#### 2.2 表格提取服务

创建 `TableExtractionService.java`：

```java
@Service
@Slf4j
public class TableExtractionService {
    
    /**
     * 从PDF中提取表格数据
     */
    public List<AssessmentTable> extractTables(PDDocument document) {
        List<AssessmentTable> tables = new ArrayList<>();
        
        try {
            // 使用Tabula-java提取表格
            ObjectExtractor extractor = new ObjectExtractor(document);
            PageIterator pageIterator = extractor.extract();
            
            while (pageIterator.hasNext()) {
                Page page = pageIterator.next();
                List<Table> pageTables = page.getTables();
                
                for (Table table : pageTables) {
                    AssessmentTable assessmentTable = convertToAssessmentTable(table);
                    if (assessmentTable != null) {
                        tables.add(assessmentTable);
                    }
                }
            }
        } catch (Exception e) {
            log.error("表格提取失败: {}", e.getMessage(), e);
        }
        
        return tables;
    }
    
    /**
     * 转换为评估表格对象
     */
    private AssessmentTable convertToAssessmentTable(Table table) {
        List<List<String>> rows = new ArrayList<>();
        
        for (List<RectangularTextContainer> row : table.getRows()) {
            List<String> cells = new ArrayList<>();
            for (RectangularTextContainer cell : row) {
                cells.add(cell.getText().trim());
            }
            rows.add(cells);
        }
        
        // 识别表格类型和结构
        TableType type = identifyTableType(rows);
        if (type == TableType.UNKNOWN) {
            return null;
        }
        
        return AssessmentTable.builder()
            .type(type)
            .headers(rows.get(0))
            .data(rows.subList(1, rows.size()))
            .build();
    }
}
```

### 第三步：数据结构化处理

#### 3.1 评估结构解析器

创建 `AssessmentStructureParser.java`：

```java
@Component
@Slf4j
public class AssessmentStructureParser {
    
    private static final Pattern SECTION_PATTERN = Pattern.compile("^(\\d+\\.?\\s*)(.*)");
    private static final Pattern QUESTION_PATTERN = Pattern.compile("^(\\d+\\.\\d+|[A-Z]\\.|\\([0-9]+\\))(.*)");
    private static final Pattern OPTION_PATTERN = Pattern.compile("^([A-Z]\\.|\\d+\\)|①②③④⑤)(.*)");
    
    /**
     * 解析PDF内容为评估结构
     */
    public AssessmentStructure parse(PDFContent content) {
        String text = content.getText();
        List<AssessmentTable> tables = content.getTables();
        
        // 1. 提取基本信息
        AssessmentMetadata metadata = extractMetadata(text);
        
        // 2. 解析章节结构
        List<AssessmentSection> sections = parseSections(text, tables);
        
        // 3. 解析评分规则
        ScoringRules scoringRules = parseScoringRules(text, tables);
        
        return AssessmentStructure.builder()
            .metadata(metadata)
            .sections(sections)
            .scoringRules(scoringRules)
            .build();
    }
    
    /**
     * 提取评估量表元数据
     */
    private AssessmentMetadata extractMetadata(String text) {
        String[] lines = text.split("\\n");
        
        String title = extractTitle(lines);
        String version = extractVersion(text);
        String description = extractDescription(text);
        ScaleType type = determineScaleType(title, text);
        
        return AssessmentMetadata.builder()
            .title(title)
            .version(version)
            .description(description)
            .type(type)
            .build();
    }
    
    /**
     * 解析章节结构
     */
    private List<AssessmentSection> parseSections(String text, List<AssessmentTable> tables) {
        List<AssessmentSection> sections = new ArrayList<>();
        String[] lines = text.split("\\n");
        
        AssessmentSection currentSection = null;
        AssessmentQuestion currentQuestion = null;
        
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;
            
            // 检查是否为章节标题
            Matcher sectionMatcher = SECTION_PATTERN.matcher(line);
            if (sectionMatcher.matches()) {
                if (currentSection != null) {
                    sections.add(currentSection);
                }
                currentSection = createSection(sectionMatcher.group(2));
                continue;
            }
            
            // 检查是否为问题
            Matcher questionMatcher = QUESTION_PATTERN.matcher(line);
            if (questionMatcher.matches() && currentSection != null) {
                if (currentQuestion != null) {
                    currentSection.addQuestion(currentQuestion);
                }
                currentQuestion = createQuestion(questionMatcher.group(2));
                continue;
            }
            
            // 检查是否为选项
            Matcher optionMatcher = OPTION_PATTERN.matcher(line);
            if (optionMatcher.matches() && currentQuestion != null) {
                AssessmentOption option = createOption(optionMatcher.group(2));
                currentQuestion.addOption(option);
            }
        }
        
        // 添加最后的章节和问题
        if (currentQuestion != null && currentSection != null) {
            currentSection.addQuestion(currentQuestion);
        }
        if (currentSection != null) {
            sections.add(currentSection);
        }
        
        // 处理表格数据
        processTableData(sections, tables);
        
        return sections;
    }
}
```

### 第四步：JSON Schema生成

#### 4.1 Schema生成服务

创建 `SchemaGeneratorService.java`：

```java
@Service
@Slf4j
public class SchemaGeneratorService {
    
    private final ObjectMapper objectMapper;
    
    public SchemaGeneratorService() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 根据评估结构生成JSON Schema
     */
    public JsonNode generateSchema(AssessmentStructure structure) {
        ObjectNode schema = objectMapper.createObjectNode();
        
        // 设置基本属性
        schema.put("$schema", "http://json-schema.org/draft-07/schema#");
        schema.put("type", "object");
        schema.put("title", structure.getMetadata().getTitle());
        schema.put("description", structure.getMetadata().getDescription());
        
        // 生成属性
        ObjectNode properties = objectMapper.createObjectNode();
        ArrayNode required = objectMapper.createArrayNode();
        
        // 添加基本信息字段
        addBasicFields(properties, required);
        
        // 添加评估章节
        for (AssessmentSection section : structure.getSections()) {
            addSectionToSchema(properties, required, section);
        }
        
        schema.set("properties", properties);
        schema.set("required", required);
        
        return schema;
    }
    
    /**
     * 添加基本信息字段
     */
    private void addBasicFields(ObjectNode properties, ArrayNode required) {
        // 评估基本信息
        ObjectNode basicInfo = objectMapper.createObjectNode();
        basicInfo.put("type", "object");
        basicInfo.put("title", "基本信息");
        
        ObjectNode basicProps = objectMapper.createObjectNode();
        
        // 被评估人信息
        addStringField(basicProps, "elderlyName", "被评估人姓名", true);
        addStringField(basicProps, "elderlyId", "身份证号", true);
        addStringField(basicProps, "assessmentDate", "评估日期", true);
        addStringField(basicProps, "assessorName", "评估员姓名", true);
        
        basicInfo.set("properties", basicProps);
        properties.set("basicInfo", basicInfo);
        required.add("basicInfo");
    }
    
    /**
     * 添加评估章节到Schema
     */
    private void addSectionToSchema(ObjectNode properties, ArrayNode required, AssessmentSection section) {
        ObjectNode sectionSchema = objectMapper.createObjectNode();
        sectionSchema.put("type", "object");
        sectionSchema.put("title", section.getTitle());
        sectionSchema.put("description", section.getDescription());
        
        ObjectNode sectionProps = objectMapper.createObjectNode();
        ArrayNode sectionRequired = objectMapper.createArrayNode();
        
        for (AssessmentQuestion question : section.getQuestions()) {
            addQuestionToSchema(sectionProps, sectionRequired, question);
        }
        
        sectionSchema.set("properties", sectionProps);
        sectionSchema.set("required", sectionRequired);
        
        String sectionKey = toCamelCase(section.getTitle());
        properties.set(sectionKey, sectionSchema);
        required.add(sectionKey);
    }
    
    /**
     * 添加问题到Schema
     */
    private void addQuestionToSchema(ObjectNode properties, ArrayNode required, AssessmentQuestion question) {
        ObjectNode questionSchema = objectMapper.createObjectNode();
        
        switch (question.getType()) {
            case SINGLE_CHOICE:
                addSingleChoiceQuestion(questionSchema, question);
                break;
            case MULTIPLE_CHOICE:
                addMultipleChoiceQuestion(questionSchema, question);
                break;
            case TEXT_INPUT:
                addTextInputQuestion(questionSchema, question);
                break;
            case NUMBER_INPUT:
                addNumberInputQuestion(questionSchema, question);
                break;
            case DATE_INPUT:
                addDateInputQuestion(questionSchema, question);
                break;
        }
        
        String questionKey = toCamelCase(question.getTitle());
        properties.set(questionKey, questionSchema);
        
        if (question.isRequired()) {
            required.add(questionKey);
        }
    }
}
```

### 第五步：数据库集成

#### 5.1 评估量表管理服务

创建 `AssessmentScaleManagementService.java`：

```java
@Service
@Transactional
@Slf4j
public class AssessmentScaleManagementService {
    
    @Autowired
    private AssessmentScaleRepository assessmentScaleRepository;
    
    @Autowired
    private PDFParserService pdfParserService;
    
    /**
     * 从PDF创建评估量表
     */
    public AssessmentScale createScaleFromPDF(MultipartFile pdfFile, String createdBy) {
        try {
            // 1. 解析PDF
            AssessmentScaleDTO scaleDTO = pdfParserService.parsePDFToAssessmentScale(pdfFile);
            
            // 2. 验证数据
            validateScaleData(scaleDTO);
            
            // 3. 检查重复
            if (assessmentScaleRepository.existsByCode(scaleDTO.getCode())) {
                throw new DuplicateScaleException("评估量表代码已存在: " + scaleDTO.getCode());
            }
            
            // 4. 创建实体
            AssessmentScale scale = new AssessmentScale();
            scale.setName(scaleDTO.getName());
            scale.setCode(scaleDTO.getCode());
            scale.setType(scaleDTO.getType());
            scale.setVersion(scaleDTO.getVersion());
            scale.setDescription(scaleDTO.getDescription());
            scale.setFormSchema(scaleDTO.getFormSchema());
            scale.setScoringRules(scaleDTO.getScoringRules());
            scale.setIsActive(true);
            
            // 5. 保存到数据库
            AssessmentScale savedScale = assessmentScaleRepository.save(scale);
            
            log.info("成功从PDF创建评估量表: {} (ID: {})", savedScale.getName(), savedScale.getId());
            return savedScale;
            
        } catch (Exception e) {
            log.error("从PDF创建评估量表失败: {}", e.getMessage(), e);
            throw new ScaleCreationException("创建评估量表失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量导入PDF文件
     */
    public List<AssessmentScale> batchImportFromPDFs(List<MultipartFile> pdfFiles, String createdBy) {
        List<AssessmentScale> createdScales = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        for (MultipartFile pdfFile : pdfFiles) {
            try {
                AssessmentScale scale = createScaleFromPDF(pdfFile, createdBy);
                createdScales.add(scale);
            } catch (Exception e) {
                errors.add(String.format("文件 %s 导入失败: %s", pdfFile.getOriginalFilename(), e.getMessage()));
            }
        }
        
        if (!errors.isEmpty()) {
            log.warn("批量导入部分失败: {}", String.join("; ", errors));
        }
        
        return createdScales;
    }
}
```

### 第六步：Web接口开发

#### 6.1 PDF导入控制器

创建 `PDFImportController.java`：

```java
@RestController
@RequestMapping("/api/pdf-import")
@Slf4j
@Validated
public class PDFImportController {
    
    @Autowired
    private AssessmentScaleManagementService scaleManagementService;
    
    /**
     * 上传PDF文件并创建评估量表
     */
    @PostMapping("/upload")
    public ResponseEntity<ApiResponse<AssessmentScaleVO>> uploadPDF(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "preview", defaultValue = "false") boolean preview,
            Authentication authentication) {
        
        try {
            // 验证文件
            validatePDFFile(file);
            
            if (preview) {
                // 预览模式：只解析不保存
                AssessmentScaleDTO previewData = pdfParserService.parsePDFToAssessmentScale(file);
                AssessmentScaleVO vo = convertToVO(previewData);
                return ResponseEntity.ok(ApiResponse.success(vo));
            } else {
                // 正式导入
                String createdBy = authentication.getName();
                AssessmentScale scale = scaleManagementService.createScaleFromPDF(file, createdBy);
                AssessmentScaleVO vo = convertToVO(scale);
                return ResponseEntity.ok(ApiResponse.success(vo));
            }
            
        } catch (Exception e) {
            log.error("PDF上传处理失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("PDF处理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 批量上传PDF文件
     */
    @PostMapping("/batch-upload")
    public ResponseEntity<ApiResponse<BatchImportResult>> batchUploadPDFs(
            @RequestParam("files") List<MultipartFile> files,
            Authentication authentication) {
        
        try {
            // 验证文件数量
            if (files.size() > 10) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("单次最多上传10个文件"));
            }
            
            // 验证每个文件
            for (MultipartFile file : files) {
                validatePDFFile(file);
            }
            
            // 批量导入
            String createdBy = authentication.getName();
            List<AssessmentScale> scales = scaleManagementService.batchImportFromPDFs(files, createdBy);
            
            BatchImportResult result = BatchImportResult.builder()
                .totalFiles(files.size())
                .successCount(scales.size())
                .failureCount(files.size() - scales.size())
                .createdScales(scales.stream().map(this::convertToVO).collect(Collectors.toList()))
                .build();
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("批量PDF上传处理失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("批量处理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取支持的PDF模板格式
     */
    @GetMapping("/supported-formats")
    public ResponseEntity<ApiResponse<List<PDFFormatInfo>>> getSupportedFormats() {
        List<PDFFormatInfo> formats = Arrays.asList(
            PDFFormatInfo.builder()
                .name("民政部老年人能力评估标准")
                .description("标准的老年人能力评估表格式")
                .version("1.0")
                .supported(true)
                .build(),
            PDFFormatInfo.builder()
                .name("interRAI评估工具")
                .description("国际居民评估工具标准格式")
                .version("1.0")
                .supported(true)
                .build(),
            PDFFormatInfo.builder()
                .name("长期护理保险评估")
                .description("长护险失能等级评估标准")
                .version("1.0")
                .supported(true)
                .build()
        );
        
        return ResponseEntity.ok(ApiResponse.success(formats));
    }
    
    /**
     * 验证PDF文件
     */
    private void validatePDFFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        if (!"application/pdf".equals(file.getContentType())) {
            throw new IllegalArgumentException("只支持PDF格式文件");
        }
        
        if (file.getSize() > 50 * 1024 * 1024) { // 50MB
            throw new IllegalArgumentException("文件大小不能超过50MB");
        }
    }
}
```

## 使用指南

### 1. 准备PDF文件

确保PDF文件符合以下要求：
- 文件格式：PDF
- 文件大小：不超过50MB
- 内容要求：包含清晰的评估标准、问题和选项
- 结构要求：有明确的章节划分和问题编号

### 2. 上传和解析

#### 2.1 单文件上传

```bash
curl -X POST \
  http://localhost:8080/api/pdf-import/upload \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/assessment.pdf' \
  -F 'preview=false'
```

#### 2.2 预览模式

```bash
curl -X POST \
  http://localhost:8080/api/pdf-import/upload \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/assessment.pdf' \
  -F 'preview=true'
```

### 3. 验证结果

上传成功后，系统会：
1. 在数据库中创建新的评估量表记录
2. 生成对应的JSON Schema
3. 配置评分规则
4. 返回量表ID和基本信息

### 4. 测试评估

使用生成的量表进行测试评估：

```bash
curl -X POST \
  http://localhost:8080/api/assessments \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "elderlyId": "ELDERLY_UUID",
    "scaleId": "GENERATED_SCALE_UUID",
    "formData": {
      "basicInfo": {
        "elderlyName": "张三",
        "elderlyId": "110101195001011234",
        "assessmentDate": "2024-01-15",
        "assessorName": "李医生"
      },
      "section1": {
        "question1": "option1",
        "question2": "option2"
      }
    }
  }'
```

## 故障排除

### 常见问题

1. **PDF解析失败**
   - 检查PDF文件是否损坏
   - 确认PDF包含可提取的文本（非纯图片）
   - 验证PDF结构是否符合预期格式

2. **表格识别不准确**
   - 确保PDF中的表格有清晰的边框
   - 检查表格是否为标准格式
   - 考虑手动调整表格识别参数

3. **Schema生成错误**
   - 验证解析出的数据结构
   - 检查问题类型识别是否正确
   - 确认选项格式是否标准

### 日志查看

```bash
# 查看PDF处理日志
tail -f logs/pdf-processing.log

# 查看应用日志
tail -f logs/application.log
```

## 扩展功能

### 1. 自定义解析规则

可以为特定格式的PDF文件创建自定义解析规则：

```java
@Component
public class CustomPDFParser implements PDFParserStrategy {
    
    @Override
    public boolean supports(PDFContent content) {
        // 检查是否支持该PDF格式
        return content.getText().contains("特定标识");
    }
    
    @Override
    public AssessmentStructure parse(PDFContent content) {
        // 自定义解析逻辑
        return customParseLogic(content);
    }
}
```

### 2. 模板管理

创建PDF模板管理功能，支持：
- 模板注册和版本管理
- 解析规则配置
- 模板匹配和自动识别

### 3. 质量检查

添加解析质量检查功能：
- 数据完整性验证
- 结构合理性检查
- 人工审核流程

## 总结

通过本指南，您可以：
1. 将PDF格式的评估标准文件转换为数据库结构
2. 自动生成JSON Schema和评分规则
3. 快速部署新的评估量表
4. 支持多种标准评估工具格式

该解决方案大大简化了评估量表的数字化过程，提高了系统的可扩展性和维护效率。