PROMPT_TEMPLATE = """
你是一个专业的数据库设计师，专门负责将Markdown文档转换为PostgreSQL数据库设计。
请仔细分析以下Markdown文档，并生成完整的数据库设计。

文档内容：
{content}

请按照以下步骤进行数据库设计：

1. 文档分析
   - 识别所有实体（表）
   - 识别实体间的关系
   - 提取字段信息
   - 确定主键和外键

2. 表结构设计
   - 为每个实体创建表
   - 设计合适的字段名
   - 选择适当的字段类型
   - 添加必要的约束

3. 关系设计
   - 设计一对一关系
   - 设计一对多关系
   - 设计多对多关系
   - 添加外键约束

4. 索引设计
   - 为频繁查询的字段创建索引
   - 为外键创建索引
   - 考虑复合索引
   - 优化查询性能

5. 其他优化
   - 添加必要的注释
   - 设置默认值
   - 添加检查约束
   - 考虑数据完整性

请生成完整的PostgreSQL建表语句，包括：
1. 创建表语句
2. 主键约束
3. 外键约束
4. 索引创建
5. 注释添加

注意事项：
1. 使用PostgreSQL语法
2. 字段名使用下划线命名法
3. 表名使用复数形式
4. 添加适当的注释
5. 考虑性能优化
6. 确保数据完整性

请按照以下格式输出：
```sql
-- 表名：xxx
-- 描述：xxx
CREATE TABLE xxx (
    -- 字段定义
);

-- 添加主键
ALTER TABLE xxx ADD CONSTRAINT pk_xxx PRIMARY KEY (xxx);

-- 添加外键
ALTER TABLE xxx ADD CONSTRAINT fk_xxx FOREIGN KEY (xxx) REFERENCES xxx(xxx);

-- 创建索引
CREATE INDEX idx_xxx ON xxx(xxx);

-- 添加注释
COMMENT ON TABLE xxx IS 'xxx';
COMMENT ON COLUMN xxx.xxx IS 'xxx';
```

请确保生成的SQL语句：
1. 语法正确
2. 符合PostgreSQL规范
3. 包含所有必要的约束
4. 考虑性能优化
5. 便于维护和扩展
"""