#!/usr/bin/env python3
"""
文档摘要法 - 另一种解决token限制的方法
先提取文档关键信息，再基于摘要进行数据库设计
"""

import requests
import json
import time
from datetime import datetime

def extract_document_summary(lm_studio_url, model, document):
    """提取文档关键信息摘要"""
    # 将文档分块（每块4000字符）
    chunk_size = 4000
    chunks = [document[i:i+chunk_size] for i in range(0, len(document), chunk_size)]
    
    summaries = []
    
    for i, chunk in enumerate(chunks):
        prompt = f"""请提取以下文档片段的关键信息，专注于数据库设计相关的内容：

## 提取要求
1. 表单字段和数据项
2. 评分规则和约束
3. 数据类型和格式
4. 业务规则和关系

## 输出格式
- 字段列表: {{字段名: 类型和约束}}
- 评分规则: {{具体的评分标准}}
- 业务关系: {{数据之间的关联}}

## 文档片段 {i+1}/{len(chunks)}:
{chunk}"""

        print(f"📄 提取第{i+1}/{len(chunks)}个文档片段...")
        
        request_body = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "stream": False
        }
        
        response = requests.post(
            f"{lm_studio_url}/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=300
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                summaries.append(result['choices'][0]['message']['content'])
        
        time.sleep(1)  # 避免请求过快
    
    # 合并所有摘要
    combined_summary = "\n\n".join([f"## 文档片段{i+1}摘要\n{summary}" for i, summary in enumerate(summaries)])
    return combined_summary

def design_database_from_summary(lm_studio_url, model, summary):
    """基于摘要设计数据库"""
    prompt = f"""你是一个经验丰富的PostgreSQL数据库设计师。

## 任务
基于以下文档摘要，设计完整的PostgreSQL数据库结构。

## 文档摘要
{summary}

## 输出要求

### 第一部分：文档分析
```markdown
## 文档分析结果
- **文档类型**: {{基于摘要识别的类型}}
- **主要内容**: {{核心内容概述}}
- **数据项目**: {{识别出的数据项目}}
- **结构特征**: {{数据特征}}
```

### 第二部分：完整SQL设计
```sql
-- 完整的PostgreSQL数据库设计
-- 包含表结构、索引、约束、触发器、注释
```

### 第三部分：JSON字段定义
```json
{{
  "database_design": {{
    // 完整的字段定义和配置
  }}
}}
```

## 设计原则
- 基于摘要信息设计，不遗漏重要字段
- SQL语法正确，可直接执行
- 包含完整的约束和索引
- 符合PostgreSQL最佳实践"""

    print("🗄️ 基于摘要设计数据库...")
    
    request_body = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "stream": False
    }
    
    response = requests.post(
        f"{lm_studio_url}/v1/chat/completions",
        json=request_body,
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer lm-studio"
        },
        timeout=900
    )
    
    if response.status_code == 200:
        result = response.json()
        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']['content']
    return None

def test_summary_approach():
    """测试文档摘要法"""
    print("🚀 文档摘要法测试 - 解决token限制")
    print("=" * 80)
    
    # 配置
    lm_studio_url = "http://192.168.1.223:1234"
    target_model = "qwq-32b"
    
    # 读取文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        document = f.read()
    
    try:
        # 获取模型
        models_response = requests.get(f"{lm_studio_url}/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
        
        models_data = models_response.json()
        selected_model = None
        
        for model in models_data['data']:
            if target_model.lower() in model['id'].lower():
                selected_model = model['id']
                break
        
        if not selected_model:
            print(f"❌ 未找到{target_model}模型")
            return False
        
        print(f"✅ 选择模型: {selected_model}")
        print(f"📊 原文档长度: {len(document)} 字符")
        
        start_time = time.time()
        
        # 第一步：提取文档摘要
        print("\n🔍 第一步：提取文档关键信息...")
        summary = extract_document_summary(lm_studio_url, selected_model, document)
        
        if not summary:
            print("❌ 文档摘要提取失败")
            return False
        
        summary_time = time.time()
        print(f"✅ 摘要提取完成，耗时: {summary_time - start_time:.1f}秒")
        print(f"📊 摘要长度: {len(summary)} 字符 (压缩率: {len(summary)/len(document)*100:.1f}%)")
        
        # 第二步：基于摘要设计数据库
        print("\n🗄️ 第二步：基于摘要设计数据库...")
        database_design = design_database_from_summary(lm_studio_url, selected_model, summary)
        
        if not database_design:
            print("❌ 数据库设计失败")
            return False
        
        end_time = time.time()
        print(f"✅ 数据库设计完成，耗时: {end_time - summary_time:.1f}秒")
        
        total_time = end_time - start_time
        print(f"\n🎉 文档摘要法测试完成！")
        print(f"⏱️ 总处理时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"/Volumes/acasis/Assessment/test_results/summary_approach_{timestamp}.md"
        
        import os
        os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
        
        complete_result = f"""# 文档摘要法测试结果

## 第一步：文档摘要
{summary}

## 第二步：数据库设计
{database_design}
"""
        
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write(f"# 文档摘要法测试结果\n\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**LM Studio地址**: {lm_studio_url}\n")
            f.write(f"**模型名称**: {selected_model}\n")
            f.write(f"**方法**: 文档摘要法\n")
            f.write(f"**原文档长度**: {len(document)} 字符\n")
            f.write(f"**摘要长度**: {len(summary)} 字符\n")
            f.write(f"**压缩率**: {len(summary)/len(document)*100:.1f}%\n")
            f.write(f"**总处理时间**: {total_time:.1f}秒\n\n")
            f.write("---\n\n")
            f.write(complete_result)
        
        print(f"📄 完整结果已保存到: {result_file}")
        
        # 质量检查
        has_document_analysis = '文档分析' in database_design
        has_sql = 'CREATE TABLE' in database_design.upper()
        has_json = '"database_design"' in database_design
        
        print(f"\n📊 文档摘要法质量检查:")
        print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
        print(f"   ✅ SQL设计: {'通过' if has_sql else '❌未通过'}")
        print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
        
        print(f"\n💡 方法特点:")
        print(f"   📊 压缩效率: {100-len(summary)/len(document)*100:.1f}% 内容压缩")
        print(f"   ⏱️ 处理时间: 分两步进行，总时间{total_time:.1f}秒")
        print(f"   🎯 适用场景: 超长文档，需要保持核心信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 文档摘要法测试")
    print("=" * 80)
    print("💡 解决方案: 先提取关键信息，再基于摘要设计")
    print("📍 优势: 大幅压缩token使用，保持核心信息")
    print("🎪 测试模型: qwq-32b")
    print("=" * 80)
    
    if test_summary_approach():
        print(f"\n✅ 文档摘要法测试成功！")
        print(f"💡 这种方法可以处理超长文档")
        print(f"🎯 通过智能摘要保持信息完整性")
    else:
        print(f"\n❌ 文档摘要法测试失败")