#!/usr/bin/env python3
"""
使用国标评估报告模板测试最终优化版通用数据库设计提示词
测试文档：老年人能力评估报告（GB/T42195-2022)
"""

import requests
import json
import time
from datetime import datetime

def test_with_national_standard():
    """使用国标文档测试最终优化版提示词"""
    print("🚀 测试最终优化版通用数据库设计提示词（国标文档）")
    print("=" * 80)
    
    # 读取最终优化版提示词
    print("📖 读取最终优化版提示词...")
    with open("/Volumes/acasis/Assessment/docs/最终优化版通用数据库设计提示词.md", "r", encoding="utf-8") as f:
        prompt_content = f.read()
    
    # 提取提示词部分
    start_marker = "```text"
    end_marker = "```"
    start_idx = prompt_content.find(start_marker) + len(start_marker)
    end_idx = prompt_content.find(end_marker, start_idx)
    final_optimized_prompt = prompt_content[start_idx:end_idx].strip()
    
    # 读取国标评估文档
    print("📄 读取国标评估报告模板...")
    with open("/Volumes/acasis/Assessment/docker/output/国标评估报告模板1.md", "r", encoding="utf-8") as f:
        national_standard_doc = f.read()
    
    # 完整的提示词 = 最终优化版提示词 + 国标文档
    full_prompt = final_optimized_prompt + "\n\n" + national_standard_doc

    try:
        print("🔍 获取LM Studio可用模型...")
        models_response = requests.get("http://*************:1234/v1/models")
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
            
        models_data = models_response.json()
        selected_model = None
        for model in models_data['data']:
            model_id = model['id'].lower()
            if 'embedding' not in model_id and 'whisper' not in model_id:
                selected_model = model['id']
                break
        
        if not selected_model:
            print("❌ 未找到合适的对话模型")
            return False
            
        print(f"✅ 选择模型: {selected_model}")
        
        # 构建请求
        request_body = {
            "model": selected_model,
            "messages": [
                {"role": "user", "content": full_prompt}
            ],
            "stream": False
        }
        
        print(f"\n📤 发送国标文档到LM Studio...")
        print(f"📊 提示词总长度: {len(full_prompt)} 字符")
        print(f"📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
        print("🎯 验证重点: 完整性、命名规范、企业级特性、JSON输出")
        print("⏳ 等待AI深度分析中（国标文档较复杂，请耐心等待）...")
        
        start_time = time.time()
        
        response = requests.post(
            "http://*************:1234/v1/chat/completions",
            json=request_body,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer lm-studio"
            },
            timeout=1800  # 30分钟超时
        )
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                ai_response = result['choices'][0]['message']['content']
                
                print(f"\n✅ 国标文档测试完成！")
                print(f"⏱️  处理时间: {analysis_time:.1f}秒 ({analysis_time/60:.1f}分钟)")
                print(f"📝 响应长度: {len(ai_response)} 字符")
                
                # 针对国标文档的特殊检查
                # 1. 核心表检查
                has_assessment_info = any(table in ai_response.lower() for table in 
                    ['assessment_info', 'assessment_records', '评估信息'])
                has_elderly_info = any(table in ai_response.lower() for table in 
                    ['elderly_info', 'elderly_person', '老年人信息'])
                has_ability_scores = any(table in ai_response.lower() for table in 
                    ['ability_score', 'assessment_score', '能力评分'])
                has_disease_info = any(table in ai_response.lower() for table in 
                    ['disease', 'diagnosis', '疾病诊断'])
                
                # 2. 国标特定字段检查
                has_adl_fields = any(field in ai_response.lower() for field in 
                    ['eating', 'bathing', 'dressing', '进食', '洗澡', '穿衣'])
                has_cognitive_fields = any(field in ai_response.lower() for field in 
                    ['time_orientation', 'space_orientation', '时间定向', '空间定向'])
                has_risk_fields = any(field in ai_response.lower() for field in 
                    ['fall_risk', 'wander_risk', '跌倒', '走失'])
                
                # 3. 基础功能检查
                has_document_analysis = '文档分析结果' in ai_response or '文档类型' in ai_response
                has_create_table = 'CREATE TABLE' in ai_response.upper()
                has_json = '"database_design"' in ai_response and '"fields"' in ai_response
                has_constraints = 'CHECK' in ai_response.upper()
                has_indexes = 'CREATE INDEX' in ai_response.upper()
                has_comments = 'COMMENT ON' in ai_response.upper()
                has_triggers = 'CREATE TRIGGER' in ai_response.upper()
                
                # 4. 优化特性检查
                snake_case_check = all(pattern in ai_response for pattern in ['_id', '_at', '_by'])
                composite_index_check = 'CREATE INDEX' in ai_response.upper() and any(
                    pattern in ai_response for pattern in [',', 'DESC', 'INCLUDE'])
                json_complete = all(section in ai_response for section in 
                    ['"fields"', '"indexes"', '"constraints"', '"business_rules"'])
                
                # 5. 企业级特性检查
                has_audit_fields = all(field in ai_response for field in 
                    ['created_by', 'updated_by', 'version'])
                has_soft_delete = 'deleted_at' in ai_response
                has_data_quality = any(field in ai_response for field in 
                    ['data_quality', 'validation_errors'])
                
                print(f"\n📊 国标文档质量检查报告:")
                
                print(f"\n1️⃣ 国标核心表设计:")
                print(f"   📋 评估信息表: {'通过' if has_assessment_info else '❌未通过'}")
                print(f"   📋 老年人信息表: {'通过' if has_elderly_info else '❌未通过'}")
                print(f"   📋 能力评分表: {'通过' if has_ability_scores else '❌未通过'}")
                print(f"   📋 疾病诊断表: {'通过' if has_disease_info else '❌未通过'}")
                
                print(f"\n2️⃣ 国标特定字段映射:")
                print(f"   🏥 ADL评估字段: {'通过' if has_adl_fields else '❌未通过'}")
                print(f"   🧠 认知评估字段: {'通过' if has_cognitive_fields else '❌未通过'}")
                print(f"   ⚠️ 风险评估字段: {'通过' if has_risk_fields else '❌未通过'}")
                
                print(f"\n3️⃣ 基础功能检查:")
                print(f"   ✅ 文档分析: {'通过' if has_document_analysis else '❌未通过'}")
                print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌未通过'}")
                print(f"   ✅ JSON定义: {'通过' if has_json else '❌未通过'}")
                print(f"   ✅ 约束条件: {'通过' if has_constraints else '❌未通过'}")
                print(f"   ✅ 索引设计: {'通过' if has_indexes else '❌未通过'}")
                print(f"   ✅ 字段注释: {'通过' if has_comments else '❌未通过'}")
                print(f"   ✅ 触发器: {'通过' if has_triggers else '❌未通过'}")
                
                print(f"\n4️⃣ 优化特性检查:")
                print(f"   🔧 snake_case命名: {'通过' if snake_case_check else '❌未通过'}")
                print(f"   🔧 复合索引设计: {'通过' if composite_index_check else '❌未通过'}")
                print(f"   🔧 完整JSON结构: {'通过' if json_complete else '❌未通过'}")
                
                print(f"\n5️⃣ 企业级特性:")
                print(f"   🏢 审计字段: {'通过' if has_audit_fields else '❌未通过'}")
                print(f"   🏢 软删除: {'通过' if has_soft_delete else '❌未通过'}")
                print(f"   🏢 数据质量: {'通过' if has_data_quality else '❌未通过'}")
                
                # 保存完整结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_file = f"/Volumes/acasis/Assessment/test_results/national_standard_test_{timestamp}.md"
                
                import os
                os.makedirs("/Volumes/acasis/Assessment/test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 国标文档数据库设计测试结果\n\n")
                    f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**LM Studio地址**: http://*************:1234\n")
                    f.write(f"**模型**: {selected_model}\n")
                    f.write(f"**处理时间**: {analysis_time:.1f}秒\n")
                    f.write(f"**测试文档**: 老年人能力评估报告（GB/T42195-2022）\n")
                    f.write(f"**文档长度**: {len(national_standard_doc)} 字符\n")
                    f.write(f"**响应长度**: {len(ai_response)} 字符\n\n")
                    f.write("---\n\n")
                    f.write("## AI生成结果\n\n")
                    f.write(ai_response)
                
                print(f"\n📄 完整结果已保存到: {result_file}")
                
                # 计算质量评分
                # 国标特定评分（40分）
                national_score = 0
                if has_assessment_info: national_score += 10
                if has_elderly_info: national_score += 10
                if has_ability_scores: national_score += 10
                if has_disease_info: national_score += 5
                if has_adl_fields: national_score += 2
                if has_cognitive_fields: national_score += 2
                if has_risk_fields: national_score += 1
                
                # 基础功能评分（50分）
                basic_score = 0
                if has_document_analysis: basic_score += 8
                if has_create_table: basic_score += 12
                if has_json: basic_score += 10
                if has_constraints: basic_score += 8
                if has_indexes: basic_score += 7
                if has_comments: basic_score += 3
                if has_triggers: basic_score += 2
                
                # 优化特性评分（20分）
                optimization_score = 0
                if snake_case_check: optimization_score += 8
                if composite_index_check: optimization_score += 7
                if json_complete: optimization_score += 5
                
                # 企业级特性评分（10分）
                enterprise_score = 0
                if has_audit_fields: enterprise_score += 4
                if has_soft_delete: enterprise_score += 3
                if has_data_quality: enterprise_score += 3
                
                total_score = national_score + basic_score + optimization_score + enterprise_score
                
                print(f"\n🏆 国标文档测试评分汇总:")
                print(f"   🇨🇳 国标适配: {national_score}/40分")
                print(f"   📋 基础功能: {basic_score}/50分")
                print(f"   🔧 优化特性: {optimization_score}/20分")
                print(f"   🏢 企业特性: {enterprise_score}/10分")
                print(f"   📊 总分: {total_score}/120分 ({total_score/120*100:.1f}%)")
                
                # 评级
                if total_score >= 108:  # 90%
                    grade = "S级 - 完美适配国标"
                elif total_score >= 96:  # 80%
                    grade = "A级 - 优秀"
                elif total_score >= 84:  # 70%
                    grade = "B级 - 良好"
                elif total_score >= 72:  # 60%
                    grade = "C级 - 合格"
                else:
                    grade = "D级 - 需改进"
                
                print(f"\n🎯 最终评级: {grade}")
                
                # 显示部分响应预览
                print(f"\n📄 响应预览（前800字符）:")
                print("=" * 80)
                print(ai_response[:800] + "..." if len(ai_response) > 800 else ai_response)
                print("=" * 80)
                
                return True
                
            else:
                print("❌ AI响应格式异常")
                return False
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏳ AI分析超时（超过30分钟）")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🇨🇳 国标文档数据库设计测试")
    print("=" * 80)
    print("📍 LM Studio地址: http://*************:1234")
    print("📄 测试文档: 老年人能力评估报告（GB/T42195-2022）")
    print("🎯 测试目标: 验证对实际国标文档的数据库设计能力")
    print("⚡ 特别关注: 复杂表结构、多维度评估、国标字段映射")
    print("=" * 80)
    
    if test_with_national_standard():
        print(f"\n✅ 国标文档测试成功完成!")
        print(f"📊 请查看详细测试结果文件")
        print(f"🎯 这是最接近实际应用场景的测试")
    else:
        print(f"\n❌ 测试失败，请检查LM Studio连接")

if __name__ == "__main__":
    main()