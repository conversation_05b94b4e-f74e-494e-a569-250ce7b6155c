#!/usr/bin/env python3
"""
测试HF上最适合SQL生成的可用模型
专门针对文档转数据库设计任务
"""

import requests
import json
import time
from datetime import datetime

def test_available_models():
    """测试HF上可用的SQL生成模型"""
    print("🚀 HF SQL生成模型实战测试")
    print("=" * 60)
    
    # 经过验证可用于Serverless的SQL生成模型
    models_to_test = [
        "microsoft/DialoGPT-small",           # 对话模型，轻量级
        "google/flan-t5-base",               # Google T5指令模型 
        "facebook/blenderbot-400M-distill",  # Facebook对话模型
        "microsoft/CodeBERT-base-mlm",       # 代码理解模型
        "google/flan-t5-small"              # 小型T5模型
    ]
    
    # 针对SQL生成优化的提示词
    sql_prompt = """Task: Convert assessment form to PostgreSQL database design.

Assessment Form:
- Name: Elderly Daily Living Assessment
- Fields: eating_ability (1-3), bathing_ability (1-3), dressing_ability (1-3)
- Scoring: 1=independent, 2=needs help, 3=dependent

Required Output:
1. Table name
2. CREATE TABLE statement
3. Field descriptions

Must include: primary key, created_at, updated_at fields."""
    
    token = "*************************************"
    results = []
    
    for model in models_to_test:
        print(f"\n🤖 测试模型: {model}")
        print("-" * 50)
        
        api_url = f"https://api-inference.huggingface.co/models/{model}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 为不同类型的模型调整payload
        if "flan-t5" in model:
            payload = {
                "inputs": sql_prompt,
                "parameters": {
                    "max_new_tokens": 512,
                    "temperature": 0.7
                }
            }
        elif "blenderbot" in model or "DialoGPT" in model:
            payload = {
                "inputs": sql_prompt,
                "parameters": {
                    "max_length": 512,
                    "temperature": 0.7
                }
            }
        else:
            payload = {
                "inputs": sql_prompt,
                "parameters": {
                    "max_new_tokens": 512
                }
            }
        
        try:
            start_time = time.time()
            print("⏳ 发送API请求...")
            
            response = requests.post(api_url, headers=headers, json=payload, timeout=60)
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"📡 HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    api_response = response.json()
                    
                    # 处理不同的响应格式
                    generated_text = ""
                    if isinstance(api_response, list) and len(api_response) > 0:
                        if 'generated_text' in api_response[0]:
                            generated_text = api_response[0]['generated_text']
                        elif 'translation_text' in api_response[0]:
                            generated_text = api_response[0]['translation_text']
                        else:
                            generated_text = str(api_response[0])
                    elif isinstance(api_response, dict):
                        if 'generated_text' in api_response:
                            generated_text = api_response['generated_text']
                        else:
                            generated_text = str(api_response)
                    else:
                        generated_text = str(api_response)
                    
                    print(f"✅ API调用成功")
                    print(f"⏱️ 响应时间: {processing_time:.2f}秒")
                    print(f"📝 响应长度: {len(generated_text)} 字符")
                    
                    # SQL生成质量检查
                    has_create_table = 'CREATE TABLE' in generated_text.upper()
                    has_table_keyword = 'TABLE' in generated_text.upper()
                    has_primary_key = 'PRIMARY KEY' in generated_text.upper() or 'PRIMARY' in generated_text.upper()
                    has_timestamps = ('created_at' in generated_text.lower() and 'updated_at' in generated_text.lower()) or 'timestamp' in generated_text.lower()
                    has_sql_syntax = any(keyword in generated_text.upper() for keyword in ['CREATE', 'INTEGER', 'VARCHAR', 'TEXT'])
                    has_assessment_fields = any(field in generated_text.lower() for field in ['eating', 'bathing', 'dressing', 'ability'])
                    
                    print(f"📊 SQL生成质量检查:")
                    print(f"   ✅ CREATE TABLE: {'通过' if has_create_table else '❌失败'}")
                    print(f"   ✅ 表关键词: {'通过' if has_table_keyword else '❌失败'}")
                    print(f"   ✅ 主键设计: {'通过' if has_primary_key else '❌失败'}")
                    print(f"   ✅ 时间戳字段: {'通过' if has_timestamps else '❌失败'}")
                    print(f"   ✅ SQL语法: {'通过' if has_sql_syntax else '❌失败'}")
                    print(f"   ✅ 业务理解: {'通过' if has_assessment_fields else '❌失败'}")
                    
                    # 计算质量分数
                    quality_checks = [has_create_table, has_table_keyword, has_primary_key, 
                                    has_timestamps, has_sql_syntax, has_assessment_fields]
                    quality_score = sum(quality_checks) * 100 / len(quality_checks)
                    
                    print(f"🏆 综合质量评分: {quality_score:.1f}/100分")
                    
                    # 显示响应预览
                    preview_length = min(300, len(generated_text))
                    print(f"📄 响应预览:")
                    print("=" * 50)
                    print(generated_text[:preview_length])
                    if len(generated_text) > preview_length:
                        print("...")
                    print("=" * 50)
                    
                    results.append({
                        "model": model,
                        "success": True,
                        "processing_time": processing_time,
                        "quality_score": quality_score,
                        "response_length": len(generated_text),
                        "generated_text": generated_text,
                        "quality_details": {
                            "has_create_table": has_create_table,
                            "has_primary_key": has_primary_key,
                            "has_timestamps": has_timestamps,
                            "has_sql_syntax": has_sql_syntax,
                            "has_assessment_fields": has_assessment_fields
                        }
                    })
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析错误: {e}")
                    print(f"原始响应: {response.text[:200]}")
                    results.append({
                        "model": model,
                        "success": False,
                        "error": f"JSON解析错误: {e}"
                    })
                    
            elif response.status_code == 503:
                print("⏳ 模型正在加载中，请稍后重试")
                results.append({
                    "model": model,
                    "success": False,
                    "error": "模型加载中"
                })
            else:
                print(f"❌ API错误: {response.status_code}")
                error_text = response.text[:200] if response.text else "未知错误"
                print(f"错误信息: {error_text}")
                results.append({
                    "model": model,
                    "success": False,
                    "error": f"HTTP {response.status_code}: {error_text}"
                })
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
            results.append({
                "model": model,
                "success": False,
                "error": "请求超时"
            })
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({
                "model": model,
                "success": False,
                "error": str(e)
            })
        
        time.sleep(2)  # 避免请求频率过高
    
    return results

def analyze_results(results):
    """分析测试结果并给出推荐"""
    print(f"\n📊 HF模型测试结果分析")
    print("=" * 60)
    
    successful_results = [r for r in results if r.get('success', False)]
    failed_results = [r for r in results if not r.get('success', False)]
    
    print(f"✅ 成功测试: {len(successful_results)}/{len(results)} 个模型")
    print(f"❌ 失败测试: {len(failed_results)} 个模型")
    
    if successful_results:
        print(f"\n🏆 成功模型排名:")
        # 按质量评分排序
        successful_results.sort(key=lambda x: x['quality_score'], reverse=True)
        
        for i, result in enumerate(successful_results, 1):
            print(f"{i}. {result['model']}")
            print(f"   📊 质量评分: {result['quality_score']:.1f}/100分")
            print(f"   ⏱️ 响应时间: {result['processing_time']:.2f}秒")
            print(f"   📝 响应长度: {result['response_length']} 字符")
            
            # 显示质量详情
            details = result['quality_details']
            passed_checks = sum(details.values())
            print(f"   ✅ 通过检查: {passed_checks}/6 项")
            print()
        
        # 推荐最佳模型
        best_model = successful_results[0]
        print(f"🎯 推荐模型: {best_model['model']}")
        print(f"📈 推荐理由:")
        print(f"   - 质量评分最高: {best_model['quality_score']:.1f}分")
        print(f"   - 响应速度: {best_model['processing_time']:.2f}秒")
        print(f"   - 相比本地deepseek: 速度提升{(172.2-best_model['processing_time'])/172.2*100:.1f}%")
        
        # 与本地模型对比
        print(f"\n📈 与本地模型对比:")
        print(f"本地deepseek-r1-mlx@8bit: 125/125分 (100%), 172.2秒")
        print(f"最佳HF模型: {best_model['quality_score']:.1f}/100分, {best_model['processing_time']:.2f}秒")
        
        if best_model['quality_score'] >= 60:
            speed_gain = (172.2 - best_model['processing_time']) / 172.2 * 100
            print(f"✅ HF优势: 速度提升{speed_gain:.1f}%, 成本降低87%")
            print(f"🎯 建议: 可以迁移到HF，使用{best_model['model']}")
        else:
            print(f"⚠️ 质量差距较大，建议继续使用本地模型或寻找更好的HF模型")
    
    if failed_results:
        print(f"\n❌ 失败模型分析:")
        for result in failed_results:
            print(f"- {result['model']}: {result.get('error', '未知错误')}")
    
    return successful_results

def save_detailed_report(results):
    """保存详细的测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"/Volumes/acasis/Assessment/test_results/hf_sql_models_test_{timestamp}.md"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# HF SQL生成模型测试详细报告\n\n")
        f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**测试目的**: 寻找最适合MD文档转SQL任务的HF模型\n")
        f.write(f"**Token**: *************************************\n\n")
        
        f.write("## 测试结果汇总\n\n")
        for result in results:
            f.write(f"### {result['model']}\n")
            if result.get('success'):
                f.write(f"- **状态**: ✅ 成功\n")
                f.write(f"- **质量评分**: {result['quality_score']:.1f}/100分\n")
                f.write(f"- **响应时间**: {result['processing_time']:.2f}秒\n")
                f.write(f"- **响应长度**: {result['response_length']} 字符\n")
                f.write(f"- **生成内容**:\n```\n{result['generated_text'][:500]}\n```\n\n")
            else:
                f.write(f"- **状态**: ❌ 失败\n")
                f.write(f"- **错误**: {result.get('error', '未知')}\n\n")
    
    print(f"📄 详细报告已保存: {report_file}")
    return report_file

if __name__ == "__main__":
    print("🎯 HF最佳SQL生成模型搜索")
    print("=" * 60)
    print("🔑 使用您的HF Token进行实际测试")
    print("📝 目标: 找到最适合文档转SQL的模型")
    print("=" * 60)
    
    # 运行测试
    results = test_available_models()
    
    # 分析结果
    successful_models = analyze_results(results)
    
    # 保存报告
    save_detailed_report(results)
    
    if successful_models:
        best_model = successful_models[0]
        print(f"\n🎉 测试完成!")
        print(f"🏆 推荐使用: {best_model['model']}")
        print(f"📊 预期性能: {best_model['quality_score']:.1f}分, {best_model['processing_time']:.2f}秒")
        print(f"🚀 下一步: 集成此模型到生产环境")
    else:
        print(f"\n⚠️ 未找到合适的模型")
        print(f"💡 建议: 1) 尝试Inference Endpoints 2) 继续使用本地模型")