# 智慧养老评估平台 - 自动环境激活配置
# 简化版本，适用于 direnv

# 初始化 conda
source /Volumes/acasis/miniconda3/miniconda3/etc/profile.d/conda.sh

# 激活 Assessment 环境
conda activate Assessment

# 显示激活信息
echo "✅ 已激活 Assessment 环境"

# 设置项目环境变量
export PROJECT_ROOT="$(pwd)"
export PROJECT_NAME="智慧养老评估平台"
export NODE_ENV="development"
export SPRING_PROFILES_ACTIVE="local"
export LOG_LEVEL="DEBUG"

# 添加项目脚本到PATH
export PATH="$PROJECT_ROOT/scripts:$PATH"

# 加载.env文件（如果存在）
if [[ -f ".env" ]]; then
    set -a
    source .env
    set +a
fi

echo "📁 项目路径: $PROJECT_ROOT"
echo "🐍 Conda环境: $CONDA_DEFAULT_ENV"
echo ""
echo "🚀 快速命令:"
echo "  dev-start-m4.sh  # 启动服务"
echo "  monitor-m4.sh    # 监控性能"
echo ""